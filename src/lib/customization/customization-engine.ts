// Template customization engine for CVLeap
// Handles dynamic styling, CSS custom properties, and template customization logic

import { 
  TemplateCustomization, 
  ColorScheme, 
  Typography, 
  LayoutSettings, 
  SpacingSettings,
  CSSCustomProperties,
  CustomizationValidation,
  DEFAULT_COLOR_SCHEME,
  DEFAULT_TYPOGRAPHY,
  DEFAULT_LAYOUT,
  DEFAULT_SPACING
} from '@/types/customization';

export class CustomizationEngine {
  private customization: TemplateCustomization;
  private cssProperties: CSSCustomProperties = {};

  constructor(customization?: Partial<TemplateCustomization>) {
    this.customization = this.mergeWithDefaults(customization);
    this.generateCSSProperties();
  }

  /**
   * Merge user customization with default values
   */
  private mergeWithDefaults(customization?: Partial<TemplateCustomization>): TemplateCustomization {
    const now = new Date();
    
    return {
      id: customization?.id || 'default',
      templateId: customization?.templateId || '',
      name: customization?.name || 'Default',
      colorScheme: { ...DEFAULT_COLOR_SCHEME, ...customization?.colorScheme },
      typography: { ...DEFAULT_TYPOGRAPHY, ...customization?.typography },
      layout: { ...DEFAULT_LAYOUT, ...customization?.layout },
      spacing: { ...DEFAULT_SPACING, ...customization?.spacing },
      isDefault: customization?.isDefault ?? true,
      createdAt: customization?.createdAt || now,
      updatedAt: customization?.updatedAt || now
    };
  }

  /**
   * Generate CSS custom properties from customization
   */
  private generateCSSProperties(): void {
    const { colorScheme, typography, layout, spacing } = this.customization;

    // Color properties
    this.cssProperties = {
      // Colors
      '--template-color-primary': colorScheme.primary,
      '--template-color-secondary': colorScheme.secondary,
      '--template-color-accent': colorScheme.accent,
      '--template-color-background': colorScheme.background,
      '--template-color-text': colorScheme.text,
      '--template-color-text-secondary': colorScheme.textSecondary,
      '--template-color-border': colorScheme.border,
      '--template-color-surface': colorScheme.surface,
      '--template-color-surface-secondary': colorScheme.surfaceSecondary,

      // Typography
      '--template-font-heading': typography.headingFont,
      '--template-font-body': typography.bodyFont,
      '--template-font-size-xs': typography.headingSize.xs,
      '--template-font-size-sm': typography.headingSize.sm,
      '--template-font-size-base': typography.headingSize.base,
      '--template-font-size-lg': typography.headingSize.lg,
      '--template-font-size-xl': typography.headingSize.xl,
      '--template-font-size-2xl': typography.headingSize['2xl'],
      '--template-font-size-3xl': typography.headingSize['3xl'],
      '--template-font-size-4xl': typography.headingSize['4xl'],
      '--template-line-height-tight': typography.lineHeight.tight,
      '--template-line-height-normal': typography.lineHeight.normal,
      '--template-line-height-relaxed': typography.lineHeight.relaxed,
      '--template-line-height-loose': typography.lineHeight.loose,
      '--template-font-weight-normal': typography.fontWeight.normal,
      '--template-font-weight-medium': typography.fontWeight.medium,
      '--template-font-weight-semibold': typography.fontWeight.semibold,
      '--template-font-weight-bold': typography.fontWeight.bold,

      // Layout
      '--template-margin-top': layout.pageMargins.top,
      '--template-margin-right': layout.pageMargins.right,
      '--template-margin-bottom': layout.pageMargins.bottom,
      '--template-margin-left': layout.pageMargins.left,
      '--template-section-spacing': layout.sectionSpacing,
      '--template-column-gap': layout.columnGap,
      '--template-max-width': layout.maxWidth,

      // Spacing
      '--template-section-gap': spacing.sectionGap,
      '--template-item-gap': spacing.itemGap,
      '--template-paragraph-gap': spacing.paragraphGap,
      '--template-list-gap': spacing.listGap
    };
  }

  /**
   * Get CSS custom properties as a style object
   */
  getCSSProperties(): CSSCustomProperties {
    return this.cssProperties;
  }

  /**
   * Get CSS custom properties as a CSS string
   */
  getCSSString(): string {
    return Object.entries(this.cssProperties)
      .map(([property, value]) => `${property}: ${value};`)
      .join('\n');
  }

  /**
   * Apply customization to a DOM element
   */
  applyToElement(element: HTMLElement): void {
    Object.entries(this.cssProperties).forEach(([property, value]) => {
      element.style.setProperty(property, value);
    });
  }

  /**
   * Update color scheme
   */
  updateColorScheme(colorScheme: Partial<ColorScheme>): void {
    this.customization.colorScheme = { ...this.customization.colorScheme, ...colorScheme };
    this.customization.updatedAt = new Date();
    this.generateCSSProperties();
  }

  /**
   * Update typography
   */
  updateTypography(typography: Partial<Typography>): void {
    this.customization.typography = { ...this.customization.typography, ...typography };
    this.customization.updatedAt = new Date();
    this.generateCSSProperties();
  }

  /**
   * Update layout settings
   */
  updateLayout(layout: Partial<LayoutSettings>): void {
    this.customization.layout = { ...this.customization.layout, ...layout };
    this.customization.updatedAt = new Date();
    this.generateCSSProperties();
  }

  /**
   * Update spacing settings
   */
  updateSpacing(spacing: Partial<SpacingSettings>): void {
    this.customization.spacing = { ...this.customization.spacing, ...spacing };
    this.customization.updatedAt = new Date();
    this.generateCSSProperties();
  }

  /**
   * Get current customization
   */
  getCustomization(): TemplateCustomization {
    return { ...this.customization };
  }

  /**
   * Set complete customization
   */
  setCustomization(customization: Partial<TemplateCustomization>): void {
    this.customization = this.mergeWithDefaults(customization);
    this.generateCSSProperties();
  }

  /**
   * Reset to default customization
   */
  resetToDefault(): void {
    this.customization = this.mergeWithDefaults();
    this.generateCSSProperties();
  }

  /**
   * Validate customization for accessibility and ATS compatibility
   */
  validateCustomization(): CustomizationValidation {
    const errors = [];
    const warnings = [];
    let atsCompatible = true;
    let accessibilityScore = 100;

    // Color contrast validation
    const contrastRatio = this.calculateContrastRatio(
      this.customization.colorScheme.text,
      this.customization.colorScheme.background
    );

    if (contrastRatio < 4.5) {
      errors.push({
        field: 'colorScheme.text',
        message: 'Text color does not meet WCAG AA contrast requirements',
        severity: 'error' as const
      });
      accessibilityScore -= 30;
      atsCompatible = false;
    } else if (contrastRatio < 7) {
      warnings.push({
        field: 'colorScheme.text',
        message: 'Text color meets WCAG AA but not AAA contrast requirements',
        suggestion: 'Consider using a darker text color for better readability'
      });
      accessibilityScore -= 10;
    }

    // Font size validation
    const baseFontSize = parseFloat(this.customization.typography.bodySize.base);
    if (baseFontSize < 12) {
      errors.push({
        field: 'typography.bodySize.base',
        message: 'Base font size is too small for ATS compatibility',
        severity: 'error' as const
      });
      atsCompatible = false;
      accessibilityScore -= 20;
    } else if (baseFontSize < 14) {
      warnings.push({
        field: 'typography.bodySize.base',
        message: 'Base font size is small, consider increasing for better readability',
        suggestion: 'Use at least 14px for optimal readability'
      });
      accessibilityScore -= 5;
    }

    // Color scheme validation for print
    if (this.customization.colorScheme.background !== '#ffffff' && 
        this.customization.colorScheme.background !== '#fff') {
      warnings.push({
        field: 'colorScheme.background',
        message: 'Non-white background may not print well',
        suggestion: 'Consider using white background for better print compatibility'
      });
      accessibilityScore -= 5;
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      atsCompatible,
      accessibilityScore
    };
  }

  /**
   * Calculate color contrast ratio
   */
  private calculateContrastRatio(color1: string, color2: string): number {
    // Simplified contrast calculation
    // In a real implementation, you'd use a proper color library
    const rgb1 = this.hexToRgb(color1);
    const rgb2 = this.hexToRgb(color2);
    
    if (!rgb1 || !rgb2) return 1;

    const l1 = this.getLuminance(rgb1);
    const l2 = this.getLuminance(rgb2);
    
    const lighter = Math.max(l1, l2);
    const darker = Math.min(l1, l2);
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  /**
   * Convert hex color to RGB
   */
  private hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  /**
   * Calculate relative luminance
   */
  private getLuminance(rgb: { r: number; g: number; b: number }): number {
    const { r, g, b } = rgb;
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  }

  /**
   * Generate CSS class names with customization
   */
  generateCustomClasses(): Record<string, string> {
    return {
      'template-primary': `color: var(--template-color-primary);`,
      'template-secondary': `color: var(--template-color-secondary);`,
      'template-accent': `color: var(--template-color-accent);`,
      'template-text': `color: var(--template-color-text);`,
      'template-text-secondary': `color: var(--template-color-text-secondary);`,
      'template-bg-primary': `background-color: var(--template-color-primary);`,
      'template-bg-secondary': `background-color: var(--template-color-secondary);`,
      'template-bg-accent': `background-color: var(--template-color-accent);`,
      'template-bg-surface': `background-color: var(--template-color-surface);`,
      'template-border': `border-color: var(--template-color-border);`,
      'template-font-heading': `font-family: var(--template-font-heading);`,
      'template-font-body': `font-family: var(--template-font-body);`,
      'template-spacing-section': `margin-bottom: var(--template-section-gap);`,
      'template-spacing-item': `margin-bottom: var(--template-item-gap);`
    };
  }
}
