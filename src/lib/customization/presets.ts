// Predefined customization presets, color palettes, and font options for CVLeap

import { 
  ColorPalette, 
  FontOption, 
  CustomizationPreset,
  ColorScheme 
} from '@/types/customization';

// Professional Color Palettes
export const COLOR_PALETTES: ColorPalette[] = [
  {
    id: 'classic-blue',
    name: 'Classic Blue',
    category: 'professional',
    colors: {
      primary: '#1e40af',
      secondary: '#64748b',
      accent: '#3b82f6',
      background: '#ffffff',
      text: '#1e293b',
      textSecondary: '#64748b',
      border: '#e2e8f0',
      surface: '#f8fafc',
      surfaceSecondary: '#f1f5f9'
    },
    preview: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)'
  },
  {
    id: 'modern-teal',
    name: 'Modern Teal',
    category: 'modern',
    colors: {
      primary: '#0d9488',
      secondary: '#6b7280',
      accent: '#14b8a6',
      background: '#ffffff',
      text: '#111827',
      textSecondary: '#6b7280',
      border: '#d1d5db',
      surface: '#f9fafb',
      surfaceSecondary: '#f3f4f6'
    },
    preview: 'linear-gradient(135deg, #0d9488 0%, #14b8a6 100%)'
  },
  {
    id: 'executive-navy',
    name: 'Executive Navy',
    category: 'professional',
    colors: {
      primary: '#1e3a8a',
      secondary: '#475569',
      accent: '#2563eb',
      background: '#ffffff',
      text: '#0f172a',
      textSecondary: '#475569',
      border: '#cbd5e1',
      surface: '#f8fafc',
      surfaceSecondary: '#e2e8f0'
    },
    preview: 'linear-gradient(135deg, #1e3a8a 0%, #2563eb 100%)'
  },
  {
    id: 'creative-purple',
    name: 'Creative Purple',
    category: 'creative',
    colors: {
      primary: '#7c3aed',
      secondary: '#6b7280',
      accent: '#a855f7',
      background: '#ffffff',
      text: '#1f2937',
      textSecondary: '#6b7280',
      border: '#d1d5db',
      surface: '#faf5ff',
      surfaceSecondary: '#f3e8ff'
    },
    preview: 'linear-gradient(135deg, #7c3aed 0%, #a855f7 100%)'
  },
  {
    id: 'minimalist-gray',
    name: 'Minimalist Gray',
    category: 'minimalist',
    colors: {
      primary: '#374151',
      secondary: '#9ca3af',
      accent: '#6b7280',
      background: '#ffffff',
      text: '#111827',
      textSecondary: '#6b7280',
      border: '#e5e7eb',
      surface: '#f9fafb',
      surfaceSecondary: '#f3f4f6'
    },
    preview: 'linear-gradient(135deg, #374151 0%, #6b7280 100%)'
  },
  {
    id: 'warm-orange',
    name: 'Warm Orange',
    category: 'creative',
    colors: {
      primary: '#ea580c',
      secondary: '#78716c',
      accent: '#f97316',
      background: '#ffffff',
      text: '#1c1917',
      textSecondary: '#78716c',
      border: '#d6d3d1',
      surface: '#fffbeb',
      surfaceSecondary: '#fef3c7'
    },
    preview: 'linear-gradient(135deg, #ea580c 0%, #f97316 100%)'
  },
  {
    id: 'forest-green',
    name: 'Forest Green',
    category: 'professional',
    colors: {
      primary: '#166534',
      secondary: '#6b7280',
      accent: '#22c55e',
      background: '#ffffff',
      text: '#14532d',
      textSecondary: '#6b7280',
      border: '#d1d5db',
      surface: '#f0fdf4',
      surfaceSecondary: '#dcfce7'
    },
    preview: 'linear-gradient(135deg, #166534 0%, #22c55e 100%)'
  },
  {
    id: 'elegant-rose',
    name: 'Elegant Rose',
    category: 'modern',
    colors: {
      primary: '#be185d',
      secondary: '#6b7280',
      accent: '#ec4899',
      background: '#ffffff',
      text: '#1f2937',
      textSecondary: '#6b7280',
      border: '#d1d5db',
      surface: '#fdf2f8',
      surfaceSecondary: '#fce7f3'
    },
    preview: 'linear-gradient(135deg, #be185d 0%, #ec4899 100%)'
  }
];

// Professional Font Options
export const FONT_OPTIONS: FontOption[] = [
  {
    id: 'inter',
    name: 'Inter',
    family: 'Inter, sans-serif',
    category: 'sans-serif',
    weights: [400, 500, 600, 700],
    preview: 'Modern and highly readable',
    isWebFont: true,
    fallback: ['system-ui', '-apple-system', 'sans-serif']
  },
  {
    id: 'roboto',
    name: 'Roboto',
    family: 'Roboto, sans-serif',
    category: 'sans-serif',
    weights: [300, 400, 500, 700],
    preview: 'Clean and professional',
    isWebFont: true,
    fallback: ['Arial', 'sans-serif']
  },
  {
    id: 'open-sans',
    name: 'Open Sans',
    family: 'Open Sans, sans-serif',
    category: 'sans-serif',
    weights: [400, 600, 700],
    preview: 'Friendly and approachable',
    isWebFont: true,
    fallback: ['Arial', 'sans-serif']
  },
  {
    id: 'lato',
    name: 'Lato',
    family: 'Lato, sans-serif',
    category: 'sans-serif',
    weights: [400, 700],
    preview: 'Elegant and sophisticated',
    isWebFont: true,
    fallback: ['Arial', 'sans-serif']
  },
  {
    id: 'source-sans-pro',
    name: 'Source Sans Pro',
    family: 'Source Sans Pro, sans-serif',
    category: 'sans-serif',
    weights: [400, 600, 700],
    preview: 'Professional and versatile',
    isWebFont: true,
    fallback: ['Arial', 'sans-serif']
  },
  {
    id: 'playfair-display',
    name: 'Playfair Display',
    family: 'Playfair Display, serif',
    category: 'serif',
    weights: [400, 700],
    preview: 'Elegant and distinctive',
    isWebFont: true,
    fallback: ['Georgia', 'serif']
  },
  {
    id: 'merriweather',
    name: 'Merriweather',
    family: 'Merriweather, serif',
    category: 'serif',
    weights: [400, 700],
    preview: 'Traditional and readable',
    isWebFont: true,
    fallback: ['Georgia', 'serif']
  },
  {
    id: 'crimson-text',
    name: 'Crimson Text',
    family: 'Crimson Text, serif',
    category: 'serif',
    weights: [400, 600],
    preview: 'Classic and academic',
    isWebFont: true,
    fallback: ['Times New Roman', 'serif']
  },
  {
    id: 'system-ui',
    name: 'System Default',
    family: 'system-ui, -apple-system, sans-serif',
    category: 'sans-serif',
    weights: [400, 500, 600, 700],
    preview: 'Native system font',
    isWebFont: false,
    fallback: ['Arial', 'sans-serif']
  }
];

// Customization Presets
export const CUSTOMIZATION_PRESETS: CustomizationPreset[] = [
  {
    id: 'modern-professional',
    name: 'Modern Professional',
    description: 'Clean, modern design perfect for tech and business roles',
    category: 'modern',
    templateIds: ['modern-template', 'clean-professional-3', 'minimal-template'],
    customization: {
      colorScheme: COLOR_PALETTES.find(p => p.id === 'classic-blue')?.colors,
      typography: {
        headingFont: 'Inter',
        bodyFont: 'Inter',
        headingSize: {
          xs: '0.75rem',
          sm: '0.875rem',
          base: '1rem',
          lg: '1.125rem',
          xl: '1.25rem',
          '2xl': '1.5rem',
          '3xl': '1.875rem',
          '4xl': '2.25rem'
        },
        bodySize: {
          xs: '0.75rem',
          sm: '0.875rem',
          base: '1rem',
          lg: '1.125rem',
          xl: '1.25rem',
          '2xl': '1.5rem',
          '3xl': '1.875rem',
          '4xl': '2.25rem'
        },
        lineHeight: {
          tight: '1.25',
          normal: '1.5',
          relaxed: '1.625',
          loose: '2'
        },
        fontWeight: {
          normal: '400',
          medium: '500',
          semibold: '600',
          bold: '700'
        }
      }
    },
    preview: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)',
    isPremium: false
  },
  {
    id: 'executive-classic',
    name: 'Executive Classic',
    description: 'Traditional, authoritative design for senior leadership roles',
    category: 'executive',
    templateIds: ['professional-sidebar-1', 'executive-template', 'corporate-template'],
    customization: {
      colorScheme: COLOR_PALETTES.find(p => p.id === 'executive-navy')?.colors,
      typography: {
        headingFont: 'Playfair Display',
        bodyFont: 'Source Sans Pro',
        headingSize: {
          xs: '0.75rem',
          sm: '0.875rem',
          base: '1rem',
          lg: '1.125rem',
          xl: '1.25rem',
          '2xl': '1.5rem',
          '3xl': '1.875rem',
          '4xl': '2.25rem'
        },
        bodySize: {
          xs: '0.75rem',
          sm: '0.875rem',
          base: '1rem',
          lg: '1.125rem',
          xl: '1.25rem',
          '2xl': '1.5rem',
          '3xl': '1.875rem',
          '4xl': '2.25rem'
        },
        lineHeight: {
          tight: '1.25',
          normal: '1.5',
          relaxed: '1.625',
          loose: '2'
        },
        fontWeight: {
          normal: '400',
          medium: '500',
          semibold: '600',
          bold: '700'
        }
      }
    },
    preview: 'linear-gradient(135deg, #1e3a8a 0%, #2563eb 100%)',
    isPremium: false
  },
  {
    id: 'creative-bold',
    name: 'Creative Bold',
    description: 'Vibrant, creative design for design and marketing roles',
    category: 'creative',
    templateIds: ['creative-template', 'dynamic-template', 'fresh-template'],
    customization: {
      colorScheme: COLOR_PALETTES.find(p => p.id === 'creative-purple')?.colors
    },
    preview: 'linear-gradient(135deg, #7c3aed 0%, #a855f7 100%)',
    isPremium: true
  },
  {
    id: 'minimalist-clean',
    name: 'Minimalist Clean',
    description: 'Ultra-clean, minimal design focusing on content',
    category: 'minimalist',
    templateIds: ['minimal-template', 'clean-template', 'simple-template'],
    customization: {
      colorScheme: COLOR_PALETTES.find(p => p.id === 'minimalist-gray')?.colors,
      typography: {
        headingFont: 'Inter',
        bodyFont: 'Inter'
      }
    },
    preview: 'linear-gradient(135deg, #374151 0%, #6b7280 100%)',
    isPremium: false
  }
];

// Helper functions
export function getColorPaletteById(id: string): ColorPalette | undefined {
  return COLOR_PALETTES.find(palette => palette.id === id);
}

export function getFontOptionById(id: string): FontOption | undefined {
  return FONT_OPTIONS.find(font => font.id === id);
}

export function getPresetById(id: string): CustomizationPreset | undefined {
  return CUSTOMIZATION_PRESETS.find(preset => preset.id === id);
}

export function getColorPalettesByCategory(category: ColorPalette['category']): ColorPalette[] {
  return COLOR_PALETTES.filter(palette => palette.category === category);
}

export function getFontsByCategory(category: FontOption['category']): FontOption[] {
  return FONT_OPTIONS.filter(font => font.category === category);
}

export function getPresetsByCategory(category: CustomizationPreset['category']): CustomizationPreset[] {
  return CUSTOMIZATION_PRESETS.filter(preset => preset.category === category);
}
