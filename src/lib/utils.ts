import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDate(date: Date | string): string {
  const d = new Date(date);
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

export function formatDateShort(date: Date | string): string {
  const d = new Date(date);
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
  });
}

export function calculateExperience(experiences: any[]): number {
  return experiences.reduce((total, exp) => {
    const start = new Date(exp.startDate);
    const end = exp.current ? new Date() : new Date(exp.endDate);
    const years = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 365);
    return total + years;
  }, 0);
}

export function extractSkillsFromText(text: string): string[] {
  // Common tech skills and keywords
  const skillPatterns = [
    /\b(JavaScript|TypeScript|Python|Java|C\+\+|C#|PHP|Ruby|Go|Rust|Swift|Kotlin)\b/gi,
    /\b(React|Vue|Angular|Node\.js|Express|Django|Flask|Spring|Laravel)\b/gi,
    /\b(AWS|Azure|GCP|Docker|Kubernetes|Jenkins|Git|GitHub|GitLab)\b/gi,
    /\b(SQL|PostgreSQL|MySQL|MongoDB|Redis|Elasticsearch)\b/gi,
    /\b(HTML|CSS|SASS|SCSS|Tailwind|Bootstrap|Material-UI)\b/gi,
    /\b(REST|GraphQL|API|Microservices|DevOps|CI\/CD|Agile|Scrum)\b/gi,
  ];

  const skills = new Set<string>();
  
  skillPatterns.forEach(pattern => {
    const matches = text.match(pattern);
    if (matches) {
      matches.forEach(match => skills.add(match));
    }
  });

  return Array.from(skills);
}

export function calculateATSScore(resume: any, jobDescription: string): number {
  const jobSkills = extractSkillsFromText(jobDescription);
  const resumeText = JSON.stringify(resume).toLowerCase();
  
  let score = 0;
  let totalChecks = 0;

  // Check for skill matches (40% of score)
  jobSkills.forEach(skill => {
    totalChecks++;
    if (resumeText.includes(skill.toLowerCase())) {
      score += 40 / jobSkills.length;
    }
  });

  // Check for formatting (20% of score)
  totalChecks++;
  if (resume.personalInfo?.email && resume.personalInfo?.phone) {
    score += 20;
  }

  // Check for experience relevance (20% of score)
  totalChecks++;
  if (resume.experience?.length > 0) {
    score += 20;
  }

  // Check for education (10% of score)
  totalChecks++;
  if (resume.education?.length > 0) {
    score += 10;
  }

  // Check for summary/objective (10% of score)
  totalChecks++;
  if (resume.summary && resume.summary.length > 50) {
    score += 10;
  }

  return Math.min(100, Math.round(score));
}

export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength).replace(/\s+\S*$/, '') + '...';
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validatePhone(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

export function formatSalary(salary: any): string {
  if (!salary) return 'Not specified';
  
  const { min, max, currency, period } = salary;
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency || 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  });

  const periodText = period === 'yearly' ? '/year' : period === 'monthly' ? '/month' : '/hour';
  
  if (min && max) {
    return `${formatter.format(min)} - ${formatter.format(max)}${periodText}`;
  } else if (min) {
    return `${formatter.format(min)}+${periodText}`;
  } else if (max) {
    return `Up to ${formatter.format(max)}${periodText}`;
  }
  
  return 'Not specified';
}

export function getApplicationStatusColor(status: string): string {
  const colors = {
    saved: 'bg-gray-100 text-gray-800',
    applied: 'bg-blue-100 text-blue-800',
    under_review: 'bg-yellow-100 text-yellow-800',
    interview_scheduled: 'bg-purple-100 text-purple-800',
    interviewed: 'bg-indigo-100 text-indigo-800',
    offer_received: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800',
    withdrawn: 'bg-gray-100 text-gray-800',
  };
  
  return colors[status as keyof typeof colors] || colors.saved;
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

export function getFileExtension(filename: string): string {
  return filename.slice(((filename.lastIndexOf('.') - 1) >>> 0) + 2);
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
