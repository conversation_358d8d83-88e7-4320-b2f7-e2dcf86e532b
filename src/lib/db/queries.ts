import { prisma } from './index';
import type { 
  User, 
  Resume, 
  Job, 
  JobApplication, 
  UserProfile,
  ApplicationStatus 
} from '@prisma/client';

// User Queries
export async function getUserById(id: string) {
  return prisma.user.findUnique({
    where: { id },
    include: {
      profile: {
        include: {
          skills: true,
          experiences: true,
          educations: true,
          certifications: true,
          languages: true,
          projects: true,
          achievements: true,
        },
      },
      subscription: true,
      analytics: true,
    },
  });
}

export async function getUserByEmail(email: string) {
  return prisma.user.findUnique({
    where: { email },
    include: {
      profile: true,
      subscription: true,
    },
  });
}

export async function createUser(data: {
  email: string;
  name?: string;
  image?: string;
}) {
  return prisma.user.create({
    data,
    include: {
      profile: true,
    },
  });
}

// Resume Queries
export async function getUserResumes(userId: string) {
  return prisma.resume.findMany({
    where: { userId },
    include: {
      template: true,
      applications: {
        include: {
          job: true,
        },
      },
    },
    orderBy: { updatedAt: 'desc' },
  });
}

export async function getResumeById(id: string, userId: string) {
  return prisma.resume.findFirst({
    where: { id, userId },
    include: {
      template: true,
      user: {
        include: {
          profile: {
            include: {
              skills: true,
              experiences: true,
              educations: true,
              certifications: true,
              languages: true,
              projects: true,
              achievements: true,
            },
          },
        },
      },
    },
  });
}

export async function createResume(data: {
  userId: string;
  title: string;
  templateId: string;
  content: any;
  atsScore?: number;
}) {
  return prisma.resume.create({
    data,
    include: {
      template: true,
    },
  });
}

export async function updateResume(
  id: string,
  userId: string,
  data: {
    title?: string;
    content?: any;
    atsScore?: number;
    isPublic?: boolean;
  }
) {
  return prisma.resume.update({
    where: { id },
    data: {
      ...data,
      updatedAt: new Date(),
    },
  });
}

// Job Queries
export async function searchJobs(filters: {
  query?: string;
  location?: string;
  remote?: boolean;
  jobType?: string;
  salaryMin?: number;
  salaryMax?: number;
  skills?: string[];
  limit?: number;
  offset?: number;
}) {
  const {
    query,
    location,
    remote,
    jobType,
    salaryMin,
    salaryMax,
    skills,
    limit = 20,
    offset = 0,
  } = filters;

  const where: any = {};

  if (query) {
    where.OR = [
      { title: { contains: query, mode: 'insensitive' } },
      { company: { contains: query, mode: 'insensitive' } },
      { description: { contains: query, mode: 'insensitive' } },
    ];
  }

  if (location) {
    where.location = { contains: location, mode: 'insensitive' };
  }

  if (remote !== undefined) {
    where.remote = remote;
  }

  if (jobType) {
    where.type = jobType;
  }

  if (salaryMin) {
    where.salaryMin = { gte: salaryMin };
  }

  if (salaryMax) {
    where.salaryMax = { lte: salaryMax };
  }

  if (skills && skills.length > 0) {
    where.skills = {
      hasSome: skills,
    };
  }

  return prisma.job.findMany({
    where,
    orderBy: { postedDate: 'desc' },
    take: limit,
    skip: offset,
  });
}

export async function getJobById(id: string) {
  return prisma.job.findUnique({
    where: { id },
    include: {
      applications: {
        include: {
          user: true,
          resume: true,
        },
      },
    },
  });
}

// Job Application Queries
export async function getUserJobApplications(userId: string) {
  return prisma.jobApplication.findMany({
    where: { userId },
    include: {
      job: true,
      resume: true,
      coverLetter: true,
    },
    orderBy: { appliedDate: 'desc' },
  });
}

export async function createJobApplication(data: {
  userId: string;
  jobId: string;
  resumeId: string;
  coverLetterId?: string;
  notes?: string;
}) {
  return prisma.jobApplication.create({
    data,
    include: {
      job: true,
      resume: true,
      coverLetter: true,
    },
  });
}

export async function updateJobApplicationStatus(
  id: string,
  userId: string,
  status: ApplicationStatus,
  notes?: string
) {
  return prisma.jobApplication.update({
    where: { id },
    data: {
      status,
      notes,
      lastUpdated: new Date(),
    },
  });
}

// Analytics Queries
export async function getUserAnalytics(userId: string) {
  return prisma.userAnalytics.findUnique({
    where: { userId },
  });
}

export async function updateUserAnalytics(userId: string) {
  const applications = await prisma.jobApplication.findMany({
    where: { userId },
    include: { job: true },
  });

  const totalApplications = applications.length;
  const responses = applications.filter(app => 
    ['UNDER_REVIEW', 'INTERVIEW_SCHEDULED', 'INTERVIEWED', 'OFFER_RECEIVED'].includes(app.status)
  );
  const interviews = applications.filter(app => 
    ['INTERVIEW_SCHEDULED', 'INTERVIEWED', 'OFFER_RECEIVED'].includes(app.status)
  );
  const offers = applications.filter(app => app.status === 'OFFER_RECEIVED');

  const responseRate = totalApplications > 0 ? (responses.length / totalApplications) * 100 : 0;
  const interviewRate = totalApplications > 0 ? (interviews.length / totalApplications) * 100 : 0;
  const offerRate = totalApplications > 0 ? (offers.length / totalApplications) * 100 : 0;

  // Calculate industry breakdown
  const industryBreakdown: Record<string, number> = {};
  applications.forEach(app => {
    // Simple industry classification based on company name or job title
    // This could be enhanced with a proper industry classification service
    const industry = classifyIndustry(app.job.company, app.job.title);
    industryBreakdown[industry] = (industryBreakdown[industry] || 0) + 1;
  });

  return prisma.userAnalytics.upsert({
    where: { userId },
    update: {
      totalApplications,
      responseRate,
      interviewRate,
      offerRate,
      industryBreakdown,
      lastCalculated: new Date(),
    },
    create: {
      userId,
      totalApplications,
      responseRate,
      interviewRate,
      offerRate,
      industryBreakdown,
    },
  });
}

// Helper function for industry classification
function classifyIndustry(company: string, jobTitle: string): string {
  const text = `${company} ${jobTitle}`.toLowerCase();
  
  if (text.includes('tech') || text.includes('software') || text.includes('developer') || text.includes('engineer')) {
    return 'Technology';
  } else if (text.includes('finance') || text.includes('bank') || text.includes('investment')) {
    return 'Finance';
  } else if (text.includes('health') || text.includes('medical') || text.includes('pharma')) {
    return 'Healthcare';
  } else if (text.includes('education') || text.includes('university') || text.includes('school')) {
    return 'Education';
  } else if (text.includes('retail') || text.includes('sales') || text.includes('marketing')) {
    return 'Retail/Marketing';
  } else {
    return 'Other';
  }
}

// Template Queries
export async function getResumeTemplates() {
  return prisma.resumeTemplate.findMany({
    orderBy: { createdAt: 'desc' },
  });
}

export async function getResumeTemplateById(id: string) {
  return prisma.resumeTemplate.findUnique({
    where: { id },
  });
}
