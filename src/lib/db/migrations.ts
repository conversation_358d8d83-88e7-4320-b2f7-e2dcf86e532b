import { prisma } from './index';

/**
 * Database migration utilities for CVLeap
 */

export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error('Database connection failed:', error);
    return false;
  }
}

export async function runHealthCheck() {
  const checks = {
    database: false,
    tables: false,
    seed: false,
  };

  try {
    // Check database connection
    checks.database = await checkDatabaseConnection();

    if (checks.database) {
      // Check if tables exist
      const tables = await prisma.$queryRaw<Array<{ tablename: string }>>`
        SELECT tablename FROM pg_tables WHERE schemaname = 'public'
      `;
      checks.tables = tables.length > 0;

      // Check if seed data exists
      if (checks.tables) {
        const templateCount = await prisma.resumeTemplate.count();
        checks.seed = templateCount > 0;
      }
    }
  } catch (error) {
    console.error('Health check failed:', error);
  }

  return checks;
}

export async function initializeDatabase() {
  console.log('🔧 Initializing database...');

  const health = await runHealthCheck();

  if (!health.database) {
    throw new Error('Cannot connect to database. Please check your DATABASE_URL.');
  }

  if (!health.tables) {
    console.log('📋 Database tables not found. Please run: npm run db:push');
    return false;
  }

  if (!health.seed) {
    console.log('🌱 Seeding database with initial data...');
    try {
      // Import and run seed function
      const { main: seedMain } = await import('../../../prisma/seed');
      await seedMain();
      console.log('✅ Database seeded successfully');
    } catch (error) {
      console.error('❌ Database seeding failed:', error);
      return false;
    }
  }

  console.log('✅ Database initialized successfully');
  return true;
}

export async function resetDatabase() {
  console.log('🗑️ Resetting database...');
  
  try {
    // Delete all data in reverse dependency order
    await prisma.agentInteraction.deleteMany();
    await prisma.userAnalytics.deleteMany();
    await prisma.subscription.deleteMany();
    await prisma.savedJob.deleteMany();
    await prisma.jobApplication.deleteMany();
    await prisma.coverLetter.deleteMany();
    await prisma.resume.deleteMany();
    await prisma.job.deleteMany();
    await prisma.resumeTemplate.deleteMany();
    await prisma.achievement.deleteMany();
    await prisma.project.deleteMany();
    await prisma.language.deleteMany();
    await prisma.certification.deleteMany();
    await prisma.education.deleteMany();
    await prisma.experience.deleteMany();
    await prisma.skill.deleteMany();
    await prisma.userProfile.deleteMany();
    await prisma.session.deleteMany();
    await prisma.account.deleteMany();
    await prisma.verificationToken.deleteMany();
    await prisma.user.deleteMany();
    await prisma.systemConfig.deleteMany();

    console.log('✅ Database reset completed');
    return true;
  } catch (error) {
    console.error('❌ Database reset failed:', error);
    return false;
  }
}

export async function getSystemStats() {
  try {
    const stats = {
      users: await prisma.user.count(),
      resumes: await prisma.resume.count(),
      jobs: await prisma.job.count(),
      applications: await prisma.jobApplication.count(),
      templates: await prisma.resumeTemplate.count(),
    };

    return stats;
  } catch (error) {
    console.error('Failed to get system stats:', error);
    return null;
  }
}
