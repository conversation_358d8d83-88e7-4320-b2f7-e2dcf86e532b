/**
 * CVLeap Performance Monitor
 * Real-time performance tracking and optimization system
 */

import { analyticsService, AnalyticsEventType } from './analytics-service';

// Performance metric types
export enum PerformanceMetricType {
  PAGE_LOAD_TIME = 'page_load_time',
  API_RESPONSE_TIME = 'api_response_time',
  AI_PROCESSING_TIME = 'ai_processing_time',
  PDF_GENERATION_TIME = 'pdf_generation_time',
  DATABASE_QUERY_TIME = 'database_query_time',
  TEMPLATE_RENDER_TIME = 'template_render_time',
  USER_INTERACTION_TIME = 'user_interaction_time',
  ERROR_RATE = 'error_rate',
  MEMORY_USAGE = 'memory_usage',
  CPU_USAGE = 'cpu_usage',
}

// Performance thresholds
export const PERFORMANCE_THRESHOLDS = {
  [PerformanceMetricType.PAGE_LOAD_TIME]: 3000, // 3 seconds
  [PerformanceMetricType.API_RESPONSE_TIME]: 1000, // 1 second
  [PerformanceMetricType.AI_PROCESSING_TIME]: 5000, // 5 seconds
  [PerformanceMetricType.PDF_GENERATION_TIME]: 10000, // 10 seconds
  [PerformanceMetricType.DATABASE_QUERY_TIME]: 500, // 500ms
  [PerformanceMetricType.TEMPLATE_RENDER_TIME]: 2000, // 2 seconds
  [PerformanceMetricType.USER_INTERACTION_TIME]: 100, // 100ms
  [PerformanceMetricType.ERROR_RATE]: 0.01, // 1%
  [PerformanceMetricType.MEMORY_USAGE]: 0.8, // 80%
  [PerformanceMetricType.CPU_USAGE]: 0.8, // 80%
};

// Performance metric interface
export interface PerformanceMetric {
  type: PerformanceMetricType;
  value: number;
  timestamp: Date;
  context?: {
    userId?: string;
    sessionId?: string;
    page?: string;
    endpoint?: string;
    userAgent?: string;
    [key: string]: any;
  };
}

// Performance alert interface
export interface PerformanceAlert {
  id: string;
  type: PerformanceMetricType;
  threshold: number;
  actualValue: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  resolved: boolean;
}

// Performance summary interface
export interface PerformanceSummary {
  timeframe: {
    start: Date;
    end: Date;
  };
  metrics: {
    [key in PerformanceMetricType]?: {
      average: number;
      median: number;
      p95: number;
      p99: number;
      min: number;
      max: number;
      count: number;
    };
  };
  alerts: PerformanceAlert[];
  trends: {
    [key in PerformanceMetricType]?: 'improving' | 'stable' | 'degrading';
  };
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetric[] = [];
  private alerts: PerformanceAlert[] = [];

  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Record a performance metric
   */
  async recordMetric(metric: PerformanceMetric): Promise<void> {
    try {
      // Store metric locally
      this.metrics.push(metric);

      // Track in analytics
      await analyticsService.trackEvent({
        userId: metric.context?.userId,
        sessionId: metric.context?.sessionId,
        eventType: AnalyticsEventType.API_CALL,
        properties: {
          metricType: metric.type,
          value: metric.value,
          page: metric.context?.page,
          endpoint: metric.context?.endpoint,
        },
        timestamp: metric.timestamp,
        userAgent: metric.context?.userAgent,
        page: metric.context?.page,
      });

      // Check for performance issues
      await this.checkThresholds(metric);

      // Clean up old metrics (keep last 1000)
      if (this.metrics.length > 1000) {
        this.metrics = this.metrics.slice(-1000);
      }
    } catch (error) {
      console.error('Failed to record performance metric:', error);
    }
  }

  /**
   * Measure and record page load time
   */
  measurePageLoad(page: string, userId?: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const loadTime = performance.now() - startTime;
      this.recordMetric({
        type: PerformanceMetricType.PAGE_LOAD_TIME,
        value: loadTime,
        timestamp: new Date(),
        context: {
          userId,
          page,
          userAgent: navigator.userAgent,
        },
      });
    };
  }

  /**
   * Measure and record API response time
   */
  measureApiCall(endpoint: string, userId?: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const responseTime = performance.now() - startTime;
      this.recordMetric({
        type: PerformanceMetricType.API_RESPONSE_TIME,
        value: responseTime,
        timestamp: new Date(),
        context: {
          userId,
          endpoint,
        },
      });
    };
  }

  /**
   * Measure and record AI processing time
   */
  measureAIProcessing(operation: string, userId?: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const processingTime = performance.now() - startTime;
      this.recordMetric({
        type: PerformanceMetricType.AI_PROCESSING_TIME,
        value: processingTime,
        timestamp: new Date(),
        context: {
          userId,
          operation,
        },
      });
    };
  }

  /**
   * Measure and record PDF generation time
   */
  measurePDFGeneration(templateId: string, userId?: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const generationTime = performance.now() - startTime;
      this.recordMetric({
        type: PerformanceMetricType.PDF_GENERATION_TIME,
        value: generationTime,
        timestamp: new Date(),
        context: {
          userId,
          templateId,
        },
      });
    };
  }

  /**
   * Record database query performance
   */
  async measureDatabaseQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>,
    userId?: string
  ): Promise<T> {
    const startTime = performance.now();
    
    try {
      const result = await queryFn();
      const queryTime = performance.now() - startTime;
      
      await this.recordMetric({
        type: PerformanceMetricType.DATABASE_QUERY_TIME,
        value: queryTime,
        timestamp: new Date(),
        context: {
          userId,
          queryName,
        },
      });
      
      return result;
    } catch (error) {
      const queryTime = performance.now() - startTime;
      
      await this.recordMetric({
        type: PerformanceMetricType.DATABASE_QUERY_TIME,
        value: queryTime,
        timestamp: new Date(),
        context: {
          userId,
          queryName,
          error: true,
        },
      });
      
      throw error;
    }
  }

  /**
   * Get performance summary for a timeframe
   */
  getPerformanceSummary(timeframe: { start: Date; end: Date }): PerformanceSummary {
    const filteredMetrics = this.metrics.filter(
      metric => metric.timestamp >= timeframe.start && metric.timestamp <= timeframe.end
    );

    const metricsByType = this.groupMetricsByType(filteredMetrics);
    const metrics: PerformanceSummary['metrics'] = {};
    const trends: PerformanceSummary['trends'] = {};

    for (const [type, typeMetrics] of Object.entries(metricsByType)) {
      const values = typeMetrics.map(m => m.value).sort((a, b) => a - b);
      
      if (values.length > 0) {
        metrics[type as PerformanceMetricType] = {
          average: values.reduce((sum, val) => sum + val, 0) / values.length,
          median: this.calculatePercentile(values, 50),
          p95: this.calculatePercentile(values, 95),
          p99: this.calculatePercentile(values, 99),
          min: values[0],
          max: values[values.length - 1],
          count: values.length,
        };

        trends[type as PerformanceMetricType] = this.calculateTrend(typeMetrics);
      }
    }

    const filteredAlerts = this.alerts.filter(
      alert => alert.timestamp >= timeframe.start && alert.timestamp <= timeframe.end
    );

    return {
      timeframe,
      metrics,
      alerts: filteredAlerts,
      trends,
    };
  }

  /**
   * Get current performance alerts
   */
  getActiveAlerts(): PerformanceAlert[] {
    return this.alerts.filter(alert => !alert.resolved);
  }

  /**
   * Resolve a performance alert
   */
  resolveAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
    }
  }

  // Private helper methods
  private async checkThresholds(metric: PerformanceMetric): Promise<void> {
    const threshold = PERFORMANCE_THRESHOLDS[metric.type];
    
    if (threshold && metric.value > threshold) {
      const severity = this.calculateSeverity(metric.value, threshold);
      
      const alert: PerformanceAlert = {
        id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: metric.type,
        threshold,
        actualValue: metric.value,
        severity,
        message: `${metric.type} exceeded threshold: ${metric.value}ms > ${threshold}ms`,
        timestamp: new Date(),
        resolved: false,
      };

      this.alerts.push(alert);

      // Send alert to monitoring services
      await this.sendAlert(alert);
    }
  }

  private calculateSeverity(value: number, threshold: number): PerformanceAlert['severity'] {
    const ratio = value / threshold;
    
    if (ratio >= 3) return 'critical';
    if (ratio >= 2) return 'high';
    if (ratio >= 1.5) return 'medium';
    return 'low';
  }

  private async sendAlert(alert: PerformanceAlert): Promise<void> {
    // Send to external monitoring services (Sentry, DataDog, etc.)
    console.warn('Performance Alert:', alert);
  }

  private groupMetricsByType(metrics: PerformanceMetric[]): Record<string, PerformanceMetric[]> {
    return metrics.reduce((groups, metric) => {
      const type = metric.type;
      if (!groups[type]) {
        groups[type] = [];
      }
      groups[type].push(metric);
      return groups;
    }, {} as Record<string, PerformanceMetric[]>);
  }

  private calculatePercentile(sortedValues: number[], percentile: number): number {
    const index = Math.ceil((percentile / 100) * sortedValues.length) - 1;
    return sortedValues[Math.max(0, index)];
  }

  private calculateTrend(metrics: PerformanceMetric[]): 'improving' | 'stable' | 'degrading' {
    if (metrics.length < 2) return 'stable';

    const sortedMetrics = metrics.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    const firstHalf = sortedMetrics.slice(0, Math.floor(sortedMetrics.length / 2));
    const secondHalf = sortedMetrics.slice(Math.floor(sortedMetrics.length / 2));

    const firstAvg = firstHalf.reduce((sum, m) => sum + m.value, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, m) => sum + m.value, 0) / secondHalf.length;

    const changePercent = ((secondAvg - firstAvg) / firstAvg) * 100;

    if (changePercent < -5) return 'improving';
    if (changePercent > 5) return 'degrading';
    return 'stable';
  }
}

export const performanceMonitor = PerformanceMonitor.getInstance();
