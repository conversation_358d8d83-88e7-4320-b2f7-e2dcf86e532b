/**
 * CVLeap Analytics Service
 * Comprehensive user behavior tracking and analytics system
 */

import { prisma } from '@/lib/prisma';

// Event types for tracking
export enum AnalyticsEventType {
  // User Authentication
  USER_SIGNUP = 'user_signup',
  USER_SIGNIN = 'user_signin',
  USER_SIGNOUT = 'user_signout',
  
  // Resume Creation
  RESUME_CREATED = 'resume_created',
  RESUME_UPDATED = 'resume_updated',
  RESUME_DELETED = 'resume_deleted',
  RESUME_EXPORTED = 'resume_exported',
  
  // Template Usage
  TEMPLATE_SELECTED = 'template_selected',
  TEMPLATE_CUSTOMIZED = 'template_customized',
  TEMPLATE_SWITCHED = 'template_switched',
  
  // AI Features
  AI_CONTENT_GENERATED = 'ai_content_generated',
  AI_SUGGESTION_ACCEPTED = 'ai_suggestion_accepted',
  AI_SUGGESTION_REJECTED = 'ai_suggestion_rejected',
  AI_OPTIMIZATION_APPLIED = 'ai_optimization_applied',
  
  // Subscription
  SUBSCRIPTION_STARTED = 'subscription_started',
  SUBSCRIPTION_UPGRADED = 'subscription_upgraded',
  SUBSCRIPTION_CANCELLED = 'subscription_cancelled',
  
  // Performance
  PAGE_LOAD = 'page_load',
  API_CALL = 'api_call',
  ERROR_OCCURRED = 'error_occurred',
  
  // User Engagement
  FEATURE_USED = 'feature_used',
  HELP_ACCESSED = 'help_accessed',
  TUTORIAL_COMPLETED = 'tutorial_completed',
  
  // Job Applications
  APPLICATION_SUBMITTED = 'application_submitted',
  INTERVIEW_SCHEDULED = 'interview_scheduled',
  JOB_OFFER_RECEIVED = 'job_offer_received',
}

// Event properties interface
export interface AnalyticsEventProperties {
  [key: string]: string | number | boolean | null;
}

// Analytics event interface
export interface AnalyticsEvent {
  userId?: string;
  sessionId?: string;
  eventType: AnalyticsEventType;
  properties: AnalyticsEventProperties;
  timestamp?: Date;
  userAgent?: string;
  ipAddress?: string;
  referrer?: string;
  page?: string;
}

// User segment interface
export interface UserSegment {
  id: string;
  name: string;
  description: string;
  criteria: {
    [key: string]: any;
  };
  userCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// Analytics metrics interface
export interface AnalyticsMetrics {
  totalUsers: number;
  activeUsers: number;
  newUsers: number;
  returningUsers: number;
  averageSessionDuration: number;
  bounceRate: number;
  conversionRate: number;
  retentionRate: number;
}

// Funnel analysis interface
export interface FunnelStep {
  name: string;
  eventType: AnalyticsEventType;
  userCount: number;
  conversionRate: number;
  dropoffRate: number;
}

export interface FunnelAnalysis {
  name: string;
  steps: FunnelStep[];
  totalConversionRate: number;
  timeframe: {
    start: Date;
    end: Date;
  };
}

class AnalyticsService {
  private static instance: AnalyticsService;

  public static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  /**
   * Track an analytics event
   */
  async trackEvent(event: AnalyticsEvent): Promise<void> {
    try {
      await prisma.analyticsEvent.create({
        data: {
          userId: event.userId,
          sessionId: event.sessionId || this.generateSessionId(),
          eventType: event.eventType,
          properties: event.properties,
          timestamp: event.timestamp || new Date(),
          userAgent: event.userAgent,
          ipAddress: event.ipAddress,
          referrer: event.referrer,
          page: event.page,
        },
      });

      // Also send to external analytics services if configured
      await this.sendToExternalServices(event);
    } catch (error) {
      console.error('Failed to track analytics event:', error);
      // Don't throw error to avoid breaking user experience
    }
  }

  /**
   * Track user behavior with context
   */
  async trackUserBehavior(
    userId: string,
    action: string,
    context: AnalyticsEventProperties = {}
  ): Promise<void> {
    await this.trackEvent({
      userId,
      eventType: AnalyticsEventType.FEATURE_USED,
      properties: {
        action,
        ...context,
      },
    });
  }

  /**
   * Track page view
   */
  async trackPageView(
    userId: string | undefined,
    page: string,
    properties: AnalyticsEventProperties = {}
  ): Promise<void> {
    await this.trackEvent({
      userId,
      eventType: AnalyticsEventType.PAGE_LOAD,
      page,
      properties: {
        page,
        ...properties,
      },
    });
  }

  /**
   * Track conversion event
   */
  async trackConversion(
    userId: string,
    conversionType: string,
    value?: number,
    properties: AnalyticsEventProperties = {}
  ): Promise<void> {
    await this.trackEvent({
      userId,
      eventType: AnalyticsEventType.SUBSCRIPTION_STARTED,
      properties: {
        conversionType,
        value,
        ...properties,
      },
    });
  }

  /**
   * Get user analytics metrics
   */
  async getUserMetrics(timeframe: { start: Date; end: Date }): Promise<AnalyticsMetrics> {
    const [
      totalUsers,
      activeUsers,
      newUsers,
      sessionData,
    ] = await Promise.all([
      this.getTotalUsers(timeframe),
      this.getActiveUsers(timeframe),
      this.getNewUsers(timeframe),
      this.getSessionData(timeframe),
    ]);

    const returningUsers = activeUsers - newUsers;
    const averageSessionDuration = sessionData.averageDuration;
    const bounceRate = sessionData.bounceRate;
    const conversionRate = await this.getConversionRate(timeframe);
    const retentionRate = await this.getRetentionRate(timeframe);

    return {
      totalUsers,
      activeUsers,
      newUsers,
      returningUsers,
      averageSessionDuration,
      bounceRate,
      conversionRate,
      retentionRate,
    };
  }

  /**
   * Analyze conversion funnel
   */
  async analyzeFunnel(
    funnelSteps: AnalyticsEventType[],
    timeframe: { start: Date; end: Date }
  ): Promise<FunnelAnalysis> {
    const steps: FunnelStep[] = [];
    let previousUserCount = 0;

    for (let i = 0; i < funnelSteps.length; i++) {
      const eventType = funnelSteps[i];
      const userCount = await this.getUserCountForEvent(eventType, timeframe);
      
      const conversionRate = i === 0 ? 100 : (userCount / previousUserCount) * 100;
      const dropoffRate = 100 - conversionRate;

      steps.push({
        name: eventType,
        eventType,
        userCount,
        conversionRate,
        dropoffRate,
      });

      if (i === 0) {
        previousUserCount = userCount;
      }
    }

    const totalConversionRate = steps.length > 0 
      ? (steps[steps.length - 1].userCount / steps[0].userCount) * 100 
      : 0;

    return {
      name: 'Conversion Funnel',
      steps,
      totalConversionRate,
      timeframe,
    };
  }

  /**
   * Get user segments
   */
  async getUserSegments(): Promise<UserSegment[]> {
    // Implementation for user segmentation
    // This would analyze user behavior patterns and create segments
    return [];
  }

  /**
   * Track performance metrics
   */
  async trackPerformance(
    metric: string,
    value: number,
    context: AnalyticsEventProperties = {}
  ): Promise<void> {
    await this.trackEvent({
      eventType: AnalyticsEventType.API_CALL,
      properties: {
        metric,
        value,
        ...context,
      },
    });
  }

  // Private helper methods
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async sendToExternalServices(event: AnalyticsEvent): Promise<void> {
    // Send to Google Analytics, Mixpanel, etc.
    // Implementation depends on configured services
  }

  private async getTotalUsers(timeframe: { start: Date; end: Date }): Promise<number> {
    const result = await prisma.analyticsEvent.groupBy({
      by: ['userId'],
      where: {
        timestamp: {
          gte: timeframe.start,
          lte: timeframe.end,
        },
        userId: {
          not: null,
        },
      },
    });
    return result.length;
  }

  private async getActiveUsers(timeframe: { start: Date; end: Date }): Promise<number> {
    const result = await prisma.analyticsEvent.groupBy({
      by: ['userId'],
      where: {
        timestamp: {
          gte: timeframe.start,
          lte: timeframe.end,
        },
        userId: {
          not: null,
        },
      },
    });
    return result.length;
  }

  private async getNewUsers(timeframe: { start: Date; end: Date }): Promise<number> {
    const result = await prisma.analyticsEvent.count({
      where: {
        eventType: AnalyticsEventType.USER_SIGNUP,
        timestamp: {
          gte: timeframe.start,
          lte: timeframe.end,
        },
      },
    });
    return result;
  }

  private async getSessionData(timeframe: { start: Date; end: Date }): Promise<{
    averageDuration: number;
    bounceRate: number;
  }> {
    // Implementation for session analysis
    return {
      averageDuration: 0,
      bounceRate: 0,
    };
  }

  private async getConversionRate(timeframe: { start: Date; end: Date }): Promise<number> {
    // Implementation for conversion rate calculation
    return 0;
  }

  private async getRetentionRate(timeframe: { start: Date; end: Date }): Promise<number> {
    // Implementation for retention rate calculation
    return 0;
  }

  private async getUserCountForEvent(
    eventType: AnalyticsEventType,
    timeframe: { start: Date; end: Date }
  ): Promise<number> {
    const result = await prisma.analyticsEvent.groupBy({
      by: ['userId'],
      where: {
        eventType,
        timestamp: {
          gte: timeframe.start,
          lte: timeframe.end,
        },
        userId: {
          not: null,
        },
      },
    });
    return result.length;
  }
}

export const analyticsService = AnalyticsService.getInstance();
