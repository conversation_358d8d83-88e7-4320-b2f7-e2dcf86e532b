/**
 * CVLeap Dashboard Analytics
 * Multi-resume management with performance insights and recommendations
 */

import { analyticsService, AnalyticsEventType } from './analytics-service';
import { prisma } from '@/lib/prisma';

// Resume performance metrics
export interface ResumePerformanceMetrics {
  resumeId: string;
  resumeName: string;
  templateId: string;
  templateName: string;
  createdAt: Date;
  lastUpdatedAt: Date;
  
  // Application metrics
  totalApplications: number;
  successfulApplications: number;
  interviewCallbacks: number;
  jobOffers: number;
  
  // Performance rates
  applicationSuccessRate: number;
  interviewCallbackRate: number;
  offerRate: number;
  
  // ATS metrics
  atsCompatibilityScore: number;
  atsOptimizationSuggestions: string[];
  
  // AI enhancement metrics
  aiOptimizationScore: number;
  aiSuggestionsApplied: number;
  aiSuggestionsAvailable: number;
  
  // Engagement metrics
  viewCount: number;
  downloadCount: number;
  shareCount: number;
  
  // Time metrics
  timeToFirstApplication: number; // in hours
  averageApplicationTime: number; // in minutes
  
  // Industry and role targeting
  targetIndustries: string[];
  targetRoles: string[];
  
  // Performance trends
  performanceTrend: 'improving' | 'stable' | 'declining';
  lastPerformanceUpdate: Date;
}

// Dashboard insights
export interface DashboardInsights {
  userId: string;
  totalResumes: number;
  activeResumes: number;
  bestPerformingResume: {
    resumeId: string;
    resumeName: string;
    successRate: number;
  };
  
  // Overall performance
  overallSuccessRate: number;
  totalApplications: number;
  totalInterviews: number;
  totalOffers: number;
  
  // Recommendations
  recommendations: Array<{
    type: 'template_suggestion' | 'ai_optimization' | 'content_improvement' | 'targeting_adjustment';
    priority: 'high' | 'medium' | 'low';
    title: string;
    description: string;
    actionUrl?: string;
    resumeId?: string;
  }>;
  
  // Goals and progress
  goals: Array<{
    id: string;
    type: 'applications' | 'interviews' | 'offers' | 'success_rate';
    target: number;
    current: number;
    progress: number; // percentage
    deadline?: Date;
  }>;
  
  // Performance comparison
  industryBenchmarks: {
    averageSuccessRate: number;
    averageInterviewRate: number;
    averageOfferRate: number;
  };
  
  // Activity summary
  recentActivity: Array<{
    type: 'resume_created' | 'resume_updated' | 'application_submitted' | 'interview_scheduled' | 'offer_received';
    timestamp: Date;
    description: string;
    resumeId?: string;
  }>;
}

// Resume comparison data
export interface ResumeComparison {
  resumes: Array<{
    resumeId: string;
    resumeName: string;
    templateName: string;
    metrics: ResumePerformanceMetrics;
  }>;
  comparisonMetrics: {
    successRate: { best: string; worst: string; average: number };
    interviewRate: { best: string; worst: string; average: number };
    atsScore: { best: string; worst: string; average: number };
    aiOptimization: { best: string; worst: string; average: number };
  };
  recommendations: Array<{
    resumeId: string;
    suggestion: string;
    impact: 'high' | 'medium' | 'low';
  }>;
}

// Goal tracking
export interface UserGoal {
  id: string;
  userId: string;
  type: 'applications' | 'interviews' | 'offers' | 'success_rate' | 'salary_target';
  title: string;
  description: string;
  target: number;
  current: number;
  deadline?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

class DashboardAnalytics {
  private static instance: DashboardAnalytics;

  public static getInstance(): DashboardAnalytics {
    if (!DashboardAnalytics.instance) {
      DashboardAnalytics.instance = new DashboardAnalytics();
    }
    return DashboardAnalytics.instance;
  }

  /**
   * Get comprehensive dashboard insights for a user
   */
  async getDashboardInsights(userId: string): Promise<DashboardInsights> {
    try {
      // Get user's resumes
      const resumes = await prisma.resume.findMany({
        where: { userId },
        include: {
          applications: true,
        },
      });

      const totalResumes = resumes.length;
      const activeResumes = resumes.filter(r => r.isActive).length;

      // Calculate overall metrics
      const allApplications = resumes.flatMap(r => r.applications);
      const totalApplications = allApplications.length;
      const successfulApplications = allApplications.filter(a => a.status === 'interview' || a.status === 'offer').length;
      const totalInterviews = allApplications.filter(a => a.status === 'interview').length;
      const totalOffers = allApplications.filter(a => a.status === 'offer').length;
      const overallSuccessRate = totalApplications > 0 ? (successfulApplications / totalApplications) * 100 : 0;

      // Find best performing resume
      let bestPerformingResume = {
        resumeId: '',
        resumeName: '',
        successRate: 0,
      };

      for (const resume of resumes) {
        const resumeApplications = resume.applications.length;
        const resumeSuccessful = resume.applications.filter(a => a.status === 'interview' || a.status === 'offer').length;
        const resumeSuccessRate = resumeApplications > 0 ? (resumeSuccessful / resumeApplications) * 100 : 0;

        if (resumeSuccessRate > bestPerformingResume.successRate) {
          bestPerformingResume = {
            resumeId: resume.id,
            resumeName: resume.title || 'Untitled Resume',
            successRate: resumeSuccessRate,
          };
        }
      }

      // Generate recommendations
      const recommendations = await this.generateRecommendations(userId, resumes);

      // Get user goals
      const goals = await this.getUserGoals(userId);

      // Get industry benchmarks
      const industryBenchmarks = await this.getIndustryBenchmarks(userId);

      // Get recent activity
      const recentActivity = await this.getRecentActivity(userId);

      return {
        userId,
        totalResumes,
        activeResumes,
        bestPerformingResume,
        overallSuccessRate,
        totalApplications,
        totalInterviews,
        totalOffers,
        recommendations,
        goals,
        industryBenchmarks,
        recentActivity,
      };
    } catch (error) {
      console.error('Failed to get dashboard insights:', error);
      throw error;
    }
  }

  /**
   * Get detailed performance metrics for a specific resume
   */
  async getResumePerformanceMetrics(resumeId: string): Promise<ResumePerformanceMetrics> {
    try {
      const resume = await prisma.resume.findUnique({
        where: { id: resumeId },
        include: {
          applications: true,
          template: true,
        },
      });

      if (!resume) {
        throw new Error('Resume not found');
      }

      const applications = resume.applications;
      const totalApplications = applications.length;
      const successfulApplications = applications.filter(a => a.status === 'interview' || a.status === 'offer').length;
      const interviewCallbacks = applications.filter(a => a.status === 'interview').length;
      const jobOffers = applications.filter(a => a.status === 'offer').length;

      // Calculate rates
      const applicationSuccessRate = totalApplications > 0 ? (successfulApplications / totalApplications) * 100 : 0;
      const interviewCallbackRate = totalApplications > 0 ? (interviewCallbacks / totalApplications) * 100 : 0;
      const offerRate = totalApplications > 0 ? (jobOffers / totalApplications) * 100 : 0;

      // Get ATS and AI metrics (would be calculated from actual data)
      const atsCompatibilityScore = await this.calculateATSScore(resumeId);
      const aiOptimizationScore = await this.calculateAIOptimizationScore(resumeId);

      // Calculate time metrics
      const timeToFirstApplication = await this.calculateTimeToFirstApplication(resumeId);
      const averageApplicationTime = await this.calculateAverageApplicationTime(resumeId);

      // Get engagement metrics
      const engagementMetrics = await this.getEngagementMetrics(resumeId);

      // Calculate performance trend
      const performanceTrend = await this.calculatePerformanceTrend(resumeId);

      return {
        resumeId,
        resumeName: resume.title || 'Untitled Resume',
        templateId: resume.templateId || '',
        templateName: resume.template?.name || 'Unknown Template',
        createdAt: resume.createdAt,
        lastUpdatedAt: resume.updatedAt,
        totalApplications,
        successfulApplications,
        interviewCallbacks,
        jobOffers,
        applicationSuccessRate,
        interviewCallbackRate,
        offerRate,
        atsCompatibilityScore,
        atsOptimizationSuggestions: [], // Would be generated from ATS analysis
        aiOptimizationScore,
        aiSuggestionsApplied: 0, // Would be tracked from AI service
        aiSuggestionsAvailable: 0, // Would be calculated from AI analysis
        viewCount: engagementMetrics.viewCount,
        downloadCount: engagementMetrics.downloadCount,
        shareCount: engagementMetrics.shareCount,
        timeToFirstApplication,
        averageApplicationTime,
        targetIndustries: [], // Would be extracted from applications
        targetRoles: [], // Would be extracted from applications
        performanceTrend,
        lastPerformanceUpdate: new Date(),
      };
    } catch (error) {
      console.error('Failed to get resume performance metrics:', error);
      throw error;
    }
  }

  /**
   * Compare multiple resumes
   */
  async compareResumes(userId: string, resumeIds: string[]): Promise<ResumeComparison> {
    try {
      const resumes = [];
      
      for (const resumeId of resumeIds) {
        const metrics = await this.getResumePerformanceMetrics(resumeId);
        const resume = await prisma.resume.findUnique({
          where: { id: resumeId },
          include: { template: true },
        });

        if (resume) {
          resumes.push({
            resumeId,
            resumeName: resume.title || 'Untitled Resume',
            templateName: resume.template?.name || 'Unknown Template',
            metrics,
          });
        }
      }

      // Calculate comparison metrics
      const successRates = resumes.map(r => r.metrics.applicationSuccessRate);
      const interviewRates = resumes.map(r => r.metrics.interviewCallbackRate);
      const atsScores = resumes.map(r => r.metrics.atsCompatibilityScore);
      const aiScores = resumes.map(r => r.metrics.aiOptimizationScore);

      const comparisonMetrics = {
        successRate: {
          best: this.getBestPerformer(resumes, 'applicationSuccessRate'),
          worst: this.getWorstPerformer(resumes, 'applicationSuccessRate'),
          average: successRates.reduce((sum, rate) => sum + rate, 0) / successRates.length,
        },
        interviewRate: {
          best: this.getBestPerformer(resumes, 'interviewCallbackRate'),
          worst: this.getWorstPerformer(resumes, 'interviewCallbackRate'),
          average: interviewRates.reduce((sum, rate) => sum + rate, 0) / interviewRates.length,
        },
        atsScore: {
          best: this.getBestPerformer(resumes, 'atsCompatibilityScore'),
          worst: this.getWorstPerformer(resumes, 'atsCompatibilityScore'),
          average: atsScores.reduce((sum, score) => sum + score, 0) / atsScores.length,
        },
        aiOptimization: {
          best: this.getBestPerformer(resumes, 'aiOptimizationScore'),
          worst: this.getWorstPerformer(resumes, 'aiOptimizationScore'),
          average: aiScores.reduce((sum, score) => sum + score, 0) / aiScores.length,
        },
      };

      // Generate comparison recommendations
      const recommendations = await this.generateComparisonRecommendations(resumes);

      return {
        resumes,
        comparisonMetrics,
        recommendations,
      };
    } catch (error) {
      console.error('Failed to compare resumes:', error);
      throw error;
    }
  }

  /**
   * Create or update user goal
   */
  async setUserGoal(goal: Omit<UserGoal, 'id' | 'createdAt' | 'updatedAt'>): Promise<UserGoal> {
    const newGoal = await prisma.userGoal.create({
      data: {
        ...goal,
        id: this.generateGoalId(),
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    return newGoal as UserGoal;
  }

  /**
   * Update goal progress
   */
  async updateGoalProgress(goalId: string, current: number): Promise<void> {
    await prisma.userGoal.update({
      where: { id: goalId },
      data: {
        current,
        updatedAt: new Date(),
      },
    });
  }

  // Private helper methods
  private async generateRecommendations(userId: string, resumes: any[]): Promise<DashboardInsights['recommendations']> {
    const recommendations = [];

    // Template suggestions based on performance
    if (resumes.length > 1) {
      // Analyze which templates perform better
      recommendations.push({
        type: 'template_suggestion' as const,
        priority: 'medium' as const,
        title: 'Consider Template Optimization',
        description: 'Some of your resumes are performing better than others. Consider using your best-performing template for all applications.',
      });
    }

    // AI optimization suggestions
    recommendations.push({
      type: 'ai_optimization' as const,
      priority: 'high' as const,
      title: 'AI Content Enhancement Available',
      description: 'Use our AI-powered content optimization to improve your resume\'s impact and ATS compatibility.',
    });

    return recommendations;
  }

  private async getUserGoals(userId: string): Promise<DashboardInsights['goals']> {
    const goals = await prisma.userGoal.findMany({
      where: {
        userId,
        isActive: true,
      },
    });

    return goals.map(goal => ({
      id: goal.id,
      type: goal.type as any,
      target: goal.target,
      current: goal.current,
      progress: goal.target > 0 ? (goal.current / goal.target) * 100 : 0,
      deadline: goal.deadline,
    }));
  }

  private async getIndustryBenchmarks(userId: string): Promise<DashboardInsights['industryBenchmarks']> {
    // This would be calculated from aggregated industry data
    return {
      averageSuccessRate: 15,
      averageInterviewRate: 8,
      averageOfferRate: 3,
    };
  }

  private async getRecentActivity(userId: string): Promise<DashboardInsights['recentActivity']> {
    // This would be fetched from analytics events
    return [];
  }

  private async calculateATSScore(resumeId: string): Promise<number> {
    // Implementation would analyze resume content for ATS compatibility
    return Math.floor(Math.random() * 40) + 60; // Placeholder: 60-100
  }

  private async calculateAIOptimizationScore(resumeId: string): Promise<number> {
    // Implementation would analyze AI enhancement opportunities
    return Math.floor(Math.random() * 30) + 70; // Placeholder: 70-100
  }

  private async calculateTimeToFirstApplication(resumeId: string): Promise<number> {
    // Implementation would calculate time from resume creation to first application
    return Math.floor(Math.random() * 168) + 24; // Placeholder: 24-192 hours
  }

  private async calculateAverageApplicationTime(resumeId: string): Promise<number> {
    // Implementation would calculate average time spent on applications
    return Math.floor(Math.random() * 30) + 15; // Placeholder: 15-45 minutes
  }

  private async getEngagementMetrics(resumeId: string): Promise<{
    viewCount: number;
    downloadCount: number;
    shareCount: number;
  }> {
    // Implementation would fetch from analytics
    return {
      viewCount: Math.floor(Math.random() * 100),
      downloadCount: Math.floor(Math.random() * 50),
      shareCount: Math.floor(Math.random() * 10),
    };
  }

  private async calculatePerformanceTrend(resumeId: string): Promise<'improving' | 'stable' | 'declining'> {
    // Implementation would analyze performance over time
    const trends = ['improving', 'stable', 'declining'] as const;
    return trends[Math.floor(Math.random() * trends.length)];
  }

  private getBestPerformer(resumes: any[], metric: string): string {
    let best = resumes[0];
    for (const resume of resumes) {
      if (resume.metrics[metric] > best.metrics[metric]) {
        best = resume;
      }
    }
    return best.resumeName;
  }

  private getWorstPerformer(resumes: any[], metric: string): string {
    let worst = resumes[0];
    for (const resume of resumes) {
      if (resume.metrics[metric] < worst.metrics[metric]) {
        worst = resume;
      }
    }
    return worst.resumeName;
  }

  private async generateComparisonRecommendations(resumes: any[]): Promise<ResumeComparison['recommendations']> {
    // Implementation would analyze differences and suggest improvements
    return [];
  }

  private generateGoalId(): string {
    return `goal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const dashboardAnalytics = DashboardAnalytics.getInstance();
