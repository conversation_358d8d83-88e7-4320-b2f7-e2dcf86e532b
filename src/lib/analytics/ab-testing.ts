/**
 * CVLeap A/B Testing Framework
 * Comprehensive experimentation system for continuous optimization
 */

import { analyticsService, AnalyticsEventType } from './analytics-service';
import { prisma } from '@/lib/prisma';

// Experiment status
export enum ExperimentStatus {
  DRAFT = 'draft',
  RUNNING = 'running',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  ARCHIVED = 'archived',
}

// Experiment type
export enum ExperimentType {
  TEMPLATE_COMPARISON = 'template_comparison',
  AI_FEATURE_TEST = 'ai_feature_test',
  ONBOARDING_FLOW = 'onboarding_flow',
  PRICING_STRATEGY = 'pricing_strategy',
  UI_COMPONENT = 'ui_component',
  CONTENT_VARIATION = 'content_variation',
}

// Variant interface
export interface ExperimentVariant {
  id: string;
  name: string;
  description: string;
  weight: number; // Traffic allocation percentage (0-100)
  config: {
    [key: string]: any;
  };
  isControl: boolean;
}

// Experiment configuration
export interface ExperimentConfig {
  id: string;
  name: string;
  description: string;
  type: ExperimentType;
  status: ExperimentStatus;
  variants: ExperimentVariant[];
  targetingRules: {
    userSegments?: string[];
    userProperties?: {
      [key: string]: any;
    };
    trafficPercentage: number; // Percentage of users to include in experiment
  };
  successMetrics: {
    primary: string;
    secondary: string[];
  };
  startDate: Date;
  endDate?: Date;
  minimumSampleSize: number;
  confidenceLevel: number; // e.g., 95
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Experiment result
export interface ExperimentResult {
  experimentId: string;
  variantId: string;
  metric: string;
  value: number;
  sampleSize: number;
  conversionRate?: number;
  confidenceInterval?: {
    lower: number;
    upper: number;
  };
  statisticalSignificance?: boolean;
  pValue?: number;
}

// User assignment
export interface UserAssignment {
  userId: string;
  experimentId: string;
  variantId: string;
  assignedAt: Date;
}

class ABTestingEngine {
  private static instance: ABTestingEngine;

  public static getInstance(): ABTestingEngine {
    if (!ABTestingEngine.instance) {
      ABTestingEngine.instance = new ABTestingEngine();
    }
    return ABTestingEngine.instance;
  }

  /**
   * Create a new experiment
   */
  async createExperiment(config: Omit<ExperimentConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<ExperimentConfig> {
    const experiment: ExperimentConfig = {
      ...config,
      id: this.generateExperimentId(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Validate experiment configuration
    this.validateExperiment(experiment);

    // Save to database
    await prisma.experiment.create({
      data: {
        id: experiment.id,
        name: experiment.name,
        description: experiment.description,
        type: experiment.type,
        status: experiment.status,
        config: experiment as any,
        createdBy: experiment.createdBy,
        createdAt: experiment.createdAt,
        updatedAt: experiment.updatedAt,
      },
    });

    return experiment;
  }

  /**
   * Get user's variant for an experiment
   */
  async getUserVariant(userId: string, experimentId: string): Promise<ExperimentVariant | null> {
    try {
      // Check if user is already assigned
      const existingAssignment = await prisma.userExperimentAssignment.findUnique({
        where: {
          userId_experimentId: {
            userId,
            experimentId,
          },
        },
      });

      if (existingAssignment) {
        const experiment = await this.getExperiment(experimentId);
        return experiment?.variants.find(v => v.id === existingAssignment.variantId) || null;
      }

      // Get experiment configuration
      const experiment = await this.getExperiment(experimentId);
      if (!experiment || experiment.status !== ExperimentStatus.RUNNING) {
        return null;
      }

      // Check if user qualifies for experiment
      const qualifies = await this.checkUserQualification(userId, experiment);
      if (!qualifies) {
        return null;
      }

      // Assign user to variant
      const variant = this.assignUserToVariant(userId, experiment);
      
      // Save assignment
      await prisma.userExperimentAssignment.create({
        data: {
          userId,
          experimentId,
          variantId: variant.id,
          assignedAt: new Date(),
        },
      });

      // Track assignment event
      await analyticsService.trackEvent({
        userId,
        eventType: AnalyticsEventType.FEATURE_USED,
        properties: {
          action: 'experiment_assigned',
          experimentId,
          variantId: variant.id,
          experimentType: experiment.type,
        },
      });

      return variant;
    } catch (error) {
      console.error('Failed to get user variant:', error);
      return null;
    }
  }

  /**
   * Track experiment conversion
   */
  async trackConversion(
    userId: string,
    experimentId: string,
    metric: string,
    value: number = 1
  ): Promise<void> {
    try {
      const assignment = await prisma.userExperimentAssignment.findUnique({
        where: {
          userId_experimentId: {
            userId,
            experimentId,
          },
        },
      });

      if (!assignment) {
        return; // User not in experiment
      }

      // Record conversion
      await prisma.experimentConversion.create({
        data: {
          userId,
          experimentId,
          variantId: assignment.variantId,
          metric,
          value,
          timestamp: new Date(),
        },
      });

      // Track in analytics
      await analyticsService.trackEvent({
        userId,
        eventType: AnalyticsEventType.FEATURE_USED,
        properties: {
          action: 'experiment_conversion',
          experimentId,
          variantId: assignment.variantId,
          metric,
          value,
        },
      });
    } catch (error) {
      console.error('Failed to track experiment conversion:', error);
    }
  }

  /**
   * Get experiment results
   */
  async getExperimentResults(experimentId: string): Promise<ExperimentResult[]> {
    const experiment = await this.getExperiment(experimentId);
    if (!experiment) {
      throw new Error('Experiment not found');
    }

    const results: ExperimentResult[] = [];

    for (const variant of experiment.variants) {
      // Get conversions for this variant
      const conversions = await prisma.experimentConversion.findMany({
        where: {
          experimentId,
          variantId: variant.id,
        },
      });

      // Get total users assigned to this variant
      const totalUsers = await prisma.userExperimentAssignment.count({
        where: {
          experimentId,
          variantId: variant.id,
        },
      });

      // Calculate metrics for each success metric
      for (const metric of [experiment.successMetrics.primary, ...experiment.successMetrics.secondary]) {
        const metricConversions = conversions.filter(c => c.metric === metric);
        const conversionCount = metricConversions.length;
        const conversionRate = totalUsers > 0 ? (conversionCount / totalUsers) * 100 : 0;

        results.push({
          experimentId,
          variantId: variant.id,
          metric,
          value: metricConversions.reduce((sum, c) => sum + c.value, 0),
          sampleSize: totalUsers,
          conversionRate,
        });
      }
    }

    // Calculate statistical significance
    return this.calculateStatisticalSignificance(results, experiment);
  }

  /**
   * Update experiment status
   */
  async updateExperimentStatus(experimentId: string, status: ExperimentStatus): Promise<void> {
    await prisma.experiment.update({
      where: { id: experimentId },
      data: {
        status,
        updatedAt: new Date(),
      },
    });
  }

  /**
   * Get all experiments
   */
  async getExperiments(status?: ExperimentStatus): Promise<ExperimentConfig[]> {
    const experiments = await prisma.experiment.findMany({
      where: status ? { status } : undefined,
      orderBy: { createdAt: 'desc' },
    });

    return experiments.map(exp => exp.config as ExperimentConfig);
  }

  // Private helper methods
  private async getExperiment(experimentId: string): Promise<ExperimentConfig | null> {
    const experiment = await prisma.experiment.findUnique({
      where: { id: experimentId },
    });

    return experiment ? (experiment.config as ExperimentConfig) : null;
  }

  private validateExperiment(experiment: ExperimentConfig): void {
    // Validate variant weights sum to 100
    const totalWeight = experiment.variants.reduce((sum, variant) => sum + variant.weight, 0);
    if (Math.abs(totalWeight - 100) > 0.01) {
      throw new Error('Variant weights must sum to 100%');
    }

    // Validate at least one control variant
    const hasControl = experiment.variants.some(variant => variant.isControl);
    if (!hasControl) {
      throw new Error('Experiment must have at least one control variant');
    }

    // Validate traffic percentage
    if (experiment.targetingRules.trafficPercentage < 0 || experiment.targetingRules.trafficPercentage > 100) {
      throw new Error('Traffic percentage must be between 0 and 100');
    }
  }

  private async checkUserQualification(userId: string, experiment: ExperimentConfig): Promise<boolean> {
    // Check traffic percentage
    const userHash = this.hashUserId(userId, experiment.id);
    const trafficThreshold = experiment.targetingRules.trafficPercentage / 100;
    
    if (userHash > trafficThreshold) {
      return false;
    }

    // Check user segments and properties
    // Implementation would check against user data
    return true;
  }

  private assignUserToVariant(userId: string, experiment: ExperimentConfig): ExperimentVariant {
    const userHash = this.hashUserId(userId, experiment.id + '_variant');
    let cumulativeWeight = 0;

    for (const variant of experiment.variants) {
      cumulativeWeight += variant.weight / 100;
      if (userHash <= cumulativeWeight) {
        return variant;
      }
    }

    // Fallback to control variant
    return experiment.variants.find(v => v.isControl) || experiment.variants[0];
  }

  private hashUserId(userId: string, salt: string): number {
    // Simple hash function for consistent user assignment
    let hash = 0;
    const str = userId + salt;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash) / 2147483647; // Normalize to 0-1
  }

  private calculateStatisticalSignificance(
    results: ExperimentResult[],
    experiment: ExperimentConfig
  ): ExperimentResult[] {
    // Implementation of statistical significance calculation
    // This would use proper statistical tests (t-test, chi-square, etc.)
    return results.map(result => ({
      ...result,
      statisticalSignificance: result.sampleSize >= experiment.minimumSampleSize,
      pValue: 0.05, // Placeholder
      confidenceInterval: {
        lower: result.conversionRate ? result.conversionRate * 0.9 : 0,
        upper: result.conversionRate ? result.conversionRate * 1.1 : 0,
      },
    }));
  }

  private generateExperimentId(): string {
    return `exp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const abTestingEngine = ABTestingEngine.getInstance();
