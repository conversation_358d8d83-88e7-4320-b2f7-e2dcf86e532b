import { z } from 'zod';

// Personal Information Schema
export const personalInfoSchema = z.object({
  fullName: z.string().min(2, 'Full name must be at least 2 characters').max(100, 'Full name must be less than 100 characters'),
  jobTitle: z.string().min(2, 'Job title must be at least 2 characters').max(100, 'Job title must be less than 100 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 characters').max(20, 'Phone number must be less than 20 characters'),
  location: z.string().min(2, 'Location must be at least 2 characters').max(100, 'Location must be less than 100 characters'),
  website: z.string().url('Please enter a valid website URL').optional().or(z.literal('')),
  linkedinUrl: z.string().url('Please enter a valid LinkedIn URL').optional().or(z.literal('')),
  githubUrl: z.string().url('Please enter a valid GitHub URL').optional().or(z.literal('')),
});

// Professional Summary Schema
export const summarySchema = z.object({
  summary: z.string()
    .min(50, 'Summary should be at least 50 characters')
    .max(500, 'Summary should be less than 500 characters')
    .optional()
    .or(z.literal('')),
});

// Experience Schema
export const experienceSchema = z.object({
  id: z.string(),
  company: z.string().min(2, 'Company name must be at least 2 characters').max(100, 'Company name must be less than 100 characters'),
  position: z.string().min(2, 'Position must be at least 2 characters').max(100, 'Position must be less than 100 characters'),
  location: z.string().min(2, 'Location must be at least 2 characters').max(100, 'Location must be less than 100 characters'),
  startDate: z.date({ required_error: 'Start date is required' }),
  endDate: z.date().optional(),
  current: z.boolean().default(false),
  description: z.array(z.string().min(10, 'Description must be at least 10 characters').max(200, 'Description must be less than 200 characters')),
  skills: z.array(z.string().min(1, 'Skill cannot be empty').max(50, 'Skill must be less than 50 characters')),
}).refine((data) => {
  if (!data.current && !data.endDate) {
    return false;
  }
  if (data.endDate && data.startDate && data.endDate < data.startDate) {
    return false;
  }
  return true;
}, {
  message: 'End date must be after start date, or position must be current',
  path: ['endDate'],
});

// Education Schema
export const educationSchema = z.object({
  id: z.string(),
  institution: z.string().min(2, 'Institution name must be at least 2 characters').max(100, 'Institution name must be less than 100 characters'),
  degree: z.string().min(2, 'Degree must be at least 2 characters').max(100, 'Degree must be less than 100 characters'),
  field: z.string().min(2, 'Field of study must be at least 2 characters').max(100, 'Field of study must be less than 100 characters'),
  location: z.string().min(2, 'Location must be at least 2 characters').max(100, 'Location must be less than 100 characters'),
  startDate: z.date({ required_error: 'Start date is required' }),
  endDate: z.date().optional(),
  gpa: z.number().min(0, 'GPA cannot be negative').max(4.0, 'GPA cannot exceed 4.0').optional(),
  achievements: z.array(z.string().min(5, 'Achievement must be at least 5 characters').max(100, 'Achievement must be less than 100 characters')).optional(),
}).refine((data) => {
  if (data.endDate && data.startDate && data.endDate < data.startDate) {
    return false;
  }
  return true;
}, {
  message: 'End date must be after start date',
  path: ['endDate'],
});

// Skills Schema
export const skillsSchema = z.object({
  skills: z.array(z.string().min(1, 'Skill cannot be empty').max(50, 'Skill must be less than 50 characters'))
    .min(3, 'Please add at least 3 skills')
    .max(20, 'Maximum 20 skills allowed'),
});

// Certification Schema
export const certificationSchema = z.object({
  id: z.string(),
  name: z.string().min(2, 'Certification name must be at least 2 characters').max(100, 'Certification name must be less than 100 characters'),
  issuer: z.string().min(2, 'Issuer must be at least 2 characters').max(100, 'Issuer must be less than 100 characters'),
  issueDate: z.date({ required_error: 'Issue date is required' }),
  expiryDate: z.date().optional(),
  credentialId: z.string().max(100, 'Credential ID must be less than 100 characters').optional().or(z.literal('')),
  url: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
}).refine((data) => {
  if (data.expiryDate && data.issueDate && data.expiryDate < data.issueDate) {
    return false;
  }
  return true;
}, {
  message: 'Expiry date must be after issue date',
  path: ['expiryDate'],
});

// Language Schema
export const languageSchema = z.object({
  id: z.string(),
  name: z.string().min(2, 'Language name must be at least 2 characters').max(50, 'Language name must be less than 50 characters'),
  proficiency: z.enum(['beginner', 'conversational', 'fluent', 'native'], {
    required_error: 'Please select a proficiency level',
  }),
});

// Project Schema
export const projectSchema = z.object({
  id: z.string(),
  name: z.string().min(2, 'Project name must be at least 2 characters').max(100, 'Project name must be less than 100 characters'),
  description: z.string().min(20, 'Description must be at least 20 characters').max(300, 'Description must be less than 300 characters'),
  technologies: z.array(z.string().min(1, 'Technology cannot be empty').max(30, 'Technology name must be less than 30 characters')),
  url: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  startDate: z.date({ required_error: 'Start date is required' }),
  endDate: z.date().optional(),
}).refine((data) => {
  if (data.endDate && data.startDate && data.endDate < data.startDate) {
    return false;
  }
  return true;
}, {
  message: 'End date must be after start date',
  path: ['endDate'],
});

// Complete Resume Data Schema
export const resumeDataSchema = z.object({
  personalInfo: personalInfoSchema,
  summary: z.string().optional(),
  experience: z.array(experienceSchema),
  education: z.array(educationSchema),
  skills: z.array(z.string()),
  certifications: z.array(certificationSchema),
  languages: z.array(languageSchema),
  projects: z.array(projectSchema).optional(),
});

// Form-specific schemas for individual sections
export const experienceFormSchema = z.object({
  experience: z.array(experienceSchema),
});

export const educationFormSchema = z.object({
  education: z.array(educationSchema),
});

export const certificationsFormSchema = z.object({
  certifications: z.array(certificationSchema),
});

export const languagesFormSchema = z.object({
  languages: z.array(languageSchema),
});

export const projectsFormSchema = z.object({
  projects: z.array(projectSchema),
});

// Type exports for TypeScript
export type PersonalInfoFormData = z.infer<typeof personalInfoSchema>;
export type SummaryFormData = z.infer<typeof summarySchema>;
export type ExperienceFormData = z.infer<typeof experienceSchema>;
export type EducationFormData = z.infer<typeof educationSchema>;
export type SkillsFormData = z.infer<typeof skillsSchema>;
export type CertificationFormData = z.infer<typeof certificationSchema>;
export type LanguageFormData = z.infer<typeof languageSchema>;
export type ProjectFormData = z.infer<typeof projectSchema>;
export type ResumeFormData = z.infer<typeof resumeDataSchema>;

// Validation helper functions
export const validatePersonalInfo = (data: unknown) => personalInfoSchema.safeParse(data);
export const validateSummary = (data: unknown) => summarySchema.safeParse(data);
export const validateExperience = (data: unknown) => experienceSchema.safeParse(data);
export const validateEducation = (data: unknown) => educationSchema.safeParse(data);
export const validateSkills = (data: unknown) => skillsSchema.safeParse(data);
export const validateCertification = (data: unknown) => certificationSchema.safeParse(data);
export const validateLanguage = (data: unknown) => languageSchema.safeParse(data);
export const validateProject = (data: unknown) => projectSchema.safeParse(data);
export const validateResumeData = (data: unknown) => resumeDataSchema.safeParse(data);

// Default values for forms
export const defaultPersonalInfo: PersonalInfoFormData = {
  fullName: '',
  jobTitle: '',
  email: '',
  phone: '',
  location: '',
  website: '',
  linkedinUrl: '',
  githubUrl: '',
};

export const defaultExperience: ExperienceFormData = {
  id: '',
  company: '',
  position: '',
  location: '',
  startDate: new Date(),
  endDate: undefined,
  current: false,
  description: [''],
  skills: [],
};

export const defaultEducation: EducationFormData = {
  id: '',
  institution: '',
  degree: '',
  field: '',
  location: '',
  startDate: new Date(),
  endDate: undefined,
  gpa: undefined,
  achievements: [],
};

export const defaultCertification: CertificationFormData = {
  id: '',
  name: '',
  issuer: '',
  issueDate: new Date(),
  expiryDate: undefined,
  credentialId: '',
  url: '',
};

export const defaultLanguage: LanguageFormData = {
  id: '',
  name: '',
  proficiency: 'conversational',
};

export const defaultProject: ProjectFormData = {
  id: '',
  name: '',
  description: '',
  technologies: [],
  url: '',
  startDate: new Date(),
  endDate: undefined,
};
