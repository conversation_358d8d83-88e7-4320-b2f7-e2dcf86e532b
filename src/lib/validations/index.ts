import { z } from 'zod';

// Authentication Validation
export const registerSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms and conditions'),
});

export const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
});

export const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
});

export const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: z.string().min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),
});

// User Profile Validation
export const userProfileSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  phone: z.string().optional(),
  location: z.string().optional(),
  website: z.string().url('Invalid URL').optional().or(z.literal('')),
  linkedinUrl: z.string().url('Invalid LinkedIn URL').optional().or(z.literal('')),
  githubUrl: z.string().url('Invalid GitHub URL').optional().or(z.literal('')),
  summary: z.string().max(500, 'Summary must be less than 500 characters').optional(),
});

// Personal Info Validation
export const personalInfoSchema = z.object({
  fullName: z.string().min(2, 'Full name is required'),
  email: z.string().email('Invalid email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 digits'),
  location: z.string().min(2, 'Location is required'),
  website: z.string().url('Invalid URL').optional().or(z.literal('')),
  linkedinUrl: z.string().url('Invalid LinkedIn URL').optional().or(z.literal('')),
  githubUrl: z.string().url('Invalid GitHub URL').optional().or(z.literal('')),
});

// Experience Validation
export const experienceSchema = z.object({
  company: z.string().min(2, 'Company name is required'),
  position: z.string().min(2, 'Position is required'),
  location: z.string().min(2, 'Location is required'),
  startDate: z.date(),
  endDate: z.date().optional(),
  current: z.boolean(),
  description: z.array(z.string()).min(1, 'At least one description point is required'),
  skills: z.array(z.string()),
}).refine((data) => {
  if (!data.current && !data.endDate) {
    return false;
  }
  if (data.endDate && data.startDate > data.endDate) {
    return false;
  }
  return true;
}, {
  message: 'End date must be after start date, or position must be current',
});

// Education Validation
export const educationSchema = z.object({
  institution: z.string().min(2, 'Institution name is required'),
  degree: z.string().min(2, 'Degree is required'),
  field: z.string().min(2, 'Field of study is required'),
  location: z.string().min(2, 'Location is required'),
  startDate: z.date(),
  endDate: z.date().optional(),
  gpa: z.number().min(0).max(4).optional(),
  achievements: z.array(z.string()).optional(),
}).refine((data) => {
  if (data.endDate && data.startDate > data.endDate) {
    return false;
  }
  return true;
}, {
  message: 'End date must be after start date',
});

// Certification Validation
export const certificationSchema = z.object({
  name: z.string().min(2, 'Certification name is required'),
  issuer: z.string().min(2, 'Issuer is required'),
  issueDate: z.date(),
  expiryDate: z.date().optional(),
  credentialId: z.string().optional(),
  url: z.string().url('Invalid URL').optional().or(z.literal('')),
}).refine((data) => {
  if (data.expiryDate && data.issueDate > data.expiryDate) {
    return false;
  }
  return true;
}, {
  message: 'Expiry date must be after issue date',
});

// Language Validation
export const languageSchema = z.object({
  name: z.string().min(2, 'Language name is required'),
  proficiency: z.enum(['Basic', 'Conversational', 'Fluent', 'Native']),
});

// Project Validation
export const projectSchema = z.object({
  name: z.string().min(2, 'Project name is required'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  technologies: z.array(z.string()).min(1, 'At least one technology is required'),
  url: z.string().url('Invalid URL').optional().or(z.literal('')),
  githubUrl: z.string().url('Invalid GitHub URL').optional().or(z.literal('')),
  startDate: z.date(),
  endDate: z.date().optional(),
}).refine((data) => {
  if (data.endDate && data.startDate > data.endDate) {
    return false;
  }
  return true;
}, {
  message: 'End date must be after start date',
});

// Achievement Validation
export const achievementSchema = z.object({
  title: z.string().min(2, 'Achievement title is required'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  date: z.date(),
  category: z.string().min(2, 'Category is required'),
});

// Resume Content Validation
export const resumeContentSchema = z.object({
  personalInfo: personalInfoSchema,
  summary: z.string().min(50, 'Summary must be at least 50 characters').max(300, 'Summary must be less than 300 characters'),
  experience: z.array(experienceSchema),
  education: z.array(educationSchema),
  skills: z.array(z.string()).min(3, 'At least 3 skills are required'),
  certifications: z.array(certificationSchema).optional(),
  languages: z.array(languageSchema).optional(),
  projects: z.array(projectSchema).optional(),
  achievements: z.array(achievementSchema).optional(),
});

// Resume Validation
export const resumeSchema = z.object({
  title: z.string().min(2, 'Resume title is required'),
  templateId: z.string().min(1, 'Template selection is required'),
  content: resumeContentSchema,
  isPublic: z.boolean().default(false),
});

// Job Application Validation
export const jobApplicationSchema = z.object({
  jobId: z.string().min(1, 'Job ID is required'),
  resumeId: z.string().min(1, 'Resume selection is required'),
  coverLetterId: z.string().optional(),
  notes: z.string().max(500, 'Notes must be less than 500 characters').optional(),
  followUpDate: z.date().optional(),
});

// Job Search Filters Validation
export const jobSearchFiltersSchema = z.object({
  query: z.string().optional(),
  location: z.string().optional(),
  remote: z.boolean().optional(),
  jobType: z.enum(['Full-time', 'Part-time', 'Contract', 'Freelance', 'Internship']).optional(),
  salaryMin: z.number().min(0).optional(),
  salaryMax: z.number().min(0).optional(),
  skills: z.array(z.string()).optional(),
  experience: z.enum(['Entry', 'Mid', 'Senior', 'Executive']).optional(),
  datePosted: z.enum(['24h', '7d', '30d', 'all']).optional(),
});

// Contact Form Validation
export const contactFormSchema = z.object({
  name: z.string().min(2, 'Name is required'),
  email: z.string().email('Invalid email address'),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  message: z.string().min(20, 'Message must be at least 20 characters'),
});

// Newsletter Subscription Validation
export const newsletterSchema = z.object({
  email: z.string().email('Invalid email address'),
});

// Password Validation
export const passwordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// AI Prompt Validation
export const aiPromptSchema = z.object({
  prompt: z.string().min(10, 'Prompt must be at least 10 characters').max(1000, 'Prompt must be less than 1000 characters'),
  context: z.string().optional(),
  type: z.enum(['resume_improvement', 'cover_letter', 'job_description_analysis', 'skill_suggestion']),
});

// File Upload Validation
export const fileUploadSchema = z.object({
  file: z.instanceof(File),
  type: z.enum(['resume', 'cover_letter', 'profile_picture']),
}).refine((data) => {
  const allowedTypes = {
    resume: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    cover_letter: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    profile_picture: ['image/jpeg', 'image/png', 'image/webp'],
  };
  
  return allowedTypes[data.type].includes(data.file.type);
}, {
  message: 'Invalid file type for the selected category',
});

export type UserProfileInput = z.infer<typeof userProfileSchema>;
export type PersonalInfoInput = z.infer<typeof personalInfoSchema>;
export type ExperienceInput = z.infer<typeof experienceSchema>;
export type EducationInput = z.infer<typeof educationSchema>;
export type CertificationInput = z.infer<typeof certificationSchema>;
export type LanguageInput = z.infer<typeof languageSchema>;
export type ProjectInput = z.infer<typeof projectSchema>;
export type AchievementInput = z.infer<typeof achievementSchema>;
export type ResumeContentInput = z.infer<typeof resumeContentSchema>;
export type ResumeInput = z.infer<typeof resumeSchema>;
export type JobApplicationInput = z.infer<typeof jobApplicationSchema>;
export type JobSearchFiltersInput = z.infer<typeof jobSearchFiltersSchema>;
export type ContactFormInput = z.infer<typeof contactFormSchema>;
export type NewsletterInput = z.infer<typeof newsletterSchema>;
export type PasswordInput = z.infer<typeof passwordSchema>;
export type AIPromptInput = z.infer<typeof aiPromptSchema>;
export type FileUploadInput = z.infer<typeof fileUploadSchema>;
