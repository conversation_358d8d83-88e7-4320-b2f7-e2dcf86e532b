import type { AgentType } from '@prisma/client';
import type { BaseAgent, AgentRequest, AgentResponse } from './base-agent';

export class AgentRegistry {
  private static instance: AgentRegistry;
  private agents: Map<AgentType, BaseAgent> = new Map();

  private constructor() {}

  public static getInstance(): AgentRegistry {
    if (!AgentRegistry.instance) {
      AgentRegistry.instance = new AgentRegistry();
    }
    return AgentRegistry.instance;
  }

  /**
   * Register an agent
   */
  register(agent: BaseAgent): void {
    const info = agent.getInfo();
    this.agents.set(info.type, agent);
    console.log(`Registered agent: ${info.name} (${info.type})`);
  }

  /**
   * Get an agent by type
   */
  getAgent(type: AgentType): BaseAgent | null {
    return this.agents.get(type) || null;
  }

  /**
   * Execute agent request
   */
  async execute(type: AgentType, request: AgentRequest): Promise<AgentResponse> {
    const agent = this.getAgent(type);
    
    if (!agent) {
      return {
        success: false,
        error: `Agent of type '${type}' not found`,
      };
    }

    try {
      return await agent.execute(request);
    } catch (error) {
      console.error(`Agent execution failed for type '${type}':`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Agent execution failed',
      };
    }
  }

  /**
   * Get all registered agents
   */
  getAllAgents(): Array<{
    type: AgentType;
    name: string;
    description: string;
    version: string;
  }> {
    return Array.from(this.agents.values()).map(agent => agent.getInfo());
  }

  /**
   * Check if agent is registered
   */
  isRegistered(type: AgentType): boolean {
    return this.agents.has(type);
  }

  /**
   * Unregister an agent
   */
  unregister(type: AgentType): boolean {
    return this.agents.delete(type);
  }

  /**
   * Get agent metrics for all agents
   */
  async getAllMetrics(userId?: string): Promise<Record<AgentType, any>> {
    const metrics: Record<string, any> = {};
    
    for (const [type, agent] of this.agents) {
      try {
        metrics[type] = await agent.getMetrics(userId);
      } catch (error) {
        console.error(`Failed to get metrics for agent ${type}:`, error);
        metrics[type] = {
          totalInteractions: 0,
          successRate: 0,
          averageTokens: 0,
          totalCost: 0,
          averageResponseTime: 0,
        };
      }
    }
    
    return metrics as Record<AgentType, any>;
  }

  /**
   * Health check for all agents
   */
  async healthCheck(): Promise<Record<AgentType, boolean>> {
    const health: Record<string, boolean> = {};
    
    for (const [type, agent] of this.agents) {
      try {
        // Simple health check - try to get agent info
        const info = agent.getInfo();
        health[type] = !!info;
      } catch (error) {
        console.error(`Health check failed for agent ${type}:`, error);
        health[type] = false;
      }
    }
    
    return health as Record<AgentType, boolean>;
  }
}

// Export singleton instance
export const agentRegistry = AgentRegistry.getInstance();

// Agent factory function
export function createAgentRequest(
  input: any,
  userId: string,
  sessionId?: string,
  metadata?: Record<string, any>,
  options?: {
    temperature?: number;
    maxTokens?: number;
    provider?: 'openai' | 'anthropic';
  }
): AgentRequest {
  return {
    input,
    context: {
      userId,
      sessionId,
      metadata,
    },
    options,
  };
}

// Utility functions for agent management
export async function executeAgent(
  type: AgentType,
  input: any,
  userId: string,
  sessionId?: string,
  metadata?: Record<string, any>,
  options?: {
    temperature?: number;
    maxTokens?: number;
    provider?: 'openai' | 'anthropic';
  }
): Promise<AgentResponse> {
  const request = createAgentRequest(input, userId, sessionId, metadata, options);
  return agentRegistry.execute(type, request);
}

// Agent initialization function
export async function initializeAgents(): Promise<void> {
  console.log('Initializing AI agents...');
  
  // Agents will be registered when their modules are imported
  // This function can be used for any global initialization logic
  
  console.log('AI agents initialized successfully');
}
