// AI-powered content enhancement service for CVLeap

import { aiService } from './service';
import { 
  ContentSuggestion, 
  ContentAnalysis, 
  ContentOptimizationRequest, 
  ContentOptimizationResponse,
  SmartContentGeneration,
  SmartContentResponse,
  ATSOptimization,
  IndustryInsights,
  JobAnalysis,
  AIContentAPIResponse
} from '@/types/ai-content';
import { ResumeContent } from '@/types';

export class AIContentService {
  private static instance: AIContentService;
  private readonly defaultSettings = {
    provider: 'openai' as const,
    model: 'gpt-4',
    temperature: 0.7,
    maxTokens: 2000,
    confidenceThreshold: 0.7
  };

  static getInstance(): AIContentService {
    if (!AIContentService.instance) {
      AIContentService.instance = new AIContentService();
    }
    return AIContentService.instance;
  }

  /**
   * Analyze resume content and provide comprehensive feedback
   */
  async analyzeContent(
    resumeContent: ResumeContent,
    jobDescription?: string
  ): Promise<AIContentAPIResponse<ContentAnalysis>> {
    try {
      const prompt = this.buildAnalysisPrompt(resumeContent, jobDescription);
      
      const response = await aiService.generateContent({
        prompt,
        options: {
          ...this.defaultSettings,
          temperature: 0.3, // Lower temperature for analysis
          structured: true
        }
      });

      if (!response.success) {
        return { success: false, error: response.error };
      }

      const analysis = this.parseAnalysisResponse(response.data);
      
      return {
        success: true,
        data: analysis,
        metadata: {
          processingTime: response.metadata?.processingTime || 0,
          tokensUsed: response.metadata?.tokensUsed || 0,
          cost: response.metadata?.cost || 0,
          model: this.defaultSettings.model,
          confidence: this.calculateOverallConfidence(analysis)
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `Content analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Generate smart content suggestions for specific sections
   */
  async generateContent(
    request: SmartContentGeneration
  ): Promise<AIContentAPIResponse<SmartContentResponse>> {
    try {
      const prompt = this.buildGenerationPrompt(request);
      
      const response = await aiService.generateContent({
        prompt,
        options: {
          ...this.defaultSettings,
          temperature: 0.8, // Higher creativity for generation
          structured: true
        }
      });

      if (!response.success) {
        return { success: false, error: response.error };
      }

      const generatedContent = this.parseGenerationResponse(response.data);
      
      return {
        success: true,
        data: generatedContent,
        metadata: {
          processingTime: response.metadata?.processingTime || 0,
          tokensUsed: response.metadata?.tokensUsed || 0,
          cost: response.metadata?.cost || 0,
          model: this.defaultSettings.model,
          confidence: generatedContent.confidence
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `Content generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Optimize resume content for specific requirements
   */
  async optimizeContent(
    request: ContentOptimizationRequest
  ): Promise<AIContentAPIResponse<ContentOptimizationResponse>> {
    try {
      const prompt = this.buildOptimizationPrompt(request);
      
      const response = await aiService.generateContent({
        prompt,
        options: {
          ...this.defaultSettings,
          temperature: 0.5, // Balanced creativity and accuracy
          structured: true,
          maxTokens: 3000 // More tokens for comprehensive optimization
        }
      });

      if (!response.success) {
        return { success: false, error: response.error };
      }

      const optimization = this.parseOptimizationResponse(response.data);
      
      return {
        success: true,
        data: optimization,
        metadata: {
          processingTime: response.metadata?.processingTime || 0,
          tokensUsed: response.metadata?.tokensUsed || 0,
          cost: response.metadata?.cost || 0,
          model: this.defaultSettings.model,
          confidence: optimization.analysis.overallScore / 100
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `Content optimization failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Analyze ATS compatibility and provide optimization suggestions
   */
  async analyzeATS(
    resumeContent: ResumeContent,
    jobDescription?: string
  ): Promise<AIContentAPIResponse<ATSOptimization>> {
    try {
      const prompt = this.buildATSAnalysisPrompt(resumeContent, jobDescription);
      
      const response = await aiService.generateContent({
        prompt,
        options: {
          ...this.defaultSettings,
          temperature: 0.2, // Very low temperature for technical analysis
          structured: true
        }
      });

      if (!response.success) {
        return { success: false, error: response.error };
      }

      const atsAnalysis = this.parseATSResponse(response.data);
      
      return {
        success: true,
        data: atsAnalysis,
        metadata: {
          processingTime: response.metadata?.processingTime || 0,
          tokensUsed: response.metadata?.tokensUsed || 0,
          cost: response.metadata?.cost || 0,
          model: this.defaultSettings.model,
          confidence: atsAnalysis.score / 100
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `ATS analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Get industry-specific insights and recommendations
   */
  async getIndustryInsights(
    industry: string,
    role: string,
    experienceLevel: string
  ): Promise<AIContentAPIResponse<IndustryInsights>> {
    try {
      const prompt = this.buildIndustryInsightsPrompt(industry, role, experienceLevel);
      
      const response = await aiService.generateContent({
        prompt,
        options: {
          ...this.defaultSettings,
          temperature: 0.4,
          structured: true
        }
      });

      if (!response.success) {
        return { success: false, error: response.error };
      }

      const insights = this.parseIndustryInsightsResponse(response.data);
      
      return {
        success: true,
        data: insights,
        metadata: {
          processingTime: response.metadata?.processingTime || 0,
          tokensUsed: response.metadata?.tokensUsed || 0,
          cost: response.metadata?.cost || 0,
          model: this.defaultSettings.model,
          confidence: 0.8 // Industry insights are generally reliable
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `Industry insights failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Analyze job description and extract key requirements
   */
  async analyzeJobDescription(jobDescription: string): Promise<AIContentAPIResponse<JobAnalysis>> {
    try {
      const prompt = this.buildJobAnalysisPrompt(jobDescription);
      
      const response = await aiService.generateContent({
        prompt,
        options: {
          ...this.defaultSettings,
          temperature: 0.3,
          structured: true
        }
      });

      if (!response.success) {
        return { success: false, error: response.error };
      }

      const jobAnalysis = this.parseJobAnalysisResponse(response.data);
      
      return {
        success: true,
        data: jobAnalysis,
        metadata: {
          processingTime: response.metadata?.processingTime || 0,
          tokensUsed: response.metadata?.tokensUsed || 0,
          cost: response.metadata?.cost || 0,
          model: this.defaultSettings.model,
          confidence: 0.85
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `Job analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Private helper methods for building prompts
  private buildAnalysisPrompt(resumeContent: ResumeContent, jobDescription?: string): string {
    return `Analyze the following resume content and provide comprehensive feedback:

**Resume Content:**
${JSON.stringify(resumeContent, null, 2)}

${jobDescription ? `**Target Job Description:**\n${jobDescription}\n` : ''}

Provide analysis in the following JSON format:
{
  "overallScore": 85,
  "atsScore": 90,
  "keywordDensity": 0.75,
  "readabilityScore": 88,
  "impactScore": 82,
  "industryAlignment": 85,
  "strengths": ["Strong quantified achievements", "Relevant technical skills"],
  "weaknesses": ["Missing industry keywords", "Generic summary"],
  "missingKeywords": ["cloud computing", "agile methodology"],
  "suggestions": [
    {
      "id": "1",
      "type": "improvement",
      "section": "summary",
      "original": "Current summary text",
      "suggested": "Improved summary text",
      "reason": "More impactful and keyword-rich",
      "confidence": 0.9,
      "impact": "high",
      "category": "keywords",
      "keywords": ["leadership", "innovation"]
    }
  ],
  "sectionScores": {
    "summary": 80,
    "experience": 90,
    "skills": 85,
    "education": 75,
    "certifications": 70,
    "projects": 80
  }
}`;
  }

  private buildGenerationPrompt(request: SmartContentGeneration): string {
    return `Generate ${request.section} content based on the following context:

**Section:** ${request.section}
**Context:** ${JSON.stringify(request.context, null, 2)}
**Options:** ${JSON.stringify(request.options, null, 2)}

Generate content in the following JSON format:
{
  "generatedContent": "Generated content here",
  "alternatives": ["Alternative 1", "Alternative 2", "Alternative 3"],
  "keywords": ["relevant", "keywords", "included"],
  "reasoning": "Explanation of content choices",
  "confidence": 0.85,
  "suggestions": ["Additional improvement suggestions"]
}`;
  }

  private buildOptimizationPrompt(request: ContentOptimizationRequest): string {
    return `Optimize the following resume content:

**Current Content:** ${JSON.stringify(request.resumeContent, null, 2)}
**Job Description:** ${request.jobDescription || 'Not provided'}
**Target Role:** ${request.targetRole || 'Not specified'}
**Target Industry:** ${request.targetIndustry || 'Not specified'}
**Optimization Type:** ${request.optimizationType}
**User Preferences:** ${JSON.stringify(request.userPreferences, null, 2)}

Provide optimization in JSON format with analysis, suggestions, and optimized content.`;
  }

  private buildATSAnalysisPrompt(resumeContent: ResumeContent, jobDescription?: string): string {
    return `Analyze ATS compatibility for the following resume:

**Resume:** ${JSON.stringify(resumeContent, null, 2)}
${jobDescription ? `**Job Description:** ${jobDescription}` : ''}

Provide ATS analysis in JSON format with score, issues, recommendations, and compatibility details.`;
  }

  private buildIndustryInsightsPrompt(industry: string, role: string, experienceLevel: string): string {
    return `Provide industry insights for:
**Industry:** ${industry}
**Role:** ${role}
**Experience Level:** ${experienceLevel}

Include trends, recommendations, and competitor analysis in JSON format.`;
  }

  private buildJobAnalysisPrompt(jobDescription: string): string {
    return `Analyze the following job description and extract key information:

**Job Description:**
${jobDescription}

Provide analysis in JSON format with title, industry, skills, keywords, responsibilities, and qualifications.`;
  }

  // Response parsing methods
  private parseAnalysisResponse(data: any): ContentAnalysis {
    // Parse and validate AI response into ContentAnalysis structure
    return data; // Simplified for now
  }

  private parseGenerationResponse(data: any): SmartContentResponse {
    return data;
  }

  private parseOptimizationResponse(data: any): ContentOptimizationResponse {
    return data;
  }

  private parseATSResponse(data: any): ATSOptimization {
    return data;
  }

  private parseIndustryInsightsResponse(data: any): IndustryInsights {
    return data;
  }

  private parseJobAnalysisResponse(data: any): JobAnalysis {
    return data;
  }

  private calculateOverallConfidence(analysis: ContentAnalysis): number {
    return analysis.overallScore / 100;
  }
}

// Export singleton instance
export const aiContentService = AIContentService.getInstance();
