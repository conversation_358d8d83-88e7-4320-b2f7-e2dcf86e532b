import { agentRegistry } from './agent-registry';
import { aiConfigManager } from './config-manager';
import { aiService } from './service';
import type { AgentRequest, AgentResponse } from './base-agent';

export interface AgentTestResult {
  agentType: string;
  operation: string;
  success: boolean;
  responseTime: number;
  tokensUsed: number;
  cost: number;
  error?: string;
  output?: any;
  mockUsed: boolean;
}

export interface AgentTestSuite {
  agentType: string;
  tests: AgentTestCase[];
}

export interface AgentTestCase {
  name: string;
  operation: string;
  input: any;
  expectedFields?: string[];
  timeout?: number;
}

// Test cases for each agent
export const AGENT_TEST_SUITES: AgentTestSuite[] = [
  {
    agentType: 'RESUME',
    tests: [
      {
        name: 'Resume Generation',
        operation: 'generate',
        input: {
          jobTitle: 'Senior Software Engineer',
          jobDescription: 'We are looking for a Senior Software Engineer with experience in React, Node.js, and TypeScript.',
          company: 'TechCorp Inc.',
          requirements: ['React', 'Node.js', 'TypeScript', '5+ years experience'],
          preferences: {
            tone: 'professional',
            length: 'standard',
            format: 'modern'
          }
        },
        expectedFields: ['resumeContent', 'atsScore', 'optimizations'],
        timeout: 30000
      },
      {
        name: 'ATS Scoring',
        operation: 'ats-score',
        input: {
          resumeContent: 'John Doe\nSoftware Engineer\nExperienced in JavaScript, React, Node.js\n\nExperience:\n- Software Developer at TechCorp (2020-2023)\n- Built web applications using React and Node.js\n- Improved application performance by 30%',
          jobDescription: 'We are looking for a Senior Software Engineer with experience in React, Node.js, and TypeScript.'
        },
        expectedFields: ['score', 'breakdown', 'suggestions', 'missingKeywords'],
        timeout: 15000
      }
    ]
  },
  {
    agentType: 'RESEARCH',
    tests: [
      {
        name: 'Job Market Analysis',
        operation: 'analyze',
        input: {
          jobTitle: 'Software Engineer',
          location: 'San Francisco, CA',
          experienceLevel: 'senior',
          skills: ['React', 'Node.js', 'TypeScript']
        },
        expectedFields: ['demandLevel', 'salaryRange', 'requiredSkills', 'emergingSkills'],
        timeout: 30000
      }
    ]
  },
  {
    agentType: 'LINKEDIN_OPTIMIZER',
    tests: [
      {
        name: 'Profile Analysis',
        operation: 'analyze',
        input: {
          headline: 'Software Engineer at TechCorp',
          summary: 'Experienced software engineer with 5 years of experience...',
          currentPosition: 'Software Engineer',
          currentCompany: 'TechCorp Inc.',
          industry: 'Technology',
          skills: ['React', 'Node.js', 'JavaScript']
        },
        expectedFields: ['scores', 'recommendations', 'keywords'],
        timeout: 20000
      },
      {
        name: 'Profile Optimization',
        operation: 'optimize',
        input: {
          profileData: {
            headline: 'Software Engineer',
            currentPosition: 'Software Engineer',
            currentCompany: 'TechCorp',
            industry: 'Technology',
            skills: ['React', 'Node.js']
          },
          targetRole: 'Senior Software Engineer',
          tone: 'professional'
        },
        expectedFields: ['optimizedProfile', 'analysis', 'recommendations'],
        timeout: 30000
      }
    ]
  }
];

export class AgentTester {
  private static instance: AgentTester;

  private constructor() {}

  static getInstance(): AgentTester {
    if (!AgentTester.instance) {
      AgentTester.instance = new AgentTester();
    }
    return AgentTester.instance;
  }

  /**
   * Run all agent tests
   */
  async runAllTests(userId: string = 'test-user'): Promise<{
    summary: {
      total: number;
      passed: number;
      failed: number;
      successRate: number;
      totalTime: number;
      developmentMode: boolean;
    };
    results: AgentTestResult[];
    errors: string[];
  }> {
    const startTime = Date.now();
    const results: AgentTestResult[] = [];
    const errors: string[] = [];

    console.log('🧪 Starting CVLeap AI Agent Test Suite...');
    console.log(`📊 Development Mode: ${aiConfigManager.isDevelopmentMode()}`);

    for (const suite of AGENT_TEST_SUITES) {
      console.log(`\n🤖 Testing ${suite.agentType} Agent...`);
      
      for (const testCase of suite.tests) {
        try {
          const result = await this.runSingleTest(suite.agentType, testCase, userId);
          results.push(result);
          
          const status = result.success ? '✅' : '❌';
          const mockIndicator = result.mockUsed ? '🎭' : '🤖';
          console.log(`  ${status} ${mockIndicator} ${testCase.name}: ${result.responseTime}ms`);
          
          if (!result.success && result.error) {
            errors.push(`${suite.agentType}.${testCase.name}: ${result.error}`);
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          errors.push(`${suite.agentType}.${testCase.name}: ${errorMessage}`);
          
          results.push({
            agentType: suite.agentType,
            operation: testCase.operation,
            success: false,
            responseTime: 0,
            tokensUsed: 0,
            cost: 0,
            error: errorMessage,
            mockUsed: aiConfigManager.isDevelopmentMode(),
          });
          
          console.log(`  ❌ ${testCase.name}: ${errorMessage}`);
        }
      }
    }

    const totalTime = Date.now() - startTime;
    const passed = results.filter(r => r.success).length;
    const failed = results.length - passed;
    const successRate = results.length > 0 ? (passed / results.length) * 100 : 0;

    const summary = {
      total: results.length,
      passed,
      failed,
      successRate: Math.round(successRate * 100) / 100,
      totalTime,
      developmentMode: aiConfigManager.isDevelopmentMode(),
    };

    console.log('\n📊 Test Summary:');
    console.log(`  Total Tests: ${summary.total}`);
    console.log(`  Passed: ${summary.passed}`);
    console.log(`  Failed: ${summary.failed}`);
    console.log(`  Success Rate: ${summary.successRate}%`);
    console.log(`  Total Time: ${summary.totalTime}ms`);
    console.log(`  Development Mode: ${summary.developmentMode}`);

    if (errors.length > 0) {
      console.log('\n❌ Errors:');
      errors.forEach(error => console.log(`  - ${error}`));
    }

    return { summary, results, errors };
  }

  /**
   * Run a single test case
   */
  async runSingleTest(
    agentType: string,
    testCase: AgentTestCase,
    userId: string
  ): Promise<AgentTestResult> {
    const startTime = Date.now();
    const isDevelopmentMode = aiConfigManager.isDevelopmentMode();

    try {
      // Create agent request
      const request: AgentRequest = {
        input: testCase.input,
        context: {
          userId,
          sessionId: `test-${Date.now()}`,
          metadata: {
            testCase: testCase.name,
            operation: testCase.operation,
          },
        },
      };

      // Execute agent
      const response = await agentRegistry.execute(agentType as any, request);
      const responseTime = Date.now() - startTime;

      // Validate response
      const isValid = this.validateResponse(response, testCase.expectedFields);

      return {
        agentType,
        operation: testCase.operation,
        success: response.success && isValid,
        responseTime,
        tokensUsed: response.metadata?.tokensUsed || 0,
        cost: response.metadata?.cost || 0,
        output: response.data,
        error: response.success ? undefined : response.error,
        mockUsed: isDevelopmentMode,
      };
    } catch (error) {
      return {
        agentType,
        operation: testCase.operation,
        success: false,
        responseTime: Date.now() - startTime,
        tokensUsed: 0,
        cost: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
        mockUsed: isDevelopmentMode,
      };
    }
  }

  /**
   * Validate agent response structure
   */
  private validateResponse(response: AgentResponse, expectedFields?: string[]): boolean {
    if (!response.success || !response.data) {
      return false;
    }

    if (!expectedFields || expectedFields.length === 0) {
      return true;
    }

    const data = response.data;
    return expectedFields.every(field => {
      const hasField = field in data;
      if (!hasField) {
        console.warn(`Missing expected field: ${field}`);
      }
      return hasField;
    });
  }

  /**
   * Test AI service health
   */
  async testAIServiceHealth(): Promise<{
    status: string;
    providers: Record<string, boolean>;
    config: { valid: boolean; errors: string[] };
    developmentMode: boolean;
  }> {
    return await aiService.getHealthStatus();
  }

  /**
   * Test specific agent with custom input
   */
  async testAgent(
    agentType: string,
    operation: string,
    input: any,
    userId: string = 'test-user'
  ): Promise<AgentTestResult> {
    const testCase: AgentTestCase = {
      name: `Custom ${operation} test`,
      operation,
      input,
    };

    return await this.runSingleTest(agentType, testCase, userId);
  }
}

export const agentTester = AgentTester.getInstance();
