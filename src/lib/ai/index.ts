// AI Service Exports
export { aiService } from './service';
export { agentRegistry, executeAgent, initializeAgents } from './agent-registry';
export { BaseAgent } from './base-agent';
export { 
  openai, 
  anthropic, 
  AI_CONFIG, 
  validateAIConfig, 
  checkAIHealth,
  calculateCost,
  estimateTokens 
} from './config';

// Type exports
export type { 
  AIProvider, 
  AIRequest, 
  AIResponse 
} from './config';

export type { 
  AgentContext, 
  AgentRequest, 
  AgentResponse 
} from './base-agent';

// Agent exports
export { ResumeAgent } from '../../agents/resume/resume-agent';
export { ResearchAgent } from '../../agents/research/research-agent';
export { ApplicationAgent } from '../../agents/application/application-agent';

// Agent type exports
export type {
  ResumeGenerationInput,
  ResumeOptimizationInput,
  ATSScoringInput,
  ResumeGenerationOutput,
  ATSScoringOutput,
} from '../../agents/resume/resume-agent';

export type {
  JobMarketAnalysisInput,
  SkillTrendsInput,
  CompanyResearchInput,
  JobMarketAnalysisOutput,
  SkillTrendsOutput,
  CompanyResearchOutput,
} from '../../agents/research/research-agent';

export type {
  JobApplicationRequest,
  CoverLetterGenerationRequest,
  ApplicationOptimizationRequest,
  JobApplicationResponse,
  CoverLetterResponse,
  ApplicationOptimizationResponse,
} from '../../agents/application/application-agent';

// Utility functions
export async function initializeAIServices(): Promise<void> {
  console.log('🤖 Initializing AI services...');
  
  try {
    // Validate configuration
    const configValidation = validateAIConfig();
    if (!configValidation.valid) {
      console.warn('⚠️ AI configuration issues:', configValidation.errors);
    }

    // Check AI service health
    const healthCheck = await checkAIHealth();
    console.log('🔍 AI service health check:', healthCheck);

    // Initialize agents
    await initializeAgents();

    console.log('✅ AI services initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize AI services:', error);
    throw error;
  }
}

// Agent factory functions
export function createResumeAgent(): ResumeAgent {
  return new ResumeAgent();
}

export function createResearchAgent(): ResearchAgent {
  return new ResearchAgent();
}

export function createApplicationAgent() {
  const { ApplicationAgent } = require('../../agents/application/application-agent');
  return new ApplicationAgent();
}

// Quick access functions for common operations
export async function generateResume(
  input: any,
  userId: string,
  options?: {
    temperature?: number;
    maxTokens?: number;
    provider?: 'openai' | 'anthropic';
  }
) {
  return executeAgent('RESUME', input, userId, undefined, undefined, options);
}

export async function analyzeJobMarket(
  input: any,
  userId: string,
  options?: {
    temperature?: number;
    maxTokens?: number;
    provider?: 'openai' | 'anthropic';
  }
) {
  return executeAgent('RESEARCH', input, userId, undefined, undefined, options);
}

export async function submitJobApplication(
  input: any,
  userId: string,
  options?: {
    temperature?: number;
    maxTokens?: number;
    provider?: 'openai' | 'anthropic';
  }
) {
  return executeAgent('APPLICATION', input, userId, undefined, undefined, options);
}

export async function generateCoverLetter(
  input: any,
  userId: string,
  options?: {
    temperature?: number;
    maxTokens?: number;
    provider?: 'openai' | 'anthropic';
  }
) {
  return executeAgent('APPLICATION', input, userId, undefined, undefined, options);
}

export async function optimizeApplication(
  input: any,
  userId: string,
  options?: {
    temperature?: number;
    maxTokens?: number;
    provider?: 'openai' | 'anthropic';
  }
) {
  return executeAgent('APPLICATION', input, userId, undefined, undefined, options);
}

export async function scoreATS(
  resumeContent: string,
  jobDescription: string,
  userId: string,
  options?: {
    temperature?: number;
    maxTokens?: number;
    provider?: 'openai' | 'anthropic';
  }
) {
  return executeAgent('RESUME', {
    resumeContent,
    jobDescription,
  }, userId, undefined, undefined, options);
}

// Agent management utilities
export async function getAgentMetrics(userId?: string) {
  return agentRegistry.getAllMetrics(userId);
}

export async function getAgentHealth() {
  return agentRegistry.healthCheck();
}

export function getRegisteredAgents() {
  return agentRegistry.getAllAgents();
}

// Error handling utilities
export class AIServiceError extends Error {
  constructor(
    message: string,
    public code: string,
    public provider?: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'AIServiceError';
  }
}

export class AgentError extends Error {
  constructor(
    message: string,
    public agentType: string,
    public operation?: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'AgentError';
  }
}

// Constants
export const SUPPORTED_AGENT_TYPES = [
  'RESEARCH',
  'RESUME', 
  'PROFILE',
  'APPLICATION',
  'ANALYTICS',
  'TEMPLATE_RENDERER'
] as const;

export const AI_PROVIDERS = ['openai', 'anthropic'] as const;

export const DEFAULT_AI_OPTIONS = {
  temperature: 0.7,
  maxTokens: 2000,
  provider: 'openai' as const,
};

// Development utilities
export async function testAIConnection(): Promise<{
  openai: boolean;
  anthropic: boolean;
  overall: boolean;
}> {
  return checkAIHealth();
}

export async function testAgent(
  agentType: string,
  userId: string
): Promise<{ success: boolean; error?: string; processingTime?: number }> {
  try {
    const testInputs: Record<string, any> = {
      RESUME: {
        resumeContent: 'Test resume content for ATS scoring',
        jobDescription: 'Test job description for software engineer position',
      },
      RESEARCH: {
        jobTitle: 'Software Engineer',
        analysisType: 'comprehensive',
      },
      APPLICATION: {
        jobTitle: 'Software Engineer',
        companyName: 'Test Company',
        jobDescription: 'Test job description for cover letter generation',
        resumeContent: 'Test resume content',
      },
    };

    const testInput = testInputs[agentType];
    if (!testInput) {
      return { success: false, error: 'Unsupported agent type for testing' };
    }

    const startTime = Date.now();
    const result = await executeAgent(agentType as any, testInput, userId, undefined, { test: true }, {
      maxTokens: 100,
      temperature: 0.1,
    });

    return {
      success: result.success,
      error: result.error,
      processingTime: Date.now() - startTime,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Test failed',
    };
  }
}
