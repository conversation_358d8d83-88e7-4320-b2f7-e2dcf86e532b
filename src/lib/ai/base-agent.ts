import { aiService } from './service';
import { prisma } from '../prisma';
import type { AIRequest, AIResponse } from './config';
import type { AgentType } from '@prisma/client';

export interface AgentContext {
  userId: string;
  sessionId?: string;
  metadata?: Record<string, any>;
}

export interface AgentRequest {
  input: any;
  context: AgentContext;
  options?: {
    temperature?: number;
    maxTokens?: number;
    provider?: 'openai' | 'anthropic';
  };
}

export interface AgentResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: {
    tokensUsed: number;
    cost: number;
    processingTime: number;
    provider: string;
    model: string;
  };
}

export abstract class BaseAgent {
  protected agentType: AgentType;
  protected name: string;
  protected description: string;
  protected version: string;

  constructor(
    agentType: AgentType,
    name: string,
    description: string,
    version: string = '1.0.0'
  ) {
    this.agentType = agentType;
    this.name = name;
    this.description = description;
    this.version = version;
  }

  /**
   * Main execution method that all agents must implement
   */
  abstract execute(request: AgentRequest): Promise<AgentResponse>;

  /**
   * Generate system prompt for this agent
   */
  protected abstract getSystemPrompt(): string;

  /**
   * Validate input for this agent
   */
  protected abstract validateInput(input: any): { valid: boolean; errors: string[] };

  /**
   * Process AI request with logging
   */
  protected async processAIRequest(
    prompt: string,
    context: AgentContext,
    options?: {
      temperature?: number;
      maxTokens?: number;
      provider?: 'openai' | 'anthropic';
      structured?: boolean;
      schema?: any;
      mockKey?: string; // For development mode mock responses
    }
  ): Promise<AIResponse> {
    const aiRequest: AIRequest = {
      prompt,
      systemPrompt: this.getSystemPrompt(),
      temperature: options?.temperature,
      maxTokens: options?.maxTokens,
      provider: options?.provider,
      context: context.metadata,
      mockKey: options?.mockKey, // Support for development mode
    };

    const startTime = Date.now();
    let response: AIResponse;

    try {
      if (options?.structured) {
        response = await aiService.generateStructuredResponse(aiRequest, options.schema);
      } else {
        response = await aiService.generateResponse(aiRequest);
      }

      // Log successful interaction
      await this.logInteraction(
        context.userId,
        prompt,
        response.content,
        response,
        true,
        context.sessionId
      );

      return response;
    } catch (error) {
      // Log failed interaction
      const errorResponse: AIResponse = {
        content: '',
        provider: aiRequest.provider || 'openai',
        model: '',
        tokensUsed: 0,
        processingTime: Date.now() - startTime,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };

      await this.logInteraction(
        context.userId,
        prompt,
        '',
        errorResponse,
        false,
        context.sessionId
      );

      return errorResponse;
    }
  }

  /**
   * Log agent interaction to database
   */
  private async logInteraction(
    userId: string,
    prompt: string,
    response: string,
    aiResponse: AIResponse,
    success: boolean,
    sessionId?: string
  ): Promise<void> {
    try {
      await prisma.agentInteraction.create({
        data: {
          userId,
          agentType: this.agentType,
          prompt,
          response,
          context: {
            sessionId,
            agentName: this.name,
            agentVersion: this.version,
            provider: aiResponse.provider,
            model: aiResponse.model,
            tokensUsed: aiResponse.tokensUsed,
            cost: aiResponse.cost,
            processingTime: aiResponse.processingTime,
          },
          success,
          tokens: aiResponse.tokensUsed,
        },
      });
    } catch (error) {
      console.error('Failed to log agent interaction:', error);
    }
  }

  /**
   * Get agent performance metrics
   */
  async getMetrics(userId?: string, timeRange?: { start: Date; end: Date }): Promise<{
    totalInteractions: number;
    successRate: number;
    averageTokens: number;
    totalCost: number;
    averageResponseTime: number;
  }> {
    const where: any = {
      agentType: this.agentType,
    };

    if (userId) {
      where.userId = userId;
    }

    if (timeRange) {
      where.createdAt = {
        gte: timeRange.start,
        lte: timeRange.end,
      };
    }

    const interactions = await prisma.agentInteraction.findMany({
      where,
      select: {
        success: true,
        tokens: true,
        context: true,
      },
    });

    const totalInteractions = interactions.length;
    const successfulInteractions = interactions.filter(i => i.success).length;
    const totalTokens = interactions.reduce((sum, i) => sum + (i.tokens || 0), 0);
    const totalCost = interactions.reduce((sum, i) => {
      const cost = (i.context as any)?.cost || 0;
      return sum + cost;
    }, 0);
    const totalResponseTime = interactions.reduce((sum, i) => {
      const time = (i.context as any)?.processingTime || 0;
      return sum + time;
    }, 0);

    return {
      totalInteractions,
      successRate: totalInteractions > 0 ? successfulInteractions / totalInteractions : 0,
      averageTokens: totalInteractions > 0 ? totalTokens / totalInteractions : 0,
      totalCost,
      averageResponseTime: totalInteractions > 0 ? totalResponseTime / totalInteractions : 0,
    };
  }

  /**
   * Get recent interactions for this agent
   */
  async getRecentInteractions(
    userId?: string,
    limit: number = 10
  ): Promise<Array<{
    id: string;
    prompt: string;
    response: string;
    success: boolean;
    createdAt: Date;
    tokensUsed: number;
    cost: number;
  }>> {
    const where: any = {
      agentType: this.agentType,
    };

    if (userId) {
      where.userId = userId;
    }

    const interactions = await prisma.agentInteraction.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: limit,
      select: {
        id: true,
        prompt: true,
        response: true,
        success: true,
        createdAt: true,
        tokens: true,
        context: true,
      },
    });

    return interactions.map(interaction => ({
      id: interaction.id,
      prompt: interaction.prompt,
      response: interaction.response,
      success: interaction.success,
      createdAt: interaction.createdAt,
      tokensUsed: interaction.tokens || 0,
      cost: (interaction.context as any)?.cost || 0,
    }));
  }

  /**
   * Cache result for future use
   */
  protected async cacheResult(
    key: string,
    data: any,
    ttl: number = 3600 // 1 hour default
  ): Promise<void> {
    // This would be implemented with Redis or similar in production
    // For now, we'll use a simple in-memory cache or database storage
    console.log(`Caching result for key: ${key}, TTL: ${ttl}s`);
  }

  /**
   * Get cached result
   */
  protected async getCachedResult(key: string): Promise<any | null> {
    // This would be implemented with Redis or similar in production
    console.log(`Getting cached result for key: ${key}`);
    return null;
  }

  /**
   * Generate cache key for request
   */
  protected generateCacheKey(input: any, context: AgentContext): string {
    const inputHash = JSON.stringify(input);
    return `${this.agentType}:${context.userId}:${Buffer.from(inputHash).toString('base64')}`;
  }

  /**
   * Get agent information
   */
  getInfo(): {
    type: AgentType;
    name: string;
    description: string;
    version: string;
  } {
    return {
      type: this.agentType,
      name: this.name,
      description: this.description,
      version: this.version,
    };
  }
}
