import { z } from 'zod';

// Environment configuration schema
const AIConfigSchema = z.object({
  openai: z.object({
    apiKey: z.string().min(1, 'OpenAI API key is required'),
    model: z.string().default('gpt-4o'),
    maxTokens: z.number().default(4000),
    temperature: z.number().min(0).max(2).default(0.7),
  }),
  anthropic: z.object({
    apiKey: z.string().min(1, 'Anthropic API key is required'),
    model: z.string().default('claude-3-5-sonnet-20241022'),
    maxTokens: z.number().default(4000),
    temperature: z.number().min(0).max(1).default(0.7),
  }),
  fallback: z.object({
    enabled: z.boolean().default(true),
    retryAttempts: z.number().default(3),
    retryDelay: z.number().default(1000),
  }),
  features: z.object({
    costTracking: z.boolean().default(true),
    debugMode: z.boolean().default(false),
    caching: z.boolean().default(true),
    rateLimiting: z.boolean().default(true),
  }),
});

export type AIConfig = z.infer<typeof AIConfigSchema>;

// Development mode configuration
const DEVELOPMENT_CONFIG: AIConfig = {
  openai: {
    apiKey: 'sk-test-development-key',
    model: 'gpt-4o',
    maxTokens: 2000,
    temperature: 0.7,
  },
  anthropic: {
    apiKey: 'sk-ant-test-development-key',
    model: 'claude-3-5-sonnet-20241022',
    maxTokens: 2000,
    temperature: 0.7,
  },
  fallback: {
    enabled: true,
    retryAttempts: 2,
    retryDelay: 500,
  },
  features: {
    costTracking: false,
    debugMode: true,
    caching: false,
    rateLimiting: false,
  },
};

// Mock responses for development
export const MOCK_AI_RESPONSES = {
  application: {
    coverLetterGeneration: {
      content: {
        opening: "Dear Hiring Manager,",
        body: [
          "I am writing to express my strong interest in the Software Engineer position at TechCorp. With over 5 years of experience in full-stack development and a proven track record of delivering scalable solutions, I am excited about the opportunity to contribute to your innovative team.",
          "In my current role at InnovateTech, I have successfully led the development of microservices architecture that improved system performance by 40% and reduced deployment time by 60%. My expertise in React, Node.js, and cloud technologies aligns perfectly with the requirements outlined in your job posting.",
          "I am particularly drawn to TechCorp's commitment to cutting-edge technology and collaborative culture. Your recent work on AI-powered solutions resonates with my passion for leveraging technology to solve complex business challenges."
        ],
        closing: "I would welcome the opportunity to discuss how my technical skills and experience can contribute to TechCorp's continued success. Thank you for your consideration.",
        fullText: "Dear Hiring Manager,\n\nI am writing to express my strong interest in the Software Engineer position at TechCorp. With over 5 years of experience in full-stack development and a proven track record of delivering scalable solutions, I am excited about the opportunity to contribute to your innovative team.\n\nIn my current role at InnovateTech, I have successfully led the development of microservices architecture that improved system performance by 40% and reduced deployment time by 60%. My expertise in React, Node.js, and cloud technologies aligns perfectly with the requirements outlined in your job posting.\n\nI am particularly drawn to TechCorp's commitment to cutting-edge technology and collaborative culture. Your recent work on AI-powered solutions resonates with my passion for leveraging technology to solve complex business challenges.\n\nI would welcome the opportunity to discuss how my technical skills and experience can contribute to TechCorp's continued success. Thank you for your consideration.\n\nSincerely,\n[Your Name]"
      },
      keyPoints: [
        "5+ years full-stack development experience",
        "Led microservices architecture implementation",
        "40% performance improvement achievement",
        "Expertise in React, Node.js, cloud technologies",
        "Alignment with company's AI-powered solutions focus"
      ],
      matchScore: 87,
      suggestions: [
        "Consider adding specific metrics from recent projects",
        "Mention any relevant certifications or training",
        "Include a brief mention of soft skills like teamwork"
      ]
    },
    submission: {
      submissionStatus: "submitted",
      submissionMethod: "company_portal",
      submittedAt: new Date().toISOString(),
      trackingInfo: {
        confirmationNumber: "APP-2024-001234",
        submissionUrl: "https://careers.techcorp.com/applications/001234",
        followUpDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      },
      nextSteps: [
        "Application submitted successfully to TechCorp careers portal",
        "Confirmation email sent to your registered email address",
        "Follow up recommended in 1 week if no response received",
        "Connect with TechCorp employees on LinkedIn for networking",
        "Prepare for potential technical screening interview"
      ]
    },
    optimization: {
      overallScore: 82,
      matchAnalysis: {
        keywordMatch: 85,
        skillsMatch: 88,
        experienceMatch: 80,
        cultureMatch: 75
      },
      recommendations: [
        {
          category: "resume",
          priority: "high",
          suggestion: "Add more quantifiable achievements with specific metrics",
          impact: "Could increase ATS score by 10-15 points"
        },
        {
          category: "cover_letter",
          priority: "medium",
          suggestion: "Include specific examples of problem-solving abilities",
          impact: "Better demonstrates value proposition to hiring managers"
        },
        {
          category: "application_timing",
          priority: "high",
          suggestion: "Apply within 48 hours of job posting for maximum visibility",
          impact: "Early applications receive 3x more attention from recruiters"
        },
        {
          category: "follow_up",
          priority: "medium",
          suggestion: "Send personalized LinkedIn connection requests to team members",
          impact: "Networking increases interview chances by 40%"
        }
      ],
      competitiveAnalysis: {
        strengthsVsRequirements: [
          "Strong technical background matches 90% of required skills",
          "Leadership experience exceeds typical candidate profile",
          "Performance improvement track record demonstrates impact"
        ],
        potentialWeaknesses: [
          "Limited experience with specific cloud platform mentioned",
          "No mention of industry-specific domain knowledge",
          "Could benefit from additional certifications"
        ],
        differentiators: [
          "Proven track record of 40% performance improvements",
          "Cross-functional team leadership experience",
          "Full-stack expertise with modern technologies"
        ]
      },
      applicationStrategy: {
        bestTimeToApply: "Tuesday-Thursday, 9-11 AM local time for maximum recruiter attention",
        followUpSchedule: [
          "Week 1: LinkedIn connection with hiring manager",
          "Week 2: Follow-up email if no response",
          "Week 3: Connect with team members on LinkedIn",
          "Week 4: Final follow-up with additional portfolio examples"
        ],
        networkingOpportunities: [
          "Attend TechCorp's upcoming virtual tech talk on AI innovations",
          "Connect with current employees through mutual LinkedIn connections",
          "Engage with company content on social media platforms"
        ]
      }
    }
  },
  resume: {
    generation: {
      resumeContent: {
        summary: "Results-driven Software Engineer with 5+ years of experience building scalable web applications. Proven track record of improving system performance by 40% and leading cross-functional teams to deliver high-impact projects.",
        experience: [
          {
            title: "Senior Software Engineer",
            company: "TechCorp Inc.",
            duration: "2021 - Present",
            achievements: [
              "Improved application performance by 40% through React optimization and code splitting",
              "Led team of 5 developers in delivering $2M revenue-generating e-commerce platform",
              "Implemented CI/CD pipeline reducing deployment time from 2 hours to 15 minutes"
            ]
          }
        ],
        skills: ["React", "Node.js", "TypeScript", "AWS", "PostgreSQL"],
        education: [
          {
            degree: "Bachelor of Science in Computer Science",
            institution: "University of Technology",
            year: "2019"
          }
        ]
      },
      atsScore: 92,
      optimizations: [
        "Added quantifiable metrics to achievements",
        "Incorporated target role keywords naturally",
        "Improved action verb usage for impact"
      ]
    },
    atsScoring: {
      score: 85,
      breakdown: {
        keywords: 80,
        formatting: 90,
        content: 85,
        overall: 85
      },
      suggestions: [
        "Add more quantifiable achievements",
        "Include TypeScript experience",
        "Optimize for ATS keyword scanning"
      ],
      missingKeywords: ["TypeScript", "scalable", "microservices"],
      improvements: [
        "Quantify achievements with specific numbers and percentages",
        "Add more technical keywords relevant to the target role",
        "Improve formatting for better ATS parsing"
      ]
    }
  },
  research: {
    jobMarket: {
      demandLevel: "high",
      competitionLevel: "medium",
      growthProjection: "15% growth over next 5 years",
      salaryRange: {
        min: 75000,
        max: 150000,
        median: 110000,
        currency: "USD"
      },
      requiredSkills: [
        { skill: "React", importance: "critical", frequency: 85 },
        { skill: "Node.js", importance: "high", frequency: 78 },
        { skill: "TypeScript", importance: "high", frequency: 72 }
      ],
      emergingSkills: ["Next.js", "GraphQL", "Docker", "Kubernetes"],
      decliningSkills: ["jQuery", "AngularJS", "PHP"],
      marketInsights: [
        "Remote work opportunities increased by 300% in the past year",
        "Companies prioritizing full-stack developers with cloud experience",
        "Strong demand for developers with AI/ML integration skills"
      ]
    }
  },
  linkedin: {
    profileAnalysis: {
      scores: {
        completeness: 85,
        visibility: 78,
        engagement: 82,
        overall: 81
      },
      missingFields: ["summary", "skills"],
      recommendations: [
        "Complete your professional summary",
        "Add more relevant skills",
        "Update your headline for better searchability"
      ],
      keywords: ["Software Engineer", "React", "Full Stack"],
      insights: [
        "Your profile views could increase by 40% with optimization",
        "Adding industry keywords will improve recruiter discovery"
      ],
      branding: {
        strengths: ["Strong technical background", "Clear career progression"],
        improvements: ["Add more personality to summary", "Include specific achievements"]
      }
    },
    optimization: {
      optimizedProfile: {
        headline: [
          "Senior Software Engineer | React & Node.js Expert | Building Scalable Web Applications",
          "Full-Stack Developer | 5+ Years Experience | Driving Innovation in FinTech",
          "Software Engineer | React Specialist | Transforming Ideas into High-Performance Applications"
        ],
        summary: [
          "Experienced software engineer with a proven track record of building scalable web applications that serve millions of users. Specialized in React, Node.js, and cloud technologies with a passion for creating efficient, maintainable code that drives business growth.",
          "Results-driven full-stack developer with 5+ years of experience delivering high-impact solutions in fast-paced environments. Expert in modern JavaScript frameworks and cloud architecture, with a track record of improving system performance by 40%+ and leading successful product launches."
        ],
        aboutSection: [
          "I'm passionate about solving complex technical challenges and building products that make a real difference. My expertise spans the full development lifecycle, from initial concept to production deployment and optimization."
        ],
        skillSuggestions: ["React", "Node.js", "TypeScript", "AWS", "PostgreSQL", "Docker"]
      },
      analysis: {
        profileScore: 85,
        completenessScore: 90,
        visibilityScore: 80,
        engagementScore: 85,
        keywordDensity: 75
      },
      recommendations: {
        missingFields: ["certifications", "volunteer experience"],
        optimizationTips: [
          "Add specific metrics to your experience descriptions",
          "Include relevant certifications to boost credibility",
          "Engage with industry content to increase visibility"
        ],
        keywordSuggestions: ["full-stack", "scalable", "performance optimization"],
        industryInsights: [
          "Profiles with quantified achievements get 3x more recruiter views",
          "Including emerging technologies increases profile relevance"
        ]
      },
      recruiterInsights: {
        searchability: 85,
        attractiveness: 80,
        professionalBranding: 90,
        networkingPotential: 75
      }
    }
  }
};

class AIConfigManager {
  private static instance: AIConfigManager;
  private config: AIConfig;
  private isDevelopment: boolean;

  private constructor() {
    this.isDevelopment = process.env.NODE_ENV === 'development';
    this.config = this.loadConfiguration();
  }

  static getInstance(): AIConfigManager {
    if (!AIConfigManager.instance) {
      AIConfigManager.instance = new AIConfigManager();
    }
    return AIConfigManager.instance;
  }

  private loadConfiguration(): AIConfig {
    if (this.isDevelopment) {
      return DEVELOPMENT_CONFIG;
    }

    try {
      const envConfig = {
        openai: {
          apiKey: process.env.OPENAI_API_KEY || '',
          model: process.env.AI_MODEL_PRIMARY || 'gpt-4o',
          maxTokens: parseInt(process.env.AI_MAX_TOKENS || '4000'),
          temperature: parseFloat(process.env.AI_TEMPERATURE || '0.7'),
        },
        anthropic: {
          apiKey: process.env.ANTHROPIC_API_KEY || '',
          model: process.env.AI_MODEL_SECONDARY || 'claude-3-5-sonnet-20241022',
          maxTokens: parseInt(process.env.AI_MAX_TOKENS || '4000'),
          temperature: parseFloat(process.env.AI_TEMPERATURE || '0.7'),
        },
        fallback: {
          enabled: process.env.AI_FALLBACK_ENABLED === 'true',
          retryAttempts: parseInt(process.env.AI_RETRY_ATTEMPTS || '3'),
          retryDelay: parseInt(process.env.AI_RETRY_DELAY || '1000'),
        },
        features: {
          costTracking: process.env.AI_COST_TRACKING === 'true',
          debugMode: process.env.AI_DEBUG_MODE === 'true',
          caching: process.env.AI_CACHING !== 'false',
          rateLimiting: process.env.AI_RATE_LIMITING !== 'false',
        },
      };

      return AIConfigSchema.parse(envConfig);
    } catch (error) {
      console.warn('Invalid AI configuration, falling back to development mode:', error);
      return DEVELOPMENT_CONFIG;
    }
  }

  getConfig(): AIConfig {
    return this.config;
  }

  isDevelopmentMode(): boolean {
    return this.isDevelopment;
  }

  validateConfiguration(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.isDevelopment) {
      if (!this.config.openai.apiKey || this.config.openai.apiKey.includes('test')) {
        errors.push('OpenAI API key is not configured for production');
      }

      if (!this.config.anthropic.apiKey || this.config.anthropic.apiKey.includes('test')) {
        errors.push('Anthropic API key is not configured for production');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  getMockResponse(agentType: string, operation: string): any {
    const responses = MOCK_AI_RESPONSES as any;
    return responses[agentType]?.[operation] || null;
  }

  updateConfig(newConfig: Partial<AIConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

export const aiConfigManager = AIConfigManager.getInstance();
export { AIConfigSchema };
