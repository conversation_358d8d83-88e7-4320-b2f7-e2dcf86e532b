import { openai, anthropic, AI_CONFIG, calculateCost, estimateTokens } from './config';
import { aiConfigManager, MOCK_AI_RESPONSES } from './config-manager';
import type { AIRequest, AIResponse, AIProvider } from './config';

export class AIService {
  private static instance: AIService;

  private constructor() {}

  public static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  /**
   * Generate AI response with automatic fallback and development mode support
   */
  async generateResponse(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();
    const config = aiConfigManager.getConfig();

    // In development mode, return mock responses if available
    if (aiConfigManager.isDevelopmentMode() && request.mockKey) {
      const mockResponse = this.getMockResponse(request.mockKey);
      if (mockResponse) {
        return {
          content: JSON.stringify(mockResponse),
          provider: 'mock',
          model: 'development-mock',
          tokensUsed: estimateTokens(JSON.stringify(mockResponse)),
          processingTime: Date.now() - startTime,
          cost: 0,
          success: true,
        };
      }
    }

    const primaryProvider = request.provider || 'openai';
    const fallbackProvider = primaryProvider === 'openai' ? 'anthropic' : 'openai';

    // Validate configuration
    const validation = aiConfigManager.validateConfiguration();
    if (!validation.valid && !aiConfigManager.isDevelopmentMode()) {
      return {
        content: '',
        provider: primaryProvider,
        model: '',
        tokensUsed: 0,
        processingTime: Date.now() - startTime,
        cost: 0,
        success: false,
        error: `AI configuration invalid: ${validation.errors.join(', ')}`,
      };
    }

    // Try primary provider first
    try {
      const response = await this.callProvider(primaryProvider, request);
      response.processingTime = Date.now() - startTime;
      return response;
    } catch (error) {
      console.warn(`Primary AI provider (${primaryProvider}) failed:`, error);

      // Try fallback provider if enabled
      if (config.fallback.enabled) {
        try {
          await this.delay(config.fallback.retryDelay);
          const response = await this.callProvider(fallbackProvider, request);
          response.processingTime = Date.now() - startTime;
          return response;
        } catch (fallbackError) {
          console.error(`Fallback AI provider (${fallbackProvider}) also failed:`, fallbackError);
        }
      }

      // Return error response if all providers fail
      return {
        content: '',
        provider: primaryProvider,
        model: '',
        tokensUsed: 0,
        processingTime: Date.now() - startTime,
        success: false,
        error: error instanceof Error ? error.message : 'AI service unavailable',
      };
    }
  }

  /**
   * Call specific AI provider
   */
  private async callProvider(provider: AIProvider, request: AIRequest): Promise<AIResponse> {
    switch (provider) {
      case 'openai':
        return this.callOpenAI(request);
      case 'anthropic':
        return this.callAnthropic(request);
      default:
        throw new Error(`Unsupported AI provider: ${provider}`);
    }
  }

  /**
   * Call OpenAI API
   */
  private async callOpenAI(request: AIRequest): Promise<AIResponse> {
    if (!openai) {
      throw new Error('OpenAI client not available in development mode');
    }

    const messages: any[] = [];

    if (request.systemPrompt) {
      messages.push({ role: 'system', content: request.systemPrompt });
    }

    messages.push({ role: 'user', content: request.prompt });

    const completion = await openai.chat.completions.create({
      model: AI_CONFIG.openai.model,
      messages,
      max_tokens: request.maxTokens || AI_CONFIG.openai.maxTokens,
      temperature: request.temperature || AI_CONFIG.openai.temperature,
    });

    const content = completion.choices[0]?.message?.content || '';
    const tokensUsed = completion.usage?.total_tokens || estimateTokens(request.prompt + content);

    return {
      content,
      provider: 'openai',
      model: AI_CONFIG.openai.model,
      tokensUsed,
      cost: calculateCost('openai', AI_CONFIG.openai.model, tokensUsed),
      processingTime: 0, // Will be set by caller
      success: true,
    };
  }

  /**
   * Call Anthropic API
   */
  private async callAnthropic(request: AIRequest): Promise<AIResponse> {
    if (!anthropic) {
      throw new Error('Anthropic client not available in development mode');
    }

    const messages: any[] = [
      { role: 'user', content: request.prompt }
    ];

    const message = await anthropic.messages.create({
      model: AI_CONFIG.anthropic.model,
      max_tokens: request.maxTokens || AI_CONFIG.anthropic.maxTokens,
      temperature: request.temperature || AI_CONFIG.anthropic.temperature,
      system: request.systemPrompt,
      messages,
    });

    const content = message.content[0]?.type === 'text' ? message.content[0].text : '';
    const tokensUsed = message.usage?.input_tokens + message.usage?.output_tokens || estimateTokens(request.prompt + content);

    return {
      content,
      provider: 'anthropic',
      model: AI_CONFIG.anthropic.model,
      tokensUsed,
      cost: calculateCost('anthropic', AI_CONFIG.anthropic.model, tokensUsed),
      processingTime: 0, // Will be set by caller
      success: true,
    };
  }

  /**
   * Generate structured response with JSON parsing
   */
  async generateStructuredResponse<T>(
    request: AIRequest,
    schema?: any
  ): Promise<AIResponse & { data?: T }> {
    const enhancedRequest = {
      ...request,
      prompt: request.prompt + '\n\nPlease respond with valid JSON only.',
    };

    const response = await this.generateResponse(enhancedRequest);

    if (response.success && response.content) {
      try {
        const data = JSON.parse(response.content) as T;
        return { ...response, data };
      } catch (error) {
        console.warn('Failed to parse AI response as JSON:', error);
        return {
          ...response,
          success: false,
          error: 'Invalid JSON response from AI',
        };
      }
    }

    return response;
  }

  /**
   * Stream AI response (for real-time applications)
   */
  async *streamResponse(request: AIRequest): AsyncGenerator<string, void, unknown> {
    const provider = request.provider || 'openai';

    if (provider === 'openai') {
      yield* this.streamOpenAI(request);
    } else {
      // Anthropic doesn't support streaming in the same way, so we'll simulate it
      const response = await this.callAnthropic(request);
      if (response.success) {
        const words = response.content.split(' ');
        for (const word of words) {
          yield word + ' ';
          await this.delay(50); // Simulate streaming delay
        }
      }
    }
  }

  /**
   * Stream OpenAI response
   */
  private async *streamOpenAI(request: AIRequest): AsyncGenerator<string, void, unknown> {
    const messages: any[] = [];
    
    if (request.systemPrompt) {
      messages.push({ role: 'system', content: request.systemPrompt });
    }
    
    messages.push({ role: 'user', content: request.prompt });

    const stream = await openai.chat.completions.create({
      model: AI_CONFIG.openai.model,
      messages,
      max_tokens: request.maxTokens || AI_CONFIG.openai.maxTokens,
      temperature: request.temperature || AI_CONFIG.openai.temperature,
      stream: true,
    });

    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content || '';
      if (content) {
        yield content;
      }
    }
  }

  /**
   * Get mock response for development mode
   */
  private getMockResponse(mockKey: string): any {
    const [agentType, operation] = mockKey.split('.');
    return aiConfigManager.getMockResponse(agentType, operation);
  }

  /**
   * Utility function for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get service health status
   */
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    providers: Record<string, boolean>;
    config: { valid: boolean; errors: string[] };
    developmentMode: boolean;
  }> {
    const config = aiConfigManager.getConfig();
    const validation = aiConfigManager.validateConfiguration();
    const isDev = aiConfigManager.isDevelopmentMode();

    let openaiHealthy = false;
    let anthropicHealthy = false;

    if (!isDev) {
      // Test OpenAI connection
      try {
        await this.callProvider('openai', {
          prompt: 'Test',
          maxTokens: 1,
          temperature: 0,
        });
        openaiHealthy = true;
      } catch (error) {
        console.warn('OpenAI health check failed:', error);
      }

      // Test Anthropic connection
      try {
        await this.callProvider('anthropic', {
          prompt: 'Test',
          maxTokens: 1,
          temperature: 0,
        });
        anthropicHealthy = true;
      } catch (error) {
        console.warn('Anthropic health check failed:', error);
      }
    } else {
      // In development mode, consider services healthy if config is valid
      openaiHealthy = true;
      anthropicHealthy = true;
    }

    const overallHealthy = openaiHealthy || anthropicHealthy;

    return {
      status: overallHealthy ? 'healthy' : validation.valid ? 'degraded' : 'unhealthy',
      providers: {
        openai: openaiHealthy,
        anthropic: anthropicHealthy,
        overall: overallHealthy,
      },
      config: validation,
      developmentMode: isDev,
    };
  }

  /**
   * Batch process multiple requests
   */
  async batchProcess(requests: AIRequest[]): Promise<AIResponse[]> {
    const promises = requests.map(request => this.generateResponse(request));
    return Promise.all(promises);
  }

  /**
   * Get service statistics
   */
  getStats(): {
    totalRequests: number;
    successRate: number;
    averageResponseTime: number;
    totalTokensUsed: number;
    totalCost: number;
  } {
    // This would be implemented with actual tracking in a production system
    return {
      totalRequests: 0,
      successRate: 0,
      averageResponseTime: 0,
      totalTokensUsed: 0,
      totalCost: 0,
    };
  }
}

// Export singleton instance
export const aiService = AIService.getInstance();
