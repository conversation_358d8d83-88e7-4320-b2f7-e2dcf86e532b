import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';

// AI Service Configuration
export const AI_CONFIG = {
  openai: {
    apiKey: process.env.OPENAI_API_KEY,
    model: process.env.AI_MODEL_PRIMARY || 'gpt-4o',
    maxTokens: parseInt(process.env.AI_MAX_TOKENS || '4000'),
    temperature: parseFloat(process.env.AI_TEMPERATURE || '0.7'),
  },
  anthropic: {
    apiKey: process.env.ANTHROPIC_API_KEY,
    model: process.env.AI_MODEL_SECONDARY || 'claude-3-5-sonnet-20241022',
    maxTokens: parseInt(process.env.AI_MAX_TOKENS || '4000'),
    temperature: parseFloat(process.env.AI_TEMPERATURE || '0.7'),
  },
  fallback: {
    enabled: true,
    retryAttempts: 3,
    retryDelay: 1000, // ms
  },
};

// Check if we're in development mode with test keys
const isDevelopmentMode =
  AI_CONFIG.openai.apiKey?.startsWith('sk-test-') ||
  AI_CONFIG.anthropic.apiKey?.startsWith('sk-ant-test-') ||
  process.env.NODE_ENV === 'development';

// OpenAI Client - only initialize with real API key
export const openai = isDevelopmentMode && AI_CONFIG.openai.apiKey?.startsWith('sk-test-')
  ? null
  : new OpenAI({
      apiKey: AI_CONFIG.openai.apiKey || 'sk-dummy-key',
    });

// Anthropic Client - only initialize with real API key
export const anthropic = isDevelopmentMode && AI_CONFIG.anthropic.apiKey?.startsWith('sk-ant-test-')
  ? null
  : new Anthropic({
      apiKey: AI_CONFIG.anthropic.apiKey || 'sk-ant-dummy-key',
    });

// AI Service Types
export type AIProvider = 'openai' | 'anthropic';

export interface AIRequest {
  prompt: string;
  systemPrompt?: string;
  maxTokens?: number;
  temperature?: number;
  provider?: AIProvider;
  context?: Record<string, any>;
  mockKey?: string; // For development mode mock responses
}

export interface AIResponse {
  content: string;
  provider: AIProvider;
  model: string;
  tokensUsed: number;
  cost?: number;
  processingTime: number;
  success: boolean;
  error?: string;
}

// Token counting utilities
export function estimateTokens(text: string): number {
  // Rough estimation: ~4 characters per token for English text
  return Math.ceil(text.length / 4);
}

// Cost calculation utilities
export function calculateCost(provider: AIProvider, model: string, tokensUsed: number): number {
  const costs = {
    openai: {
      'gpt-4o': { input: 0.0025, output: 0.01 }, // per 1K tokens
      'gpt-4o-mini': { input: 0.00015, output: 0.0006 },
      'gpt-3.5-turbo': { input: 0.0005, output: 0.0015 },
    },
    anthropic: {
      'claude-3-5-sonnet-20241022': { input: 0.003, output: 0.015 },
      'claude-3-haiku-20240307': { input: 0.00025, output: 0.00125 },
    },
  };

  const modelCosts = costs[provider]?.[model];
  if (!modelCosts) return 0;

  // Simplified cost calculation (assuming equal input/output split)
  const avgCost = (modelCosts.input + modelCosts.output) / 2;
  return (tokensUsed / 1000) * avgCost;
}

// Validate AI configuration
export function validateAIConfig(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!AI_CONFIG.openai.apiKey || AI_CONFIG.openai.apiKey === 'your-openai-api-key') {
    errors.push('OpenAI API key is not configured');
  }

  if (!AI_CONFIG.anthropic.apiKey || AI_CONFIG.anthropic.apiKey === 'your-anthropic-api-key') {
    errors.push('Anthropic API key is not configured');
  }

  if (AI_CONFIG.openai.maxTokens < 100 || AI_CONFIG.openai.maxTokens > 128000) {
    errors.push('Invalid max tokens configuration');
  }

  if (AI_CONFIG.openai.temperature < 0 || AI_CONFIG.openai.temperature > 2) {
    errors.push('Invalid temperature configuration');
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

// Health check for AI services
export async function checkAIHealth(): Promise<{
  openai: boolean;
  anthropic: boolean;
  overall: boolean;
}> {
  const results = {
    openai: false,
    anthropic: false,
    overall: false,
  };

  // Test OpenAI
  try {
    if (AI_CONFIG.openai.apiKey && AI_CONFIG.openai.apiKey !== 'your-openai-api-key') {
      await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: 'test' }],
        max_tokens: 1,
      });
      results.openai = true;
    }
  } catch (error) {
    console.warn('OpenAI health check failed:', error);
  }

  // Test Anthropic
  try {
    if (AI_CONFIG.anthropic.apiKey && AI_CONFIG.anthropic.apiKey !== 'your-anthropic-api-key') {
      await anthropic.messages.create({
        model: 'claude-3-haiku-20240307',
        max_tokens: 1,
        messages: [{ role: 'user', content: 'test' }],
      });
      results.anthropic = true;
    }
  } catch (error) {
    console.warn('Anthropic health check failed:', error);
  }

  results.overall = results.openai || results.anthropic;
  return results;
}
