'use client';

import { useState, useCallback, useEffect, useMemo } from 'react';
import { 
  TemplateCustomization, 
  ColorScheme, 
  Typography, 
  LayoutSettings, 
  SpacingSettings,
  CustomizationValidation,
  CustomizationPreset,
  DEFAULT_COLOR_SCHEME,
  DEFAULT_TYPOGRAPHY,
  DEFAULT_LAYOUT,
  DEFAULT_SPACING
} from '@/types/customization';
import { CustomizationEngine } from '@/lib/customization/customization-engine';

export interface UseTemplateCustomizationOptions {
  templateId: string;
  initialCustomization?: Partial<TemplateCustomization>;
  autoSave?: boolean;
  autoSaveDelay?: number;
}

export interface UseTemplateCustomizationReturn {
  // State
  customization: TemplateCustomization;
  engine: CustomizationEngine;
  isDirty: boolean;
  isLoading: boolean;
  validation: CustomizationValidation;
  
  // Actions
  updateColorScheme: (colors: Partial<ColorScheme>) => void;
  updateTypography: (typography: Partial<Typography>) => void;
  updateLayout: (layout: Partial<LayoutSettings>) => void;
  updateSpacing: (spacing: Partial<SpacingSettings>) => void;
  applyPreset: (preset: CustomizationPreset) => void;
  resetToDefault: () => void;
  saveCustomization: () => Promise<void>;
  loadCustomization: (customization: TemplateCustomization) => void;
  
  // Utilities
  getCSSProperties: () => Record<string, string>;
  getCSSString: () => string;
  exportCustomization: () => string;
  importCustomization: (data: string) => boolean;
}

export function useTemplateCustomization({
  templateId,
  initialCustomization,
  autoSave = false,
  autoSaveDelay = 2000
}: UseTemplateCustomizationOptions): UseTemplateCustomizationReturn {
  
  // Initialize customization with defaults
  const createDefaultCustomization = useCallback((): TemplateCustomization => {
    const now = new Date();
    return {
      id: `custom-${templateId}-${Date.now()}`,
      templateId,
      name: 'Custom',
      colorScheme: DEFAULT_COLOR_SCHEME,
      typography: DEFAULT_TYPOGRAPHY,
      layout: DEFAULT_LAYOUT,
      spacing: DEFAULT_SPACING,
      isDefault: false,
      createdAt: now,
      updatedAt: now,
      ...initialCustomization
    };
  }, [templateId, initialCustomization]);

  const [customization, setCustomization] = useState<TemplateCustomization>(createDefaultCustomization);
  const [isDirty, setIsDirty] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Create customization engine
  const engine = useMemo(() => new CustomizationEngine(customization), [customization]);

  // Validate customization
  const validation = useMemo(() => engine.validateCustomization(), [engine]);

  // Auto-save functionality
  useEffect(() => {
    if (!autoSave || !isDirty) return;

    const timeoutId = setTimeout(() => {
      saveCustomization();
    }, autoSaveDelay);

    return () => clearTimeout(timeoutId);
  }, [customization, isDirty, autoSave, autoSaveDelay]);

  // Load customization from localStorage on mount
  useEffect(() => {
    const savedCustomization = localStorage.getItem(`template-customization-${templateId}`);
    if (savedCustomization) {
      try {
        const parsed = JSON.parse(savedCustomization);
        setCustomization(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.warn('Failed to load saved customization:', error);
      }
    }
  }, [templateId]);

  // Update color scheme
  const updateColorScheme = useCallback((colors: Partial<ColorScheme>) => {
    setCustomization(prev => ({
      ...prev,
      colorScheme: { ...prev.colorScheme, ...colors },
      updatedAt: new Date()
    }));
    setIsDirty(true);
  }, []);

  // Update typography
  const updateTypography = useCallback((typography: Partial<Typography>) => {
    setCustomization(prev => ({
      ...prev,
      typography: { ...prev.typography, ...typography },
      updatedAt: new Date()
    }));
    setIsDirty(true);
  }, []);

  // Update layout
  const updateLayout = useCallback((layout: Partial<LayoutSettings>) => {
    setCustomization(prev => ({
      ...prev,
      layout: { ...prev.layout, ...layout },
      updatedAt: new Date()
    }));
    setIsDirty(true);
  }, []);

  // Update spacing
  const updateSpacing = useCallback((spacing: Partial<SpacingSettings>) => {
    setCustomization(prev => ({
      ...prev,
      spacing: { ...prev.spacing, ...spacing },
      updatedAt: new Date()
    }));
    setIsDirty(true);
  }, []);

  // Apply preset
  const applyPreset = useCallback((preset: CustomizationPreset) => {
    setCustomization(prev => ({
      ...prev,
      ...preset.customization,
      id: prev.id,
      templateId: prev.templateId,
      name: `${preset.name} Custom`,
      updatedAt: new Date()
    }));
    setIsDirty(true);
  }, []);

  // Reset to default
  const resetToDefault = useCallback(() => {
    setCustomization(createDefaultCustomization());
    setIsDirty(true);
  }, [createDefaultCustomization]);

  // Save customization
  const saveCustomization = useCallback(async () => {
    setIsLoading(true);
    try {
      // Save to localStorage
      localStorage.setItem(`template-customization-${templateId}`, JSON.stringify(customization));
      
      // In a real app, you might also save to a backend API
      // await api.saveCustomization(customization);
      
      setIsDirty(false);
    } catch (error) {
      console.error('Failed to save customization:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [templateId, customization]);

  // Load customization
  const loadCustomization = useCallback((newCustomization: TemplateCustomization) => {
    setCustomization(newCustomization);
    setIsDirty(false);
  }, []);

  // Get CSS properties
  const getCSSProperties = useCallback(() => {
    return engine.getCSSProperties();
  }, [engine]);

  // Get CSS string
  const getCSSString = useCallback(() => {
    return engine.getCSSString();
  }, [engine]);

  // Export customization
  const exportCustomization = useCallback(() => {
    return JSON.stringify(customization, null, 2);
  }, [customization]);

  // Import customization
  const importCustomization = useCallback((data: string): boolean => {
    try {
      const imported = JSON.parse(data);
      if (imported.templateId === templateId) {
        setCustomization(imported);
        setIsDirty(true);
        return true;
      } else {
        console.warn('Template ID mismatch in imported customization');
        return false;
      }
    } catch (error) {
      console.error('Failed to import customization:', error);
      return false;
    }
  }, [templateId]);

  return {
    // State
    customization,
    engine,
    isDirty,
    isLoading,
    validation,
    
    // Actions
    updateColorScheme,
    updateTypography,
    updateLayout,
    updateSpacing,
    applyPreset,
    resetToDefault,
    saveCustomization,
    loadCustomization,
    
    // Utilities
    getCSSProperties,
    getCSSString,
    exportCustomization,
    importCustomization
  };
}
