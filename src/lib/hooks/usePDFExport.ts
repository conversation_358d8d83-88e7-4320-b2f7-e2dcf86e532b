'use client';

import { useState, useCallback } from 'react';
import { ResumeContent } from '@/types';

export interface PDFExportOptions {
  quality?: 'high' | 'medium' | 'low';
  format?: 'A4' | 'Letter';
  margin?: {
    top?: string;
    right?: string;
    bottom?: string;
    left?: string;
  };
  filename?: string;
}

export interface PDFExportState {
  isExporting: boolean;
  progress: number;
  error: string | null;
  lastExported: Date | null;
}

export interface PDFExportResult {
  success: boolean;
  filename?: string;
  error?: string;
  downloadUrl?: string;
}

export function usePDFExport() {
  const [state, setState] = useState<PDFExportState>({
    isExporting: false,
    progress: 0,
    error: null,
    lastExported: null
  });

  const exportToPDF = useCallback(async (
    resumeData: ResumeContent,
    templateId: string,
    options: PDFExportOptions = {}
  ): Promise<PDFExportResult> => {
    setState(prev => ({
      ...prev,
      isExporting: true,
      progress: 0,
      error: null
    }));

    try {
      // Validate required data
      if (!resumeData || !templateId) {
        throw new Error('Resume data and template ID are required');
      }

      // Set default options
      const exportOptions: PDFExportOptions = {
        quality: 'high',
        format: 'A4',
        margin: {
          top: '0.5in',
          right: '0.5in',
          bottom: '0.5in',
          left: '0.5in'
        },
        filename: `resume-${new Date().toISOString().split('T')[0]}.pdf`,
        ...options
      };

      // Update progress
      setState(prev => ({ ...prev, progress: 20 }));

      // Make API request to generate PDF
      const response = await fetch('/api/resumes/export/pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          resumeData,
          templateId,
          options: exportOptions
        })
      });

      setState(prev => ({ ...prev, progress: 60 }));

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      // Get the PDF blob
      const pdfBlob = await response.blob();
      setState(prev => ({ ...prev, progress: 80 }));

      // Create download URL
      const downloadUrl = URL.createObjectURL(pdfBlob);
      
      // Trigger download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = exportOptions.filename || 'resume.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up URL after a delay
      setTimeout(() => {
        URL.revokeObjectURL(downloadUrl);
      }, 1000);

      setState(prev => ({
        ...prev,
        isExporting: false,
        progress: 100,
        lastExported: new Date()
      }));

      return {
        success: true,
        filename: exportOptions.filename,
        downloadUrl
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      setState(prev => ({
        ...prev,
        isExporting: false,
        progress: 0,
        error: errorMessage
      }));

      return {
        success: false,
        error: errorMessage
      };
    }
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  const reset = useCallback(() => {
    setState({
      isExporting: false,
      progress: 0,
      error: null,
      lastExported: null
    });
  }, []);

  return {
    ...state,
    exportToPDF,
    clearError,
    reset
  };
}

// Utility function for generating filename
export function generatePDFFilename(
  personalInfo?: { fullName?: string },
  templateName?: string
): string {
  const date = new Date().toISOString().split('T')[0];
  const name = personalInfo?.fullName?.replace(/[^a-zA-Z0-9]/g, '_') || 'resume';
  const template = templateName?.replace(/[^a-zA-Z0-9]/g, '_') || '';
  
  return `${name}${template ? `_${template}` : ''}_${date}.pdf`;
}

// Utility function for validating resume data completeness
export function validateResumeForExport(resumeData: ResumeContent): {
  isValid: boolean;
  missingFields: string[];
  warnings: string[];
} {
  const missingFields: string[] = [];
  const warnings: string[] = [];

  // Check required fields
  if (!resumeData.personalInfo?.fullName) {
    missingFields.push('Full name');
  }
  if (!resumeData.personalInfo?.email) {
    missingFields.push('Email address');
  }
  if (!resumeData.experience || resumeData.experience.length === 0) {
    missingFields.push('Work experience');
  }

  // Check recommended fields
  if (!resumeData.summary || resumeData.summary.length < 50) {
    warnings.push('Professional summary is missing or too short');
  }
  if (!resumeData.skills || resumeData.skills.length < 3) {
    warnings.push('Add more skills (recommended: 5-10)');
  }
  if (!resumeData.education || resumeData.education.length === 0) {
    warnings.push('Education section is empty');
  }

  return {
    isValid: missingFields.length === 0,
    missingFields,
    warnings
  };
}
