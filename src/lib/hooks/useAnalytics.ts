/**
 * Analytics Hook
 * React hook for tracking analytics events and performance metrics
 */

'use client';

import { useEffect, useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { AnalyticsEventType, AnalyticsEventProperties } from '@/lib/analytics/analytics-service';
import { PerformanceMetricType } from '@/lib/analytics/performance-monitor';

interface UseAnalyticsOptions {
  trackPageViews?: boolean;
  trackPerformance?: boolean;
  sessionTimeout?: number; // in minutes
}

interface AnalyticsHook {
  trackEvent: (eventType: AnalyticsEventType, properties?: AnalyticsEventProperties) => Promise<void>;
  trackPageView: (page?: string, properties?: AnalyticsEventProperties) => Promise<void>;
  trackConversion: (conversionType: string, value?: number, properties?: AnalyticsEventProperties) => Promise<void>;
  trackError: (error: Error, context?: AnalyticsEventProperties) => Promise<void>;
  measurePerformance: (metricType: PerformanceMetricType, operation: () => Promise<any>) => Promise<any>;
  startTiming: (name: string) => () => void;
  sessionId: string;
}

export function useAnalytics(options: UseAnalyticsOptions = {}): AnalyticsHook {
  const { data: session } = useSession();
  const sessionIdRef = useRef<string>('');
  const timingsRef = useRef<Map<string, number>>(new Map());
  const pageViewTrackedRef = useRef<Set<string>>(new Set());

  const {
    trackPageViews = true,
    trackPerformance = true,
    sessionTimeout = 30,
  } = options;

  // Generate or retrieve session ID
  useEffect(() => {
    if (!sessionIdRef.current) {
      sessionIdRef.current = generateSessionId();
    }
  }, []);

  // Track page views automatically
  useEffect(() => {
    if (trackPageViews && typeof window !== 'undefined') {
      const currentPath = window.location.pathname;
      
      if (!pageViewTrackedRef.current.has(currentPath)) {
        trackPageView(currentPath);
        pageViewTrackedRef.current.add(currentPath);
      }
    }
  }, [trackPageViews]);

  // Track performance metrics automatically
  useEffect(() => {
    if (trackPerformance && typeof window !== 'undefined') {
      // Track page load performance
      const handleLoad = () => {
        if (window.performance && window.performance.timing) {
          const timing = window.performance.timing;
          const loadTime = timing.loadEventEnd - timing.navigationStart;
          
          trackEvent(AnalyticsEventType.PAGE_LOAD, {
            loadTime,
            page: window.location.pathname,
          });
        }
      };

      if (document.readyState === 'complete') {
        handleLoad();
      } else {
        window.addEventListener('load', handleLoad);
        return () => window.removeEventListener('load', handleLoad);
      }
    }
  }, [trackPerformance]);

  // Track user behavior events
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleVisibilityChange = () => {
        if (document.hidden) {
          trackEvent(AnalyticsEventType.FEATURE_USED, {
            action: 'page_hidden',
            page: window.location.pathname,
          });
        } else {
          trackEvent(AnalyticsEventType.FEATURE_USED, {
            action: 'page_visible',
            page: window.location.pathname,
          });
        }
      };

      const handleBeforeUnload = () => {
        trackEvent(AnalyticsEventType.FEATURE_USED, {
          action: 'page_unload',
          page: window.location.pathname,
          sessionDuration: Date.now() - parseInt(sessionIdRef.current.split('_')[1]),
        });
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);
      window.addEventListener('beforeunload', handleBeforeUnload);

      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        window.removeEventListener('beforeunload', handleBeforeUnload);
      };
    }
  }, []);

  const trackEvent = useCallback(async (
    eventType: AnalyticsEventType,
    properties: AnalyticsEventProperties = {}
  ): Promise<void> => {
    try {
      const response = await fetch('/api/analytics/track', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          eventType,
          properties: {
            ...properties,
            timestamp: Date.now(),
            userAgent: navigator.userAgent,
            screen: {
              width: window.screen.width,
              height: window.screen.height,
            },
            viewport: {
              width: window.innerWidth,
              height: window.innerHeight,
            },
          },
          sessionId: sessionIdRef.current,
          page: window.location.pathname,
          referrer: document.referrer,
        }),
      });

      if (!response.ok) {
        console.warn('Failed to track analytics event:', response.statusText);
      }
    } catch (error) {
      console.warn('Failed to track analytics event:', error);
    }
  }, []);

  const trackPageView = useCallback(async (
    page?: string,
    properties: AnalyticsEventProperties = {}
  ): Promise<void> => {
    const currentPage = page || window.location.pathname;
    
    await trackEvent(AnalyticsEventType.PAGE_LOAD, {
      page: currentPage,
      title: document.title,
      url: window.location.href,
      ...properties,
    });
  }, [trackEvent]);

  const trackConversion = useCallback(async (
    conversionType: string,
    value?: number,
    properties: AnalyticsEventProperties = {}
  ): Promise<void> => {
    await trackEvent(AnalyticsEventType.SUBSCRIPTION_STARTED, {
      conversionType,
      value,
      ...properties,
    });
  }, [trackEvent]);

  const trackError = useCallback(async (
    error: Error,
    context: AnalyticsEventProperties = {}
  ): Promise<void> => {
    await trackEvent(AnalyticsEventType.ERROR_OCCURRED, {
      errorMessage: error.message,
      errorStack: error.stack,
      errorName: error.name,
      page: window.location.pathname,
      ...context,
    });
  }, [trackEvent]);

  const measurePerformance = useCallback(async <T>(
    metricType: PerformanceMetricType,
    operation: () => Promise<T>
  ): Promise<T> => {
    const startTime = performance.now();
    
    try {
      const result = await operation();
      const duration = performance.now() - startTime;
      
      await trackEvent(AnalyticsEventType.API_CALL, {
        metricType,
        duration,
        success: true,
      });
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      await trackEvent(AnalyticsEventType.API_CALL, {
        metricType,
        duration,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      throw error;
    }
  }, [trackEvent]);

  const startTiming = useCallback((name: string): (() => void) => {
    const startTime = performance.now();
    timingsRef.current.set(name, startTime);
    
    return () => {
      const endTime = performance.now();
      const startTime = timingsRef.current.get(name);
      
      if (startTime !== undefined) {
        const duration = endTime - startTime;
        timingsRef.current.delete(name);
        
        trackEvent(AnalyticsEventType.FEATURE_USED, {
          action: 'timing_measurement',
          timingName: name,
          duration,
        });
      }
    };
  }, [trackEvent]);

  return {
    trackEvent,
    trackPageView,
    trackConversion,
    trackError,
    measurePerformance,
    startTiming,
    sessionId: sessionIdRef.current,
  };
}

// Helper function to generate session ID
function generateSessionId(): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return `session_${timestamp}_${random}`;
}

// Hook for tracking specific user interactions
export function useInteractionTracking() {
  const { trackEvent } = useAnalytics();

  const trackClick = useCallback((element: string, context?: AnalyticsEventProperties) => {
    trackEvent(AnalyticsEventType.FEATURE_USED, {
      action: 'click',
      element,
      ...context,
    });
  }, [trackEvent]);

  const trackFormSubmit = useCallback((formName: string, context?: AnalyticsEventProperties) => {
    trackEvent(AnalyticsEventType.FEATURE_USED, {
      action: 'form_submit',
      formName,
      ...context,
    });
  }, [trackEvent]);

  const trackFeatureUsage = useCallback((feature: string, context?: AnalyticsEventProperties) => {
    trackEvent(AnalyticsEventType.FEATURE_USED, {
      action: 'feature_usage',
      feature,
      ...context,
    });
  }, [trackEvent]);

  const trackSearch = useCallback((query: string, results: number, context?: AnalyticsEventProperties) => {
    trackEvent(AnalyticsEventType.FEATURE_USED, {
      action: 'search',
      query,
      results,
      ...context,
    });
  }, [trackEvent]);

  return {
    trackClick,
    trackFormSubmit,
    trackFeatureUsage,
    trackSearch,
  };
}

// Hook for A/B testing
export function useABTesting() {
  const { trackEvent } = useAnalytics();

  const getVariant = useCallback(async (experimentId: string) => {
    try {
      const response = await fetch(`/api/analytics/ab-testing?action=get-variant&experimentId=${experimentId}`);
      
      if (!response.ok) {
        return null;
      }
      
      const { variant } = await response.json();
      return variant;
    } catch (error) {
      console.warn('Failed to get A/B test variant:', error);
      return null;
    }
  }, []);

  const trackConversion = useCallback(async (experimentId: string, metric: string, value?: number) => {
    try {
      await fetch('/api/analytics/ab-testing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'track-conversion',
          experimentId,
          metric,
          value,
        }),
      });
    } catch (error) {
      console.warn('Failed to track A/B test conversion:', error);
    }
  }, []);

  return {
    getVariant,
    trackConversion,
  };
}
