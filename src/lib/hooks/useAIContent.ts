'use client';

import { useState, useCallback, useEffect, useMemo } from 'react';
import { 
  ContentSuggestion, 
  ContentAnalysis, 
  ContentOptimizationRequest,
  ContentOptimizationResponse,
  SmartContentGeneration,
  SmartContentResponse,
  ATSOptimization,
  RealTimeSuggestion,
  ContentMetrics
} from '@/types/ai-content';
import { ResumeContent } from '@/types';
import { aiContentService } from '@/lib/ai/content-service';

export interface UseAIContentOptions {
  resumeContent: ResumeContent;
  jobDescription?: string;
  enableRealTime?: boolean;
  autoAnalyze?: boolean;
  debounceMs?: number;
}

export interface UseAIContentReturn {
  // State
  analysis: ContentAnalysis | null;
  suggestions: ContentSuggestion[];
  realTimeSuggestions: RealTimeSuggestion[];
  atsOptimization: ATSOptimization | null;
  isAnalyzing: boolean;
  isGenerating: boolean;
  isOptimizing: boolean;
  error: string | null;
  metrics: ContentMetrics;

  // Actions
  analyzeContent: () => Promise<void>;
  generateContent: (request: SmartContentGeneration) => Promise<SmartContentResponse | null>;
  optimizeContent: (request: ContentOptimizationRequest) => Promise<ContentOptimizationResponse | null>;
  analyzeATS: () => Promise<void>;
  applySuggestion: (suggestionId: string) => void;
  rejectSuggestion: (suggestionId: string) => void;
  dismissSuggestion: (suggestionId: string) => void;
  clearSuggestions: () => void;
  refreshAnalysis: () => Promise<void>;

  // Utilities
  getSuggestionsBySection: (section: string) => ContentSuggestion[];
  getSuggestionsByType: (type: ContentSuggestion['type']) => ContentSuggestion[];
  getHighImpactSuggestions: () => ContentSuggestion[];
  calculateImprovementScore: () => number;
}

export function useAIContent({
  resumeContent,
  jobDescription,
  enableRealTime = false,
  autoAnalyze = true,
  debounceMs = 2000
}: UseAIContentOptions): UseAIContentReturn {

  // State
  const [analysis, setAnalysis] = useState<ContentAnalysis | null>(null);
  const [suggestions, setSuggestions] = useState<ContentSuggestion[]>([]);
  const [realTimeSuggestions, setRealTimeSuggestions] = useState<RealTimeSuggestion[]>([]);
  const [atsOptimization, setAtsOptimization] = useState<ATSOptimization | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [metrics, setMetrics] = useState<ContentMetrics>({
    totalSuggestions: 0,
    acceptedSuggestions: 0,
    rejectedSuggestions: 0,
    averageConfidence: 0,
    improvementScore: 0,
    timeSpent: 0,
    sectionsOptimized: [],
    keywordsAdded: 0,
    atsScoreImprovement: 0
  });

  // Debounced content analysis
  useEffect(() => {
    if (!autoAnalyze) return;

    const timeoutId = setTimeout(() => {
      analyzeContent();
    }, debounceMs);

    return () => clearTimeout(timeoutId);
  }, [resumeContent, jobDescription, autoAnalyze, debounceMs]);

  // Real-time suggestions
  useEffect(() => {
    if (!enableRealTime || !analysis) return;

    // Generate real-time suggestions based on current analysis
    const newRealTimeSuggestions = analysis.suggestions
      .filter(s => s.confidence > 0.8 && s.impact === 'high')
      .map(suggestion => ({
        id: `rt-${suggestion.id}`,
        field: suggestion.field || suggestion.section,
        currentValue: suggestion.original || '',
        suggestion,
        timestamp: new Date(),
        isActive: true
      }));

    setRealTimeSuggestions(newRealTimeSuggestions);
  }, [analysis, enableRealTime]);

  // Analyze content
  const analyzeContent = useCallback(async () => {
    setIsAnalyzing(true);
    setError(null);

    try {
      const response = await aiContentService.analyzeContent(resumeContent, jobDescription);
      
      if (response.success && response.data) {
        setAnalysis(response.data);
        setSuggestions(response.data.suggestions);
        
        // Update metrics
        setMetrics(prev => ({
          ...prev,
          totalSuggestions: response.data!.suggestions.length,
          averageConfidence: response.data!.suggestions.reduce((acc, s) => acc + s.confidence, 0) / response.data!.suggestions.length
        }));
      } else {
        setError(response.error || 'Analysis failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsAnalyzing(false);
    }
  }, [resumeContent, jobDescription]);

  // Generate content
  const generateContent = useCallback(async (
    request: SmartContentGeneration
  ): Promise<SmartContentResponse | null> => {
    setIsGenerating(true);
    setError(null);

    try {
      const response = await aiContentService.generateContent(request);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        setError(response.error || 'Content generation failed');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      return null;
    } finally {
      setIsGenerating(false);
    }
  }, []);

  // Optimize content
  const optimizeContent = useCallback(async (
    request: ContentOptimizationRequest
  ): Promise<ContentOptimizationResponse | null> => {
    setIsOptimizing(true);
    setError(null);

    try {
      const response = await aiContentService.optimizeContent(request);
      
      if (response.success && response.data) {
        // Update analysis and suggestions with optimization results
        setAnalysis(response.data.analysis);
        setSuggestions(response.data.suggestions);
        
        return response.data;
      } else {
        setError(response.error || 'Content optimization failed');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      return null;
    } finally {
      setIsOptimizing(false);
    }
  }, []);

  // Analyze ATS compatibility
  const analyzeATS = useCallback(async () => {
    setIsAnalyzing(true);
    setError(null);

    try {
      const response = await aiContentService.analyzeATS(resumeContent, jobDescription);
      
      if (response.success && response.data) {
        setAtsOptimization(response.data);
      } else {
        setError(response.error || 'ATS analysis failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsAnalyzing(false);
    }
  }, [resumeContent, jobDescription]);

  // Apply suggestion
  const applySuggestion = useCallback((suggestionId: string) => {
    setSuggestions(prev => prev.filter(s => s.id !== suggestionId));
    setRealTimeSuggestions(prev => 
      prev.map(rts => 
        rts.suggestion.id === suggestionId 
          ? { ...rts, userAction: 'accepted', isActive: false }
          : rts
      )
    );
    
    setMetrics(prev => ({
      ...prev,
      acceptedSuggestions: prev.acceptedSuggestions + 1
    }));
  }, []);

  // Reject suggestion
  const rejectSuggestion = useCallback((suggestionId: string) => {
    setSuggestions(prev => prev.filter(s => s.id !== suggestionId));
    setRealTimeSuggestions(prev => 
      prev.map(rts => 
        rts.suggestion.id === suggestionId 
          ? { ...rts, userAction: 'rejected', isActive: false }
          : rts
      )
    );
    
    setMetrics(prev => ({
      ...prev,
      rejectedSuggestions: prev.rejectedSuggestions + 1
    }));
  }, []);

  // Dismiss suggestion
  const dismissSuggestion = useCallback((suggestionId: string) => {
    setRealTimeSuggestions(prev => 
      prev.map(rts => 
        rts.suggestion.id === suggestionId 
          ? { ...rts, userAction: 'ignored', isActive: false }
          : rts
      )
    );
  }, []);

  // Clear all suggestions
  const clearSuggestions = useCallback(() => {
    setSuggestions([]);
    setRealTimeSuggestions([]);
  }, []);

  // Refresh analysis
  const refreshAnalysis = useCallback(async () => {
    await analyzeContent();
    if (atsOptimization) {
      await analyzeATS();
    }
  }, [analyzeContent, analyzeATS, atsOptimization]);

  // Utility functions
  const getSuggestionsBySection = useCallback((section: string) => {
    return suggestions.filter(s => s.section === section);
  }, [suggestions]);

  const getSuggestionsByType = useCallback((type: ContentSuggestion['type']) => {
    return suggestions.filter(s => s.type === type);
  }, [suggestions]);

  const getHighImpactSuggestions = useCallback(() => {
    return suggestions.filter(s => s.impact === 'high' && s.confidence > 0.8);
  }, [suggestions]);

  const calculateImprovementScore = useCallback(() => {
    if (!analysis) return 0;
    
    const baseScore = analysis.overallScore;
    const potentialImprovement = suggestions
      .filter(s => s.impact === 'high')
      .reduce((acc, s) => acc + (s.confidence * 10), 0);
    
    return Math.min(100, baseScore + potentialImprovement);
  }, [analysis, suggestions]);

  return {
    // State
    analysis,
    suggestions,
    realTimeSuggestions,
    atsOptimization,
    isAnalyzing,
    isGenerating,
    isOptimizing,
    error,
    metrics,

    // Actions
    analyzeContent,
    generateContent,
    optimizeContent,
    analyzeATS,
    applySuggestion,
    rejectSuggestion,
    dismissSuggestion,
    clearSuggestions,
    refreshAnalysis,

    // Utilities
    getSuggestionsBySection,
    getSuggestionsByType,
    getHighImpactSuggestions,
    calculateImprovementScore
  };
}
