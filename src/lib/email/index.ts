import nodemailer from 'nodemailer';

// Create reusable transporter object using SMTP transport
const createTransporter = () => {
  // In development, use a test account or log emails to console
  if (process.env.NODE_ENV === 'development' || !process.env.SMTP_HOST) {
    return nodemailer.createTransport({
      streamTransport: true,
      newline: 'unix',
      buffer: true,
    });
  }

  return nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });
};

const transporter = createTransporter();

/**
 * Send email verification email
 */
export async function sendVerificationEmail(email: string, token: string) {
  const verificationUrl = `${process.env.NEXTAUTH_URL}/auth/verify-email?token=${token}`;

  const mailOptions = {
    from: `"CVLeap" <${process.env.SMTP_USER}>`,
    to: email,
    subject: 'Verify your CVLeap account',
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Verify your CVLeap account</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to CVLeap!</h1>
          </div>
          
          <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
            <h2 style="color: #333; margin-top: 0;">Verify your email address</h2>
            <p>Thank you for signing up for CVLeap! To complete your registration and start building amazing resumes, please verify your email address by clicking the button below:</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${verificationUrl}" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">Verify Email Address</a>
            </div>
            
            <p>If the button doesn't work, you can also copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #666; font-size: 14px;">${verificationUrl}</p>
            
            <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
            
            <p style="font-size: 14px; color: #666;">
              This verification link will expire in 24 hours. If you didn't create an account with CVLeap, you can safely ignore this email.
            </p>
            
            <p style="font-size: 14px; color: #666;">
              Best regards,<br>
              The CVLeap Team
            </p>
          </div>
        </body>
      </html>
    `,
  };

  if (process.env.NODE_ENV === 'development') {
    console.log('📧 Email would be sent:', {
      to: email,
      subject: mailOptions.subject,
      verificationUrl,
    });
  } else {
    await transporter.sendMail(mailOptions);
  }
}

/**
 * Send password reset email
 */
export async function sendPasswordResetEmail(email: string, token: string) {
  const resetUrl = `${process.env.NEXTAUTH_URL}/auth/reset-password?token=${token}`;

  const mailOptions = {
    from: `"CVLeap" <${process.env.SMTP_USER}>`,
    to: email,
    subject: 'Reset your CVLeap password',
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Reset your CVLeap password</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 28px;">Password Reset</h1>
          </div>
          
          <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
            <h2 style="color: #333; margin-top: 0;">Reset your password</h2>
            <p>We received a request to reset your CVLeap account password. Click the button below to create a new password:</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetUrl}" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">Reset Password</a>
            </div>
            
            <p>If the button doesn't work, you can also copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #666; font-size: 14px;">${resetUrl}</p>
            
            <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
            
            <p style="font-size: 14px; color: #666;">
              This password reset link will expire in 1 hour. If you didn't request a password reset, you can safely ignore this email.
            </p>
            
            <p style="font-size: 14px; color: #666;">
              Best regards,<br>
              The CVLeap Team
            </p>
          </div>
        </body>
      </html>
    `,
  };

  if (process.env.NODE_ENV === 'development') {
    console.log('📧 Password reset email would be sent:', {
      to: email,
      subject: mailOptions.subject,
      resetUrl,
    });
  } else {
    await transporter.sendMail(mailOptions);
  }
}

/**
 * Send welcome email after successful registration
 */
export async function sendWelcomeEmail(email: string, name: string) {
  const dashboardUrl = `${process.env.NEXTAUTH_URL}/dashboard`;

  const mailOptions = {
    from: `"CVLeap" <${process.env.SMTP_USER}>`,
    to: email,
    subject: 'Welcome to CVLeap - Let\'s build your perfect resume!',
    html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Welcome to CVLeap</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to CVLeap!</h1>
          </div>
          
          <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
            <h2 style="color: #333; margin-top: 0;">Hi ${name}!</h2>
            <p>Congratulations on joining CVLeap! You're now part of a community that's revolutionizing the way people build resumes and find their dream jobs.</p>
            
            <h3 style="color: #333;">What you can do with CVLeap:</h3>
            <ul style="color: #666;">
              <li>🤖 <strong>AI-Powered Resume Builder</strong> - Create professional resumes with AI assistance</li>
              <li>📊 <strong>ATS Optimization</strong> - Ensure your resume passes applicant tracking systems</li>
              <li>💼 <strong>Job Application Tracker</strong> - Manage your job search with our Kanban-style dashboard</li>
              <li>📈 <strong>Career Analytics</strong> - Track your success and get insights to improve</li>
              <li>🎨 <strong>Professional Templates</strong> - Choose from our collection of industry-specific templates</li>
            </ul>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${dashboardUrl}" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">Get Started</a>
            </div>
            
            <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
            
            <p style="font-size: 14px; color: #666;">
              Need help getting started? Check out our <a href="${process.env.NEXTAUTH_URL}/help" style="color: #667eea;">help center</a> or reply to this email - we're here to help!
            </p>
            
            <p style="font-size: 14px; color: #666;">
              Best regards,<br>
              The CVLeap Team
            </p>
          </div>
        </body>
      </html>
    `,
  };

  if (process.env.NODE_ENV === 'development') {
    console.log('📧 Welcome email would be sent:', {
      to: email,
      subject: mailOptions.subject,
      dashboardUrl,
    });
  } else {
    await transporter.sendMail(mailOptions);
  }
}
