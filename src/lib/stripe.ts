import Stripe from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set');
}

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2024-12-18.acacia',
  typescript: true,
});

export const STRIPE_PLANS = {
  FREE: {
    name: 'Free',
    description: 'Perfect for getting started',
    price: 0,
    priceId: null,
    features: [
      '3 resume templates',
      'Basic customization',
      'PDF export',
      'Community support',
    ],
    limits: {
      resumes: 3,
      templates: 3,
      aiGenerations: 5,
      exports: 10,
    },
  },
  PRO: {
    name: 'Pro',
    description: 'For serious job seekers',
    price: 9.99,
    priceId: process.env.STRIPE_PRO_PRICE_ID,
    features: [
      'All 60+ premium templates',
      'Advanced AI content optimization',
      'Unlimited customization',
      'ATS compatibility analysis',
      'Multiple export formats',
      'Priority support',
    ],
    limits: {
      resumes: 50,
      templates: -1, // unlimited
      aiGenerations: 100,
      exports: -1, // unlimited
    },
  },
  ENTERPRISE: {
    name: 'Enterprise',
    description: 'For teams and organizations',
    price: 29.99,
    priceId: process.env.STRIPE_ENTERPRISE_PRICE_ID,
    features: [
      'Everything in Pro',
      'Team collaboration',
      'Custom branding',
      'Advanced analytics',
      'API access',
      'Dedicated support',
      'Custom integrations',
    ],
    limits: {
      resumes: -1, // unlimited
      templates: -1, // unlimited
      aiGenerations: -1, // unlimited
      exports: -1, // unlimited
    },
  },
} as const;

export type StripePlan = keyof typeof STRIPE_PLANS;

export async function createCheckoutSession({
  priceId,
  userId,
  userEmail,
  successUrl,
  cancelUrl,
}: {
  priceId: string;
  userId: string;
  userEmail: string;
  successUrl: string;
  cancelUrl: string;
}) {
  const session = await stripe.checkout.sessions.create({
    mode: 'subscription',
    payment_method_types: ['card'],
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    success_url: successUrl,
    cancel_url: cancelUrl,
    customer_email: userEmail,
    metadata: {
      userId,
    },
    subscription_data: {
      metadata: {
        userId,
      },
    },
  });

  return session;
}

export async function createBillingPortalSession({
  customerId,
  returnUrl,
}: {
  customerId: string;
  returnUrl: string;
}) {
  const session = await stripe.billingPortal.sessions.create({
    customer: customerId,
    return_url: returnUrl,
  });

  return session;
}

export async function getSubscriptionStatus(subscriptionId: string) {
  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    return subscription;
  } catch (error) {
    console.error('Error retrieving subscription:', error);
    return null;
  }
}

export async function cancelSubscription(subscriptionId: string) {
  try {
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true,
    });
    return subscription;
  } catch (error) {
    console.error('Error canceling subscription:', error);
    throw error;
  }
}

export async function reactivateSubscription(subscriptionId: string) {
  try {
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: false,
    });
    return subscription;
  } catch (error) {
    console.error('Error reactivating subscription:', error);
    throw error;
  }
}
