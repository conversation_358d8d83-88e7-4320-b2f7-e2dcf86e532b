/**
 * CVLeap Advanced Onboarding System
 * Guided user experience with progressive disclosure and contextual help
 */

import { analyticsService, AnalyticsEventType } from '@/lib/analytics/analytics-service';
import { prisma } from '@/lib/prisma';

// Onboarding step status
export enum OnboardingStepStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  SKIPPED = 'skipped',
}

// Onboarding flow type
export enum OnboardingFlowType {
  FIRST_TIME_USER = 'first_time_user',
  RESUME_CREATION = 'resume_creation',
  TEMPLATE_SELECTION = 'template_selection',
  AI_FEATURES = 'ai_features',
  CUSTOMIZATION = 'customization',
  EXPORT_SHARING = 'export_sharing',
}

// Onboarding step interface
export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  component: string; // React component name
  props?: {
    [key: string]: any;
  };
  prerequisites?: string[]; // Step IDs that must be completed first
  optional: boolean;
  estimatedTime: number; // in minutes
  helpContent?: {
    text: string;
    videoUrl?: string;
    links?: Array<{
      text: string;
      url: string;
    }>;
  };
  validationRules?: {
    [key: string]: any;
  };
}

// Onboarding flow configuration
export interface OnboardingFlow {
  id: string;
  type: OnboardingFlowType;
  name: string;
  description: string;
  steps: OnboardingStep[];
  targetAudience: {
    userSegments?: string[];
    userProperties?: {
      [key: string]: any;
    };
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// User onboarding progress
export interface UserOnboardingProgress {
  userId: string;
  flowId: string;
  currentStepId?: string;
  completedSteps: string[];
  skippedSteps: string[];
  startedAt: Date;
  completedAt?: Date;
  lastActiveAt: Date;
  metadata?: {
    [key: string]: any;
  };
}

// Onboarding analytics
export interface OnboardingAnalytics {
  flowId: string;
  totalUsers: number;
  completionRate: number;
  averageCompletionTime: number;
  dropoffPoints: Array<{
    stepId: string;
    dropoffRate: number;
  }>;
  stepAnalytics: Array<{
    stepId: string;
    completionRate: number;
    averageTime: number;
    skipRate: number;
  }>;
}

class OnboardingSystem {
  private static instance: OnboardingSystem;

  public static getInstance(): OnboardingSystem {
    if (!OnboardingSystem.instance) {
      OnboardingSystem.instance = new OnboardingSystem();
    }
    return OnboardingSystem.instance;
  }

  /**
   * Initialize onboarding for a user
   */
  async initializeOnboarding(userId: string, flowType: OnboardingFlowType): Promise<UserOnboardingProgress> {
    try {
      // Get the appropriate flow
      const flow = await this.getOnboardingFlow(flowType);
      if (!flow) {
        throw new Error(`Onboarding flow not found: ${flowType}`);
      }

      // Check if user already has progress for this flow
      const existingProgress = await prisma.userOnboardingProgress.findUnique({
        where: {
          userId_flowId: {
            userId,
            flowId: flow.id,
          },
        },
      });

      if (existingProgress) {
        return existingProgress as UserOnboardingProgress;
      }

      // Create new progress
      const progress: UserOnboardingProgress = {
        userId,
        flowId: flow.id,
        currentStepId: flow.steps[0]?.id,
        completedSteps: [],
        skippedSteps: [],
        startedAt: new Date(),
        lastActiveAt: new Date(),
      };

      await prisma.userOnboardingProgress.create({
        data: progress,
      });

      // Track onboarding start
      await analyticsService.trackEvent({
        userId,
        eventType: AnalyticsEventType.FEATURE_USED,
        properties: {
          action: 'onboarding_started',
          flowType,
          flowId: flow.id,
        },
      });

      return progress;
    } catch (error) {
      console.error('Failed to initialize onboarding:', error);
      throw error;
    }
  }

  /**
   * Get user's onboarding progress
   */
  async getUserProgress(userId: string, flowType: OnboardingFlowType): Promise<UserOnboardingProgress | null> {
    const flow = await this.getOnboardingFlow(flowType);
    if (!flow) return null;

    const progress = await prisma.userOnboardingProgress.findUnique({
      where: {
        userId_flowId: {
          userId,
          flowId: flow.id,
        },
      },
    });

    return progress as UserOnboardingProgress | null;
  }

  /**
   * Complete an onboarding step
   */
  async completeStep(userId: string, flowId: string, stepId: string): Promise<void> {
    try {
      const progress = await prisma.userOnboardingProgress.findUnique({
        where: {
          userId_flowId: {
            userId,
            flowId,
          },
        },
      });

      if (!progress) {
        throw new Error('Onboarding progress not found');
      }

      const flow = await this.getOnboardingFlowById(flowId);
      if (!flow) {
        throw new Error('Onboarding flow not found');
      }

      // Update progress
      const completedSteps = [...(progress.completedSteps as string[]), stepId];
      const nextStep = this.getNextStep(flow, stepId, completedSteps);

      await prisma.userOnboardingProgress.update({
        where: {
          userId_flowId: {
            userId,
            flowId,
          },
        },
        data: {
          completedSteps,
          currentStepId: nextStep?.id,
          lastActiveAt: new Date(),
          completedAt: nextStep ? undefined : new Date(),
        },
      });

      // Track step completion
      await analyticsService.trackEvent({
        userId,
        eventType: AnalyticsEventType.FEATURE_USED,
        properties: {
          action: 'onboarding_step_completed',
          flowId,
          stepId,
          isFlowCompleted: !nextStep,
        },
      });

      // Track flow completion if this was the last step
      if (!nextStep) {
        await analyticsService.trackEvent({
          userId,
          eventType: AnalyticsEventType.TUTORIAL_COMPLETED,
          properties: {
            flowId,
            flowType: flow.type,
            completionTime: Date.now() - new Date(progress.startedAt).getTime(),
          },
        });
      }
    } catch (error) {
      console.error('Failed to complete onboarding step:', error);
      throw error;
    }
  }

  /**
   * Skip an onboarding step
   */
  async skipStep(userId: string, flowId: string, stepId: string): Promise<void> {
    try {
      const progress = await prisma.userOnboardingProgress.findUnique({
        where: {
          userId_flowId: {
            userId,
            flowId,
          },
        },
      });

      if (!progress) {
        throw new Error('Onboarding progress not found');
      }

      const flow = await this.getOnboardingFlowById(flowId);
      if (!flow) {
        throw new Error('Onboarding flow not found');
      }

      // Update progress
      const skippedSteps = [...(progress.skippedSteps as string[]), stepId];
      const nextStep = this.getNextStep(flow, stepId, progress.completedSteps as string[], skippedSteps);

      await prisma.userOnboardingProgress.update({
        where: {
          userId_flowId: {
            userId,
            flowId,
          },
        },
        data: {
          skippedSteps,
          currentStepId: nextStep?.id,
          lastActiveAt: new Date(),
          completedAt: nextStep ? undefined : new Date(),
        },
      });

      // Track step skip
      await analyticsService.trackEvent({
        userId,
        eventType: AnalyticsEventType.FEATURE_USED,
        properties: {
          action: 'onboarding_step_skipped',
          flowId,
          stepId,
        },
      });
    } catch (error) {
      console.error('Failed to skip onboarding step:', error);
      throw error;
    }
  }

  /**
   * Get onboarding analytics
   */
  async getOnboardingAnalytics(flowId: string, timeframe?: { start: Date; end: Date }): Promise<OnboardingAnalytics> {
    const flow = await this.getOnboardingFlowById(flowId);
    if (!flow) {
      throw new Error('Onboarding flow not found');
    }

    const whereClause = {
      flowId,
      ...(timeframe && {
        startedAt: {
          gte: timeframe.start,
          lte: timeframe.end,
        },
      }),
    };

    // Get total users who started the flow
    const totalUsers = await prisma.userOnboardingProgress.count({
      where: whereClause,
    });

    // Get completed flows
    const completedFlows = await prisma.userOnboardingProgress.count({
      where: {
        ...whereClause,
        completedAt: {
          not: null,
        },
      },
    });

    const completionRate = totalUsers > 0 ? (completedFlows / totalUsers) * 100 : 0;

    // Calculate average completion time
    const completedFlowsData = await prisma.userOnboardingProgress.findMany({
      where: {
        ...whereClause,
        completedAt: {
          not: null,
        },
      },
      select: {
        startedAt: true,
        completedAt: true,
      },
    });

    const averageCompletionTime = completedFlowsData.length > 0
      ? completedFlowsData.reduce((sum, flow) => {
          const duration = new Date(flow.completedAt!).getTime() - new Date(flow.startedAt).getTime();
          return sum + duration;
        }, 0) / completedFlowsData.length
      : 0;

    // Calculate step analytics and dropoff points
    const stepAnalytics = [];
    const dropoffPoints = [];

    for (const step of flow.steps) {
      const stepCompletions = await prisma.userOnboardingProgress.count({
        where: {
          ...whereClause,
          completedSteps: {
            has: step.id,
          },
        },
      });

      const stepSkips = await prisma.userOnboardingProgress.count({
        where: {
          ...whereClause,
          skippedSteps: {
            has: step.id,
          },
        },
      });

      const stepCompletionRate = totalUsers > 0 ? (stepCompletions / totalUsers) * 100 : 0;
      const stepSkipRate = totalUsers > 0 ? (stepSkips / totalUsers) * 100 : 0;
      const dropoffRate = 100 - stepCompletionRate - stepSkipRate;

      stepAnalytics.push({
        stepId: step.id,
        completionRate: stepCompletionRate,
        averageTime: 0, // Would need additional tracking for step timing
        skipRate: stepSkipRate,
      });

      dropoffPoints.push({
        stepId: step.id,
        dropoffRate,
      });
    }

    return {
      flowId,
      totalUsers,
      completionRate,
      averageCompletionTime,
      dropoffPoints,
      stepAnalytics,
    };
  }

  /**
   * Create or update onboarding flow
   */
  async createOnboardingFlow(flow: Omit<OnboardingFlow, 'id' | 'createdAt' | 'updatedAt'>): Promise<OnboardingFlow> {
    const newFlow: OnboardingFlow = {
      ...flow,
      id: this.generateFlowId(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await prisma.onboardingFlow.create({
      data: {
        id: newFlow.id,
        type: newFlow.type,
        name: newFlow.name,
        description: newFlow.description,
        config: newFlow as any,
        isActive: newFlow.isActive,
        createdAt: newFlow.createdAt,
        updatedAt: newFlow.updatedAt,
      },
    });

    return newFlow;
  }

  // Private helper methods
  private async getOnboardingFlow(type: OnboardingFlowType): Promise<OnboardingFlow | null> {
    const flow = await prisma.onboardingFlow.findFirst({
      where: {
        type,
        isActive: true,
      },
    });

    return flow ? (flow.config as OnboardingFlow) : null;
  }

  private async getOnboardingFlowById(flowId: string): Promise<OnboardingFlow | null> {
    const flow = await prisma.onboardingFlow.findUnique({
      where: { id: flowId },
    });

    return flow ? (flow.config as OnboardingFlow) : null;
  }

  private getNextStep(
    flow: OnboardingFlow,
    currentStepId: string,
    completedSteps: string[],
    skippedSteps: string[] = []
  ): OnboardingStep | null {
    const currentIndex = flow.steps.findIndex(step => step.id === currentStepId);
    
    for (let i = currentIndex + 1; i < flow.steps.length; i++) {
      const step = flow.steps[i];
      
      // Check if prerequisites are met
      if (step.prerequisites) {
        const prerequisitesMet = step.prerequisites.every(prereq => 
          completedSteps.includes(prereq) || skippedSteps.includes(prereq)
        );
        
        if (!prerequisitesMet) {
          continue;
        }
      }
      
      // Check if step is already completed or skipped
      if (completedSteps.includes(step.id) || skippedSteps.includes(step.id)) {
        continue;
      }
      
      return step;
    }
    
    return null;
  }

  private generateFlowId(): string {
    return `flow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const onboardingSystem = OnboardingSystem.getInstance();
