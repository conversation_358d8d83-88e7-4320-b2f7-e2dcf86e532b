// Monitoring and error tracking utilities for CVLeap

interface ErrorContext {
  userId?: string;
  action?: string;
  metadata?: Record<string, any>;
  timestamp?: Date;
}

interface PerformanceMetric {
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count';
  timestamp: Date;
  context?: Record<string, any>;
}

class MonitoringService {
  private static instance: MonitoringService;
  private isProduction = process.env.NODE_ENV === 'production';
  private sentryDsn = process.env.SENTRY_DSN;

  static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService();
    }
    return MonitoringService.instance;
  }

  // Error tracking
  captureError(error: Error, context?: ErrorContext): void {
    const errorData = {
      message: error.message,
      stack: error.stack,
      name: error.name,
      timestamp: new Date().toISOString(),
      context,
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version,
    };

    // Log to console in development
    if (!this.isProduction) {
      console.error('Error captured:', errorData);
    }

    // Send to external monitoring service in production
    if (this.isProduction && this.sentryDsn) {
      this.sendToSentry(errorData);
    }

    // Store in database for internal tracking
    this.storeErrorInDatabase(errorData);
  }

  // Performance monitoring
  trackPerformance(metric: PerformanceMetric): void {
    const performanceData = {
      ...metric,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
    };

    if (!this.isProduction) {
      console.log('Performance metric:', performanceData);
    }

    // Send to analytics service
    this.sendPerformanceMetric(performanceData);
  }

  // User action tracking
  trackUserAction(action: string, userId: string, metadata?: Record<string, any>): void {
    const actionData = {
      action,
      userId,
      metadata,
      timestamp: new Date().toISOString(),
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
    };

    if (!this.isProduction) {
      console.log('User action:', actionData);
    }

    this.storeUserAction(actionData);
  }

  // API monitoring
  trackAPICall(endpoint: string, method: string, statusCode: number, duration: number, userId?: string): void {
    const apiData = {
      endpoint,
      method,
      statusCode,
      duration,
      userId,
      timestamp: new Date().toISOString(),
      success: statusCode >= 200 && statusCode < 300,
    };

    if (!this.isProduction) {
      console.log('API call:', apiData);
    }

    this.storeAPIMetric(apiData);
  }

  // AI usage tracking
  trackAIUsage(operation: string, model: string, tokensUsed: number, cost: number, userId: string, success: boolean): void {
    const aiData = {
      operation,
      model,
      tokensUsed,
      cost,
      userId,
      success,
      timestamp: new Date().toISOString(),
    };

    if (!this.isProduction) {
      console.log('AI usage:', aiData);
    }

    this.storeAIUsage(aiData);
  }

  // Subscription events
  trackSubscriptionEvent(event: string, userId: string, plan: string, metadata?: Record<string, any>): void {
    const subscriptionData = {
      event,
      userId,
      plan,
      metadata,
      timestamp: new Date().toISOString(),
    };

    if (!this.isProduction) {
      console.log('Subscription event:', subscriptionData);
    }

    this.storeSubscriptionEvent(subscriptionData);
  }

  // Health check
  async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; checks: Record<string, boolean> }> {
    const checks = {
      database: await this.checkDatabase(),
      redis: await this.checkRedis(),
      ai_service: await this.checkAIService(),
      stripe: await this.checkStripe(),
    };

    const allHealthy = Object.values(checks).every(check => check);

    return {
      status: allHealthy ? 'healthy' : 'unhealthy',
      checks,
    };
  }

  // Private methods
  private async sendToSentry(errorData: any): Promise<void> {
    try {
      // In a real implementation, you would use the Sentry SDK
      // For now, we'll just log that we would send to Sentry
      console.log('Would send to Sentry:', errorData);
    } catch (error) {
      console.error('Failed to send error to Sentry:', error);
    }
  }

  private async storeErrorInDatabase(errorData: any): Promise<void> {
    try {
      // Store error in database for internal tracking
      // This would use Prisma to store in an errors table
      console.log('Would store error in database:', errorData);
    } catch (error) {
      console.error('Failed to store error in database:', error);
    }
  }

  private async sendPerformanceMetric(performanceData: any): Promise<void> {
    try {
      // Send to analytics service (e.g., Vercel Analytics, Google Analytics)
      console.log('Would send performance metric:', performanceData);
    } catch (error) {
      console.error('Failed to send performance metric:', error);
    }
  }

  private async storeUserAction(actionData: any): Promise<void> {
    try {
      // Store user action for analytics
      console.log('Would store user action:', actionData);
    } catch (error) {
      console.error('Failed to store user action:', error);
    }
  }

  private async storeAPIMetric(apiData: any): Promise<void> {
    try {
      // Store API metrics for monitoring
      console.log('Would store API metric:', apiData);
    } catch (error) {
      console.error('Failed to store API metric:', error);
    }
  }

  private async storeAIUsage(aiData: any): Promise<void> {
    try {
      // Store AI usage for billing and monitoring
      console.log('Would store AI usage:', aiData);
    } catch (error) {
      console.error('Failed to store AI usage:', error);
    }
  }

  private async storeSubscriptionEvent(subscriptionData: any): Promise<void> {
    try {
      // Store subscription events for analytics
      console.log('Would store subscription event:', subscriptionData);
    } catch (error) {
      console.error('Failed to store subscription event:', error);
    }
  }

  private async checkDatabase(): Promise<boolean> {
    try {
      // Check database connectivity
      return true;
    } catch {
      return false;
    }
  }

  private async checkRedis(): Promise<boolean> {
    try {
      // Check Redis connectivity if using Redis
      return true;
    } catch {
      return false;
    }
  }

  private async checkAIService(): Promise<boolean> {
    try {
      // Check AI service connectivity
      return true;
    } catch {
      return false;
    }
  }

  private async checkStripe(): Promise<boolean> {
    try {
      // Check Stripe API connectivity
      return true;
    } catch {
      return false;
    }
  }
}

// Export singleton instance
export const monitoring = MonitoringService.getInstance();

// Utility functions for common monitoring tasks
export const withErrorTracking = <T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  context?: Omit<ErrorContext, 'timestamp'>
) => {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      monitoring.captureError(error as Error, { ...context, timestamp: new Date() });
      throw error;
    }
  };
};

export const withPerformanceTracking = <T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  metricName: string,
  context?: Record<string, any>
) => {
  return async (...args: T): Promise<R> => {
    const startTime = Date.now();
    try {
      const result = await fn(...args);
      const duration = Date.now() - startTime;
      monitoring.trackPerformance({
        name: metricName,
        value: duration,
        unit: 'ms',
        timestamp: new Date(),
        context,
      });
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      monitoring.trackPerformance({
        name: `${metricName}_error`,
        value: duration,
        unit: 'ms',
        timestamp: new Date(),
        context: { ...context, error: (error as Error).message },
      });
      throw error;
    }
  };
};
