// Error handling and fallback mechanisms for resume templates
// Provides graceful degradation when templates fail to load or render

import React from 'react';
import { ResumeData } from './template-registry';

export interface TemplateError {
  code: string;
  message: string;
  details?: string;
  timestamp: Date;
  templateId?: string;
  recoverable: boolean;
}

export interface FallbackOptions {
  showErrorDetails: boolean;
  enableRetry: boolean;
  fallbackTemplateId?: string;
  customErrorMessage?: string;
}

export class TemplateErrorHandler {
  private static instance: TemplateErrorHandler;
  private errorLog: TemplateError[] = [];

  private constructor() {}

  public static getInstance(): TemplateErrorHandler {
    if (!TemplateErrorHandler.instance) {
      TemplateErrorHandler.instance = new TemplateErrorHandler();
    }
    return TemplateErrorHandler.instance;
  }

  /**
   * Log a template error
   */
  public logError(error: Omit<TemplateError, 'timestamp'>): void {
    const templateError: TemplateError = {
      ...error,
      timestamp: new Date()
    };

    this.errorLog.push(templateError);
    
    // Keep only last 100 errors to prevent memory issues
    if (this.errorLog.length > 100) {
      this.errorLog = this.errorLog.slice(-100);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Template Error:', templateError);
    }

    // In production, you might want to send to an error tracking service
    if (process.env.NODE_ENV === 'production') {
      this.sendToErrorTracking(templateError);
    }
  }

  /**
   * Get recent errors for debugging
   */
  public getRecentErrors(templateId?: string): TemplateError[] {
    const errors = templateId 
      ? this.errorLog.filter(error => error.templateId === templateId)
      : this.errorLog;
    
    return errors.slice(-10); // Return last 10 errors
  }

  /**
   * Clear error log
   */
  public clearErrors(): void {
    this.errorLog = [];
  }

  /**
   * Check if a template has recent errors
   */
  public hasRecentErrors(templateId: string, withinMinutes: number = 5): boolean {
    const cutoff = new Date(Date.now() - withinMinutes * 60 * 1000);
    return this.errorLog.some(error => 
      error.templateId === templateId && 
      error.timestamp > cutoff
    );
  }

  /**
   * Send error to tracking service (placeholder)
   */
  private sendToErrorTracking(error: TemplateError): void {
    // Placeholder for error tracking service integration
    // e.g., Sentry, LogRocket, etc.
    console.log('Would send to error tracking:', error);
  }
}

// Error boundary component for template rendering
export class TemplateErrorBoundary extends React.Component<
  {
    children: React.ReactNode;
    templateId: string;
    fallbackOptions?: Partial<FallbackOptions>;
    onError?: (error: TemplateError) => void;
  },
  {
    hasError: boolean;
    error: TemplateError | null;
    retryCount: number;
  }
> {
  private errorHandler = TemplateErrorHandler.getInstance();

  constructor(props: any) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): any {
    return {
      hasError: true,
      error: {
        code: 'TEMPLATE_RENDER_ERROR',
        message: error.message,
        details: error.stack,
        timestamp: new Date(),
        recoverable: true
      }
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const templateError: TemplateError = {
      code: 'TEMPLATE_RENDER_ERROR',
      message: error.message,
      details: `${error.stack}\n\nComponent Stack:\n${errorInfo.componentStack}`,
      timestamp: new Date(),
      templateId: this.props.templateId,
      recoverable: true
    };

    this.errorHandler.logError(templateError);
    
    if (this.props.onError) {
      this.props.onError(templateError);
    }
  }

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      retryCount: prevState.retryCount + 1
    }));
  };

  render() {
    if (this.state.hasError) {
      return (
        <TemplateFallback
          error={this.state.error}
          templateId={this.props.templateId}
          options={this.props.fallbackOptions}
          onRetry={this.handleRetry}
          retryCount={this.state.retryCount}
        />
      );
    }

    return this.props.children;
  }
}

// Fallback component when templates fail
export function TemplateFallback({
  error,
  templateId,
  options = {},
  onRetry,
  retryCount = 0
}: {
  error: TemplateError | null;
  templateId: string;
  options?: Partial<FallbackOptions>;
  onRetry?: () => void;
  retryCount?: number;
}) {
  const defaultOptions: FallbackOptions = {
    showErrorDetails: process.env.NODE_ENV === 'development',
    enableRetry: true,
    customErrorMessage: undefined,
    ...options
  };

  const canRetry = defaultOptions.enableRetry && retryCount < 3;

  return (
    <div className="template-fallback bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
      <div className="max-w-md mx-auto">
        {/* Error Icon */}
        <div className="mb-4">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>

        {/* Error Message */}
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Template Loading Error
        </h3>
        
        <p className="text-sm text-gray-600 mb-4">
          {defaultOptions.customErrorMessage || 
           'We encountered an issue loading this resume template. Please try again or select a different template.'}
        </p>

        {/* Error Details (Development Only) */}
        {defaultOptions.showErrorDetails && error && (
          <details className="mb-4 text-left">
            <summary className="cursor-pointer text-sm font-medium text-gray-700 mb-2">
              Error Details
            </summary>
            <div className="bg-gray-100 rounded p-3 text-xs font-mono text-gray-800 overflow-auto max-h-32">
              <div><strong>Code:</strong> {error.code}</div>
              <div><strong>Message:</strong> {error.message}</div>
              <div><strong>Template ID:</strong> {templateId}</div>
              <div><strong>Time:</strong> {error.timestamp.toISOString()}</div>
              {error.details && (
                <div className="mt-2">
                  <strong>Details:</strong>
                  <pre className="whitespace-pre-wrap">{error.details}</pre>
                </div>
              )}
            </div>
          </details>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {canRetry && onRetry && (
            <button
              onClick={onRetry}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg
                className="-ml-1 mr-2 h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              Retry {retryCount > 0 && `(${retryCount}/3)`}
            </button>
          )}
          
          <button
            onClick={() => window.location.reload()}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Refresh Page
          </button>
        </div>

        {/* Fallback Template Option */}
        {defaultOptions.fallbackTemplateId && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-600 mb-2">
              Or try our basic template:
            </p>
            <button
              onClick={() => {
                // This would trigger a template switch to the fallback template
                console.log('Switch to fallback template:', defaultOptions.fallbackTemplateId);
              }}
              className="text-sm text-blue-600 hover:text-blue-500 underline"
            >
              Use Basic Template
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

// Simple fallback template component
export function BasicFallbackTemplate({ data }: { data: ResumeData }) {
  return (
    <div className="bg-white p-8 max-w-2xl mx-auto font-sans text-gray-900">
      {/* Header */}
      <header className="mb-8 text-center border-b pb-6">
        <h1 className="text-3xl font-bold mb-2">{data.personalInfo.name}</h1>
        <h2 className="text-xl text-gray-600 mb-4">{data.personalInfo.jobTitle}</h2>
        <div className="text-sm space-y-1">
          {data.personalInfo.email && <div>Email: {data.personalInfo.email}</div>}
          {data.personalInfo.phone && <div>Phone: {data.personalInfo.phone}</div>}
          {data.personalInfo.location && <div>Location: {data.personalInfo.location}</div>}
        </div>
      </header>

      {/* Summary */}
      {data.summary && (
        <section className="mb-6">
          <h3 className="text-lg font-semibold mb-3 border-b border-gray-300 pb-1">
            Professional Summary
          </h3>
          <p className="text-gray-700 leading-relaxed">{data.summary}</p>
        </section>
      )}

      {/* Experience */}
      {data.experience && data.experience.length > 0 && (
        <section className="mb-6">
          <h3 className="text-lg font-semibold mb-3 border-b border-gray-300 pb-1">
            Work Experience
          </h3>
          {data.experience.map((exp, index) => (
            <div key={index} className="mb-4">
              <div className="flex justify-between items-start mb-1">
                <h4 className="font-medium">{exp.jobTitle}</h4>
                <span className="text-sm text-gray-600">{exp.dates}</span>
              </div>
              <div className="text-gray-700 mb-2">{exp.company}</div>
              <p className="text-sm text-gray-600">{exp.description}</p>
            </div>
          ))}
        </section>
      )}

      {/* Education */}
      {data.education && data.education.length > 0 && (
        <section className="mb-6">
          <h3 className="text-lg font-semibold mb-3 border-b border-gray-300 pb-1">
            Education
          </h3>
          {data.education.map((edu, index) => (
            <div key={index} className="mb-2">
              <div className="flex justify-between items-start">
                <div>
                  <div className="font-medium">{edu.degree}</div>
                  <div className="text-gray-700">{edu.institution}</div>
                </div>
                <span className="text-sm text-gray-600">{edu.dates}</span>
              </div>
            </div>
          ))}
        </section>
      )}

      {/* Skills */}
      {data.skills && data.skills.length > 0 && (
        <section>
          <h3 className="text-lg font-semibold mb-3 border-b border-gray-300 pb-1">
            Skills
          </h3>
          <div className="flex flex-wrap gap-2">
            {data.skills.map((skill, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
              >
                {skill}
              </span>
            ))}
          </div>
        </section>
      )}
    </div>
  );
}

// Export singleton instance
export const templateErrorHandler = TemplateErrorHandler.getInstance();
