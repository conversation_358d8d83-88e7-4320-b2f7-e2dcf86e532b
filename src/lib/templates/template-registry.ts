// Template registry for CVLeap resume templates
// This file manages the registration and loading of all template components

import React from 'react';
import { ResumeContent } from '@/types';
import { TemplateMetadata, TemplateComponent } from './template-catalog';

// Import all template components
import ProfessionalSidebarTemplate from '@/components/templates/ProfessionalSidebarTemplate';
import ModernPhotoCVTemplate from '@/components/templates/ModernPhotoCVTemplate';
import CleanProfessionalTemplate from '@/components/templates/CleanProfessionalTemplate';
import TwoColumnSidebarTemplate from '@/components/templates/TwoColumnSidebarTemplate';

// Import newly integrated templates from 1comp directory
import { Classic001Template } from '@/components/templates/Classic001Template';
import CleanTemplate from '@/components/templates/CleanTemplate';
import { CorporateTemplate } from '@/components/templates/CorporateTemplate';
import { CreativeTemplate } from '@/components/templates/CreativeTemplate';
import { DynamicTemplate } from '@/components/templates/DynamicTemplate';
import { ElegantTemplate } from '@/components/templates/ElegantTemplate';
import EssentialTemplate from '@/components/templates/EssentialTemplate';
import { ExecutiveBlueTemplate } from '@/components/templates/ExecutiveBlueTemplate';
import { ExecutiveTemplate } from '@/components/templates/ExecutiveTemplate';
import { Fresh001Template } from '@/components/templates/Fresh001Template';
import { FreshTemplate } from '@/components/templates/FreshTemplate';
import { MinimalTemplate } from '@/components/templates/MinimalTemplate';
import { ModernTealTemplate } from '@/components/templates/ModernTealTemplate';
import { ModernTemplate } from '@/components/templates/ModernTemplate';
import { PlaceholderTemplate } from '@/components/templates/PlaceholderTemplate';
import { ProfessionalTemplate } from '@/components/templates/ProfessionalTemplate';
import { PureTemplate } from '@/components/templates/PureTemplate';
import SimpleTemplate from '@/components/templates/SimpleTemplate';
import { TechnicalBlueTemplate } from '@/components/templates/TechnicalBlueTemplate';

// Template component registry - Maps template IDs to React components
export const TEMPLATE_COMPONENTS: Record<string, React.ComponentType<any>> = {
  // Original templates
  'professional-sidebar-1': ProfessionalSidebarTemplate,
  'modern-photo-cv': ModernPhotoCVTemplate,
  'clean-professional-3': CleanProfessionalTemplate,
  'two-column-sidebar': TwoColumnSidebarTemplate,

  // Newly integrated templates (Templates 42-60)
  'classic-001-template': Classic001Template,
  'clean-template': CleanTemplate,
  'corporate-template': CorporateTemplate,
  'creative-template': CreativeTemplate,
  'dynamic-template': DynamicTemplate,
  'elegant-template': ElegantTemplate,
  'essential-template': EssentialTemplate,
  'executive-blue-template': ExecutiveBlueTemplate,
  'executive-template': ExecutiveTemplate,
  'fresh-001-template': Fresh001Template,
  'fresh-template': FreshTemplate,
  'minimal-template': MinimalTemplate,
  'modern-teal-template': ModernTealTemplate,
  'modern-template': ModernTemplate,
  'placeholder-template': PlaceholderTemplate,
  'professional-template': ProfessionalTemplate,
  'pure-template': PureTemplate,
  'simple-template': SimpleTemplate,
  'technical-blue-template': TechnicalBlueTemplate,

  // Figma-extracted templates (Templates 1-41) - Using placeholder components for now
  // These will be dynamically generated or use fallback rendering
  'decorative-header-5': PlaceholderTemplate,
  'creative-design-6': PlaceholderTemplate,
  'decorative-footer-7': PlaceholderTemplate,
  'personality-traits-8': PlaceholderTemplate,
  'green-sidebar-9': PlaceholderTemplate,
  'contact-badges-10': PlaceholderTemplate,
  'attorney-resume-11': PlaceholderTemplate,
  'ux-designer-12': PlaceholderTemplate,
  'traditional-layout-13': PlaceholderTemplate,
  'timeline-style-14': PlaceholderTemplate,
  'business-consultant': PlaceholderTemplate,
  'andrew-bolton-designer': PlaceholderTemplate,
  'dianne-russell-uiux': PlaceholderTemplate,
  'john-doe-uxui': PlaceholderTemplate,
  'danna-alquati-backend': PlaceholderTemplate,
  'professional-template-21': PlaceholderTemplate,
  'anne-harris-graphic': PlaceholderTemplate,
  'creative-circular-photo': PlaceholderTemplate,
  'jason-reyes-analyst': PlaceholderTemplate,
  'natasha-wilson-sales': PlaceholderTemplate,
  'rana-muktyomber-marketing': PlaceholderTemplate,
  'james-smith-ux-timeline': PlaceholderTemplate,
  'clean-skills-bars': PlaceholderTemplate,
  'john-doe-ux-blue': PlaceholderTemplate,
  'john-doe-product-two-column': PlaceholderTemplate,
  'john-doe-ux-timeline-2': PlaceholderTemplate,
  'james-smith-experience-portfolio': PlaceholderTemplate,
  'james-smith-ux-photo': PlaceholderTemplate,
  'resume-4-projects': PlaceholderTemplate,
  'emma-harrison-pm': PlaceholderTemplate,
  'priya-kapoor-analyst': PlaceholderTemplate,
  'rahul-mehta-marketing': PlaceholderTemplate,
  'robyn-kingsley-designer': PlaceholderTemplate,
  'aarav-mehta-frontend': PlaceholderTemplate,
  'rukia-sharma-resume': PlaceholderTemplate,
  'jone-don-resume': PlaceholderTemplate,
};

// Template registry class for managing templates
export class TemplateRegistry {
  private static instance: TemplateRegistry;
  private templates: Map<string, TemplateComponent> = new Map();

  private constructor() {
    this.initializeTemplates();
  }

  public static getInstance(): TemplateRegistry {
    if (!TemplateRegistry.instance) {
      TemplateRegistry.instance = new TemplateRegistry();
    }
    return TemplateRegistry.instance;
  }

  private initializeTemplates(): void {
    // Register all available templates
    Object.entries(TEMPLATE_COMPONENTS).forEach(([id, component]) => {
      this.registerTemplate(id, component);
    });
  }

  public registerTemplate(id: string, component: React.ComponentType<any>, metadata?: TemplateMetadata): void {
    const templateComponent: TemplateComponent = {
      id,
      component,
      metadata: metadata || this.getMetadataById(id)
    };
    this.templates.set(id, templateComponent);
  }

  public getTemplate(id: string): TemplateComponent | undefined {
    return this.templates.get(id);
  }

  public getAllTemplates(): TemplateComponent[] {
    return Array.from(this.templates.values());
  }

  public getTemplatesByCategory(category: string): TemplateComponent[] {
    return this.getAllTemplates().filter(template => 
      template.metadata?.category === category || category === 'all'
    );
  }

  public searchTemplates(query: string): TemplateComponent[] {
    const lowercaseQuery = query.toLowerCase();
    return this.getAllTemplates().filter(template => {
      const metadata = template.metadata;
      if (!metadata) return false;
      
      return (
        metadata.name.toLowerCase().includes(lowercaseQuery) ||
        metadata.description.toLowerCase().includes(lowercaseQuery) ||
        metadata.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
      );
    });
  }

  public getTemplateComponent(id: string): React.ComponentType<any> | undefined {
    const template = this.getTemplate(id);
    return template?.component;
  }

  private getMetadataById(id: string): TemplateMetadata {
    // Import template catalog to get metadata
    const { TEMPLATE_CATALOG } = require('./template-catalog');
    return TEMPLATE_CATALOG.find((template: TemplateMetadata) => template.id === id) || {
      id,
      name: 'Unknown Template',
      category: 'professional' as const,
      description: 'Template description not available',
      figmaNodeId: '',
      dimensions: { width: 595, height: 842 },
      sections: [],
      colorScheme: {
        primary: '#000000',
        background: '#ffffff',
        text: '#000000'
      },
      layout: 'single-column' as const,
      hasPhoto: false,
      atsOptimized: true,
      isPremium: false,
      tags: []
    };
  }

  public isTemplateAvailable(id: string): boolean {
    return this.templates.has(id);
  }

  public getTemplateIds(): string[] {
    return Array.from(this.templates.keys());
  }
}

// Export singleton instance
export const templateRegistry = TemplateRegistry.getInstance();

// Helper functions for easy access
export function getTemplateComponent(id: string): React.ComponentType<any> | undefined {
  return templateRegistry.getTemplateComponent(id);
}

export function getAllTemplates(): TemplateComponent[] {
  return templateRegistry.getAllTemplates();
}

export function getTemplatesByCategory(category: string): TemplateComponent[] {
  return templateRegistry.getTemplatesByCategory(category);
}

export function searchTemplates(query: string): TemplateComponent[] {
  return templateRegistry.searchTemplates(query);
}

export function isTemplateAvailable(id: string): boolean {
  return templateRegistry.isTemplateAvailable(id);
}

// Re-export ResumeContent from main types for template compatibility
export type { ResumeContent as ResumeData } from '@/types';

// Template props interface
export interface TemplateProps {
  data: ResumeContent;
  isPreview?: boolean;
  className?: string;
}

// Template validation function
export function validateTemplateData(data: ResumeContent, templateId: string): boolean {
  const template = templateRegistry.getTemplate(templateId);
  if (!template || !template.metadata) return false;

  const requiredSections = template.metadata.sections;
  
  // Check if all required sections have data
  for (const section of requiredSections) {
    switch (section) {
      case 'contact':
        if (!data.personalInfo.fullName || !data.personalInfo.email) return false;
        break;
      case 'experience':
        if (!data.experience || data.experience.length === 0) return false;
        break;
      case 'education':
        if (!data.education || data.education.length === 0) return false;
        break;
      case 'skills':
        if (!data.skills || data.skills.length === 0) return false;
        break;
      case 'profile':
      case 'summary':
        if (!data.summary) return false;
        break;
      case 'profile-picture':
        // Photo is optional in our current schema
        break;
    }
  }

  return true;
}
