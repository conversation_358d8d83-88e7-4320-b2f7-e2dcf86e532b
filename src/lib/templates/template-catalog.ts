// Template catalog for CVLeap resume templates
// This file contains metadata and configuration for all available resume templates

export interface TemplateMetadata {
  id: string;
  name: string;
  category: 'professional' | 'modern' | 'creative' | 'minimalist' | 'executive' | 'academic';
  description: string;
  figmaNodeId: string;
  dimensions: {
    width: number;
    height: number;
  };
  sections: string[];
  colorScheme: {
    primary: string;
    secondary?: string;
    accent?: string;
    background: string;
    text: string;
  };
  layout: 'single-column' | 'two-column' | 'sidebar-left' | 'sidebar-right';
  hasPhoto: boolean;
  atsOptimized: boolean;
  isPremium: boolean;
  previewUrl?: string;
  tags: string[];
}

export interface TemplateComponent {
  id: string;
  component: React.ComponentType<any>;
  metadata: TemplateMetadata;
}

// Template catalog with the templates we've extracted
export const TEMPLATE_CATALOG: TemplateMetadata[] = [
  {
    id: 'professional-sidebar-1',
    name: 'Professional Sidebar Resume',
    category: 'professional',
    description: 'Clean two-column layout with dark sidebar for contact info and skills',
    figmaNodeId: '132:1364',
    dimensions: { width: 583, height: 777 },
    sections: ['contact', 'education', 'skills', 'certifications', 'software', 'profile', 'experience'],
    colorScheme: {
      primary: '#2d3542',
      accent: '#bd9047',
      background: '#ffffff',
      text: '#444444'
    },
    layout: 'sidebar-left',
    hasPhoto: true,
    atsOptimized: true,
    isPremium: false,
    tags: ['professional', 'sidebar', 'two-column', 'dark-theme', 'business']
  },
  {
    id: 'modern-photo-cv',
    name: 'Modern CV with Photo',
    category: 'modern',
    description: 'Contemporary design with profile photo and colored sidebar',
    figmaNodeId: '132:1473',
    dimensions: { width: 595, height: 842 },
    sections: ['profile-picture', 'contact', 'skills', 'awards', 'hobbies', 'links', 'experience', 'education', 'volunteer'],
    colorScheme: {
      primary: '#2e235f',
      secondary: '#c3c2c2',
      background: '#ffffff',
      text: '#2a2a2a'
    },
    layout: 'sidebar-left',
    hasPhoto: true,
    atsOptimized: true,
    isPremium: false,
    tags: ['modern', 'photo', 'colorful', 'sidebar', 'creative']
  },
  {
    id: 'clean-professional-3',
    name: 'Clean Professional Layout',
    category: 'minimalist',
    description: 'Minimalist design with clear sections and professional typography',
    figmaNodeId: '16410:6665',
    dimensions: { width: 595, height: 852 },
    sections: ['header', 'summary', 'experience', 'projects', 'skills', 'education'],
    colorScheme: {
      primary: '#473bba',
      background: '#f8f9fa',
      text: '#252525'
    },
    layout: 'two-column',
    hasPhoto: false,
    atsOptimized: true,
    isPremium: false,
    tags: ['minimalist', 'clean', 'professional', 'two-column', 'modern']
  },
  {
    id: 'two-column-sidebar-4',
    name: 'Two Column Sidebar',
    category: 'professional',
    description: 'Professional layout with left sidebar for profile, skills, and contact information',
    figmaNodeId: '132:2056',
    dimensions: { width: 594, height: 842 },
    sections: ['header', 'contact', 'summary', 'experience', 'education', 'skills'],
    colorScheme: {
      primary: '#e4f2ef',
      secondary: '#dfae4f',
      accent: '#f2f1ed',
      background: '#ffffff',
      text: '#353743'
    },
    layout: 'sidebar-left',
    hasPhoto: false,
    atsOptimized: true,
    isPremium: false,
    tags: ['professional', 'sidebar', 'two-column', 'modern', 'business']
  },
  {
    id: 'decorative-header-5',
    name: 'Decorative Header Resume',
    category: 'creative',
    description: 'Header with decorative background, left column for main content, right column for contact/education/skills',
    figmaNodeId: '284:4907',
    dimensions: { width: 594, height: 842 },
    sections: ['header', 'contact', 'experience', 'education', 'skills', 'summary'],
    colorScheme: {
      primary: '#b8d5e7',
      secondary: '#333333',
      background: '#ffffff',
      text: '#333333'
    },
    layout: 'two-column',
    hasPhoto: true,
    atsOptimized: false,
    isPremium: false,
    tags: ['creative', 'decorative', 'header', 'two-column', 'artistic']
  },
  {
    id: 'creative-design-6',
    name: 'Creative Design Resume',
    category: 'creative',
    description: 'Creative design with decorative elements, profile photo, skills with progress bars',
    figmaNodeId: '142:389',
    dimensions: { width: 594, height: 842 },
    sections: ['header', 'photo', 'contact', 'experience', 'education', 'skills', 'summary'],
    colorScheme: {
      primary: '#4a90e2',
      secondary: '#f5f5f5',
      accent: '#e74c3c',
      background: '#ffffff',
      text: '#333333'
    },
    layout: 'two-column',
    hasPhoto: true,
    atsOptimized: false,
    isPremium: false,
    tags: ['creative', 'artistic', 'photo', 'progress-bars', 'colorful']
  },
  {
    id: 'decorative-footer-7',
    name: 'Decorative Footer Resume',
    category: 'creative',
    description: 'Header with decorative background, two-column layout, decorative footer',
    figmaNodeId: '142:686',
    dimensions: { width: 594, height: 842 },
    sections: ['header', 'contact', 'experience', 'education', 'skills', 'summary'],
    colorScheme: {
      primary: '#b8d5e7',
      secondary: '#333333',
      background: '#ffffff',
      text: '#333333'
    },
    layout: 'two-column',
    hasPhoto: true,
    atsOptimized: false,
    isPremium: false,
    tags: ['creative', 'decorative', 'footer', 'header', 'artistic']
  },
  {
    id: 'personality-traits-8',
    name: 'Personality Traits Resume',
    category: 'modern',
    description: 'Profile photo on right, skills section, personality traits with circular indicators',
    figmaNodeId: '142:520',
    dimensions: { width: 594, height: 842 },
    sections: ['header', 'photo', 'contact', 'experience', 'education', 'skills', 'languages', 'traits'],
    colorScheme: {
      primary: '#f1f6fa',
      secondary: '#333333',
      accent: '#4a90e2',
      background: '#f1f6fa',
      text: '#333333'
    },
    layout: 'two-column',
    hasPhoto: true,
    atsOptimized: true,
    isPremium: false,
    tags: ['modern', 'personality', 'traits', 'photo', 'circular-indicators']
  },
  {
    id: 'green-sidebar-9',
    name: 'Green Sidebar Resume',
    category: 'professional',
    description: 'Left sidebar with profile image, main content area, skills with circular progress indicators',
    figmaNodeId: '142:816',
    dimensions: { width: 594, height: 842 },
    sections: ['header', 'photo', 'contact', 'profile', 'experience', 'education', 'skills'],
    colorScheme: {
      primary: '#e4f2ef',
      secondary: '#7d4701',
      accent: '#333333',
      background: '#ffffff',
      text: '#333333'
    },
    layout: 'sidebar-left',
    hasPhoto: true,
    atsOptimized: true,
    isPremium: false,
    tags: ['professional', 'sidebar', 'green', 'circular-progress', 'photo']
  },
  {
    id: 'contact-badges-10',
    name: 'Contact Badges Resume',
    category: 'professional',
    description: 'Simple layout with contact badges, experience section, skills tags',
    figmaNodeId: '132:1617',
    dimensions: { width: 594, height: 842 },
    sections: ['header', 'contact', 'summary', 'experience', 'education', 'skills', 'languages'],
    colorScheme: {
      primary: '#e4f2ef',
      secondary: '#333333',
      background: '#ffffff',
      text: '#333333'
    },
    layout: 'single-column',
    hasPhoto: true,
    atsOptimized: true,
    isPremium: false,
    tags: ['professional', 'badges', 'simple', 'tags', 'contact-icons']
  },
  {
    id: 'attorney-resume-11',
    name: 'Attorney Professional Resume',
    category: 'professional',
    description: 'Professional layout with photo, sidebar for contact/skills/values, main content area',
    figmaNodeId: '132:2152',
    dimensions: { width: 594, height: 842 },
    sections: ['header', 'photo', 'contact', 'profile', 'experience', 'education', 'skills', 'values'],
    colorScheme: {
      primary: '#1e2027',
      secondary: '#dfae4f',
      accent: '#f2f1ed',
      background: '#ffffff',
      text: '#353743'
    },
    layout: 'sidebar-left',
    hasPhoto: true,
    atsOptimized: true,
    isPremium: false,
    tags: ['professional', 'attorney', 'legal', 'sidebar', 'photo']
  },
  {
    id: 'ux-designer-12',
    name: 'UX Designer Resume',
    category: 'creative',
    description: 'Clean two-column layout with left main content, right sidebar for skills/tools',
    figmaNodeId: '132:2475',
    dimensions: { width: 594, height: 842 },
    sections: ['header', 'contact', 'experience', 'education', 'skills', 'tools'],
    colorScheme: {
      primary: '#005ba2',
      secondary: '#454545',
      background: '#ffffff',
      text: '#000000'
    },
    layout: 'two-column',
    hasPhoto: false,
    atsOptimized: true,
    isPremium: false,
    tags: ['creative', 'ux-designer', 'clean', 'tools', 'skills-focused']
  },
  {
    id: 'traditional-layout-13',
    name: 'Traditional Layout Resume',
    category: 'professional',
    description: 'Traditional layout with photo, profile, education, employment, skills with progress bars, certifications',
    figmaNodeId: '132:1709',
    dimensions: { width: 594, height: 842 },
    sections: ['header', 'photo', 'contact', 'profile', 'education', 'employment', 'skills', 'certifications'],
    colorScheme: {
      primary: '#212121',
      secondary: '#3e6af2',
      background: '#ffffff',
      text: '#212121'
    },
    layout: 'two-column',
    hasPhoto: true,
    atsOptimized: true,
    isPremium: false,
    tags: ['professional', 'traditional', 'photo', 'progress-bars', 'certifications']
  },
  {
    id: 'timeline-style-14',
    name: 'Timeline Style Resume',
    category: 'modern',
    description: 'Modern timeline-style layout with experience items, skills sections',
    figmaNodeId: '132:1795',
    dimensions: { width: 594, height: 842 },
    sections: ['header', 'photo', 'contact', 'profile', 'experience', 'skills', 'social'],
    colorScheme: {
      primary: '#4b76c4',
      secondary: '#2c2c2c',
      accent: '#ffffff',
      background: '#ffffff',
      text: '#2c2c2c'
    },
    layout: 'single-column',
    hasPhoto: true,
    atsOptimized: false,
    isPremium: true,
    tags: ['modern', 'timeline', 'experience-focused', 'photo', 'social-links']
  },

  // Template 16: Business Consultant Resume
  {
    id: 'business-consultant',
    name: 'Business Consultant',
    category: 'professional',
    description: 'Professional business consultant resume with header, sidebar, and main content sections',
    thumbnail: '/templates/thumbnails/business-consultant.png',
    isPremium: false,
    tags: ['professional', 'business', 'consultant', 'clean'],
    industry: ['Business', 'Consulting', 'Finance'],
    experience: ['mid', 'senior', 'executive'],
    figmaNodeId: '16410:6444',
    styles: {
      primaryColor: '#1f2937',
      secondaryColor: '#6b7280',
      accentColor: '#f9fafb',
      fontFamily: 'Inter',
      layout: 'header-sidebar-main'
    },
    sections: ['header', 'sidebar', 'experience', 'education', 'skills'],
    features: ['ats-friendly', 'executive-level', 'customizable-colors'],
    createdAt: new Date('2024-01-16'),
    updatedAt: new Date('2024-01-16')
  },

  // Template 17: Andrew Bolton Web Designer
  {
    id: 'andrew-bolton-designer',
    name: 'Andrew Bolton Designer',
    category: 'creative',
    description: 'Creative web designer resume with photo and skills progress bars',
    thumbnail: '/templates/thumbnails/andrew-bolton-designer.png',
    isPremium: true,
    tags: ['creative', 'designer', 'photo', 'progress-bars'],
    industry: ['Design', 'Technology', 'Creative'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '132:3405',
    styles: {
      primaryColor: '#7c3aed',
      secondaryColor: '#a78bfa',
      accentColor: '#f3f4f6',
      fontFamily: 'Inter',
      layout: 'photo-sidebar'
    },
    sections: ['header', 'photo', 'experience', 'education', 'skills', 'contact'],
    features: ['photo-included', 'progress-bars', 'creative-design'],
    createdAt: new Date('2024-01-17'),
    updatedAt: new Date('2024-01-17')
  },

  // Template 18: Dianne Russell UI/UX Designer
  {
    id: 'dianne-russell-uiux',
    name: 'Dianne Russell UI/UX',
    category: 'creative',
    description: 'UI/UX designer resume with gradient background and clean layout',
    thumbnail: '/templates/thumbnails/dianne-russell-uiux.png',
    isPremium: true,
    tags: ['creative', 'ui-ux', 'gradient', 'modern'],
    industry: ['Design', 'Technology', 'Startups'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '132:2302',
    styles: {
      primaryColor: '#3b82f6',
      secondaryColor: '#60a5fa',
      accentColor: '#dbeafe',
      fontFamily: 'Inter',
      layout: 'gradient-modern'
    },
    sections: ['header', 'experience', 'education', 'skills', 'projects'],
    features: ['gradient-background', 'modern-design', 'creative-layout'],
    createdAt: new Date('2024-01-18'),
    updatedAt: new Date('2024-01-18')
  },

  // Template 19: John Doe UXUI Designer
  {
    id: 'john-doe-uxui',
    name: 'John Doe UXUI',
    category: 'creative',
    description: 'UXUI designer resume with blue header and two-column education/experience layout',
    thumbnail: '/templates/thumbnails/john-doe-uxui.png',
    isPremium: false,
    tags: ['creative', 'ux-ui', 'blue-header', 'two-column'],
    industry: ['Design', 'Technology', 'Digital'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '132:2863',
    styles: {
      primaryColor: '#1e40af',
      secondaryColor: '#3b82f6',
      accentColor: '#eff6ff',
      fontFamily: 'Inter',
      layout: 'header-two-column'
    },
    sections: ['header', 'experience', 'education', 'skills', 'contact'],
    features: ['blue-header', 'two-column-layout', 'skills-progress'],
    createdAt: new Date('2024-01-19'),
    updatedAt: new Date('2024-01-19')
  },

  // Template 20: Danna Alquati Backend Developer
  {
    id: 'danna-alquati-backend',
    name: 'Danna Alquati Backend',
    category: 'professional',
    description: 'Backend developer resume with dark sidebar and colorful section dividers',
    thumbnail: '/templates/thumbnails/danna-alquati-backend.png',
    isPremium: true,
    tags: ['technical', 'backend', 'dark-sidebar', 'colorful'],
    industry: ['Technology', 'Software', 'Engineering'],
    experience: ['mid', 'senior'],
    figmaNodeId: '132:2960',
    styles: {
      primaryColor: '#1f2937',
      secondaryColor: '#374151',
      accentColor: '#f59e0b',
      fontFamily: 'Inter',
      layout: 'dark-sidebar'
    },
    sections: ['header', 'sidebar', 'experience', 'education', 'skills', 'projects'],
    features: ['dark-theme', 'colorful-accents', 'technical-focus'],
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20')
  },

  // Template 21: Professional Resume Template
  {
    id: 'professional-template-21',
    name: 'Professional Template 21',
    category: 'professional',
    description: 'Professional resume with photo, skills, and experience sections',
    thumbnail: '/templates/thumbnails/professional-template-21.png',
    isPremium: false,
    tags: ['professional', 'photo', 'skills', 'clean'],
    industry: ['Business', 'Technology', 'Finance'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '132:3088',
    styles: {
      primaryColor: '#2563eb',
      secondaryColor: '#64748b',
      accentColor: '#f1f5f9',
      fontFamily: 'Inter',
      layout: 'standard'
    },
    sections: ['header', 'photo', 'experience', 'education', 'skills', 'contact'],
    features: ['ats-friendly', 'photo-optional', 'customizable-colors'],
    createdAt: new Date('2024-01-21'),
    updatedAt: new Date('2024-01-21')
  },

  // Template 22: Anne Harris Graphic Designer
  {
    id: 'anne-harris-graphic',
    name: 'Anne Harris Graphic',
    category: 'creative',
    description: 'Anne Harris graphic designer resume with purple header and green skills bars',
    thumbnail: '/templates/thumbnails/anne-harris-graphic.png',
    isPremium: true,
    tags: ['creative', 'graphic-design', 'purple-header', 'skills-bars'],
    industry: ['Design', 'Creative', 'Marketing'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '132:3201',
    styles: {
      primaryColor: '#7c3aed',
      secondaryColor: '#a78bfa',
      accentColor: '#10b981',
      fontFamily: 'Inter',
      layout: 'header-skills'
    },
    sections: ['header', 'experience', 'education', 'skills', 'portfolio'],
    features: ['purple-header', 'green-skills-bars', 'creative-layout'],
    createdAt: new Date('2024-01-22'),
    updatedAt: new Date('2024-01-22')
  },

  // Template 23: Creative Resume with Circular Photo
  {
    id: 'creative-circular-photo',
    name: 'Creative Circular Photo',
    category: 'creative',
    description: 'Creative resume with circular profile photo and skill level indicators',
    thumbnail: '/templates/thumbnails/creative-circular-photo.png',
    isPremium: true,
    tags: ['creative', 'circular-photo', 'skill-indicators', 'modern'],
    industry: ['Design', 'Creative', 'Technology'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '132:3288',
    styles: {
      primaryColor: '#3b82f6',
      secondaryColor: '#60a5fa',
      accentColor: '#dbeafe',
      fontFamily: 'Inter',
      layout: 'circular-photo'
    },
    sections: ['header', 'photo', 'experience', 'education', 'skills', 'contact'],
    features: ['circular-photo', 'skill-indicators', 'modern-design'],
    createdAt: new Date('2024-01-23'),
    updatedAt: new Date('2024-01-23')
  },

  // Template 24: Jason Reyes Data Analyst
  {
    id: 'jason-reyes-analyst',
    name: 'Jason Reyes Analyst',
    category: 'professional',
    description: 'Jason Reyes data analyst resume with clean professional layout',
    thumbnail: '/templates/thumbnails/jason-reyes-analyst.png',
    isPremium: false,
    tags: ['professional', 'data-analyst', 'clean', 'technical'],
    industry: ['Technology', 'Data', 'Analytics'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '16410:6257',
    styles: {
      primaryColor: '#1f2937',
      secondaryColor: '#6b7280',
      accentColor: '#f9fafb',
      fontFamily: 'Inter',
      layout: 'clean-professional'
    },
    sections: ['header', 'experience', 'education', 'skills', 'projects'],
    features: ['clean-layout', 'technical-focus', 'data-oriented'],
    createdAt: new Date('2024-01-24'),
    updatedAt: new Date('2024-01-24')
  },

  // Template 25: Natasha Wilson Sales Executive
  {
    id: 'natasha-wilson-sales',
    name: 'Natasha Wilson Sales',
    category: 'professional',
    description: 'Natasha Wilson sales executive resume with dark blue sidebar',
    thumbnail: '/templates/thumbnails/natasha-wilson-sales.png',
    isPremium: false,
    tags: ['professional', 'sales', 'dark-blue-sidebar', 'executive'],
    industry: ['Sales', 'Business', 'Marketing'],
    experience: ['mid', 'senior', 'executive'],
    figmaNodeId: '16410:5983',
    styles: {
      primaryColor: '#1e40af',
      secondaryColor: '#3b82f6',
      accentColor: '#eff6ff',
      fontFamily: 'Inter',
      layout: 'sidebar-executive'
    },
    sections: ['header', 'sidebar', 'experience', 'education', 'skills', 'achievements'],
    features: ['dark-blue-sidebar', 'executive-level', 'sales-focused'],
    createdAt: new Date('2024-01-25'),
    updatedAt: new Date('2024-01-25')
  },

  // Template 26: Rana Muktyomber Marketing Specialist
  {
    id: 'rana-muktyomber-marketing',
    name: 'Rana Muktyomber Marketing',
    category: 'professional',
    description: 'Rana Muktyomber marketing specialist resume with blue sidebar',
    thumbnail: '/templates/thumbnails/rana-muktyomber-marketing.png',
    isPremium: false,
    tags: ['professional', 'marketing', 'blue-sidebar', 'specialist'],
    industry: ['Marketing', 'Business', 'Digital'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '16410:6569',
    styles: {
      primaryColor: '#2563eb',
      secondaryColor: '#3b82f6',
      accentColor: '#dbeafe',
      fontFamily: 'Inter',
      layout: 'blue-sidebar'
    },
    sections: ['header', 'sidebar', 'experience', 'education', 'skills', 'certifications'],
    features: ['blue-sidebar', 'marketing-focused', 'specialist-level'],
    createdAt: new Date('2024-01-26'),
    updatedAt: new Date('2024-01-26')
  },

  // Template 27: James Smith UX Designer Timeline
  {
    id: 'james-smith-ux-timeline',
    name: 'James Smith UX Timeline',
    category: 'creative',
    description: 'James Smith UX designer resume with timeline and photo',
    thumbnail: '/templates/thumbnails/james-smith-ux-timeline.png',
    isPremium: true,
    tags: ['creative', 'ux-designer', 'timeline', 'photo'],
    industry: ['Design', 'Technology', 'UX'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '132:2630',
    styles: {
      primaryColor: '#7c3aed',
      secondaryColor: '#a78bfa',
      accentColor: '#f3f4f6',
      fontFamily: 'Inter',
      layout: 'timeline-photo'
    },
    sections: ['header', 'photo', 'timeline', 'experience', 'education', 'skills'],
    features: ['timeline-layout', 'photo-included', 'ux-focused'],
    createdAt: new Date('2024-01-27'),
    updatedAt: new Date('2024-01-27')
  },

  // Template 28: Clean Resume with Skills Bars
  {
    id: 'clean-skills-bars',
    name: 'Clean Skills Bars',
    category: 'professional',
    description: 'Clean resume with skills bars and strengths sections',
    thumbnail: '/templates/thumbnails/clean-skills-bars.png',
    isPremium: false,
    tags: ['professional', 'clean', 'skills-bars', 'strengths'],
    industry: ['Business', 'Technology', 'General'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '132:2564',
    styles: {
      primaryColor: '#1f2937',
      secondaryColor: '#6b7280',
      accentColor: '#f9fafb',
      fontFamily: 'Inter',
      layout: 'clean-bars'
    },
    sections: ['header', 'experience', 'education', 'skills', 'strengths'],
    features: ['clean-design', 'skills-bars', 'strengths-section'],
    createdAt: new Date('2024-01-28'),
    updatedAt: new Date('2024-01-28')
  },

  // Template 29: John Doe UX Designer Blue Accents
  {
    id: 'john-doe-ux-blue',
    name: 'John Doe UX Blue',
    category: 'creative',
    description: 'John Doe UX designer resume with blue accents',
    thumbnail: '/templates/thumbnails/john-doe-ux-blue.png',
    isPremium: false,
    tags: ['creative', 'ux-designer', 'blue-accents', 'modern'],
    industry: ['Design', 'Technology', 'UX'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '132:2525',
    styles: {
      primaryColor: '#2563eb',
      secondaryColor: '#3b82f6',
      accentColor: '#dbeafe',
      fontFamily: 'Inter',
      layout: 'blue-accents'
    },
    sections: ['header', 'experience', 'education', 'skills', 'projects'],
    features: ['blue-accents', 'modern-design', 'ux-focused'],
    createdAt: new Date('2024-01-29'),
    updatedAt: new Date('2024-01-29')
  },

  // Template 30: John Doe Product Designer Two-Column
  {
    id: 'john-doe-product-two-column',
    name: 'John Doe Product Two-Column',
    category: 'creative',
    description: 'John Doe product designer resume with two-column layout',
    thumbnail: '/templates/thumbnails/john-doe-product-two-column.png',
    isPremium: true,
    tags: ['creative', 'product-designer', 'two-column', 'modern'],
    industry: ['Design', 'Technology', 'Product'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '132:3637',
    styles: {
      primaryColor: '#7c3aed',
      secondaryColor: '#a78bfa',
      accentColor: '#f3f4f6',
      fontFamily: 'Inter',
      layout: 'two-column-modern'
    },
    sections: ['header', 'experience', 'education', 'skills', 'portfolio'],
    features: ['two-column-layout', 'modern-design', 'product-focused'],
    createdAt: new Date('2024-01-30'),
    updatedAt: new Date('2024-01-30')
  },

  // Template 31: John Doe UX Designer Timeline
  {
    id: 'john-doe-ux-timeline-2',
    name: 'John Doe UX Timeline 2',
    category: 'creative',
    description: 'John Doe UX Designer resume with timeline and skills sections',
    thumbnail: '/templates/thumbnails/john-doe-ux-timeline-2.png',
    isPremium: false,
    tags: ['creative', 'ux-designer', 'timeline', 'skills'],
    industry: ['Design', 'Technology', 'UX'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '132:2816',
    styles: {
      primaryColor: '#0088ff',
      secondaryColor: '#005ba2',
      accentColor: '#6683eb',
      fontFamily: 'Inter',
      layout: 'timeline-skills'
    },
    sections: ['header', 'summary', 'experience', 'education', 'skills', 'contact'],
    features: ['timeline-design', 'blue-accents', 'skills-focused'],
    createdAt: new Date('2024-01-31'),
    updatedAt: new Date('2024-01-31')
  },

  // Template 32: James Smith Experience Portfolio
  {
    id: 'james-smith-experience-portfolio',
    name: 'James Smith Experience Portfolio',
    category: 'creative',
    description: 'James Smith resume with experience, portfolio, skills, education, and certification sections',
    thumbnail: '/templates/thumbnails/james-smith-experience-portfolio.png',
    isPremium: true,
    tags: ['creative', 'portfolio', 'experience', 'certifications'],
    industry: ['Design', 'Technology', 'Creative'],
    experience: ['mid', 'senior'],
    figmaNodeId: '132:3519',
    styles: {
      primaryColor: '#393e4d',
      secondaryColor: '#404553',
      accentColor: '#f9c068',
      fontFamily: 'Euclid Circular A',
      layout: 'portfolio-experience'
    },
    sections: ['header', 'experience', 'portfolio', 'skills', 'education', 'certifications'],
    features: ['portfolio-section', 'certifications', 'modern-layout'],
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-02-01')
  },

  // Template 33: James Smith UX Designer Photo
  {
    id: 'james-smith-ux-photo',
    name: 'James Smith UX Photo',
    category: 'creative',
    description: 'James Smith UX Designer resume with photo and skills tags',
    thumbnail: '/templates/thumbnails/james-smith-ux-photo.png',
    isPremium: true,
    tags: ['creative', 'ux-designer', 'photo', 'skills-tags'],
    industry: ['Design', 'Technology', 'UX'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '132:2758',
    styles: {
      primaryColor: '#379c87',
      secondaryColor: '#333333',
      accentColor: '#e4f2ef',
      fontFamily: 'Lora',
      layout: 'photo-skills-tags'
    },
    sections: ['header', 'photo', 'experience', 'education', 'skills', 'contact'],
    features: ['photo-included', 'skills-tags', 'green-accents'],
    createdAt: new Date('2024-02-02'),
    updatedAt: new Date('2024-02-02')
  },

  // Template 34: Resume 4 - Projects Focused
  {
    id: 'resume-4-projects',
    name: 'Resume 4 Projects',
    category: 'professional',
    description: 'Projects-focused resume with detailed project descriptions, hackathons, and achievements',
    thumbnail: '/templates/thumbnails/resume-4-projects.png',
    isPremium: false,
    tags: ['professional', 'projects', 'hackathons', 'achievements'],
    industry: ['Technology', 'Software', 'Engineering'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '16410:6326',
    styles: {
      primaryColor: '#005f5f',
      secondaryColor: '#454545',
      accentColor: '#f9fafb',
      fontFamily: 'Inter',
      layout: 'projects-focused'
    },
    sections: ['header', 'projects', 'hackathons', 'achievements', 'skills'],
    features: ['projects-focused', 'hackathons-section', 'achievements-list'],
    createdAt: new Date('2024-02-03'),
    updatedAt: new Date('2024-02-03')
  },

  // Template 35: Resume 6 - Emma Harrison PM
  {
    id: 'emma-harrison-pm',
    name: 'Emma Harrison PM',
    category: 'professional',
    description: 'Project Manager resume with PM skills, education, and certifications',
    thumbnail: '/templates/thumbnails/emma-harrison-pm.png',
    isPremium: false,
    tags: ['professional', 'project-manager', 'pm-skills', 'certifications'],
    industry: ['Project Management', 'Business', 'Technology'],
    experience: ['mid', 'senior', 'executive'],
    figmaNodeId: '16410:6247',
    styles: {
      primaryColor: '#101214',
      secondaryColor: '#505050',
      accentColor: '#f9fafb',
      fontFamily: 'Inter',
      layout: 'pm-focused'
    },
    sections: ['header', 'experience', 'projects', 'skills', 'education', 'certifications'],
    features: ['pm-skills', 'project-highlights', 'certifications'],
    createdAt: new Date('2024-02-04'),
    updatedAt: new Date('2024-02-04')
  },

  // Template 36: Resume 7 - Priya Kapoor Business Analyst
  {
    id: 'priya-kapoor-analyst',
    name: 'Priya Kapoor Analyst',
    category: 'professional',
    description: 'Business Analyst resume with photo, projects, and skills sections',
    thumbnail: '/templates/thumbnails/priya-kapoor-analyst.png',
    isPremium: true,
    tags: ['professional', 'business-analyst', 'photo', 'projects'],
    industry: ['Business', 'Analytics', 'Technology'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '16410:6188',
    styles: {
      primaryColor: '#212121',
      secondaryColor: '#6b7280',
      accentColor: '#f3f4f6',
      fontFamily: 'IBM Plex Sans',
      layout: 'photo-projects'
    },
    sections: ['header', 'photo', 'profile', 'projects', 'internships', 'skills', 'education'],
    features: ['photo-included', 'projects-section', 'skills-list'],
    createdAt: new Date('2024-02-05'),
    updatedAt: new Date('2024-02-05')
  },

  // Template 37: Resume 8 - Rahul Mehta Digital Marketing
  {
    id: 'rahul-mehta-marketing',
    name: 'Rahul Mehta Marketing',
    category: 'professional',
    description: 'Digital Marketing Specialist resume with dark sidebar and skills bars',
    thumbnail: '/templates/thumbnails/rahul-mehta-marketing.png',
    isPremium: true,
    tags: ['professional', 'digital-marketing', 'dark-sidebar', 'skills-bars'],
    industry: ['Marketing', 'Digital', 'Technology'],
    experience: ['mid', 'senior'],
    figmaNodeId: '16410:6108',
    styles: {
      primaryColor: '#212121',
      secondaryColor: '#ffffff',
      accentColor: '#f3f4f6',
      fontFamily: 'IBM Plex Sans',
      layout: 'dark-sidebar'
    },
    sections: ['header', 'profile', 'education', 'employment', 'skills', 'languages'],
    features: ['dark-sidebar', 'skills-bars', 'languages-section'],
    createdAt: new Date('2024-02-06'),
    updatedAt: new Date('2024-02-06')
  },

  // Template 38: A4-3 - Robyn Kingsley Designer
  {
    id: 'robyn-kingsley-designer',
    name: 'Robyn Kingsley Designer',
    category: 'creative',
    description: 'Creative designer resume with photo, sidebar, and contact information',
    thumbnail: '/templates/thumbnails/robyn-kingsley-designer.png',
    isPremium: true,
    tags: ['creative', 'designer', 'photo', 'sidebar'],
    industry: ['Design', 'Creative', 'Technology'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '16410:5857',
    styles: {
      primaryColor: '#5c6168',
      secondaryColor: '#30353d',
      accentColor: '#e8eaee',
      fontFamily: 'Inter',
      layout: 'sidebar-photo'
    },
    sections: ['header', 'photo', 'profile', 'experience', 'education', 'skills', 'contact'],
    features: ['photo-included', 'sidebar-layout', 'contact-icons'],
    createdAt: new Date('2024-02-07'),
    updatedAt: new Date('2024-02-07')
  },

  // Template 39: Resume 10 - Aarav Mehta Frontend Developer
  {
    id: 'aarav-mehta-frontend',
    name: 'Aarav Mehta Frontend',
    category: 'professional',
    description: 'Frontend Developer resume with photo, skills, and projects sections',
    thumbnail: '/templates/thumbnails/aarav-mehta-frontend.png',
    isPremium: false,
    tags: ['professional', 'frontend-developer', 'photo', 'projects'],
    industry: ['Technology', 'Software', 'Frontend'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '16410:5803',
    styles: {
      primaryColor: '#73808d',
      secondaryColor: '#101214',
      accentColor: '#f9fafb',
      fontFamily: 'Arial',
      layout: 'photo-skills'
    },
    sections: ['header', 'photo', 'contacts', 'experience', 'education', 'summary', 'projects', 'skills'],
    features: ['photo-included', 'skills-section', 'projects-list'],
    createdAt: new Date('2024-02-08'),
    updatedAt: new Date('2024-02-08')
  },

  // Template 40: Resume 11 - Rukia Sharma
  {
    id: 'rukia-sharma-resume',
    name: 'Rukia Sharma Resume',
    category: 'professional',
    description: 'Professional resume with social links, education, experience, and awards',
    thumbnail: '/templates/thumbnails/rukia-sharma-resume.png',
    isPremium: true,
    tags: ['professional', 'social-links', 'awards', 'certifications'],
    industry: ['Technology', 'Business', 'General'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '16410:5639',
    styles: {
      primaryColor: '#414042',
      secondaryColor: '#ffffff',
      accentColor: '#f3f4f6',
      fontFamily: 'Rubik',
      layout: 'social-awards'
    },
    sections: ['header', 'profile', 'contact', 'education', 'experience', 'certifications', 'skills', 'awards', 'social'],
    features: ['social-links', 'awards-section', 'certifications'],
    createdAt: new Date('2024-02-09'),
    updatedAt: new Date('2024-02-09')
  },

  // Template 41: Resume - Jone Don
  {
    id: 'jone-don-resume',
    name: 'Jone Don Resume',
    category: 'professional',
    description: 'Clean professional resume with blue accents and structured sections',
    thumbnail: '/templates/thumbnails/jone-don-resume.png',
    isPremium: false,
    tags: ['professional', 'clean', 'blue-accents', 'structured'],
    industry: ['Business', 'Technology', 'General'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '16410:5546',
    styles: {
      primaryColor: '#001acc',
      secondaryColor: '#101214',
      accentColor: '#f9fafb',
      fontFamily: 'Avenir',
      layout: 'clean-structured'
    },
    sections: ['header', 'profile', 'experience', 'education', 'projects', 'references', 'skills', 'certificates', 'languages'],
    features: ['blue-accents', 'clean-design', 'structured-layout'],
    createdAt: new Date('2024-02-10'),
    updatedAt: new Date('2024-02-10')
  },

  // Template 42: Classic001Template
  {
    id: 'classic-001-template',
    name: 'Classic 001',
    category: 'professional',
    description: 'Classic professional resume with traditional layout and clean typography',
    thumbnail: '/templates/thumbnails/classic-001-template.png',
    isPremium: false,
    tags: ['professional', 'classic', 'traditional', 'clean'],
    industry: ['Business', 'Finance', 'Legal', 'General'],
    experience: ['entry', 'mid', 'senior', 'executive'],
    figmaNodeId: '',
    styles: {
      primaryColor: '#000000',
      secondaryColor: '#666666',
      accentColor: '#f9fafb',
      fontFamily: 'Inter',
      layout: 'classic-traditional'
    },
    sections: ['header', 'summary', 'experience', 'education', 'skills', 'certifications'],
    features: ['classic-design', 'traditional-layout', 'clean-typography'],
    createdAt: new Date('2024-02-11'),
    updatedAt: new Date('2024-02-11')
  },

  // Template 43: CleanTemplate
  {
    id: 'clean-template',
    name: 'Clean Template',
    category: 'professional',
    description: 'Minimalist clean design with excellent readability and modern styling',
    thumbnail: '/templates/thumbnails/clean-template.png',
    isPremium: false,
    tags: ['professional', 'clean', 'minimalist', 'modern'],
    industry: ['Technology', 'Design', 'Business', 'General'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '',
    styles: {
      primaryColor: '#1f2937',
      secondaryColor: '#6b7280',
      accentColor: '#f9fafb',
      fontFamily: 'Inter',
      layout: 'clean-minimal'
    },
    sections: ['header', 'summary', 'experience', 'education', 'skills'],
    features: ['clean-design', 'minimalist-layout', 'excellent-readability'],
    createdAt: new Date('2024-02-11'),
    updatedAt: new Date('2024-02-11')
  },

  // Template 44: CorporateTemplate
  {
    id: 'corporate-template',
    name: 'Corporate Template',
    category: 'professional',
    description: 'Professional corporate design suitable for business and executive roles',
    thumbnail: '/templates/thumbnails/corporate-template.png',
    isPremium: true,
    tags: ['professional', 'corporate', 'executive', 'business'],
    industry: ['Business', 'Finance', 'Consulting', 'Management'],
    experience: ['mid', 'senior', 'executive'],
    figmaNodeId: '',
    styles: {
      primaryColor: '#1e40af',
      secondaryColor: '#374151',
      accentColor: '#f3f4f6',
      fontFamily: 'Inter',
      layout: 'corporate-executive'
    },
    sections: ['header', 'summary', 'experience', 'education', 'skills', 'achievements'],
    features: ['corporate-design', 'executive-layout', 'professional-styling'],
    createdAt: new Date('2024-02-11'),
    updatedAt: new Date('2024-02-11')
  },

  // Template 45: CreativeTemplate
  {
    id: 'creative-template',
    name: 'Creative Template',
    category: 'creative',
    description: 'Creative design template perfect for designers and creative professionals',
    thumbnail: '/templates/thumbnails/creative-template.png',
    isPremium: true,
    tags: ['creative', 'design', 'artistic', 'portfolio'],
    industry: ['Design', 'Creative', 'Marketing', 'Media'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '',
    styles: {
      primaryColor: '#7c3aed',
      secondaryColor: '#4b5563',
      accentColor: '#f3f4f6',
      fontFamily: 'Inter',
      layout: 'creative-portfolio'
    },
    sections: ['header', 'portfolio', 'experience', 'skills', 'education', 'projects'],
    features: ['creative-design', 'portfolio-focus', 'artistic-layout'],
    createdAt: new Date('2024-02-11'),
    updatedAt: new Date('2024-02-11')
  },

  // Template 46: DynamicTemplate
  {
    id: 'dynamic-template',
    name: 'Dynamic Template',
    category: 'professional',
    description: 'Dynamic and energetic design with modern visual elements',
    thumbnail: '/templates/thumbnails/dynamic-template.png',
    isPremium: false,
    tags: ['professional', 'dynamic', 'modern', 'energetic'],
    industry: ['Technology', 'Startup', 'Marketing', 'Sales'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '',
    styles: {
      primaryColor: '#059669',
      secondaryColor: '#374151',
      accentColor: '#f0fdf4',
      fontFamily: 'Inter',
      layout: 'dynamic-modern'
    },
    sections: ['header', 'summary', 'experience', 'skills', 'education'],
    features: ['dynamic-design', 'modern-elements', 'energetic-styling'],
    createdAt: new Date('2024-02-11'),
    updatedAt: new Date('2024-02-11')
  },

  // Template 47: ElegantTemplate
  {
    id: 'elegant-template',
    name: 'Elegant Template',
    category: 'professional',
    description: 'Elegant and sophisticated design with purple accents and clean layout',
    thumbnail: '/templates/thumbnails/elegant-template.png',
    isPremium: true,
    tags: ['professional', 'elegant', 'sophisticated', 'purple-accents'],
    industry: ['Design', 'Business', 'Consulting', 'Technology'],
    experience: ['mid', 'senior', 'executive'],
    figmaNodeId: '',
    styles: {
      primaryColor: '#7c3aed',
      secondaryColor: '#374151',
      accentColor: '#f9fafb',
      fontFamily: 'Inter',
      layout: 'elegant-sophisticated'
    },
    sections: ['header', 'summary', 'experience', 'education', 'skills', 'contact'],
    features: ['elegant-design', 'purple-accents', 'sophisticated-layout'],
    createdAt: new Date('2024-02-11'),
    updatedAt: new Date('2024-02-11')
  },

  // Template 48: EssentialTemplate
  {
    id: 'essential-template',
    name: 'Essential Template',
    category: 'professional',
    description: 'Essential professional template with all core resume sections',
    thumbnail: '/templates/thumbnails/essential-template.png',
    isPremium: false,
    tags: ['professional', 'essential', 'comprehensive', 'standard'],
    industry: ['General', 'Business', 'Technology', 'Healthcare'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '',
    styles: {
      primaryColor: '#1f2937',
      secondaryColor: '#6b7280',
      accentColor: '#f9fafb',
      fontFamily: 'Inter',
      layout: 'essential-standard'
    },
    sections: ['header', 'summary', 'experience', 'education', 'skills', 'certifications'],
    features: ['essential-sections', 'comprehensive-layout', 'standard-design'],
    createdAt: new Date('2024-02-11'),
    updatedAt: new Date('2024-02-11')
  },

  // Template 49: ExecutiveBlueTemplate
  {
    id: 'executive-blue-template',
    name: 'Executive Blue',
    category: 'professional',
    description: 'Executive-level template with professional blue color scheme',
    thumbnail: '/templates/thumbnails/executive-blue-template.png',
    isPremium: true,
    tags: ['professional', 'executive', 'blue-theme', 'leadership'],
    industry: ['Business', 'Finance', 'Consulting', 'Management'],
    experience: ['senior', 'executive'],
    figmaNodeId: '',
    styles: {
      primaryColor: '#1e40af',
      secondaryColor: '#374151',
      accentColor: '#eff6ff',
      fontFamily: 'Inter',
      layout: 'executive-blue'
    },
    sections: ['header', 'executive-summary', 'leadership', 'experience', 'education', 'achievements'],
    features: ['executive-design', 'blue-theme', 'leadership-focus'],
    createdAt: new Date('2024-02-11'),
    updatedAt: new Date('2024-02-11')
  },

  // Template 50: ExecutiveTemplate
  {
    id: 'executive-template',
    name: 'Executive Template',
    category: 'professional',
    description: 'Premium executive template for C-level and senior management positions',
    thumbnail: '/templates/thumbnails/executive-template.png',
    isPremium: true,
    tags: ['professional', 'executive', 'c-level', 'premium'],
    industry: ['Business', 'Finance', 'Technology', 'Healthcare'],
    experience: ['senior', 'executive'],
    figmaNodeId: '',
    styles: {
      primaryColor: '#000000',
      secondaryColor: '#374151',
      accentColor: '#f9fafb',
      fontFamily: 'Inter',
      layout: 'executive-premium'
    },
    sections: ['header', 'executive-summary', 'leadership', 'experience', 'board-positions', 'education'],
    features: ['executive-design', 'c-level-focus', 'premium-layout'],
    createdAt: new Date('2024-02-11'),
    updatedAt: new Date('2024-02-11')
  },

  // Template 51: Fresh001Template
  {
    id: 'fresh-001-template',
    name: 'Fresh 001',
    category: 'professional',
    description: 'Fresh and modern design with contemporary styling elements',
    thumbnail: '/templates/thumbnails/fresh-001-template.png',
    isPremium: false,
    tags: ['professional', 'fresh', 'modern', 'contemporary'],
    industry: ['Technology', 'Startup', 'Design', 'Marketing'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '',
    styles: {
      primaryColor: '#10b981',
      secondaryColor: '#374151',
      accentColor: '#f0fdf4',
      fontFamily: 'Inter',
      layout: 'fresh-modern'
    },
    sections: ['header', 'summary', 'experience', 'skills', 'education', 'projects'],
    features: ['fresh-design', 'modern-styling', 'contemporary-layout'],
    createdAt: new Date('2024-02-11'),
    updatedAt: new Date('2024-02-11')
  },

  // Template 52: FreshTemplate
  {
    id: 'fresh-template',
    name: 'Fresh Template',
    category: 'professional',
    description: 'Fresh and vibrant design perfect for young professionals',
    thumbnail: '/templates/thumbnails/fresh-template.png',
    isPremium: false,
    tags: ['professional', 'fresh', 'vibrant', 'young-professional'],
    industry: ['Technology', 'Startup', 'Marketing', 'Design'],
    experience: ['entry', 'mid'],
    figmaNodeId: '',
    styles: {
      primaryColor: '#06b6d4',
      secondaryColor: '#374151',
      accentColor: '#f0f9ff',
      fontFamily: 'Inter',
      layout: 'fresh-vibrant'
    },
    sections: ['header', 'summary', 'experience', 'skills', 'education'],
    features: ['fresh-design', 'vibrant-colors', 'young-professional-focus'],
    createdAt: new Date('2024-02-11'),
    updatedAt: new Date('2024-02-11')
  },

  // Template 53: MinimalTemplate
  {
    id: 'minimal-template',
    name: 'Minimal Template',
    category: 'professional',
    description: 'Ultra-minimal design focusing on content and readability',
    thumbnail: '/templates/thumbnails/minimal-template.png',
    isPremium: false,
    tags: ['professional', 'minimal', 'clean', 'content-focused'],
    industry: ['Technology', 'Design', 'Writing', 'Research'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '',
    styles: {
      primaryColor: '#000000',
      secondaryColor: '#6b7280',
      accentColor: '#ffffff',
      fontFamily: 'Inter',
      layout: 'ultra-minimal'
    },
    sections: ['header', 'summary', 'experience', 'education', 'skills'],
    features: ['minimal-design', 'content-focused', 'ultra-clean'],
    createdAt: new Date('2024-02-11'),
    updatedAt: new Date('2024-02-11')
  },

  // Template 54: ModernTealTemplate
  {
    id: 'modern-teal-template',
    name: 'Modern Teal',
    category: 'professional',
    description: 'Modern design with teal accents and contemporary layout',
    thumbnail: '/templates/thumbnails/modern-teal-template.png',
    isPremium: true,
    tags: ['professional', 'modern', 'teal-accents', 'contemporary'],
    industry: ['Technology', 'Healthcare', 'Business', 'Design'],
    experience: ['mid', 'senior'],
    figmaNodeId: '',
    styles: {
      primaryColor: '#0d9488',
      secondaryColor: '#374151',
      accentColor: '#f0fdfa',
      fontFamily: 'Inter',
      layout: 'modern-teal'
    },
    sections: ['header', 'summary', 'experience', 'skills', 'education', 'certifications'],
    features: ['modern-design', 'teal-accents', 'contemporary-layout'],
    createdAt: new Date('2024-02-11'),
    updatedAt: new Date('2024-02-11')
  },

  // Template 55: ModernTemplate
  {
    id: 'modern-template',
    name: 'Modern Template',
    category: 'professional',
    description: 'Modern professional template with clean lines and contemporary styling',
    thumbnail: '/templates/thumbnails/modern-template.png',
    isPremium: false,
    tags: ['professional', 'modern', 'clean-lines', 'contemporary'],
    industry: ['Technology', 'Business', 'Design', 'General'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '',
    styles: {
      primaryColor: '#1f2937',
      secondaryColor: '#6b7280',
      accentColor: '#f9fafb',
      fontFamily: 'Inter',
      layout: 'modern-professional'
    },
    sections: ['header', 'summary', 'experience', 'skills', 'education'],
    features: ['modern-design', 'clean-lines', 'professional-styling'],
    createdAt: new Date('2024-02-11'),
    updatedAt: new Date('2024-02-11')
  },

  // Template 56: PlaceholderTemplate
  {
    id: 'placeholder-template',
    name: 'Placeholder Template',
    category: 'professional',
    description: 'Versatile template with placeholder content for easy customization',
    thumbnail: '/templates/thumbnails/placeholder-template.png',
    isPremium: false,
    tags: ['professional', 'versatile', 'customizable', 'placeholder'],
    industry: ['General', 'Business', 'Technology', 'Any'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '',
    styles: {
      primaryColor: '#374151',
      secondaryColor: '#6b7280',
      accentColor: '#f9fafb',
      fontFamily: 'Inter',
      layout: 'versatile-placeholder'
    },
    sections: ['header', 'summary', 'experience', 'education', 'skills'],
    features: ['versatile-design', 'easy-customization', 'placeholder-content'],
    createdAt: new Date('2024-02-11'),
    updatedAt: new Date('2024-02-11')
  },

  // Template 57: ProfessionalTemplate
  {
    id: 'professional-template',
    name: 'Professional Template',
    category: 'professional',
    description: 'Classic professional template suitable for all industries and experience levels',
    thumbnail: '/templates/thumbnails/professional-template.png',
    isPremium: false,
    tags: ['professional', 'classic', 'versatile', 'industry-standard'],
    industry: ['Business', 'Finance', 'Technology', 'Healthcare', 'General'],
    experience: ['entry', 'mid', 'senior', 'executive'],
    figmaNodeId: '',
    styles: {
      primaryColor: '#1f2937',
      secondaryColor: '#374151',
      accentColor: '#f9fafb',
      fontFamily: 'Inter',
      layout: 'professional-standard'
    },
    sections: ['header', 'summary', 'experience', 'education', 'skills', 'certifications'],
    features: ['professional-design', 'industry-standard', 'versatile-layout'],
    createdAt: new Date('2024-02-11'),
    updatedAt: new Date('2024-02-11')
  },

  // Template 58: PureTemplate
  {
    id: 'pure-template',
    name: 'Pure Template',
    category: 'professional',
    description: 'Pure and simple design with focus on clarity and readability',
    thumbnail: '/templates/thumbnails/pure-template.png',
    isPremium: false,
    tags: ['professional', 'pure', 'simple', 'clarity'],
    industry: ['Technology', 'Research', 'Academia', 'Writing'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '',
    styles: {
      primaryColor: '#000000',
      secondaryColor: '#6b7280',
      accentColor: '#ffffff',
      fontFamily: 'Inter',
      layout: 'pure-simple'
    },
    sections: ['header', 'summary', 'experience', 'education', 'skills'],
    features: ['pure-design', 'simple-layout', 'clarity-focused'],
    createdAt: new Date('2024-02-11'),
    updatedAt: new Date('2024-02-11')
  },

  // Template 59: SimpleTemplate
  {
    id: 'simple-template',
    name: 'Simple Template',
    category: 'professional',
    description: 'Simple and straightforward template for quick resume creation',
    thumbnail: '/templates/thumbnails/simple-template.png',
    isPremium: false,
    tags: ['professional', 'simple', 'straightforward', 'quick'],
    industry: ['General', 'Business', 'Technology', 'Any'],
    experience: ['entry', 'mid', 'senior'],
    figmaNodeId: '',
    styles: {
      primaryColor: '#374151',
      secondaryColor: '#6b7280',
      accentColor: '#f9fafb',
      fontFamily: 'Inter',
      layout: 'simple-straightforward'
    },
    sections: ['header', 'summary', 'experience', 'education', 'skills'],
    features: ['simple-design', 'straightforward-layout', 'quick-creation'],
    createdAt: new Date('2024-02-11'),
    updatedAt: new Date('2024-02-11')
  },

  // Template 60: TechnicalBlueTemplate
  {
    id: 'technical-blue-template',
    name: 'Technical Blue',
    category: 'professional',
    description: 'Technical-focused template with blue accents for IT and engineering professionals',
    thumbnail: '/templates/thumbnails/technical-blue-template.png',
    isPremium: true,
    tags: ['professional', 'technical', 'blue-accents', 'engineering'],
    industry: ['Technology', 'Engineering', 'IT', 'Software'],
    experience: ['mid', 'senior'],
    figmaNodeId: '',
    styles: {
      primaryColor: '#1e40af',
      secondaryColor: '#374151',
      accentColor: '#eff6ff',
      fontFamily: 'Inter',
      layout: 'technical-blue'
    },
    sections: ['header', 'summary', 'technical-skills', 'experience', 'projects', 'education'],
    features: ['technical-focus', 'blue-accents', 'engineering-layout'],
    createdAt: new Date('2024-02-11'),
    updatedAt: new Date('2024-02-11')
  }
];

// Template categories for filtering
export const TEMPLATE_CATEGORIES = [
  { id: 'all', name: 'All Templates', count: TEMPLATE_CATALOG.length },
  { id: 'professional', name: 'Professional', count: TEMPLATE_CATALOG.filter(t => t.category === 'professional').length },
  { id: 'modern', name: 'Modern', count: TEMPLATE_CATALOG.filter(t => t.category === 'modern').length },
  { id: 'creative', name: 'Creative', count: TEMPLATE_CATALOG.filter(t => t.category === 'creative').length },
  { id: 'minimalist', name: 'Minimalist', count: TEMPLATE_CATALOG.filter(t => t.category === 'minimalist').length },
  { id: 'executive', name: 'Executive', count: TEMPLATE_CATALOG.filter(t => t.category === 'executive').length },
  { id: 'academic', name: 'Academic', count: TEMPLATE_CATALOG.filter(t => t.category === 'academic').length }
];

// Helper functions
export function getTemplateById(id: string): TemplateMetadata | undefined {
  return TEMPLATE_CATALOG.find(template => template.id === id);
}

export function getTemplatesByCategory(category: string): TemplateMetadata[] {
  if (category === 'all') return TEMPLATE_CATALOG;
  return TEMPLATE_CATALOG.filter(template => template.category === category);
}

export function getTemplatesByTags(tags: string[]): TemplateMetadata[] {
  return TEMPLATE_CATALOG.filter(template => 
    tags.some(tag => template.tags.includes(tag))
  );
}

export function searchTemplates(query: string): TemplateMetadata[] {
  const lowercaseQuery = query.toLowerCase();
  return TEMPLATE_CATALOG.filter(template => 
    template.name.toLowerCase().includes(lowercaseQuery) ||
    template.description.toLowerCase().includes(lowercaseQuery) ||
    template.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  );
}
