// Template switching functionality for CVLeap
// Handles template changes while preserving user data

import { ResumeData, validateTemplateData, templateRegistry } from './template-registry';
import { TemplateMetadata } from './template-catalog';

export interface TemplateSwitchResult {
  success: boolean;
  warnings: string[];
  errors: string[];
  adaptedData?: ResumeData;
}

export interface FieldMapping {
  sourceField: string;
  targetField: string;
  transform?: (value: any) => any;
}

// Field mappings between different template data structures
const FIELD_MAPPINGS: Record<string, FieldMapping[]> = {
  // Professional Sidebar Template mappings
  'professional-sidebar-1': [
    { sourceField: 'summary', targetField: 'profile' },
    { sourceField: 'personalInfo.location', targetField: 'personalInfo.location' },
    { sourceField: 'education.institution', targetField: 'education.school' },
    { sourceField: 'education.dates', targetField: 'education.year' }
  ],
  
  // Modern Photo CV Template mappings
  'modern-photo-cv': [
    { sourceField: 'profile', targetField: 'summary' },
    { sourceField: 'personalInfo.birthdate', targetField: 'personalInfo.birthdate' },
    { sourceField: 'education.school', targetField: 'education.institution' },
    { sourceField: 'education.year', targetField: 'education.dates' }
  ],
  
  // Clean Professional Template mappings
  'clean-professional-3': [
    { sourceField: 'profile', targetField: 'summary' },
    { sourceField: 'education.school', targetField: 'education.institution' },
    { sourceField: 'education.year', targetField: 'education.dates' }
  ]
};

export class TemplateSwitcher {
  private static instance: TemplateSwitcher;

  private constructor() {}

  public static getInstance(): TemplateSwitcher {
    if (!TemplateSwitcher.instance) {
      TemplateSwitcher.instance = new TemplateSwitcher();
    }
    return TemplateSwitcher.instance;
  }

  /**
   * Switch from one template to another while preserving user data
   */
  public switchTemplate(
    currentData: ResumeData,
    fromTemplateId: string,
    toTemplateId: string
  ): TemplateSwitchResult {
    const result: TemplateSwitchResult = {
      success: false,
      warnings: [],
      errors: []
    };

    try {
      // Get template metadata
      const fromTemplate = templateRegistry.getTemplate(fromTemplateId);
      const toTemplate = templateRegistry.getTemplate(toTemplateId);

      if (!fromTemplate || !toTemplate) {
        result.errors.push('Template not found');
        return result;
      }

      // Adapt data structure for the new template
      const adaptedData = this.adaptDataStructure(
        currentData,
        fromTemplate.metadata,
        toTemplate.metadata
      );

      // Validate adapted data against new template requirements
      const validationResult = this.validateAdaptedData(adaptedData, toTemplate.metadata);
      result.warnings.push(...validationResult.warnings);
      result.errors.push(...validationResult.errors);

      // Check for data loss warnings
      const dataLossWarnings = this.checkForDataLoss(
        currentData,
        adaptedData,
        fromTemplate.metadata,
        toTemplate.metadata
      );
      result.warnings.push(...dataLossWarnings);

      result.adaptedData = adaptedData;
      result.success = result.errors.length === 0;

      return result;
    } catch (error) {
      result.errors.push(`Template switching failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  /**
   * Adapt data structure from one template format to another
   */
  private adaptDataStructure(
    data: ResumeData,
    fromMetadata: TemplateMetadata | undefined,
    toMetadata: TemplateMetadata | undefined
  ): ResumeData {
    if (!fromMetadata || !toMetadata) {
      return { ...data };
    }

    const adaptedData: ResumeData = JSON.parse(JSON.stringify(data)); // Deep clone

    // Apply field mappings for the target template
    const mappings = FIELD_MAPPINGS[toMetadata.id] || [];
    
    for (const mapping of mappings) {
      const sourceValue = this.getNestedValue(data, mapping.sourceField);
      if (sourceValue !== undefined) {
        const transformedValue = mapping.transform ? mapping.transform(sourceValue) : sourceValue;
        this.setNestedValue(adaptedData, mapping.targetField, transformedValue);
      }
    }

    // Handle template-specific adaptations
    this.applyTemplateSpecificAdaptations(adaptedData, fromMetadata, toMetadata);

    return adaptedData;
  }

  /**
   * Apply template-specific data adaptations
   */
  private applyTemplateSpecificAdaptations(
    data: ResumeData,
    fromMetadata: TemplateMetadata,
    toMetadata: TemplateMetadata
  ): void {
    // Handle photo requirements
    if (toMetadata.hasPhoto && !fromMetadata.hasPhoto && !data.personalInfo.photo) {
      // Template requires photo but current data doesn't have one
      // This will be flagged as a warning
    }

    // Handle section requirements
    if (toMetadata.sections.includes('summary') && !data.summary && data.profile) {
      data.summary = data.profile;
    }

    if (toMetadata.sections.includes('profile') && !data.profile && data.summary) {
      data.profile = data.summary;
    }

    // Handle layout-specific adaptations
    if (toMetadata.layout === 'sidebar-left' || toMetadata.layout === 'sidebar-right') {
      // Ensure contact info is properly formatted for sidebar layouts
      this.optimizeForSidebarLayout(data);
    }

    // Handle color scheme adaptations
    if (toMetadata.colorScheme.primary !== fromMetadata.colorScheme.primary) {
      // Template has different color scheme - no data changes needed
      // but could be used for theme-specific optimizations
    }
  }

  /**
   * Optimize data for sidebar layouts
   */
  private optimizeForSidebarLayout(data: ResumeData): void {
    // Ensure skills are in a format suitable for sidebar display
    if (data.skills && data.skills.length > 10) {
      // Consider grouping or limiting skills for sidebar display
      // This is a UX consideration rather than data loss
    }

    // Ensure contact info is complete for sidebar display
    if (!data.personalInfo.phone || !data.personalInfo.email) {
      // Flag as warning - sidebar templates typically need complete contact info
    }
  }

  /**
   * Validate adapted data against template requirements
   */
  private validateAdaptedData(
    data: ResumeData,
    metadata: TemplateMetadata | undefined
  ): { warnings: string[]; errors: string[] } {
    const warnings: string[] = [];
    const errors: string[] = [];

    if (!metadata) {
      errors.push('Template metadata not available');
      return { warnings, errors };
    }

    // Check required sections
    for (const section of metadata.sections) {
      switch (section) {
        case 'contact':
          if (!data.personalInfo.name) errors.push('Name is required');
          if (!data.personalInfo.email) errors.push('Email is required');
          break;
        case 'experience':
          if (!data.experience || data.experience.length === 0) {
            warnings.push('No work experience provided');
          }
          break;
        case 'education':
          if (!data.education || data.education.length === 0) {
            warnings.push('No education information provided');
          }
          break;
        case 'skills':
          if (!data.skills || data.skills.length === 0) {
            warnings.push('No skills listed');
          }
          break;
        case 'profile-picture':
          if (metadata.hasPhoto && !data.personalInfo.photo) {
            warnings.push('This template works best with a profile photo');
          }
          break;
        case 'summary':
        case 'profile':
          if (!data.summary && !data.profile) {
            warnings.push('Professional summary recommended for this template');
          }
          break;
      }
    }

    return { warnings, errors };
  }

  /**
   * Check for potential data loss when switching templates
   */
  private checkForDataLoss(
    originalData: ResumeData,
    adaptedData: ResumeData,
    fromMetadata: TemplateMetadata | undefined,
    toMetadata: TemplateMetadata | undefined
  ): string[] {
    const warnings: string[] = [];

    if (!fromMetadata || !toMetadata) return warnings;

    // Check if new template supports all sections from original template
    const fromSections = fromMetadata.sections;
    const toSections = toMetadata.sections;

    const unsupportedSections = fromSections.filter(section => !toSections.includes(section));
    
    for (const section of unsupportedSections) {
      switch (section) {
        case 'certifications':
          if (originalData.certifications && originalData.certifications.length > 0) {
            warnings.push('Certifications section not supported in new template');
          }
          break;
        case 'awards':
          if (originalData.awards && originalData.awards.length > 0) {
            warnings.push('Awards section not supported in new template');
          }
          break;
        case 'volunteer':
          if (originalData.volunteer && originalData.volunteer.length > 0) {
            warnings.push('Volunteer experience section not supported in new template');
          }
          break;
        case 'projects':
          if (originalData.projects && originalData.projects.length > 0) {
            warnings.push('Projects section not supported in new template');
          }
          break;
        case 'hobbies':
          if (originalData.hobbies && originalData.hobbies.length > 0) {
            warnings.push('Hobbies section not supported in new template');
          }
          break;
      }
    }

    // Check for photo requirements
    if (!fromMetadata.hasPhoto && toMetadata.hasPhoto && !originalData.personalInfo.photo) {
      warnings.push('New template is designed for use with a profile photo');
    }

    return warnings;
  }

  /**
   * Get nested object value using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      if (current && typeof current === 'object' && key in current) {
        return current[key];
      }
      return undefined;
    }, obj);
  }

  /**
   * Set nested object value using dot notation
   */
  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop();
    
    if (!lastKey) return;

    const target = keys.reduce((current, key) => {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      return current[key];
    }, obj);

    target[lastKey] = value;
  }
}

// Export singleton instance
export const templateSwitcher = TemplateSwitcher.getInstance();

// Helper functions
export function switchTemplate(
  currentData: ResumeData,
  fromTemplateId: string,
  toTemplateId: string
): TemplateSwitchResult {
  return templateSwitcher.switchTemplate(currentData, fromTemplateId, toTemplateId);
}

export function canSwitchTemplate(
  data: ResumeData,
  fromTemplateId: string,
  toTemplateId: string
): boolean {
  const result = switchTemplate(data, fromTemplateId, toTemplateId);
  return result.success;
}
