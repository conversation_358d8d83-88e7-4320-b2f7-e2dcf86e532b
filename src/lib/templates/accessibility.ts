// Accessibility utilities for resume templates
// Ensures templates are screen reader friendly and WCAG compliant

import { ResumeData } from './template-registry';

export interface AccessibilityOptions {
  includeAriaLabels: boolean;
  includeSkipLinks: boolean;
  highContrast: boolean;
  largeText: boolean;
  screenReaderOptimized: boolean;
}

export interface AccessibilityReport {
  score: number; // 0-100
  issues: AccessibilityIssue[];
  recommendations: string[];
  wcagLevel: 'A' | 'AA' | 'AAA' | 'FAIL';
}

export interface AccessibilityIssue {
  type: 'error' | 'warning' | 'info';
  category: 'color' | 'structure' | 'navigation' | 'content' | 'images';
  message: string;
  element?: string;
  fix?: string;
}

export class TemplateAccessibilityChecker {
  private static instance: TemplateAccessibilityChecker;

  private constructor() {}

  public static getInstance(): TemplateAccessibilityChecker {
    if (!TemplateAccessibilityChecker.instance) {
      TemplateAccessibilityChecker.instance = new TemplateAccessibilityChecker();
    }
    return TemplateAccessibilityChecker.instance;
  }

  /**
   * Check template accessibility compliance
   */
  public checkTemplate(
    templateId: string,
    resumeData: ResumeData,
    options: Partial<AccessibilityOptions> = {}
  ): AccessibilityReport {
    const defaultOptions: AccessibilityOptions = {
      includeAriaLabels: true,
      includeSkipLinks: false,
      highContrast: false,
      largeText: false,
      screenReaderOptimized: true,
      ...options
    };

    const issues: AccessibilityIssue[] = [];
    let score = 100;

    // Check color contrast
    const colorIssues = this.checkColorContrast(templateId);
    issues.push(...colorIssues);
    score -= colorIssues.length * 10;

    // Check content structure
    const structureIssues = this.checkContentStructure(resumeData);
    issues.push(...structureIssues);
    score -= structureIssues.length * 5;

    // Check navigation
    const navigationIssues = this.checkNavigation(templateId);
    issues.push(...navigationIssues);
    score -= navigationIssues.length * 8;

    // Check images and media
    const imageIssues = this.checkImages(resumeData);
    issues.push(...imageIssues);
    score -= imageIssues.length * 7;

    // Check text content
    const textIssues = this.checkTextContent(resumeData);
    issues.push(...textIssues);
    score -= textIssues.length * 3;

    // Ensure score doesn't go below 0
    score = Math.max(0, score);

    // Determine WCAG level
    let wcagLevel: 'A' | 'AA' | 'AAA' | 'FAIL';
    if (score >= 95) wcagLevel = 'AAA';
    else if (score >= 85) wcagLevel = 'AA';
    else if (score >= 70) wcagLevel = 'A';
    else wcagLevel = 'FAIL';

    // Generate recommendations
    const recommendations = this.generateRecommendations(issues, defaultOptions);

    return {
      score,
      issues,
      recommendations,
      wcagLevel
    };
  }

  /**
   * Check color contrast ratios
   */
  private checkColorContrast(templateId: string): AccessibilityIssue[] {
    const issues: AccessibilityIssue[] = [];

    // This would typically analyze the template's color scheme
    // For now, we'll provide general guidance based on template ID
    const commonIssues = [
      {
        type: 'warning' as const,
        category: 'color' as const,
        message: 'Ensure text has sufficient contrast ratio (4.5:1 for normal text, 3:1 for large text)',
        fix: 'Use darker text colors or lighter backgrounds to improve contrast'
      }
    ];

    // Template-specific checks
    if (templateId.includes('creative') || templateId.includes('colorful')) {
      issues.push({
        type: 'warning',
        category: 'color',
        message: 'Creative templates may have contrast issues with colored backgrounds',
        fix: 'Test all text/background combinations with a contrast checker'
      });
    }

    return issues;
  }

  /**
   * Check content structure and semantic markup
   */
  private checkContentStructure(resumeData: ResumeData): AccessibilityIssue[] {
    const issues: AccessibilityIssue[] = [];

    // Check for proper heading hierarchy
    if (!resumeData.personalInfo.name) {
      issues.push({
        type: 'error',
        category: 'structure',
        message: 'Missing main heading (name)',
        fix: 'Ensure the resume has a clear main heading with the person\'s name'
      });
    }

    // Check for section structure
    const sections = ['experience', 'education', 'skills'];
    sections.forEach(section => {
      if (!resumeData[section as keyof ResumeData] || 
          (Array.isArray(resumeData[section as keyof ResumeData]) && 
           (resumeData[section as keyof ResumeData] as any[]).length === 0)) {
        issues.push({
          type: 'warning',
          category: 'structure',
          message: `Missing or empty ${section} section`,
          fix: `Add content to the ${section} section or remove it entirely`
        });
      }
    });

    return issues;
  }

  /**
   * Check navigation and focus management
   */
  private checkNavigation(templateId: string): AccessibilityIssue[] {
    const issues: AccessibilityIssue[] = [];

    // General navigation recommendations
    issues.push({
      type: 'info',
      category: 'navigation',
      message: 'Ensure all interactive elements are keyboard accessible',
      fix: 'Add proper tabindex and focus styles to interactive elements'
    });

    return issues;
  }

  /**
   * Check images and alternative text
   */
  private checkImages(resumeData: ResumeData): AccessibilityIssue[] {
    const issues: AccessibilityIssue[] = [];

    // Check profile photo
    if (resumeData.personalInfo.photo) {
      issues.push({
        type: 'info',
        category: 'images',
        message: 'Profile photo should have appropriate alt text',
        element: 'profile-photo',
        fix: 'Add alt text like "Professional headshot of [Name]" or make decorative if not essential'
      });
    }

    // Check for any other images in content
    const allContent = JSON.stringify(resumeData);
    if (allContent.includes('http') && (allContent.includes('.jpg') || allContent.includes('.png'))) {
      issues.push({
        type: 'warning',
        category: 'images',
        message: 'Images detected in content - ensure they have alt text',
        fix: 'Add descriptive alt text for all images or mark as decorative'
      });
    }

    return issues;
  }

  /**
   * Check text content for accessibility
   */
  private checkTextContent(resumeData: ResumeData): AccessibilityIssue[] {
    const issues: AccessibilityIssue[] = [];

    // Check for very long text blocks
    if (resumeData.summary && resumeData.summary.length > 500) {
      issues.push({
        type: 'info',
        category: 'content',
        message: 'Very long summary text may be difficult to read',
        fix: 'Consider breaking long text into shorter paragraphs or bullet points'
      });
    }

    // Check for missing essential information
    if (!resumeData.personalInfo.email && !resumeData.personalInfo.phone) {
      issues.push({
        type: 'warning',
        category: 'content',
        message: 'Missing contact information',
        fix: 'Include at least one form of contact information (email or phone)'
      });
    }

    return issues;
  }

  /**
   * Generate accessibility recommendations
   */
  private generateRecommendations(
    issues: AccessibilityIssue[],
    options: AccessibilityOptions
  ): string[] {
    const recommendations: string[] = [];

    // High-priority recommendations based on issues
    const errorCount = issues.filter(i => i.type === 'error').length;
    const warningCount = issues.filter(i => i.type === 'warning').length;

    if (errorCount > 0) {
      recommendations.push(`Fix ${errorCount} critical accessibility error(s) to ensure basic usability`);
    }

    if (warningCount > 0) {
      recommendations.push(`Address ${warningCount} accessibility warning(s) to improve user experience`);
    }

    // General recommendations
    recommendations.push(
      'Use semantic HTML elements (headings, lists, sections) for proper structure',
      'Ensure all text has sufficient color contrast (4.5:1 minimum)',
      'Add alt text to all images or mark decorative images appropriately',
      'Test with screen readers and keyboard-only navigation',
      'Use clear, descriptive link text and button labels'
    );

    // Option-specific recommendations
    if (options.screenReaderOptimized) {
      recommendations.push(
        'Add ARIA labels and descriptions where helpful',
        'Use proper heading hierarchy (h1, h2, h3, etc.)',
        'Include skip links for long content sections'
      );
    }

    if (options.highContrast) {
      recommendations.push(
        'Use high contrast color combinations',
        'Avoid relying solely on color to convey information'
      );
    }

    return recommendations;
  }
}

// Export singleton instance
export const accessibilityChecker = TemplateAccessibilityChecker.getInstance();

// Utility functions for template components
export function getAccessibilityProps(elementType: string, content?: string) {
  const props: Record<string, any> = {};

  switch (elementType) {
    case 'profile-photo':
      props['alt'] = content ? `Professional photo of ${content}` : 'Professional headshot';
      props['role'] = 'img';
      break;
    
    case 'contact-info':
      props['aria-label'] = 'Contact information';
      break;
    
    case 'experience-section':
      props['aria-label'] = 'Work experience';
      props['role'] = 'region';
      break;
    
    case 'education-section':
      props['aria-label'] = 'Education background';
      props['role'] = 'region';
      break;
    
    case 'skills-section':
      props['aria-label'] = 'Skills and competencies';
      props['role'] = 'region';
      break;
    
    case 'main-heading':
      props['role'] = 'banner';
      break;
    
    case 'section-heading':
      props['role'] = 'heading';
      break;
  }

  return props;
}

export function generateSkipLinks(sections: string[]): Array<{ href: string; text: string }> {
  return sections.map(section => ({
    href: `#${section}-section`,
    text: `Skip to ${section.replace('-', ' ')}`
  }));
}

export function validateColorContrast(
  foreground: string,
  background: string,
  fontSize: number = 16
): { ratio: number; passes: boolean; level: 'AA' | 'AAA' | 'FAIL' } {
  // This is a simplified implementation
  // In a real application, you would use a proper color contrast calculation library
  
  const minRatio = fontSize >= 18 ? 3 : 4.5; // Large text vs normal text
  const ratio = 4.5; // Placeholder - would calculate actual ratio
  
  let level: 'AA' | 'AAA' | 'FAIL';
  if (ratio >= 7) level = 'AAA';
  else if (ratio >= minRatio) level = 'AA';
  else level = 'FAIL';
  
  return {
    ratio,
    passes: ratio >= minRatio,
    level
  };
}
