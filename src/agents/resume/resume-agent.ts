import { BaseAgent } from '../../lib/ai/base-agent';
import type { AgentRequest, AgentResponse } from '../../lib/ai/base-agent';
import { prisma } from '../../lib/prisma';
import { z } from 'zod';

// Input validation schemas
const ResumeGenerationSchema = z.object({
  jobTitle: z.string().min(1, 'Job title is required'),
  jobDescription: z.string().optional(),
  companyName: z.string().optional(),
  targetIndustry: z.string().optional(),
  experienceLevel: z.enum(['entry', 'mid', 'senior', 'executive']).default('mid'),
  resumeType: z.enum(['chronological', 'functional', 'combination']).default('chronological'),
  includeSkills: z.array(z.string()).optional(),
  customInstructions: z.string().optional(),
});

const ResumeOptimizationSchema = z.object({
  resumeId: z.string().min(1, 'Resume ID is required'),
  jobDescription: z.string().min(1, 'Job description is required'),
  optimizationType: z.enum(['ats', 'keywords', 'content', 'format']).default('ats'),
});

const ATSScoringSchema = z.object({
  resumeContent: z.string().min(1, 'Resume content is required'),
  jobDescription: z.string().min(1, 'Job description is required'),
});

export interface ResumeGenerationInput {
  jobTitle: string;
  jobDescription?: string;
  companyName?: string;
  targetIndustry?: string;
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
  resumeType: 'chronological' | 'functional' | 'combination';
  includeSkills?: string[];
  customInstructions?: string;
}

export interface ResumeOptimizationInput {
  resumeId: string;
  jobDescription: string;
  optimizationType: 'ats' | 'keywords' | 'content' | 'format';
}

export interface ATSScoringInput {
  resumeContent: string;
  jobDescription: string;
}

export interface ResumeGenerationOutput {
  resumeId: string;
  content: {
    summary: string;
    experience: Array<{
      title: string;
      company: string;
      duration: string;
      bullets: string[];
    }>;
    skills: string[];
    education: Array<{
      degree: string;
      school: string;
      year: string;
    }>;
    certifications?: string[];
    projects?: Array<{
      name: string;
      description: string;
      technologies: string[];
    }>;
  };
  atsScore: number;
  suggestions: string[];
}

export interface ATSScoringOutput {
  score: number;
  breakdown: {
    keywords: number;
    formatting: number;
    content: number;
    overall: number;
  };
  suggestions: string[];
  missingKeywords: string[];
  improvements: string[];
}

export class ResumeAgent extends BaseAgent {
  constructor() {
    super(
      'RESUME',
      'Resume Generation & Optimization Agent',
      'Generates ATS-optimized resumes and provides content optimization suggestions',
      '1.0.0'
    );
  }

  protected getSystemPrompt(): string {
    return `You are an expert resume writer and ATS optimization specialist with 15+ years of experience helping professionals land their dream jobs. Your expertise includes:

1. **Resume Writing**: Creating compelling, achievement-focused content that highlights quantifiable results
2. **ATS Optimization**: Understanding how Applicant Tracking Systems parse and rank resumes
3. **Industry Knowledge**: Tailoring resumes for specific industries and roles
4. **Keyword Strategy**: Identifying and incorporating relevant keywords naturally
5. **Format Optimization**: Ensuring clean, scannable formatting that works across all systems

**Guidelines:**
- Always focus on achievements and quantifiable results (numbers, percentages, dollar amounts)
- Use action verbs and industry-specific terminology
- Ensure ATS compatibility with clean formatting and standard section headers
- Tailor content to match job requirements and company culture
- Keep content concise but impactful
- Include relevant keywords naturally within context
- Provide specific, actionable suggestions for improvement

**Response Format:**
- Always respond with valid JSON when requested
- Include confidence scores and reasoning for recommendations
- Provide alternative suggestions when applicable
- Explain the rationale behind optimization choices`;
  }

  protected validateInput(input: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!input || typeof input !== 'object') {
      errors.push('Input must be a valid object');
      return { valid: false, errors };
    }

    // Determine input type and validate accordingly
    if ('jobTitle' in input) {
      const result = ResumeGenerationSchema.safeParse(input);
      if (!result.success) {
        errors.push(...result.error.errors.map(e => e.message));
      }
    } else if ('resumeId' in input) {
      const result = ResumeOptimizationSchema.safeParse(input);
      if (!result.success) {
        errors.push(...result.error.errors.map(e => e.message));
      }
    } else if ('resumeContent' in input) {
      const result = ATSScoringSchema.safeParse(input);
      if (!result.success) {
        errors.push(...result.error.errors.map(e => e.message));
      }
    } else {
      errors.push('Invalid input type. Expected resume generation, optimization, or ATS scoring input.');
    }

    return { valid: errors.length === 0, errors };
  }

  async execute(request: AgentRequest): Promise<AgentResponse> {
    const validation = this.validateInput(request.input);
    if (!validation.valid) {
      return {
        success: false,
        error: `Validation failed: ${validation.errors.join(', ')}`,
      };
    }

    try {
      // Determine the type of operation
      if ('jobTitle' in request.input) {
        return await this.generateResume(request.input as ResumeGenerationInput, request);
      } else if ('resumeId' in request.input) {
        return await this.optimizeResume(request.input as ResumeOptimizationInput, request);
      } else if ('resumeContent' in request.input) {
        return await this.scoreATS(request.input as ATSScoringInput, request);
      } else {
        return {
          success: false,
          error: 'Unknown operation type',
        };
      }
    } catch (error) {
      console.error('Resume agent execution failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Resume agent execution failed',
      };
    }
  }

  private async generateResume(
    input: ResumeGenerationInput,
    request: AgentRequest
  ): Promise<AgentResponse<ResumeGenerationOutput>> {
    // Get user profile data
    const userProfile = await this.getUserProfile(request.context.userId);
    
    const prompt = this.buildResumeGenerationPrompt(input, userProfile);
    
    const aiResponse = await this.processAIRequest(prompt, request.context, {
      structured: true,
      temperature: 0.7,
      maxTokens: 3000,
      mockKey: 'resume.generation', // Development mode mock response
    });

    if (!aiResponse.success) {
      return {
        success: false,
        error: aiResponse.error,
      };
    }

    // Save generated resume to database
    const resumeData = (aiResponse as any).data;
    const savedResume = await this.saveResume(request.context.userId, input, resumeData);

    return {
      success: true,
      data: {
        resumeId: savedResume.id,
        ...resumeData,
      },
      metadata: {
        tokensUsed: aiResponse.tokensUsed,
        cost: aiResponse.cost || 0,
        processingTime: aiResponse.processingTime,
        provider: aiResponse.provider,
        model: aiResponse.model,
      },
    };
  }

  private async optimizeResume(
    input: ResumeOptimizationInput,
    request: AgentRequest
  ): Promise<AgentResponse> {
    // Get existing resume
    const resume = await prisma.resume.findUnique({
      where: { id: input.resumeId },
      include: { user: true },
    });

    if (!resume || resume.userId !== request.context.userId) {
      return {
        success: false,
        error: 'Resume not found or access denied',
      };
    }

    const prompt = this.buildOptimizationPrompt(input, resume);
    
    const aiResponse = await this.processAIRequest(prompt, request.context, {
      structured: true,
      temperature: 0.5,
      maxTokens: 2000,
      mockKey: 'resume.optimization', // Development mode mock response
    });

    if (!aiResponse.success) {
      return {
        success: false,
        error: aiResponse.error,
      };
    }

    return {
      success: true,
      data: (aiResponse as any).data,
      metadata: {
        tokensUsed: aiResponse.tokensUsed,
        cost: aiResponse.cost || 0,
        processingTime: aiResponse.processingTime,
        provider: aiResponse.provider,
        model: aiResponse.model,
      },
    };
  }

  private async scoreATS(
    input: ATSScoringInput,
    request: AgentRequest
  ): Promise<AgentResponse<ATSScoringOutput>> {
    const prompt = this.buildATSScoringPrompt(input);
    
    const aiResponse = await this.processAIRequest(prompt, request.context, {
      structured: true,
      temperature: 0.3,
      maxTokens: 1500,
      mockKey: 'resume.atsScoring', // Development mode mock response
    });

    if (!aiResponse.success) {
      return {
        success: false,
        error: aiResponse.error,
      };
    }

    return {
      success: true,
      data: (aiResponse as any).data,
      metadata: {
        tokensUsed: aiResponse.tokensUsed,
        cost: aiResponse.cost || 0,
        processingTime: aiResponse.processingTime,
        provider: aiResponse.provider,
        model: aiResponse.model,
      },
    };
  }

  private buildResumeGenerationPrompt(input: ResumeGenerationInput, userProfile: any): string {
    return `Generate a professional, ATS-optimized resume for the following requirements:

**Job Details:**
- Title: ${input.jobTitle}
- Company: ${input.companyName || 'Not specified'}
- Industry: ${input.targetIndustry || 'Not specified'}
- Experience Level: ${input.experienceLevel}
- Resume Type: ${input.resumeType}

**Job Description:**
${input.jobDescription || 'Not provided'}

**User Profile:**
${JSON.stringify(userProfile, null, 2)}

**Additional Requirements:**
${input.customInstructions || 'None'}

Generate a complete resume with the following JSON structure:
{
  "summary": "Professional summary (2-3 sentences)",
  "experience": [
    {
      "title": "Job title",
      "company": "Company name",
      "duration": "Start - End dates",
      "bullets": ["Achievement-focused bullet points with quantifiable results"]
    }
  ],
  "skills": ["Relevant technical and soft skills"],
  "education": [
    {
      "degree": "Degree name",
      "school": "Institution name",
      "year": "Graduation year"
    }
  ],
  "certifications": ["Relevant certifications"],
  "projects": [
    {
      "name": "Project name",
      "description": "Brief description",
      "technologies": ["Technologies used"]
    }
  ],
  "atsScore": 85,
  "suggestions": ["Specific improvement suggestions"]
}`;
  }

  private buildOptimizationPrompt(input: ResumeOptimizationInput, resume: any): string {
    return `Optimize the following resume for better ATS compatibility and job matching:

**Optimization Type:** ${input.optimizationType}

**Target Job Description:**
${input.jobDescription}

**Current Resume Content:**
${JSON.stringify(resume.content, null, 2)}

Provide optimization suggestions in the following JSON format:
{
  "optimizedContent": "Updated resume content",
  "changes": ["List of specific changes made"],
  "atsScore": 90,
  "keywordMatches": ["Keywords successfully incorporated"],
  "suggestions": ["Additional improvement recommendations"]
}`;
  }

  private buildATSScoringPrompt(input: ATSScoringInput): string {
    return `Analyze the following resume against the job description and provide an ATS compatibility score:

**Job Description:**
${input.jobDescription}

**Resume Content:**
${input.resumeContent}

Provide a detailed ATS analysis in the following JSON format:
{
  "score": 85,
  "breakdown": {
    "keywords": 80,
    "formatting": 90,
    "content": 85,
    "overall": 85
  },
  "suggestions": ["Specific improvement suggestions"],
  "missingKeywords": ["Important keywords not found in resume"],
  "improvements": ["Detailed recommendations for better ATS performance"]
}`;
  }

  private async getUserProfile(userId: string): Promise<any> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        profile: {
          include: {
            skills: true,
            experiences: true,
            educations: true,
            certifications: true,
            projects: true,
          },
        },
      },
    });

    return user?.profile || {};
  }

  private async saveResume(userId: string, input: ResumeGenerationInput, content: any): Promise<any> {
    return await prisma.resume.create({
      data: {
        userId,
        title: `${input.jobTitle} Resume`,
        content: content,
        templateId: 'default', // This would be dynamic based on user selection
        atsScore: content.atsScore || 0,
      },
    });
  }
}
