import { BaseAgent } from '@/lib/ai/base-agent';
import { AgentType } from '@prisma/client';

export interface LinkedInOptimizationRequest {
  profileData: {
    headline?: string;
    summary?: string;
    aboutSection?: string;
    currentPosition?: string;
    currentCompany?: string;
    industry?: string;
    location?: string;
    skills?: string[];
    experience?: Array<{
      title: string;
      company: string;
      description?: string;
      duration?: string;
    }>;
  };
  targetRole?: string;
  targetIndustry?: string;
  optimizationGoals?: string[];
  tone?: 'professional' | 'creative' | 'technical' | 'executive';
}

export interface LinkedInOptimizationResponse {
  optimizedProfile: {
    headline: string[];
    summary: string[];
    aboutSection: string[];
    skillSuggestions: string[];
  };
  analysis: {
    profileScore: number;
    completenessScore: number;
    visibilityScore: number;
    engagementScore: number;
    keywordDensity: number;
  };
  recommendations: {
    missingFields: string[];
    optimizationTips: string[];
    keywordSuggestions: string[];
    industryInsights: string[];
  };
  recruiterInsights: {
    searchability: number;
    attractiveness: number;
    professionalBranding: number;
    networkingPotential: number;
  };
}

export class LinkedInAgent extends BaseAgent {
  constructor() {
    super(
      AgentType.LINKEDIN_OPTIMIZER,
      'LinkedIn Profile Optimization Agent',
      'Analyzes and optimizes LinkedIn profiles for maximum recruiter visibility and engagement',
      '1.0.0'
    );
  }

  async execute(request: any): Promise<any> {
    try {
      // Determine the type of LinkedIn operation
      if ('profileData' in request.input && 'targetRole' in request.input) {
        return await this.optimizeProfile(request.input, request.context);
      } else if ('headline' in request.input || 'summary' in request.input) {
        return await this.analyzeProfile(request.input, request.context);
      } else {
        return {
          success: false,
          error: 'Unknown LinkedIn operation type',
        };
      }
    } catch (error) {
      console.error('LinkedIn agent execution failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'LinkedIn agent execution failed',
      };
    }
  }

  async optimizeProfile(
    request: LinkedInOptimizationRequest,
    context: { userId: string; sessionId?: string; metadata?: Record<string, any> }
  ): Promise<LinkedInOptimizationResponse> {
    const prompt = this.buildOptimizationPrompt(request);

    const aiResponse = await this.processAIRequest(prompt, context, {
      structured: true,
      temperature: 0.7,
      maxTokens: 3000,
      mockKey: 'linkedin.optimization', // Development mode mock response
    });

    if (!aiResponse.success) {
      throw new Error(aiResponse.error || 'LinkedIn optimization failed');
    }

    try {
      return this.parseOptimizationResponse(aiResponse.content);
    } catch (error) {
      // Return fallback response if parsing fails
      return this.getFallbackOptimizationResponse();
    }
  }

  async analyzeProfile(
    profileData: any,
    context: { userId: string; sessionId?: string; metadata?: Record<string, any> }
  ): Promise<any> {
    const prompt = `
Analyze this LinkedIn profile and provide detailed insights:

Profile Data:
${JSON.stringify(profileData, null, 2)}

Please provide a comprehensive analysis including:

1. Profile Completeness Score (0-100)
2. Recruiter Visibility Score (0-100)
3. Engagement Potential Score (0-100)
4. Overall Profile Score (0-100)
5. Missing fields that should be completed
6. Specific optimization recommendations
7. Keyword suggestions for better searchability
8. Industry-specific insights
9. Networking recommendations
10. Professional branding assessment

Return the analysis as a JSON object with the following structure:
{
  "scores": {
    "completeness": 85,
    "visibility": 78,
    "engagement": 82,
    "overall": 81
  },
  "missingFields": ["field1", "field2"],
  "recommendations": ["tip1", "tip2"],
  "keywords": ["keyword1", "keyword2"],
  "insights": ["insight1", "insight2"],
  "branding": {
    "strengths": ["strength1"],
    "improvements": ["improvement1"]
  }
}
`;

    const aiResponse = await this.processAIRequest(prompt, context, {
      structured: true,
      temperature: 0.6,
      maxTokens: 2000,
      mockKey: 'linkedin.profileAnalysis', // Development mode mock response
    });

    if (!aiResponse.success) {
      return this.generateFallbackAnalysis(profileData);
    }

    try {
      return JSON.parse(aiResponse.content);
    } catch (error) {
      // Fallback analysis if JSON parsing fails
      return this.generateFallbackAnalysis(profileData);
    }
  }

  async generateHeadlines(profileData: any, targetRole?: string, count: number = 5): Promise<string[]> {
    const prompt = `
Generate ${count} optimized LinkedIn headlines for this professional:

Current Profile:
- Position: ${profileData.currentPosition || 'Not specified'}
- Company: ${profileData.currentCompany || 'Not specified'}
- Industry: ${profileData.industry || 'Not specified'}
- Target Role: ${targetRole || 'Not specified'}
- Skills: ${profileData.skills?.join(', ') || 'Not specified'}

Requirements:
- Maximum 220 characters each
- Include target role and key value proposition
- Use industry-relevant keywords
- Professional and engaging tone
- Focus on achievements and impact

Return as a JSON array of strings.
`;

    const response = await this.generateContent(prompt, {
      maxTokens: 1000,
      temperature: 0.8,
    });

    try {
      const headlines = JSON.parse(response);
      return Array.isArray(headlines) ? headlines : [response];
    } catch (error) {
      return this.generateFallbackHeadlines(profileData, targetRole, count);
    }
  }

  async generateSummary(profileData: any, targetRole?: string, tone: string = 'professional'): Promise<string[]> {
    const prompt = `
Write 2-3 optimized LinkedIn summaries for this professional:

Profile Information:
- Current Role: ${profileData.currentPosition || 'Not specified'}
- Company: ${profileData.currentCompany || 'Not specified'}
- Industry: ${profileData.industry || 'Not specified'}
- Target Role: ${targetRole || 'Not specified'}
- Experience: ${profileData.experience?.map((e: any) => `${e.title} at ${e.company}`).join(', ') || 'Not specified'}
- Skills: ${profileData.skills?.join(', ') || 'Not specified'}

Requirements:
- Tone: ${tone}
- 300-500 words each
- Start with a strong value proposition
- Include specific achievements and metrics
- Use industry keywords naturally
- End with a call to action
- Optimize for recruiter searches

Return as a JSON array of strings.
`;

    const response = await this.generateContent(prompt, {
      maxTokens: 1500,
      temperature: 0.7,
    });

    try {
      const summaries = JSON.parse(response);
      return Array.isArray(summaries) ? summaries : [response];
    } catch (error) {
      return this.generateFallbackSummary(profileData, targetRole);
    }
  }

  async optimizeSkills(currentSkills: string[], targetRole?: string, targetIndustry?: string): Promise<any> {
    const prompt = `
Optimize the skills section for this LinkedIn profile:

Current Skills: ${currentSkills.join(', ') || 'None'}
Target Role: ${targetRole || 'Not specified'}
Target Industry: ${targetIndustry || 'Not specified'}

Provide recommendations for:
1. Skills to add (trending and relevant)
2. Skills to prioritize (most important for target role)
3. Skills to remove (outdated or irrelevant)
4. Skill categories to focus on
5. Industry-specific skills

Return as JSON:
{
  "toAdd": ["skill1", "skill2"],
  "toPrioritize": ["skill1", "skill2"],
  "toRemove": ["skill1", "skill2"],
  "categories": {
    "technical": ["skill1"],
    "soft": ["skill1"],
    "industry": ["skill1"]
  },
  "trending": ["skill1", "skill2"]
}
`;

    const response = await this.generateContent(prompt, {
      maxTokens: 1000,
      temperature: 0.6,
    });

    try {
      return JSON.parse(response);
    } catch (error) {
      return this.generateFallbackSkillsOptimization(currentSkills, targetRole);
    }
  }

  private buildOptimizationPrompt(request: LinkedInOptimizationRequest): string {
    return `
You are a LinkedIn optimization expert. Analyze and optimize this LinkedIn profile:

Profile Data:
${JSON.stringify(request.profileData, null, 2)}

Target Role: ${request.targetRole || 'Not specified'}
Target Industry: ${request.targetIndustry || 'Not specified'}
Optimization Goals: ${request.optimizationGoals?.join(', ') || 'General optimization'}
Tone: ${request.tone || 'professional'}

Provide comprehensive optimization including:

1. 5 optimized headlines (max 220 chars each)
2. 3 optimized summaries (300-500 words each)
3. 2 optimized about sections (200-300 words each)
4. Skill recommendations and optimization
5. Profile analysis and scoring
6. Recruiter-focused insights
7. Keyword optimization suggestions
8. Industry-specific recommendations

Return as JSON with the exact structure:
{
  "optimizedProfile": {
    "headline": ["headline1", "headline2", "headline3", "headline4", "headline5"],
    "summary": ["summary1", "summary2", "summary3"],
    "aboutSection": ["about1", "about2"],
    "skillSuggestions": ["skill1", "skill2", "skill3"]
  },
  "analysis": {
    "profileScore": 85,
    "completenessScore": 90,
    "visibilityScore": 80,
    "engagementScore": 85,
    "keywordDensity": 75
  },
  "recommendations": {
    "missingFields": ["field1", "field2"],
    "optimizationTips": ["tip1", "tip2"],
    "keywordSuggestions": ["keyword1", "keyword2"],
    "industryInsights": ["insight1", "insight2"]
  },
  "recruiterInsights": {
    "searchability": 85,
    "attractiveness": 80,
    "professionalBranding": 90,
    "networkingPotential": 75
  }
}
`;
  }

  private parseOptimizationResponse(response: string): LinkedInOptimizationResponse {
    try {
      return JSON.parse(response);
    } catch (error) {
      // Return fallback response if parsing fails
      return {
        optimizedProfile: {
          headline: ['Professional | Expert | Industry Leader'],
          summary: ['Experienced professional with proven track record...'],
          aboutSection: ['Passionate about driving results...'],
          skillSuggestions: ['Leadership', 'Strategy', 'Innovation'],
        },
        analysis: {
          profileScore: 75,
          completenessScore: 80,
          visibilityScore: 70,
          engagementScore: 75,
          keywordDensity: 65,
        },
        recommendations: {
          missingFields: ['headline', 'summary'],
          optimizationTips: ['Complete your profile', 'Add more keywords'],
          keywordSuggestions: ['Leadership', 'Strategy'],
          industryInsights: ['Focus on digital transformation'],
        },
        recruiterInsights: {
          searchability: 70,
          attractiveness: 75,
          professionalBranding: 80,
          networkingPotential: 70,
        },
      };
    }
  }

  private generateFallbackAnalysis(profileData: any): any {
    const completeness = this.calculateCompleteness(profileData);
    return {
      scores: {
        completeness,
        visibility: Math.max(50, completeness - 10),
        engagement: Math.max(40, completeness - 20),
        overall: Math.max(60, completeness - 5),
      },
      missingFields: this.getMissingFields(profileData),
      recommendations: ['Complete your profile', 'Add more keywords', 'Update your summary'],
      keywords: ['Leadership', 'Strategy', 'Innovation'],
      insights: ['Focus on your unique value proposition'],
      branding: {
        strengths: ['Professional experience'],
        improvements: ['Add more specific achievements'],
      },
    };
  }

  private generateFallbackHeadlines(profileData: any, targetRole?: string, count: number = 5): string[] {
    const role = targetRole || profileData.currentPosition || 'Professional';
    const company = profileData.currentCompany || 'Leading Company';
    const industry = profileData.industry || 'Industry';

    return [
      `${role} | ${profileData.currentPosition || 'Expert'} at ${company}`,
      `Experienced ${role} | Driving Innovation in ${industry}`,
      `${role} Specialist | Helping Companies Achieve Excellence`,
      `Senior ${role} | ${industry} Expert | Growth-Focused Leader`,
      `${role} | Strategic Thinker | Results-Driven Professional`,
    ].slice(0, count);
  }

  private generateFallbackSummary(profileData: any, targetRole?: string): string[] {
    const role = targetRole || profileData.currentPosition || 'professional';
    const industry = profileData.industry || 'the industry';

    return [
      `Experienced ${role} with a proven track record of delivering results in ${industry}. Passionate about innovation and driving growth through strategic thinking and hands-on execution.`,
      `Results-driven ${role} specializing in ${industry} solutions. Known for building high-performing teams and delivering exceptional outcomes that exceed expectations.`,
    ];
  }

  private generateFallbackSkillsOptimization(currentSkills: string[], targetRole?: string): any {
    return {
      toAdd: ['Leadership', 'Strategy', 'Innovation', 'Communication'],
      toPrioritize: currentSkills.slice(0, 5),
      toRemove: [],
      categories: {
        technical: currentSkills.filter(skill => 
          skill.toLowerCase().includes('tech') || 
          skill.toLowerCase().includes('software') ||
          skill.toLowerCase().includes('programming')
        ),
        soft: ['Leadership', 'Communication', 'Teamwork'],
        industry: ['Strategy', 'Innovation', 'Growth'],
      },
      trending: ['AI/ML', 'Digital Transformation', 'Data Analytics'],
    };
  }

  private calculateCompleteness(profileData: any): number {
    const fields = ['headline', 'summary', 'currentPosition', 'currentCompany', 'industry', 'location'];
    const completedFields = fields.filter(field => profileData[field] && profileData[field].trim() !== '');
    return Math.round((completedFields.length / fields.length) * 100);
  }

  private getMissingFields(profileData: any): string[] {
    const fields = ['headline', 'summary', 'currentPosition', 'currentCompany', 'industry', 'location'];
    return fields.filter(field => !profileData[field] || profileData[field].trim() === '');
  }

  private getFallbackOptimizationResponse(): LinkedInOptimizationResponse {
    return {
      optimizedProfile: {
        headline: ['Professional | Expert | Industry Leader'],
        summary: ['Experienced professional with proven track record...'],
        aboutSection: ['Passionate about driving results...'],
        skillSuggestions: ['Leadership', 'Strategy', 'Innovation'],
      },
      analysis: {
        profileScore: 75,
        completenessScore: 80,
        visibilityScore: 70,
        engagementScore: 75,
        keywordDensity: 65,
      },
      recommendations: {
        missingFields: ['headline', 'summary'],
        optimizationTips: ['Complete your profile', 'Add more keywords'],
        keywordSuggestions: ['Leadership', 'Strategy'],
        industryInsights: ['Focus on digital transformation'],
      },
      recruiterInsights: {
        searchability: 70,
        attractiveness: 75,
        professionalBranding: 80,
        networkingPotential: 70,
      },
    };
  }
}
