import { BaseAgent } from '../../lib/ai/base-agent';
import type { AgentRequest, AgentResponse } from '../../lib/ai/base-agent';
import { z } from 'zod';

// Input validation schemas
const JobMarketAnalysisSchema = z.object({
  jobTitle: z.string().min(1, 'Job title is required'),
  location: z.string().optional(),
  industry: z.string().optional(),
  experienceLevel: z.enum(['entry', 'mid', 'senior', 'executive']).optional(),
  analysisType: z.enum(['salary', 'skills', 'trends', 'comprehensive']).default('comprehensive'),
});

const SkillTrendsSchema = z.object({
  skills: z.array(z.string()).min(1, 'At least one skill is required'),
  industry: z.string().optional(),
  timeframe: z.enum(['6months', '1year', '2years', '5years']).default('1year'),
});

const CompanyResearchSchema = z.object({
  companyName: z.string().min(1, 'Company name is required'),
  researchType: z.enum(['culture', 'financials', 'hiring', 'comprehensive']).default('comprehensive'),
});

export interface JobMarketAnalysisInput {
  jobTitle: string;
  location?: string;
  industry?: string;
  experienceLevel?: 'entry' | 'mid' | 'senior' | 'executive';
  analysisType: 'salary' | 'skills' | 'trends' | 'comprehensive';
}

export interface SkillTrendsInput {
  skills: string[];
  industry?: string;
  timeframe: '6months' | '1year' | '2years' | '5years';
}

export interface CompanyResearchInput {
  companyName: string;
  researchType: 'culture' | 'financials' | 'hiring' | 'comprehensive';
}

export interface JobMarketAnalysisOutput {
  jobTitle: string;
  marketOverview: {
    demandLevel: 'low' | 'medium' | 'high' | 'very-high';
    competitionLevel: 'low' | 'medium' | 'high' | 'very-high';
    growthProjection: string;
    marketSize: string;
  };
  salaryData: {
    min: number;
    max: number;
    median: number;
    currency: string;
    location: string;
  };
  requiredSkills: Array<{
    skill: string;
    importance: 'low' | 'medium' | 'high' | 'critical';
    frequency: number; // percentage of job postings mentioning this skill
  }>;
  emergingSkills: string[];
  careerPath: {
    nextRoles: string[];
    timeToAdvancement: string;
    requiredExperience: string;
  };
  recommendations: string[];
}

export interface SkillTrendsOutput {
  skills: Array<{
    name: string;
    trend: 'declining' | 'stable' | 'growing' | 'hot';
    demandChange: number; // percentage change
    averageSalaryImpact: number; // percentage salary increase
    relatedSkills: string[];
    learningResources: string[];
  }>;
  industryInsights: string[];
  recommendations: string[];
}

export interface CompanyResearchOutput {
  companyName: string;
  overview: {
    size: string;
    industry: string;
    founded: string;
    headquarters: string;
  };
  culture: {
    values: string[];
    workEnvironment: string;
    benefits: string[];
    diversityInclusion: string;
  };
  hiring: {
    activeOpenings: number;
    hiringTrends: string;
    interviewProcess: string;
    averageTimeToHire: string;
  };
  financials: {
    revenue: string;
    growth: string;
    stability: 'low' | 'medium' | 'high';
    funding: string;
  };
  recommendations: string[];
}

export class ResearchAgent extends BaseAgent {
  constructor() {
    super(
      'RESEARCH',
      'Job Market Research Agent',
      'Provides comprehensive job market analysis, skill trends, and company research',
      '1.0.0'
    );
  }

  protected getSystemPrompt(): string {
    return `You are an expert job market researcher and career analyst with deep knowledge of:

1. **Labor Market Dynamics**: Understanding supply and demand across industries and roles
2. **Salary Benchmarking**: Accurate compensation data across different markets and experience levels
3. **Skill Trend Analysis**: Identifying emerging technologies and declining skills
4. **Industry Intelligence**: Deep insights into sector-specific hiring patterns and requirements
5. **Company Research**: Comprehensive analysis of organizational culture, hiring practices, and market position

**Data Sources & Methodology:**
- Aggregate data from major job boards (LinkedIn, Indeed, Glassdoor, etc.)
- Industry reports and surveys from reputable sources
- Government labor statistics and economic indicators
- Professional network insights and hiring manager feedback
- Technology adoption trends and market research

**Analysis Framework:**
- Quantitative analysis with statistical confidence intervals
- Qualitative insights from industry experts and hiring managers
- Regional and temporal trend analysis
- Cross-industry skill transferability assessment
- Future-focused predictions based on market indicators

**Guidelines:**
- Provide data-driven insights with confidence levels
- Include both current state and future projections
- Consider regional variations and market dynamics
- Highlight actionable recommendations for career development
- Acknowledge limitations and data recency where applicable
- Use industry-standard terminology and classifications

**Response Format:**
- Always respond with valid JSON when requested
- Include confidence scores for predictions and recommendations
- Provide specific, actionable insights
- Reference data sources and methodology where relevant`;
  }

  protected validateInput(input: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!input || typeof input !== 'object') {
      errors.push('Input must be a valid object');
      return { valid: false, errors };
    }

    // Determine input type and validate accordingly
    if ('jobTitle' in input) {
      const result = JobMarketAnalysisSchema.safeParse(input);
      if (!result.success) {
        errors.push(...result.error.errors.map(e => e.message));
      }
    } else if ('skills' in input) {
      const result = SkillTrendsSchema.safeParse(input);
      if (!result.success) {
        errors.push(...result.error.errors.map(e => e.message));
      }
    } else if ('companyName' in input) {
      const result = CompanyResearchSchema.safeParse(input);
      if (!result.success) {
        errors.push(...result.error.errors.map(e => e.message));
      }
    } else {
      errors.push('Invalid input type. Expected job market analysis, skill trends, or company research input.');
    }

    return { valid: errors.length === 0, errors };
  }

  async execute(request: AgentRequest): Promise<AgentResponse> {
    const validation = this.validateInput(request.input);
    if (!validation.valid) {
      return {
        success: false,
        error: `Validation failed: ${validation.errors.join(', ')}`,
      };
    }

    try {
      // Determine the type of research operation
      if ('jobTitle' in request.input) {
        return await this.analyzeJobMarket(request.input as JobMarketAnalysisInput, request);
      } else if ('skills' in request.input) {
        return await this.analyzeSkillTrends(request.input as SkillTrendsInput, request);
      } else if ('companyName' in request.input) {
        return await this.researchCompany(request.input as CompanyResearchInput, request);
      } else {
        return {
          success: false,
          error: 'Unknown research operation type',
        };
      }
    } catch (error) {
      console.error('Research agent execution failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Research agent execution failed',
      };
    }
  }

  private async analyzeJobMarket(
    input: JobMarketAnalysisInput,
    request: AgentRequest
  ): Promise<AgentResponse<JobMarketAnalysisOutput>> {
    const prompt = this.buildJobMarketAnalysisPrompt(input);
    
    const aiResponse = await this.processAIRequest(prompt, request.context, {
      structured: true,
      temperature: 0.3,
      maxTokens: 2500,
      mockKey: 'research.jobMarket', // Development mode mock response
    });

    if (!aiResponse.success) {
      return {
        success: false,
        error: aiResponse.error,
      };
    }

    return {
      success: true,
      data: (aiResponse as any).data,
      metadata: {
        tokensUsed: aiResponse.tokensUsed,
        cost: aiResponse.cost || 0,
        processingTime: aiResponse.processingTime,
        provider: aiResponse.provider,
        model: aiResponse.model,
      },
    };
  }

  private async analyzeSkillTrends(
    input: SkillTrendsInput,
    request: AgentRequest
  ): Promise<AgentResponse<SkillTrendsOutput>> {
    const prompt = this.buildSkillTrendsPrompt(input);
    
    const aiResponse = await this.processAIRequest(prompt, request.context, {
      structured: true,
      temperature: 0.3,
      maxTokens: 2000,
    });

    if (!aiResponse.success) {
      return {
        success: false,
        error: aiResponse.error,
      };
    }

    return {
      success: true,
      data: (aiResponse as any).data,
      metadata: {
        tokensUsed: aiResponse.tokensUsed,
        cost: aiResponse.cost || 0,
        processingTime: aiResponse.processingTime,
        provider: aiResponse.provider,
        model: aiResponse.model,
      },
    };
  }

  private async researchCompany(
    input: CompanyResearchInput,
    request: AgentRequest
  ): Promise<AgentResponse<CompanyResearchOutput>> {
    const prompt = this.buildCompanyResearchPrompt(input);
    
    const aiResponse = await this.processAIRequest(prompt, request.context, {
      structured: true,
      temperature: 0.3,
      maxTokens: 2000,
    });

    if (!aiResponse.success) {
      return {
        success: false,
        error: aiResponse.error,
      };
    }

    return {
      success: true,
      data: (aiResponse as any).data,
      metadata: {
        tokensUsed: aiResponse.tokensUsed,
        cost: aiResponse.cost || 0,
        processingTime: aiResponse.processingTime,
        provider: aiResponse.provider,
        model: aiResponse.model,
      },
    };
  }

  private buildJobMarketAnalysisPrompt(input: JobMarketAnalysisInput): string {
    return `Conduct a comprehensive job market analysis for the following position:

**Job Title:** ${input.jobTitle}
**Location:** ${input.location || 'Global/Remote'}
**Industry:** ${input.industry || 'All industries'}
**Experience Level:** ${input.experienceLevel || 'All levels'}
**Analysis Type:** ${input.analysisType}

Provide a detailed market analysis in the following JSON format:
{
  "jobTitle": "${input.jobTitle}",
  "marketOverview": {
    "demandLevel": "high",
    "competitionLevel": "medium",
    "growthProjection": "15% growth over next 5 years",
    "marketSize": "Approximately 500,000 professionals globally"
  },
  "salaryData": {
    "min": 75000,
    "max": 150000,
    "median": 110000,
    "currency": "USD",
    "location": "${input.location || 'United States'}"
  },
  "requiredSkills": [
    {
      "skill": "Skill name",
      "importance": "critical",
      "frequency": 85
    }
  ],
  "emergingSkills": ["List of trending skills"],
  "careerPath": {
    "nextRoles": ["Senior positions"],
    "timeToAdvancement": "2-3 years",
    "requiredExperience": "Specific experience needed"
  },
  "recommendations": ["Actionable career advice"]
}

Base your analysis on current market data, industry trends, and hiring patterns.`;
  }

  private buildSkillTrendsPrompt(input: SkillTrendsInput): string {
    return `Analyze the market trends for the following skills over the ${input.timeframe} timeframe:

**Skills to Analyze:** ${input.skills.join(', ')}
**Industry Focus:** ${input.industry || 'All industries'}
**Timeframe:** ${input.timeframe}

Provide a comprehensive skill trend analysis in the following JSON format:
{
  "skills": [
    {
      "name": "Skill name",
      "trend": "growing",
      "demandChange": 25,
      "averageSalaryImpact": 15,
      "relatedSkills": ["Complementary skills"],
      "learningResources": ["Recommended learning platforms/courses"]
    }
  ],
  "industryInsights": ["Key industry observations"],
  "recommendations": ["Strategic skill development advice"]
}

Consider market demand, salary impact, and future projections for each skill.`;
  }

  private buildCompanyResearchPrompt(input: CompanyResearchInput): string {
    return `Research the following company and provide comprehensive insights:

**Company Name:** ${input.companyName}
**Research Type:** ${input.researchType}

Provide detailed company research in the following JSON format:
{
  "companyName": "${input.companyName}",
  "overview": {
    "size": "Company size (employees)",
    "industry": "Primary industry",
    "founded": "Year founded",
    "headquarters": "Location"
  },
  "culture": {
    "values": ["Core company values"],
    "workEnvironment": "Description of work culture",
    "benefits": ["Key benefits and perks"],
    "diversityInclusion": "D&I initiatives and commitment"
  },
  "hiring": {
    "activeOpenings": 150,
    "hiringTrends": "Current hiring patterns",
    "interviewProcess": "Typical interview structure",
    "averageTimeToHire": "Timeline from application to offer"
  },
  "financials": {
    "revenue": "Annual revenue",
    "growth": "Growth trajectory",
    "stability": "high",
    "funding": "Funding status and investors"
  },
  "recommendations": ["Job application and interview tips specific to this company"]
}

Focus on providing accurate, up-to-date information that would be valuable for job seekers.`;
  }
}
