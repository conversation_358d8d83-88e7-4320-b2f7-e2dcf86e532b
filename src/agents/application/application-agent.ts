import { BaseAgent } from '../../lib/ai/base-agent';
import type { AgentRequest, AgentResponse } from '../../lib/ai/base-agent';
import { prisma } from '../../lib/db';
import { z } from 'zod';
import type { ApplicationStatus, JobType } from '@prisma/client';

// Input validation schemas
const JobApplicationSchema = z.object({
  jobId: z.string().min(1, 'Job ID is required'),
  resumeId: z.string().min(1, 'Resume ID is required'),
  generateCoverLetter: z.boolean().default(true),
  customMessage: z.string().optional(),
  applicationNotes: z.string().optional(),
  followUpDate: z.string().optional().transform((val) => val ? new Date(val) : undefined),
});

const CoverLetterGenerationSchema = z.object({
  jobTitle: z.string().min(1, 'Job title is required'),
  companyName: z.string().min(1, 'Company name is required'),
  jobDescription: z.string().min(1, 'Job description is required'),
  resumeContent: z.string().min(1, 'Resume content is required'),
  tone: z.enum(['professional', 'enthusiastic', 'creative', 'technical']).default('professional'),
  length: z.enum(['short', 'medium', 'long']).default('medium'),
  customInstructions: z.string().optional(),
});

const ApplicationOptimizationSchema = z.object({
  jobDescription: z.string().min(1, 'Job description is required'),
  resumeContent: z.string().min(1, 'Resume content is required'),
  applicationHistory: z.array(z.object({
    jobTitle: z.string(),
    company: z.string(),
    status: z.string(),
    appliedDate: z.string(),
  })).optional(),
  targetRole: z.string().optional(),
});

export interface JobApplicationRequest {
  jobId: string;
  resumeId: string;
  generateCoverLetter?: boolean;
  customMessage?: string;
  applicationNotes?: string;
  followUpDate?: Date;
}

export interface CoverLetterGenerationRequest {
  jobTitle: string;
  companyName: string;
  jobDescription: string;
  resumeContent: string;
  tone?: 'professional' | 'enthusiastic' | 'creative' | 'technical';
  length?: 'short' | 'medium' | 'long';
  customInstructions?: string;
}

export interface ApplicationOptimizationRequest {
  jobDescription: string;
  resumeContent: string;
  applicationHistory?: Array<{
    jobTitle: string;
    company: string;
    status: string;
    appliedDate: string;
  }>;
  targetRole?: string;
}

export interface JobApplicationResponse {
  applicationId: string;
  status: ApplicationStatus;
  coverLetterId?: string;
  coverLetterContent?: string;
  submissionStatus: 'success' | 'pending' | 'failed';
  submissionDetails?: {
    platform: string;
    submittedAt: Date;
    confirmationNumber?: string;
  };
  recommendations?: string[];
}

export interface CoverLetterResponse {
  content: string;
  title?: string;
  keyPoints: string[];
  tone: string;
  wordCount: number;
  atsOptimized: boolean;
}

export interface ApplicationOptimizationResponse {
  overallScore: number;
  recommendations: Array<{
    category: string;
    priority: 'high' | 'medium' | 'low';
    suggestion: string;
    impact: string;
  }>;
  keywordMatches: {
    matched: string[];
    missing: string[];
    score: number;
  };
  applicationStrategy: {
    bestTimeToApply: string;
    followUpSchedule: string[];
    customizationTips: string[];
  };
}

export class ApplicationAgent extends BaseAgent {
  constructor() {
    super(
      'APPLICATION',
      'Job Application Agent',
      'Automates job applications, generates personalized cover letters, and optimizes application success rates',
      '1.0.0'
    );
  }

  protected getSystemPrompt(): string {
    return `You are an expert Job Application Agent specializing in:

1. **Automated Job Application Submission**
   - Analyze job requirements and match with candidate profiles
   - Generate personalized application materials
   - Optimize application timing and strategy

2. **Cover Letter Generation**
   - Create compelling, personalized cover letters
   - Match tone and style to company culture
   - Incorporate relevant keywords for ATS optimization
   - Highlight key achievements and relevant experience

3. **Application Optimization**
   - Analyze application success patterns
   - Provide strategic recommendations for improvement
   - Optimize keyword matching and ATS compatibility
   - Suggest timing and follow-up strategies

4. **Success Rate Analysis**
   - Track application performance metrics
   - Identify patterns in successful applications
   - Recommend improvements based on data

Always provide actionable, specific recommendations that increase the likelihood of application success. Focus on personalization, relevance, and professional presentation.`;
  }

  protected validateInput(input: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Determine operation type and validate accordingly
    if ('jobId' in input && 'resumeId' in input) {
      const result = JobApplicationSchema.safeParse(input);
      if (!result.success) {
        errors.push(...result.error.errors.map(e => e.message));
      }
    } else if ('jobTitle' in input && 'companyName' in input && 'jobDescription' in input) {
      const result = CoverLetterGenerationSchema.safeParse(input);
      if (!result.success) {
        errors.push(...result.error.errors.map(e => e.message));
      }
    } else if ('jobDescription' in input && 'resumeContent' in input) {
      const result = ApplicationOptimizationSchema.safeParse(input);
      if (!result.success) {
        errors.push(...result.error.errors.map(e => e.message));
      }
    } else {
      errors.push('Invalid input: must be job application, cover letter generation, or application optimization request');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  async execute(request: AgentRequest): Promise<AgentResponse> {
    try {
      const validation = this.validateInput(request.input);
      if (!validation.valid) {
        return {
          success: false,
          error: `Validation failed: ${validation.errors.join(', ')}`,
        };
      }

      // Determine operation type and execute accordingly
      if ('jobId' in request.input && 'resumeId' in request.input) {
        return await this.submitJobApplication(request.input, request.context);
      } else if ('jobTitle' in request.input && 'companyName' in request.input) {
        return await this.generateCoverLetter(request.input, request.context);
      } else if ('jobDescription' in request.input && 'resumeContent' in request.input) {
        return await this.optimizeApplication(request.input, request.context);
      } else {
        return {
          success: false,
          error: 'Unknown application operation type',
        };
      }
    } catch (error) {
      console.error('Application agent execution failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Application agent execution failed',
      };
    }
  }

  async submitJobApplication(
    request: JobApplicationRequest,
    context: { userId: string; sessionId?: string; metadata?: Record<string, any> }
  ): Promise<AgentResponse<JobApplicationResponse>> {
    try {
      // Fetch job and resume data
      const [job, resume] = await Promise.all([
        prisma.job.findUnique({ where: { id: request.jobId } }),
        prisma.resume.findUnique({ where: { id: request.resumeId } }),
      ]);

      if (!job || !resume) {
        return {
          success: false,
          error: 'Job or resume not found',
        };
      }

      let coverLetterId: string | undefined;
      let coverLetterContent: string | undefined;

      // Generate cover letter if requested
      if (request.generateCoverLetter) {
        const coverLetterResponse = await this.generateCoverLetter({
          jobTitle: job.title,
          companyName: job.company,
          jobDescription: job.description,
          resumeContent: resume.content || '',
          tone: 'professional',
          length: 'medium',
          customInstructions: request.customMessage,
        }, context);

        if (coverLetterResponse.success && coverLetterResponse.data) {
          coverLetterContent = coverLetterResponse.data.content;
          
          // Save cover letter to database
          const coverLetter = await prisma.coverLetter.create({
            data: {
              userId: context.userId,
              jobId: request.jobId,
              title: coverLetterResponse.data.title || `Application for ${job.title} at ${job.company}`,
              content: coverLetterContent,
            },
          });
          coverLetterId = coverLetter.id;
        }
      }

      // Create job application record
      const application = await prisma.jobApplication.create({
        data: {
          userId: context.userId,
          jobId: request.jobId,
          resumeId: request.resumeId,
          coverLetterId,
          status: 'APPLIED',
          notes: request.applicationNotes,
          followUpDate: request.followUpDate,
        },
      });

      // Generate application recommendations
      const recommendations = await this.generateApplicationRecommendations(job, resume);

      return {
        success: true,
        data: {
          applicationId: application.id,
          status: application.status,
          coverLetterId,
          coverLetterContent,
          submissionStatus: 'success',
          submissionDetails: {
            platform: job.source,
            submittedAt: new Date(),
          },
          recommendations,
        },
      };
    } catch (error) {
      console.error('Job application submission failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Job application submission failed',
      };
    }
  }

  async generateCoverLetter(
    request: CoverLetterGenerationRequest,
    context: { userId: string; sessionId?: string; metadata?: Record<string, any> }
  ): Promise<AgentResponse<CoverLetterResponse>> {
    const prompt = this.buildCoverLetterPrompt(request);

    const aiResponse = await this.processAIRequest(prompt, context, {
      structured: true,
      temperature: 0.7,
      maxTokens: 2000,
      mockKey: 'application.coverLetter', // Development mode mock response
    });

    if (!aiResponse.success) {
      return this.getFallbackCoverLetterResponse(request);
    }

    try {
      return {
        success: true,
        data: this.parseCoverLetterResponse(aiResponse.content, request),
      };
    } catch (error) {
      return this.getFallbackCoverLetterResponse(request);
    }
  }

  async optimizeApplication(
    request: ApplicationOptimizationRequest,
    context: { userId: string; sessionId?: string; metadata?: Record<string, any> }
  ): Promise<AgentResponse<ApplicationOptimizationResponse>> {
    const prompt = this.buildOptimizationPrompt(request);

    const aiResponse = await this.processAIRequest(prompt, context, {
      structured: true,
      temperature: 0.6,
      maxTokens: 2500,
      mockKey: 'application.optimization', // Development mode mock response
    });

    if (!aiResponse.success) {
      return this.getFallbackOptimizationResponse();
    }

    try {
      return {
        success: true,
        data: this.parseOptimizationResponse(aiResponse.content),
      };
    } catch (error) {
      return this.getFallbackOptimizationResponse();
    }
  }

  private buildCoverLetterPrompt(request: CoverLetterGenerationRequest): string {
    const lengthGuide = {
      short: '150-200 words',
      medium: '250-350 words',
      long: '400-500 words',
    };

    return `Generate a personalized cover letter with the following specifications:

**Job Details:**
- Position: ${request.jobTitle}
- Company: ${request.companyName}
- Job Description: ${request.jobDescription}

**Candidate Resume:**
${request.resumeContent}

**Requirements:**
- Tone: ${request.tone}
- Length: ${lengthGuide[request.length]} (${request.length})
- Custom Instructions: ${request.customInstructions || 'None'}

**Output Format (JSON):**
{
  "content": "Full cover letter content",
  "title": "Cover letter title/subject line",
  "keyPoints": ["Key point 1", "Key point 2", "Key point 3"],
  "tone": "${request.tone}",
  "wordCount": number,
  "atsOptimized": true/false
}

**Guidelines:**
1. Start with a compelling opening that shows knowledge of the company
2. Highlight 2-3 most relevant experiences from the resume
3. Include specific keywords from the job description
4. Show enthusiasm and cultural fit
5. End with a strong call to action
6. Ensure ATS optimization with relevant keywords
7. Maintain professional formatting and structure`;
  }

  private buildOptimizationPrompt(request: ApplicationOptimizationRequest): string {
    const historyText = request.applicationHistory?.length
      ? request.applicationHistory.map(app =>
          `- ${app.jobTitle} at ${app.company}: ${app.status} (Applied: ${app.appliedDate})`
        ).join('\n')
      : 'No application history provided';

    return `Analyze this job application for optimization opportunities:

**Job Description:**
${request.jobDescription}

**Candidate Resume:**
${request.resumeContent}

**Application History:**
${historyText}

**Target Role:** ${request.targetRole || 'Not specified'}

**Analysis Required:**
1. Overall application strength score (0-100)
2. Specific recommendations for improvement
3. Keyword matching analysis
4. Application strategy recommendations

**Output Format (JSON):**
{
  "overallScore": number,
  "recommendations": [
    {
      "category": "resume|cover_letter|timing|strategy",
      "priority": "high|medium|low",
      "suggestion": "Specific actionable suggestion",
      "impact": "Expected impact description"
    }
  ],
  "keywordMatches": {
    "matched": ["keyword1", "keyword2"],
    "missing": ["missing1", "missing2"],
    "score": number
  },
  "applicationStrategy": {
    "bestTimeToApply": "Timing recommendation",
    "followUpSchedule": ["Follow-up action 1", "Follow-up action 2"],
    "customizationTips": ["Tip 1", "Tip 2"]
  }
}

Provide specific, actionable recommendations that will improve application success rate.`;
  }

  private parseCoverLetterResponse(content: string, request: CoverLetterGenerationRequest): CoverLetterResponse {
    try {
      const parsed = JSON.parse(content);
      return {
        content: parsed.content || this.generateFallbackCoverLetter(request),
        title: parsed.title || `Application for ${request.jobTitle} at ${request.companyName}`,
        keyPoints: parsed.keyPoints || ['Relevant experience', 'Strong skills match', 'Enthusiasm for role'],
        tone: parsed.tone || request.tone,
        wordCount: parsed.wordCount || parsed.content?.split(' ').length || 0,
        atsOptimized: parsed.atsOptimized || true,
      };
    } catch (error) {
      return {
        content: this.generateFallbackCoverLetter(request),
        title: `Application for ${request.jobTitle} at ${request.companyName}`,
        keyPoints: ['Relevant experience', 'Strong skills match', 'Enthusiasm for role'],
        tone: request.tone,
        wordCount: 250,
        atsOptimized: true,
      };
    }
  }

  private parseOptimizationResponse(content: string): ApplicationOptimizationResponse {
    try {
      const parsed = JSON.parse(content);
      return {
        overallScore: parsed.overallScore || 75,
        recommendations: parsed.recommendations || [
          {
            category: 'resume',
            priority: 'high' as const,
            suggestion: 'Tailor resume keywords to match job description',
            impact: 'Improve ATS compatibility and keyword matching',
          },
        ],
        keywordMatches: parsed.keywordMatches || {
          matched: ['JavaScript', 'React', 'Node.js'],
          missing: ['TypeScript', 'AWS', 'Docker'],
          score: 70,
        },
        applicationStrategy: parsed.applicationStrategy || {
          bestTimeToApply: 'Apply within 24-48 hours of job posting',
          followUpSchedule: ['Follow up after 1 week', 'Second follow-up after 2 weeks'],
          customizationTips: ['Customize resume for each application', 'Research company culture'],
        },
      };
    } catch (error) {
      return this.getFallbackOptimizationData();
    }
  }

  private async generateApplicationRecommendations(job: any, resume: any): Promise<string[]> {
    const recommendations: string[] = [];

    // Basic recommendations based on job and resume analysis
    if (job.skills?.length > 0) {
      recommendations.push('Highlight relevant technical skills mentioned in the job description');
    }

    if (job.requirements?.length > 0) {
      recommendations.push('Address key requirements directly in your cover letter');
    }

    if (job.atsKeywords?.length > 0) {
      recommendations.push('Ensure your resume includes ATS-friendly keywords from the job posting');
    }

    recommendations.push('Follow up within 1-2 weeks if you don\'t hear back');
    recommendations.push('Research the company culture and values before interviewing');

    return recommendations;
  }

  private generateFallbackCoverLetter(request: CoverLetterGenerationRequest): string {
    return `Dear Hiring Manager,

I am writing to express my strong interest in the ${request.jobTitle} position at ${request.companyName}. With my background and experience, I am confident that I would be a valuable addition to your team.

My qualifications align well with the requirements outlined in your job description. I have demonstrated expertise in the key areas you're seeking, and I'm excited about the opportunity to contribute to ${request.companyName}'s continued success.

I would welcome the opportunity to discuss how my skills and experience can benefit your organization. Thank you for considering my application, and I look forward to hearing from you soon.

Best regards,
[Your Name]`;
  }

  private getFallbackCoverLetterResponse(request: CoverLetterGenerationRequest): AgentResponse<CoverLetterResponse> {
    return {
      success: true,
      data: {
        content: this.generateFallbackCoverLetter(request),
        title: `Application for ${request.jobTitle} at ${request.companyName}`,
        keyPoints: ['Relevant experience', 'Strong skills match', 'Enthusiasm for role'],
        tone: request.tone,
        wordCount: 150,
        atsOptimized: true,
      },
    };
  }

  private getFallbackOptimizationResponse(): AgentResponse<ApplicationOptimizationResponse> {
    return {
      success: true,
      data: this.getFallbackOptimizationData(),
    };
  }

  private getFallbackOptimizationData(): ApplicationOptimizationResponse {
    return {
      overallScore: 75,
      recommendations: [
        {
          category: 'resume',
          priority: 'high',
          suggestion: 'Tailor resume keywords to match job description',
          impact: 'Improve ATS compatibility and keyword matching',
        },
        {
          category: 'cover_letter',
          priority: 'medium',
          suggestion: 'Personalize cover letter for each application',
          impact: 'Show genuine interest and research',
        },
        {
          category: 'timing',
          priority: 'medium',
          suggestion: 'Apply within 24-48 hours of job posting',
          impact: 'Increase visibility among early applicants',
        },
      ],
      keywordMatches: {
        matched: ['JavaScript', 'React', 'Node.js'],
        missing: ['TypeScript', 'AWS', 'Docker'],
        score: 70,
      },
      applicationStrategy: {
        bestTimeToApply: 'Apply within 24-48 hours of job posting for maximum visibility',
        followUpSchedule: [
          'Send thank you email within 24 hours of application',
          'Follow up after 1 week if no response',
          'Second follow-up after 2 weeks',
        ],
        customizationTips: [
          'Research company values and incorporate them into your application',
          'Customize resume keywords for each job application',
          'Use specific examples that match job requirements',
        ],
      },
    };
  }
}
