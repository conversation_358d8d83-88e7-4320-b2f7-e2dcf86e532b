// Template customization types for CVLeap

export interface TemplateCustomization {
  id: string;
  templateId: string;
  name: string;
  colorScheme: ColorScheme;
  typography: Typography;
  layout: LayoutSettings;
  spacing: SpacingSettings;
  isDefault?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
  textSecondary: string;
  border: string;
  surface: string;
  surfaceSecondary: string;
}

export interface Typography {
  headingFont: string;
  bodyFont: string;
  headingSize: TypographyScale;
  bodySize: TypographyScale;
  lineHeight: LineHeightScale;
  fontWeight: FontWeightScale;
}

export interface TypographyScale {
  xs: string;
  sm: string;
  base: string;
  lg: string;
  xl: string;
  '2xl': string;
  '3xl': string;
  '4xl': string;
}

export interface LineHeightScale {
  tight: string;
  normal: string;
  relaxed: string;
  loose: string;
}

export interface FontWeightScale {
  normal: string;
  medium: string;
  semibold: string;
  bold: string;
}

export interface LayoutSettings {
  pageMargins: Margins;
  sectionSpacing: string;
  columnGap: string;
  maxWidth: string;
  alignment: 'left' | 'center' | 'right';
}

export interface SpacingSettings {
  sectionGap: string;
  itemGap: string;
  paragraphGap: string;
  listGap: string;
}

export interface Margins {
  top: string;
  right: string;
  bottom: string;
  left: string;
}

// Predefined color palettes
export interface ColorPalette {
  id: string;
  name: string;
  category: 'professional' | 'modern' | 'creative' | 'minimalist';
  colors: ColorScheme;
  preview: string; // CSS gradient for preview
}

// Font options
export interface FontOption {
  id: string;
  name: string;
  family: string;
  category: 'serif' | 'sans-serif' | 'monospace';
  weights: number[];
  preview: string;
  isWebFont: boolean;
  fallback: string[];
}

// Customization presets
export interface CustomizationPreset {
  id: string;
  name: string;
  description: string;
  category: 'professional' | 'modern' | 'creative' | 'minimalist' | 'executive';
  templateIds: string[]; // Compatible template IDs
  customization: Partial<TemplateCustomization>;
  preview: string;
  isPremium: boolean;
}

// Customization validation
export interface CustomizationValidation {
  isValid: boolean;
  errors: CustomizationError[];
  warnings: CustomizationWarning[];
  atsCompatible: boolean;
  accessibilityScore: number;
}

export interface CustomizationError {
  field: string;
  message: string;
  severity: 'error' | 'warning';
}

export interface CustomizationWarning {
  field: string;
  message: string;
  suggestion: string;
}

// CSS custom properties mapping
export interface CSSCustomProperties {
  [key: string]: string;
}

// Customization context
export interface CustomizationContext {
  templateId: string;
  customization: TemplateCustomization;
  isEditing: boolean;
  isDirty: boolean;
  validation: CustomizationValidation;
}

// Customization actions
export type CustomizationAction =
  | { type: 'SET_COLOR_SCHEME'; payload: ColorScheme }
  | { type: 'SET_TYPOGRAPHY'; payload: Typography }
  | { type: 'SET_LAYOUT'; payload: LayoutSettings }
  | { type: 'SET_SPACING'; payload: SpacingSettings }
  | { type: 'APPLY_PRESET'; payload: CustomizationPreset }
  | { type: 'RESET_TO_DEFAULT' }
  | { type: 'SAVE_CUSTOMIZATION'; payload: TemplateCustomization }
  | { type: 'LOAD_CUSTOMIZATION'; payload: TemplateCustomization }
  | { type: 'VALIDATE_CUSTOMIZATION' };

// Export options with customization
export interface CustomizedExportOptions {
  includeCustomization: boolean;
  customization?: TemplateCustomization;
  format: 'pdf' | 'png' | 'html';
  quality: 'high' | 'medium' | 'low';
}

// Template variant
export interface TemplateVariant {
  id: string;
  baseTemplateId: string;
  name: string;
  description: string;
  customization: TemplateCustomization;
  isPublic: boolean;
  createdBy: string;
  downloads: number;
  rating: number;
  tags: string[];
}

// Customization analytics
export interface CustomizationAnalytics {
  templateId: string;
  customizationId: string;
  usageCount: number;
  exportCount: number;
  userRating: number;
  popularColors: string[];
  popularFonts: string[];
  conversionRate: number;
}

// Default values
export const DEFAULT_COLOR_SCHEME: ColorScheme = {
  primary: '#3b82f6',
  secondary: '#64748b',
  accent: '#06b6d4',
  background: '#ffffff',
  text: '#1e293b',
  textSecondary: '#64748b',
  border: '#e2e8f0',
  surface: '#f8fafc',
  surfaceSecondary: '#f1f5f9'
};

export const DEFAULT_TYPOGRAPHY: Typography = {
  headingFont: 'Inter',
  bodyFont: 'Inter',
  headingSize: {
    xs: '0.75rem',
    sm: '0.875rem',
    base: '1rem',
    lg: '1.125rem',
    xl: '1.25rem',
    '2xl': '1.5rem',
    '3xl': '1.875rem',
    '4xl': '2.25rem'
  },
  bodySize: {
    xs: '0.75rem',
    sm: '0.875rem',
    base: '1rem',
    lg: '1.125rem',
    xl: '1.25rem',
    '2xl': '1.5rem',
    '3xl': '1.875rem',
    '4xl': '2.25rem'
  },
  lineHeight: {
    tight: '1.25',
    normal: '1.5',
    relaxed: '1.625',
    loose: '2'
  },
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700'
  }
};

export const DEFAULT_LAYOUT: LayoutSettings = {
  pageMargins: {
    top: '1rem',
    right: '1rem',
    bottom: '1rem',
    left: '1rem'
  },
  sectionSpacing: '2rem',
  columnGap: '2rem',
  maxWidth: '8.5in',
  alignment: 'left'
};

export const DEFAULT_SPACING: SpacingSettings = {
  sectionGap: '1.5rem',
  itemGap: '0.75rem',
  paragraphGap: '0.5rem',
  listGap: '0.25rem'
};
