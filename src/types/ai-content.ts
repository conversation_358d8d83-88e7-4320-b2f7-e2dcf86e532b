// AI-powered content enhancement types for CVLeap

export interface ContentSuggestion {
  id: string;
  type: 'improvement' | 'addition' | 'replacement' | 'optimization';
  section: 'summary' | 'experience' | 'skills' | 'education' | 'certifications' | 'projects';
  field?: string; // Specific field within section
  original?: string;
  suggested: string;
  reason: string;
  confidence: number; // 0-1 score
  impact: 'low' | 'medium' | 'high';
  category: 'ats' | 'keywords' | 'clarity' | 'impact' | 'formatting' | 'industry';
  keywords?: string[];
}

export interface ContentAnalysis {
  overallScore: number; // 0-100
  atsScore: number; // 0-100
  keywordDensity: number; // 0-1
  readabilityScore: number; // 0-100
  impactScore: number; // 0-100
  industryAlignment: number; // 0-100
  
  strengths: string[];
  weaknesses: string[];
  missingKeywords: string[];
  suggestions: ContentSuggestion[];
  
  sectionScores: {
    summary: number;
    experience: number;
    skills: number;
    education: number;
    certifications: number;
    projects: number;
  };
}

export interface JobAnalysis {
  title: string;
  company?: string;
  industry: string;
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
  requiredSkills: string[];
  preferredSkills: string[];
  keywords: string[];
  responsibilities: string[];
  qualifications: string[];
  salaryRange?: {
    min: number;
    max: number;
    currency: string;
  };
}

export interface ContentOptimizationRequest {
  resumeContent: any; // ResumeContent
  jobDescription?: string;
  targetRole?: string;
  targetIndustry?: string;
  optimizationType: 'ats' | 'keywords' | 'impact' | 'clarity' | 'industry' | 'comprehensive';
  userPreferences?: {
    tone: 'professional' | 'casual' | 'technical' | 'creative';
    length: 'concise' | 'detailed' | 'balanced';
    focus: 'achievements' | 'skills' | 'experience' | 'leadership';
  };
}

export interface ContentOptimizationResponse {
  analysis: ContentAnalysis;
  suggestions: ContentSuggestion[];
  optimizedContent?: any; // Partial ResumeContent with suggested changes
  keywordRecommendations: string[];
  industryInsights: string[];
  competitorAnalysis?: {
    commonSkills: string[];
    emergingTrends: string[];
    salaryBenchmark?: number;
  };
}

export interface SmartContentGeneration {
  section: 'summary' | 'experience' | 'skills' | 'projects';
  context: {
    role?: string;
    industry?: string;
    experience?: any[];
    skills?: string[];
    achievements?: string[];
  };
  options: {
    length: 'short' | 'medium' | 'long';
    style: 'bullet' | 'paragraph' | 'hybrid';
    focus: 'achievements' | 'responsibilities' | 'skills' | 'impact';
    tone: 'professional' | 'dynamic' | 'technical' | 'leadership';
  };
}

export interface SmartContentResponse {
  generatedContent: string | string[];
  alternatives: string[];
  keywords: string[];
  reasoning: string;
  confidence: number;
  suggestions: string[];
}

export interface ATSOptimization {
  score: number; // 0-100
  issues: {
    type: 'formatting' | 'keywords' | 'structure' | 'length' | 'readability';
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    suggestion: string;
    section?: string;
  }[];
  recommendations: {
    keywords: string[];
    formatting: string[];
    structure: string[];
    content: string[];
  };
  compatibility: {
    applicantTrackingSystems: string[];
    parsingAccuracy: number;
    keywordMatching: number;
    formatCompliance: number;
  };
}

export interface IndustryInsights {
  industry: string;
  trends: {
    emergingSkills: string[];
    decliningSkills: string[];
    hotKeywords: string[];
    salaryTrends: {
      role: string;
      averageSalary: number;
      growth: number;
    }[];
  };
  recommendations: {
    skillsToAdd: string[];
    skillsToEmphasize: string[];
    certificationsSuggested: string[];
    experienceHighlights: string[];
  };
  competitorAnalysis: {
    commonSkills: string[];
    differentiators: string[];
    marketDemand: number;
  };
}

export interface ContentTemplate {
  id: string;
  name: string;
  type: 'summary' | 'experience' | 'achievement' | 'skill_description';
  industry: string[];
  role: string[];
  experienceLevel: string[];
  template: string;
  variables: string[];
  examples: string[];
}

export interface AIContentSettings {
  provider: 'openai' | 'anthropic' | 'local';
  model: string;
  temperature: number;
  maxTokens: number;
  enableRealTimeSuggestions: boolean;
  enableAutoOptimization: boolean;
  suggestionFrequency: 'low' | 'medium' | 'high';
  confidenceThreshold: number;
  categories: {
    ats: boolean;
    keywords: boolean;
    clarity: boolean;
    impact: boolean;
    formatting: boolean;
    industry: boolean;
  };
}

export interface ContentMetrics {
  totalSuggestions: number;
  acceptedSuggestions: number;
  rejectedSuggestions: number;
  averageConfidence: number;
  improvementScore: number;
  timeSpent: number;
  sectionsOptimized: string[];
  keywordsAdded: number;
  atsScoreImprovement: number;
}

export interface RealTimeSuggestion {
  id: string;
  field: string;
  currentValue: string;
  suggestion: ContentSuggestion;
  timestamp: Date;
  isActive: boolean;
  userAction?: 'accepted' | 'rejected' | 'modified' | 'ignored';
}

export interface ContentHistory {
  id: string;
  resumeId: string;
  timestamp: Date;
  action: 'suggestion' | 'optimization' | 'generation' | 'analysis';
  section: string;
  originalContent: string;
  modifiedContent: string;
  suggestions: ContentSuggestion[];
  userFeedback?: {
    rating: number;
    comment?: string;
  };
}

// API Response Types
export interface AIContentAPIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: {
    processingTime: number;
    tokensUsed: number;
    cost: number;
    model: string;
    confidence: number;
  };
}

// Batch Processing Types
export interface BatchOptimizationRequest {
  resumes: string[]; // Resume IDs
  jobDescription?: string;
  optimizationType: ContentOptimizationRequest['optimizationType'];
  options?: {
    parallel: boolean;
    priority: 'speed' | 'quality';
    notifications: boolean;
  };
}

export interface BatchOptimizationResponse {
  jobId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number; // 0-100
  results: {
    resumeId: string;
    status: 'pending' | 'completed' | 'failed';
    optimization?: ContentOptimizationResponse;
    error?: string;
  }[];
  estimatedCompletion?: Date;
}

// Integration Types
export interface LinkedInIntegration {
  profileData: {
    headline: string;
    summary: string;
    experience: any[];
    skills: string[];
    education: any[];
  };
  suggestions: ContentSuggestion[];
  syncOptions: {
    autoImport: boolean;
    fields: string[];
    frequency: 'manual' | 'daily' | 'weekly';
  };
}

export interface JobBoardIntegration {
  source: 'linkedin' | 'indeed' | 'glassdoor' | 'custom';
  jobData: JobAnalysis;
  matchScore: number;
  recommendations: ContentSuggestion[];
  applicationOptimization: {
    coverLetterSuggestions: string[];
    keywordAlignment: string[];
    experienceHighlights: string[];
  };
}
