// Core User Types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
  subscription?: Subscription;
  profile?: UserProfile;
}

export interface UserProfile {
  id: string;
  userId: string;
  phone?: string;
  location?: string;
  website?: string;
  linkedinUrl?: string;
  githubUrl?: string;
  summary?: string;
  skills: string[];
  experience: Experience[];
  education: Education[];
  certifications: Certification[];
  languages: Language[];
}

// Resume Types
export interface Resume {
  id: string;
  userId: string;
  title: string;
  templateId: string;
  content: ResumeContent;
  atsScore?: number;
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ResumeContent {
  personalInfo: PersonalInfo;
  summary: string;
  experience: Experience[];
  education: Education[];
  skills: string[];
  certifications: Certification[];
  languages: Language[];
  projects?: Project[];
  achievements?: Achievement[];
}

export interface PersonalInfo {
  fullName: string;
  email: string;
  phone: string;
  location: string;
  website?: string;
  linkedinUrl?: string;
  githubUrl?: string;
}

export interface Experience {
  id: string;
  company: string;
  position: string;
  location: string;
  startDate: Date;
  endDate?: Date;
  current: boolean;
  description: string[];
  skills: string[];
}

export interface Education {
  id: string;
  institution: string;
  degree: string;
  field: string;
  location: string;
  startDate: Date;
  endDate?: Date;
  gpa?: number;
  achievements?: string[];
}

export interface Certification {
  id: string;
  name: string;
  issuer: string;
  issueDate: Date;
  expiryDate?: Date;
  credentialId?: string;
  url?: string;
}

export interface Language {
  id: string;
  name: string;
  proficiency: 'Basic' | 'Conversational' | 'Fluent' | 'Native';
}

export interface Project {
  id: string;
  name: string;
  description: string;
  technologies: string[];
  url?: string;
  githubUrl?: string;
  startDate: Date;
  endDate?: Date;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  date: Date;
  category: string;
}

// Job Types
export interface Job {
  id: string;
  title: string;
  company: string;
  location: string;
  type: 'Full-time' | 'Part-time' | 'Contract' | 'Freelance' | 'Internship';
  remote: boolean;
  salary?: SalaryRange;
  description: string;
  requirements: string[];
  skills: string[];
  postedDate: Date;
  applicationDeadline?: Date;
  source: string;
  url: string;
  atsKeywords: string[];
}

export interface SalaryRange {
  min: number;
  max: number;
  currency: string;
  period: 'hourly' | 'monthly' | 'yearly';
}

// Application Tracking Types
export interface JobApplication {
  id: string;
  userId: string;
  jobId: string;
  resumeId: string;
  coverLetterId?: string;
  status: ApplicationStatus;
  appliedDate: Date;
  lastUpdated: Date;
  notes?: string;
  followUpDate?: Date;
  interviewDates: Date[];
  feedback?: string;
}

export type ApplicationStatus = 
  | 'saved'
  | 'applied'
  | 'under_review'
  | 'interview_scheduled'
  | 'interviewed'
  | 'offer_received'
  | 'rejected'
  | 'withdrawn';

// Template Types
export interface ResumeTemplate {
  id: string;
  name: string;
  category: string;
  description: string;
  preview: string;
  isPremium: boolean;
  atsOptimized: boolean;
  styles: TemplateStyles;
}

export interface TemplateStyles {
  colors: {
    primary: string;
    secondary: string;
    text: string;
    background: string;
  };
  fonts: {
    heading: string;
    body: string;
  };
  layout: 'single-column' | 'two-column' | 'modern' | 'classic';
}

// AI Agent Types
export interface AgentResponse {
  success: boolean;
  data?: any;
  error?: string;
  suggestions?: string[];
}

export interface ResumeAnalysis {
  atsScore: number;
  missingKeywords: string[];
  suggestions: string[];
  strengths: string[];
  weaknesses: string[];
  industryMatch: number;
}

// Subscription Types
export interface Subscription {
  id: string;
  userId: string;
  plan: 'free' | 'pro' | 'enterprise';
  status: 'active' | 'canceled' | 'past_due';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
}

// Analytics Types
export interface UserAnalytics {
  userId: string;
  totalApplications: number;
  responseRate: number;
  interviewRate: number;
  offerRate: number;
  averageResponseTime: number;
  topSkills: string[];
  industryBreakdown: Record<string, number>;
  monthlyStats: MonthlyStats[];
}

export interface MonthlyStats {
  month: string;
  applications: number;
  responses: number;
  interviews: number;
  offers: number;
}
