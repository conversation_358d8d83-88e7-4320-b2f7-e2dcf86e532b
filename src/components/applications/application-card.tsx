'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Building, 
  MapPin, 
  Calendar, 
  DollarSign,
  ExternalLink,
  MoreHorizontal,
  Edit,
  Trash2,
  Clock,
  FileText,
  MessageSquare
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';

interface Application {
  id: string;
  status: string;
  appliedDate: string;
  lastUpdated: string;
  notes?: string;
  followUpDate?: string;
  interviewDates: string[];
  feedback?: string;
  job: {
    id: string;
    title: string;
    company: string;
    location: string;
    type: string;
    remote: boolean;
    salaryMin?: number;
    salaryMax?: number;
    salaryCurrency?: string;
    salaryPeriod?: string;
    url: string;
    source: string;
    postedDate: string;
  };
  resume: {
    id: string;
    title: string;
  };
  coverLetter?: {
    id: string;
    title: string;
  };
}

interface ApplicationCardProps {
  application: Application;
  onStatusChange: (applicationId: string, newStatus: string) => void;
  onUpdate: () => void;
  variant?: 'kanban' | 'list';
}

const APPLICATION_STATUSES = [
  { key: 'SAVED', label: 'Saved' },
  { key: 'APPLIED', label: 'Applied' },
  { key: 'UNDER_REVIEW', label: 'Under Review' },
  { key: 'INTERVIEW_SCHEDULED', label: 'Interview Scheduled' },
  { key: 'INTERVIEWED', label: 'Interviewed' },
  { key: 'OFFER_RECEIVED', label: 'Offer Received' },
  { key: 'REJECTED', label: 'Rejected' },
  { key: 'WITHDRAWN', label: 'Withdrawn' },
];

export default function ApplicationCard({ 
  application, 
  onStatusChange, 
  onUpdate, 
  variant = 'kanban' 
}: ApplicationCardProps) {
  const [loading, setLoading] = useState(false);

  const formatSalary = (min?: number, max?: number, currency = 'USD', period = 'YEARLY') => {
    if (!min && !max) return null;
    
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });

    const periodText = period === 'HOURLY' ? '/hr' : period === 'MONTHLY' ? '/mo' : '/yr';
    
    if (min && max) {
      return `${formatter.format(min)} - ${formatter.format(max)}${periodText}`;
    } else if (min) {
      return `${formatter.format(min)}+${periodText}`;
    } else if (max) {
      return `Up to ${formatter.format(max)}${periodText}`;
    }
    return null;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  };

  const getStatusColor = (status: string) => {
    const colors = {
      SAVED: 'bg-gray-100 text-gray-800',
      APPLIED: 'bg-blue-100 text-blue-800',
      UNDER_REVIEW: 'bg-yellow-100 text-yellow-800',
      INTERVIEW_SCHEDULED: 'bg-purple-100 text-purple-800',
      INTERVIEWED: 'bg-indigo-100 text-indigo-800',
      OFFER_RECEIVED: 'bg-green-100 text-green-800',
      REJECTED: 'bg-red-100 text-red-800',
      WITHDRAWN: 'bg-gray-100 text-gray-800',
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const handleStatusChange = async (newStatus: string) => {
    setLoading(true);
    try {
      await onStatusChange(application.id, newStatus);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (confirm('Are you sure you want to delete this application?')) {
      setLoading(true);
      try {
        const response = await fetch(`/api/applications/${application.id}`, {
          method: 'DELETE',
        });
        if (response.ok) {
          onUpdate();
        }
      } catch (error) {
        console.error('Error deleting application:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  const salary = formatSalary(
    application.job.salaryMin,
    application.job.salaryMax,
    application.job.salaryCurrency,
    application.job.salaryPeriod
  );

  if (variant === 'list') {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-start justify-between mb-2">
                <h3 className="text-lg font-semibold text-gray-900">
                  {application.job.title}
                </h3>
                <Badge className={`${getStatusColor(application.status)} border-0 ml-4`}>
                  {APPLICATION_STATUSES.find(s => s.key === application.status)?.label}
                </Badge>
              </div>
              
              <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                <div className="flex items-center gap-1">
                  <Building className="h-4 w-4" />
                  <span>{application.job.company}</span>
                </div>
                <div className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  <span>{application.job.location}</span>
                  {application.job.remote && <Badge variant="secondary" className="ml-1">Remote</Badge>}
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>Applied {formatDate(application.appliedDate)}</span>
                </div>
              </div>

              {salary && (
                <div className="flex items-center gap-1 text-sm text-green-600 mb-3">
                  <DollarSign className="h-4 w-4" />
                  <span className="font-medium">{salary}</span>
                </div>
              )}

              <div className="flex items-center gap-4 text-xs text-gray-500">
                <div className="flex items-center gap-1">
                  <FileText className="h-3 w-3" />
                  <span>{application.resume.title}</span>
                </div>
                {application.coverLetter && (
                  <div className="flex items-center gap-1">
                    <FileText className="h-3 w-3" />
                    <span>{application.coverLetter.title}</span>
                  </div>
                )}
                {application.notes && (
                  <div className="flex items-center gap-1">
                    <MessageSquare className="h-3 w-3" />
                    <span>Has notes</span>
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2 ml-4">
              <Button variant="outline" size="sm" asChild>
                <a href={application.job.url} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4" />
                </a>
              </Button>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" disabled={loading}>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Application
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  {APPLICATION_STATUSES.filter(s => s.key !== application.status).map((status) => (
                    <DropdownMenuItem
                      key={status.key}
                      onClick={() => handleStatusChange(status.key)}
                    >
                      Move to {status.label}
                    </DropdownMenuItem>
                  ))}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleDelete} className="text-red-600">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Application
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer">
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-start justify-between">
            <h4 className="font-medium text-sm text-gray-900 line-clamp-2">
              {application.job.title}
            </h4>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" disabled={loading}>
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                {APPLICATION_STATUSES.filter(s => s.key !== application.status).map((status) => (
                  <DropdownMenuItem
                    key={status.key}
                    onClick={() => handleStatusChange(status.key)}
                  >
                    Move to {status.label}
                  </DropdownMenuItem>
                ))}
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleDelete} className="text-red-600">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <div className="space-y-2 text-xs text-gray-600">
            <div className="flex items-center gap-1">
              <Building className="h-3 w-3" />
              <span className="truncate">{application.job.company}</span>
            </div>
            
            <div className="flex items-center gap-1">
              <MapPin className="h-3 w-3" />
              <span className="truncate">{application.job.location}</span>
            </div>

            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>{formatDate(application.appliedDate)}</span>
            </div>

            {salary && (
              <div className="flex items-center gap-1 text-green-600">
                <DollarSign className="h-3 w-3" />
                <span className="font-medium text-xs">{salary}</span>
              </div>
            )}
          </div>

          <div className="flex items-center justify-between pt-2">
            <div className="flex items-center gap-1">
              {application.notes && (
                <MessageSquare className="h-3 w-3 text-gray-400" />
              )}
              {application.interviewDates.length > 0 && (
                <Clock className="h-3 w-3 text-purple-500" />
              )}
            </div>
            
            <Button variant="ghost" size="sm" asChild>
              <a href={application.job.url} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-3 w-3" />
              </a>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
