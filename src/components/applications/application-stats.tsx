'use client';

import { Card, CardContent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Briefcase, 
  Clock, 
  CheckCircle, 
  TrendingUp,
  Users,
  Calendar,
  Target,
  Award
} from 'lucide-react';

interface ApplicationStatsProps {
  statusCounts: Record<string, number>;
}

export default function ApplicationStats({ statusCounts }: ApplicationStatsProps) {
  const totalApplications = Object.values(statusCounts).reduce((sum, count) => sum + count, 0);
  const activeApplications = (statusCounts.APPLIED || 0) + 
                           (statusCounts.UNDER_REVIEW || 0) + 
                           (statusCounts.INTERVIEW_SCHEDULED || 0) + 
                           (statusCounts.INTERVIEWED || 0);
  const successfulApplications = statusCounts.OFFER_RECEIVED || 0;
  const rejectedApplications = statusCounts.REJECTED || 0;

  const responseRate = totalApplications > 0 
    ? Math.round(((activeApplications + successfulApplications) / totalApplications) * 100)
    : 0;

  const successRate = totalApplications > 0 
    ? Math.round((successfulApplications / totalApplications) * 100)
    : 0;

  const stats = [
    {
      title: 'Total Applications',
      value: totalApplications,
      icon: Briefcase,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      description: 'All time applications',
    },
    {
      title: 'Active Applications',
      value: activeApplications,
      icon: Clock,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      description: 'In progress',
    },
    {
      title: 'Response Rate',
      value: `${responseRate}%`,
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      description: 'Companies that responded',
    },
    {
      title: 'Success Rate',
      value: `${successRate}%`,
      icon: Award,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      description: 'Offers received',
    },
  ];

  const statusBreakdown = [
    { key: 'SAVED', label: 'Saved', count: statusCounts.SAVED || 0, color: 'bg-gray-100 text-gray-800' },
    { key: 'APPLIED', label: 'Applied', count: statusCounts.APPLIED || 0, color: 'bg-blue-100 text-blue-800' },
    { key: 'UNDER_REVIEW', label: 'Under Review', count: statusCounts.UNDER_REVIEW || 0, color: 'bg-yellow-100 text-yellow-800' },
    { key: 'INTERVIEW_SCHEDULED', label: 'Interview Scheduled', count: statusCounts.INTERVIEW_SCHEDULED || 0, color: 'bg-purple-100 text-purple-800' },
    { key: 'INTERVIEWED', label: 'Interviewed', count: statusCounts.INTERVIEWED || 0, color: 'bg-indigo-100 text-indigo-800' },
    { key: 'OFFER_RECEIVED', label: 'Offer Received', count: statusCounts.OFFER_RECEIVED || 0, color: 'bg-green-100 text-green-800' },
    { key: 'REJECTED', label: 'Rejected', count: statusCounts.REJECTED || 0, color: 'bg-red-100 text-red-800' },
    { key: 'WITHDRAWN', label: 'Withdrawn', count: statusCounts.WITHDRAWN || 0, color: 'bg-gray-100 text-gray-800' },
  ];

  return (
    <div className="space-y-6">
      {/* Main Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.title}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Status Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Application Status Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            {statusBreakdown.map((status) => (
              <div key={status.key} className="text-center">
                <div className="mb-2">
                  <Badge className={`${status.color} border-0 text-lg font-bold px-3 py-1`}>
                    {status.count}
                  </Badge>
                </div>
                <p className="text-xs text-gray-600 font-medium">{status.label}</p>
              </div>
            ))}
          </div>

          {totalApplications > 0 && (
            <div className="mt-6 pt-6 border-t">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Progress Overview</span>
                <span className="text-gray-900 font-medium">
                  {successfulApplications} offers from {totalApplications} applications
                </span>
              </div>
              
              <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-blue-500 via-purple-500 to-green-500 h-2 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${Math.max(5, (activeApplications + successfulApplications) / totalApplications * 100)}%` 
                  }}
                ></div>
              </div>
              
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>Started</span>
                <span>In Progress</span>
                <span>Success</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Insights */}
      {totalApplications > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-full">
                  <Target className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {responseRate}% Response Rate
                  </p>
                  <p className="text-xs text-gray-600">
                    {responseRate >= 20 ? 'Great response rate!' : 'Keep applying to improve this'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-full">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {successRate}% Success Rate
                  </p>
                  <p className="text-xs text-gray-600">
                    {successRate >= 5 ? 'Excellent conversion!' : 'Focus on quality applications'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-full">
                  <Calendar className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {statusCounts.INTERVIEW_SCHEDULED || 0} Interviews
                  </p>
                  <p className="text-xs text-gray-600">
                    {(statusCounts.INTERVIEW_SCHEDULED || 0) > 0 ? 'Upcoming interviews scheduled' : 'No interviews scheduled'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
