'use client';

import React, { useState } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  Wand2, 
  <PERSON><PERSON>, 
  <PERSON>fresh<PERSON><PERSON>, 
  <PERSON>Circle,
  ArrowRight,
  Lightbulb,
  Target,
  Zap,
  Settings
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useAIContent } from '@/lib/hooks/useAIContent';
import { SmartContentGeneration, SmartContentResponse } from '@/types/ai-content';
import { ResumeContent } from '@/types';

interface SmartContentGeneratorProps {
  resumeContent: ResumeContent;
  section: 'summary' | 'experience' | 'skills' | 'projects';
  onContentGenerated?: (content: string | string[]) => void;
  onContentApply?: (content: string | string[]) => void;
  className?: string;
}

export default function SmartContentGenerator({
  resumeContent,
  section,
  onContentGenerated,
  onContentApply,
  className = ''
}: SmartContentGeneratorProps) {
  const [generationOptions, setGenerationOptions] = useState<SmartContentGeneration['options']>({
    length: 'medium',
    style: 'bullet',
    focus: 'achievements',
    tone: 'professional'
  });
  
  const [generatedContent, setGeneratedContent] = useState<SmartContentResponse | null>(null);
  const [selectedAlternative, setSelectedAlternative] = useState<number>(0);
  const [customContext, setCustomContext] = useState('');

  const { generateContent, isGenerating } = useAIContent({
    resumeContent,
    enableRealTime: false,
    autoAnalyze: false
  });

  const handleGenerate = async () => {
    const context = {
      role: resumeContent.personalInfo?.jobTitle || '',
      industry: '', // Could be derived from experience
      experience: resumeContent.experience || [],
      skills: resumeContent.skills || [],
      achievements: resumeContent.experience?.flatMap(exp => exp.description) || []
    };

    if (customContext.trim()) {
      context.achievements.push(customContext);
    }

    const request: SmartContentGeneration = {
      section,
      context,
      options: generationOptions
    };

    const result = await generateContent(request);
    if (result) {
      setGeneratedContent(result);
      setSelectedAlternative(0);
      onContentGenerated?.(result.generatedContent);
    }
  };

  const handleApplyContent = () => {
    if (!generatedContent) return;
    
    const contentToApply = selectedAlternative === 0 
      ? generatedContent.generatedContent
      : generatedContent.alternatives[selectedAlternative - 1];
    
    onContentApply?.(contentToApply);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getContentPreview = () => {
    if (!generatedContent) return '';
    
    if (selectedAlternative === 0) {
      return Array.isArray(generatedContent.generatedContent) 
        ? generatedContent.generatedContent.join('\n• ')
        : generatedContent.generatedContent;
    }
    
    const alternative = generatedContent.alternatives[selectedAlternative - 1];
    return Array.isArray(alternative) ? alternative.join('\n• ') : alternative;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center space-x-2">
        <Wand2 className="h-5 w-5 text-purple-600" />
        <h3 className="text-lg font-semibold">Smart Content Generator</h3>
        <Badge variant="outline" className="capitalize">{section}</Badge>
      </div>

      {/* Generation Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-4 w-4" />
            <span>Generation Options</span>
          </CardTitle>
          <CardDescription>
            Customize how AI generates content for your {section} section
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="length">Content Length</Label>
              <Select
                value={generationOptions.length}
                onValueChange={(value: 'short' | 'medium' | 'long') => 
                  setGenerationOptions(prev => ({ ...prev, length: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="short">Short & Concise</SelectItem>
                  <SelectItem value="medium">Medium Detail</SelectItem>
                  <SelectItem value="long">Comprehensive</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="style">Writing Style</Label>
              <Select
                value={generationOptions.style}
                onValueChange={(value: 'bullet' | 'paragraph' | 'hybrid') => 
                  setGenerationOptions(prev => ({ ...prev, style: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bullet">Bullet Points</SelectItem>
                  <SelectItem value="paragraph">Paragraph</SelectItem>
                  <SelectItem value="hybrid">Mixed Format</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="focus">Content Focus</Label>
              <Select
                value={generationOptions.focus}
                onValueChange={(value: 'achievements' | 'responsibilities' | 'skills' | 'impact') => 
                  setGenerationOptions(prev => ({ ...prev, focus: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="achievements">Achievements</SelectItem>
                  <SelectItem value="responsibilities">Responsibilities</SelectItem>
                  <SelectItem value="skills">Skills & Expertise</SelectItem>
                  <SelectItem value="impact">Business Impact</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tone">Writing Tone</Label>
              <Select
                value={generationOptions.tone}
                onValueChange={(value: 'professional' | 'dynamic' | 'technical' | 'leadership') => 
                  setGenerationOptions(prev => ({ ...prev, tone: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="professional">Professional</SelectItem>
                  <SelectItem value="dynamic">Dynamic & Energetic</SelectItem>
                  <SelectItem value="technical">Technical & Precise</SelectItem>
                  <SelectItem value="leadership">Leadership Focused</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Custom Context */}
          <div className="space-y-2">
            <Label htmlFor="context">Additional Context (Optional)</Label>
            <Textarea
              id="context"
              placeholder="Add any specific achievements, projects, or details you want to highlight..."
              value={customContext}
              onChange={(e) => setCustomContext(e.target.value)}
              rows={3}
            />
          </div>

          <Button 
            onClick={handleGenerate} 
            disabled={isGenerating}
            className="w-full"
          >
            {isGenerating ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4 mr-2" />
                Generate Content
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Generated Content */}
      {generatedContent && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Lightbulb className="h-4 w-4 text-yellow-500" />
                <span>Generated Content</span>
              </div>
              <Badge className="bg-green-100 text-green-800">
                {Math.round(generatedContent.confidence * 100)}% confidence
              </Badge>
            </CardTitle>
            <CardDescription>
              {generatedContent.reasoning}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Content Alternatives */}
            <Tabs value={selectedAlternative.toString()} onValueChange={(value) => setSelectedAlternative(parseInt(value))}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="0">Original</TabsTrigger>
                {generatedContent.alternatives.slice(0, 3).map((_, index) => (
                  <TabsTrigger key={index + 1} value={(index + 1).toString()}>
                    Alt {index + 1}
                  </TabsTrigger>
                ))}
              </TabsList>

              <TabsContent value="0" className="space-y-3">
                <ContentPreview content={generatedContent.generatedContent} />
              </TabsContent>

              {generatedContent.alternatives.slice(0, 3).map((alternative, index) => (
                <TabsContent key={index + 1} value={(index + 1).toString()} className="space-y-3">
                  <ContentPreview content={alternative} />
                </TabsContent>
              ))}
            </Tabs>

            {/* Keywords */}
            {generatedContent.keywords.length > 0 && (
              <div className="space-y-2">
                <Label>Keywords Included:</Label>
                <div className="flex flex-wrap gap-2">
                  {generatedContent.keywords.map((keyword, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {keyword}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Suggestions */}
            {generatedContent.suggestions.length > 0 && (
              <div className="space-y-2">
                <Label>Additional Suggestions:</Label>
                <ul className="space-y-1">
                  {generatedContent.suggestions.map((suggestion, index) => (
                    <li key={index} className="flex items-start space-x-2 text-sm text-gray-600">
                      <Target className="h-3 w-3 mt-1 text-blue-500" />
                      <span>{suggestion}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            <Separator />

            {/* Actions */}
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                onClick={() => copyToClipboard(getContentPreview())}
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy
              </Button>

              <div className="flex items-center space-x-2">
                <Button variant="outline" onClick={handleGenerate}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Regenerate
                </Button>
                <Button onClick={handleApplyContent}>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Apply Content
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Content Preview Component
interface ContentPreviewProps {
  content: string | string[];
}

function ContentPreview({ content }: ContentPreviewProps) {
  const displayContent = Array.isArray(content) ? content : [content];

  return (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
      {displayContent.map((item, index) => (
        <div key={index} className="flex items-start space-x-2 mb-2 last:mb-0">
          {Array.isArray(content) && <span className="text-gray-400">•</span>}
          <span className="text-sm text-gray-900 leading-relaxed">{item}</span>
        </div>
      ))}
    </div>
  );
}
