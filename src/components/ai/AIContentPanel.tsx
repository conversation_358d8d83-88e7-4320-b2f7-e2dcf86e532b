'use client';

import React, { useState } from 'react';
import { 
  Brain, 
  Sparkles, 
  Target, 
  TrendingUp, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  RefreshCw,
  Lightbulb,
  Zap,
  BarChart3,
  Settings,
  Eye,
  EyeOff
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { useAIContent } from '@/lib/hooks/useAIContent';
import { ContentSuggestion } from '@/types/ai-content';
import { ResumeContent } from '@/types';

interface AIContentPanelProps {
  resumeContent: ResumeContent;
  jobDescription?: string;
  onContentUpdate?: (content: Partial<ResumeContent>) => void;
  onSuggestionApply?: (suggestion: ContentSuggestion) => void;
  className?: string;
}

export default function AIContentPanel({
  resumeContent,
  jobDescription,
  onContentUpdate,
  onSuggestionApply,
  className = ''
}: AIContentPanelProps) {
  const [enableRealTime, setEnableRealTime] = useState(true);
  const [showPreview, setShowPreview] = useState(false);
  const [selectedOptimization, setSelectedOptimization] = useState<'ats' | 'keywords' | 'impact' | 'comprehensive'>('comprehensive');

  const {
    analysis,
    suggestions,
    realTimeSuggestions,
    atsOptimization,
    isAnalyzing,
    isGenerating,
    isOptimizing,
    error,
    metrics,
    analyzeContent,
    optimizeContent,
    analyzeATS,
    applySuggestion,
    rejectSuggestion,
    dismissSuggestion,
    getSuggestionsBySection,
    getHighImpactSuggestions,
    calculateImprovementScore
  } = useAIContent({
    resumeContent,
    jobDescription,
    enableRealTime,
    autoAnalyze: true
  });

  const handleApplySuggestion = (suggestion: ContentSuggestion) => {
    applySuggestion(suggestion.id);
    onSuggestionApply?.(suggestion);
  };

  const handleOptimizeContent = async () => {
    const result = await optimizeContent({
      resumeContent,
      jobDescription,
      optimizationType: selectedOptimization,
      userPreferences: {
        tone: 'professional',
        length: 'balanced',
        focus: 'achievements'
      }
    });

    if (result?.optimizedContent) {
      onContentUpdate?.(result.optimizedContent);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getImpactBadgeColor = (impact: ContentSuggestion['impact']) => {
    switch (impact) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Brain className="h-5 w-5 text-purple-600" />
          <h2 className="text-lg font-semibold">AI Content Assistant</h2>
          {isAnalyzing && <RefreshCw className="h-4 w-4 animate-spin text-gray-400" />}
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Switch
              checked={enableRealTime}
              onCheckedChange={setEnableRealTime}
              id="real-time"
            />
            <label htmlFor="real-time" className="text-sm text-gray-600">
              Real-time suggestions
            </label>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={analyzeContent}
            disabled={isAnalyzing}
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-4">
            <div className="flex items-center space-x-2 text-red-800">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Overview Cards */}
      {analysis && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Overall Score</p>
                  <p className={`text-2xl font-bold ${getScoreColor(analysis.overallScore)}`}>
                    {analysis.overallScore}
                  </p>
                </div>
                <BarChart3 className="h-8 w-8 text-gray-400" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">ATS Score</p>
                  <p className={`text-2xl font-bold ${getScoreColor(analysis.atsScore)}`}>
                    {analysis.atsScore}
                  </p>
                </div>
                <Target className="h-8 w-8 text-gray-400" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Suggestions</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {suggestions.length}
                  </p>
                </div>
                <Lightbulb className="h-8 w-8 text-gray-400" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Potential Score</p>
                  <p className={`text-2xl font-bold ${getScoreColor(calculateImprovementScore())}`}>
                    {calculateImprovementScore()}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-gray-400" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs defaultValue="suggestions" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="suggestions">
            <Sparkles className="h-4 w-4 mr-1" />
            Suggestions
          </TabsTrigger>
          <TabsTrigger value="optimization">
            <Zap className="h-4 w-4 mr-1" />
            Optimize
          </TabsTrigger>
          <TabsTrigger value="ats">
            <Target className="h-4 w-4 mr-1" />
            ATS Analysis
          </TabsTrigger>
          <TabsTrigger value="insights">
            <BarChart3 className="h-4 w-4 mr-1" />
            Insights
          </TabsTrigger>
        </TabsList>

        {/* Suggestions Tab */}
        <TabsContent value="suggestions" className="space-y-4">
          {/* High Impact Suggestions */}
          {getHighImpactSuggestions().length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Zap className="h-5 w-5 text-orange-500" />
                  <span>High Impact Suggestions</span>
                </CardTitle>
                <CardDescription>
                  These suggestions can significantly improve your resume
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {getHighImpactSuggestions().map((suggestion) => (
                  <SuggestionCard
                    key={suggestion.id}
                    suggestion={suggestion}
                    onApply={() => handleApplySuggestion(suggestion)}
                    onReject={() => rejectSuggestion(suggestion.id)}
                  />
                ))}
              </CardContent>
            </Card>
          )}

          {/* All Suggestions by Section */}
          {['summary', 'experience', 'skills', 'education'].map((section) => {
            const sectionSuggestions = getSuggestionsBySection(section);
            if (sectionSuggestions.length === 0) return null;

            return (
              <Card key={section}>
                <CardHeader>
                  <CardTitle className="capitalize">{section} Suggestions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {sectionSuggestions.map((suggestion) => (
                    <SuggestionCard
                      key={suggestion.id}
                      suggestion={suggestion}
                      onApply={() => handleApplySuggestion(suggestion)}
                      onReject={() => rejectSuggestion(suggestion.id)}
                    />
                  ))}
                </CardContent>
              </Card>
            );
          })}

          {suggestions.length === 0 && !isAnalyzing && (
            <Card>
              <CardContent className="pt-8 pb-8 text-center">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Great job! No suggestions at the moment.
                </h3>
                <p className="text-gray-600">
                  Your resume looks good. Try adding a job description for more targeted suggestions.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Optimization Tab */}
        <TabsContent value="optimization" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Content Optimization</CardTitle>
              <CardDescription>
                Automatically optimize your resume for better performance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                {(['ats', 'keywords', 'impact', 'comprehensive'] as const).map((type) => (
                  <Button
                    key={type}
                    variant={selectedOptimization === type ? 'default' : 'outline'}
                    onClick={() => setSelectedOptimization(type)}
                    className="justify-start"
                  >
                    <span className="capitalize">{type}</span>
                  </Button>
                ))}
              </div>
              
              <Button
                onClick={handleOptimizeContent}
                disabled={isOptimizing}
                className="w-full"
              >
                {isOptimizing ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Optimizing...
                  </>
                ) : (
                  <>
                    <Zap className="h-4 w-4 mr-2" />
                    Optimize Content
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* ATS Analysis Tab */}
        <TabsContent value="ats" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>ATS Compatibility Analysis</CardTitle>
              <CardDescription>
                Check how well your resume works with Applicant Tracking Systems
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={analyzeATS} disabled={isAnalyzing} className="mb-4">
                <Target className="h-4 w-4 mr-2" />
                Analyze ATS Compatibility
              </Button>
              
              {atsOptimization && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">ATS Score</span>
                    <span className={`text-lg font-bold ${getScoreColor(atsOptimization.score)}`}>
                      {atsOptimization.score}/100
                    </span>
                  </div>
                  <Progress value={atsOptimization.score} className="h-2" />
                  
                  {atsOptimization.issues.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium">Issues Found:</h4>
                      {atsOptimization.issues.map((issue, index) => (
                        <div key={index} className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                          <div className="flex items-start space-x-2">
                            <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5" />
                            <div>
                              <p className="text-sm font-medium text-yellow-800">{issue.description}</p>
                              <p className="text-sm text-yellow-700">{issue.suggestion}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value="insights" className="space-y-4">
          {analysis && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Strengths</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {analysis.strengths.map((strength, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                        <span className="text-sm">{strength}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Areas for Improvement</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {analysis.weaknesses.map((weakness, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <AlertCircle className="h-4 w-4 text-yellow-500 mt-0.5" />
                        <span className="text-sm">{weakness}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Suggestion Card Component
interface SuggestionCardProps {
  suggestion: ContentSuggestion;
  onApply: () => void;
  onReject: () => void;
}

function SuggestionCard({ suggestion, onApply, onReject }: SuggestionCardProps) {
  const getImpactBadgeColor = (impact: ContentSuggestion['impact']) => {
    switch (impact) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-4 border border-gray-200 rounded-lg space-y-3">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <Badge className={getImpactBadgeColor(suggestion.impact)}>
              {suggestion.impact} impact
            </Badge>
            <Badge variant="outline" className="text-xs">
              {Math.round(suggestion.confidence * 100)}% confidence
            </Badge>
          </div>
          <p className="text-sm text-gray-900 mb-2">{suggestion.reason}</p>
          {suggestion.suggested && (
            <div className="bg-green-50 border border-green-200 rounded p-2">
              <p className="text-sm text-green-800">{suggestion.suggested}</p>
            </div>
          )}
        </div>
      </div>
      
      <div className="flex items-center justify-end space-x-2">
        <Button variant="outline" size="sm" onClick={onReject}>
          <XCircle className="h-4 w-4 mr-1" />
          Reject
        </Button>
        <Button size="sm" onClick={onApply}>
          <CheckCircle className="h-4 w-4 mr-1" />
          Apply
        </Button>
      </div>
    </div>
  );
}
