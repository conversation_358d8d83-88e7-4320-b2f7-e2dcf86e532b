'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Linkedin, 
  MapPin, 
  Building, 
  Users, 
  Eye,
  ExternalLink,
  Download,
  Share2,
  User
} from 'lucide-react';

interface ProfilePreviewProps {
  profile: any;
}

export default function ProfilePreview({ profile }: ProfilePreviewProps) {
  if (!profile) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-gray-500">No profile data available</p>
        </CardContent>
      </Card>
    );
  }

  const handleExportProfile = () => {
    const profileData = {
      headline: profile.headline,
      summary: profile.summary,
      aboutSection: profile.aboutSection,
      currentPosition: profile.currentPosition,
      currentCompany: profile.currentCompany,
      location: profile.location,
      industry: profile.industry,
      isOpenToWork: profile.isOpenToWork,
    };

    const dataStr = JSON.stringify(profileData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = 'linkedin-profile-data.json';
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  return (
    <div className="space-y-6">
      {/* Preview Actions */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-gray-900 mb-1">Profile Preview</h3>
              <p className="text-sm text-gray-600">
                See how your optimized profile will look to recruiters and connections
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline" onClick={handleExportProfile}>
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </Button>
              {profile.profileUrl && (
                <Button variant="outline" asChild>
                  <a href={profile.profileUrl} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    View on LinkedIn
                  </a>
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* LinkedIn-Style Profile Preview */}
      <Card className="overflow-hidden">
        <div className="h-32 bg-gradient-to-r from-blue-600 to-blue-800"></div>
        <CardContent className="p-0">
          {/* Profile Header */}
          <div className="px-6 pb-6">
            <div className="flex items-start gap-6 -mt-16">
              {/* Profile Image */}
              <div className="relative">
                {profile.profileImageUrl ? (
                  <img
                    src={profile.profileImageUrl}
                    alt="Profile"
                    className="w-32 h-32 rounded-full border-4 border-white bg-white object-cover"
                  />
                ) : (
                  <div className="w-32 h-32 rounded-full border-4 border-white bg-gray-200 flex items-center justify-center">
                    <User className="h-16 w-16 text-gray-400" />
                  </div>
                )}
                {profile.isOpenToWork && (
                  <div className="absolute -bottom-2 -right-2">
                    <Badge className="bg-green-600 text-white">
                      #OpenToWork
                    </Badge>
                  </div>
                )}
              </div>

              {/* Profile Info */}
              <div className="flex-1 pt-16">
                <div className="flex items-start justify-between">
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900 mb-1">
                      {profile.name || 'Your Name'}
                    </h1>
                    <p className="text-lg text-gray-700 mb-2">
                      {profile.headline || 'Your Professional Headline'}
                    </p>
                    <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                      {profile.location && (
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          {profile.location}
                        </div>
                      )}
                      {profile.currentCompany && (
                        <div className="flex items-center gap-1">
                          <Building className="h-4 w-4" />
                          {profile.currentCompany}
                        </div>
                      )}
                      {profile.connectionsCount && (
                        <div className="flex items-center gap-1">
                          <Users className="h-4 w-4" />
                          {profile.connectionsCount} connections
                        </div>
                      )}
                    </div>
                    {profile.industry && (
                      <Badge variant="outline" className="mb-3">
                        {profile.industry}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button className="bg-blue-600 hover:bg-blue-700">
                      <Linkedin className="h-4 w-4 mr-2" />
                      Connect
                    </Button>
                    <Button variant="outline">
                      Message
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Profile Stats */}
          {(profile.profileViews || profile.searchAppearances) && (
            <div className="px-6 py-4 border-t bg-gray-50">
              <div className="flex items-center gap-6 text-sm">
                {profile.profileViews && (
                  <div className="flex items-center gap-2">
                    <Eye className="h-4 w-4 text-gray-500" />
                    <span className="text-gray-700">
                      {profile.profileViews} profile views
                    </span>
                  </div>
                )}
                {profile.searchAppearances && (
                  <div className="flex items-center gap-2">
                    <span className="text-gray-700">
                      {profile.searchAppearances} search appearances
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* About Section */}
      {(profile.summary || profile.aboutSection) && (
        <Card>
          <CardHeader>
            <CardTitle>About</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {profile.summary && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Professional Summary</h4>
                  <p className="text-gray-700 whitespace-pre-wrap leading-relaxed">
                    {profile.summary}
                  </p>
                </div>
              )}
              {profile.aboutSection && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">About</h4>
                  <p className="text-gray-700 whitespace-pre-wrap leading-relaxed">
                    {profile.aboutSection}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Experience Section */}
      {profile.currentPosition && (
        <Card>
          <CardHeader>
            <CardTitle>Experience</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
                <Building className="h-6 w-6 text-gray-500" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900">{profile.currentPosition}</h3>
                <p className="text-gray-600">{profile.currentCompany}</p>
                <p className="text-sm text-gray-500">Present</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Profile Optimization Score */}
      <Card>
        <CardHeader>
          <CardTitle>Profile Optimization Score</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 mb-1">
                {profile.profileScore || 0}%
              </div>
              <div className="text-sm text-gray-600">Overall Score</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 mb-1">
                {profile.completenessScore || 0}%
              </div>
              <div className="text-sm text-gray-600">Completeness</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600 mb-1">
                {profile.visibilityScore || 0}%
              </div>
              <div className="text-sm text-gray-600">Visibility</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600 mb-1">
                {profile.engagementScore || 0}%
              </div>
              <div className="text-sm text-gray-600">Engagement</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Keyword Analysis */}
      {profile.keywordSuggestions && profile.keywordSuggestions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Keyword Optimization</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <p className="text-sm text-gray-600">
                Keywords found in your profile that help with recruiter searches:
              </p>
              <div className="flex flex-wrap gap-2">
                {profile.keywordSuggestions.map((keyword: string, index: number) => (
                  <Badge key={index} variant="outline">
                    {keyword}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
