'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  <PERSON><PERSON>les, 
  Copy,
  RefreshCw,
  Lightbulb,
  Target,
  TrendingUp,
  CheckCircle,
  ArrowRight,
  Wand2
} from 'lucide-react';

interface OptimizationSuggestionsProps {
  profile: any;
  onUpdate: (updates: any) => void;
}

export default function OptimizationSuggestions({ profile, onUpdate }: OptimizationSuggestionsProps) {
  const [optimizing, setOptimizing] = useState<string | null>(null);
  const [suggestions, setSuggestions] = useState<any>({});

  const handleOptimizeSection = async (section: string, targetRole?: string, targetIndustry?: string) => {
    setOptimizing(section);
    try {
      const response = await fetch('/api/linkedin/optimize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          section,
          targetRole,
          targetIndustry,
          currentContent: profile?.[section],
          tone: 'professional',
          keywords: profile?.keywordSuggestions || [],
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setSuggestions(prev => ({
          ...prev,
          [section]: data.optimizedContent,
        }));
      }
    } catch (error) {
      console.error('Error optimizing section:', error);
    } finally {
      setOptimizing(null);
    }
  };

  const handleApplySuggestion = (section: string, content: string) => {
    onUpdate({ [section]: content });
  };

  const optimizationSections = [
    {
      id: 'headline',
      title: 'Professional Headline',
      description: 'Your headline is the first thing recruiters see. Make it count.',
      icon: Target,
      currentContent: profile?.headline,
      suggestions: profile?.headlineSuggestions || suggestions.headline?.suggestions || [],
      maxLength: 220,
    },
    {
      id: 'summary',
      title: 'Professional Summary',
      description: 'A compelling summary that highlights your value proposition.',
      icon: TrendingUp,
      currentContent: profile?.summary,
      suggestions: profile?.summarySuggestions || suggestions.summary?.suggestions || [],
      maxLength: 2000,
    },
    {
      id: 'about',
      title: 'About Section',
      description: 'Tell your professional story in a more personal way.',
      icon: Lightbulb,
      currentContent: profile?.aboutSection,
      suggestions: suggestions.about?.suggestions || [],
      maxLength: 2600,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5" />
            AI-Powered Optimization
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {optimizationSections.map((section) => {
              const Icon = section.icon;
              const hasContent = section.currentContent && section.currentContent.trim() !== '';
              const hasSuggestions = section.suggestions.length > 0;
              
              return (
                <div key={section.id} className="p-4 border rounded-lg">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Icon className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{section.title}</h3>
                      <p className="text-xs text-gray-600">{section.description}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {hasContent ? (
                        <Badge className="bg-green-100 text-green-800">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Complete
                        </Badge>
                      ) : (
                        <Badge className="bg-yellow-100 text-yellow-800">
                          Missing
                        </Badge>
                      )}
                      {hasSuggestions && (
                        <Badge className="bg-blue-100 text-blue-800">
                          {section.suggestions.length} suggestions
                        </Badge>
                      )}
                    </div>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleOptimizeSection(section.id)}
                      disabled={optimizing === section.id}
                    >
                      {optimizing === section.id ? (
                        <RefreshCw className="h-3 w-3 animate-spin" />
                      ) : (
                        <Sparkles className="h-3 w-3" />
                      )}
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Suggestions */}
      {optimizationSections.map((section) => {
        if (section.suggestions.length === 0) return null;
        
        const Icon = section.icon;
        
        return (
          <Card key={section.id}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon className="h-5 w-5" />
                {section.title} Suggestions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Current Content */}
              {section.currentContent && (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">Current Content</h4>
                  <p className="text-sm text-gray-700 whitespace-pre-wrap">
                    {section.currentContent}
                  </p>
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-xs text-gray-500">
                      {section.currentContent.length} / {section.maxLength} characters
                    </span>
                  </div>
                </div>
              )}

              {/* AI Suggestions */}
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">AI-Generated Suggestions</h4>
                {section.suggestions.map((suggestion: string, index: number) => (
                  <div key={index} className="p-4 border border-blue-200 rounded-lg bg-blue-50">
                    <div className="flex items-start justify-between mb-2">
                      <Badge className="bg-blue-100 text-blue-800">
                        Option {index + 1}
                      </Badge>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => navigator.clipboard.writeText(suggestion)}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleApplySuggestion(section.id, suggestion)}
                          className="flex items-center gap-1"
                        >
                          Apply
                          <ArrowRight className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-sm text-gray-700 whitespace-pre-wrap">
                      {suggestion}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-xs text-gray-500">
                        {suggestion.length} / {section.maxLength} characters
                      </span>
                      {suggestion.length > section.maxLength && (
                        <Badge className="bg-red-100 text-red-800">
                          Too long
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Generate More Button */}
              <Button
                variant="outline"
                onClick={() => handleOptimizeSection(section.id)}
                disabled={optimizing === section.id}
                className="w-full flex items-center gap-2"
              >
                {optimizing === section.id ? (
                  <>
                    <RefreshCw className="h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4" />
                    Generate More Suggestions
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        );
      })}

      {/* Skills Optimization */}
      {suggestions.skills && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Skills Optimization
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {suggestions.skills.recommended && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Recommended Skills</h4>
                <div className="flex flex-wrap gap-2">
                  {suggestions.skills.recommended.map((skill: string, index: number) => (
                    <Badge key={index} className="bg-green-100 text-green-800">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {suggestions.skills.toAdd && suggestions.skills.toAdd.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Skills to Add</h4>
                <div className="flex flex-wrap gap-2">
                  {suggestions.skills.toAdd.map((skill: string, index: number) => (
                    <Badge key={index} className="bg-blue-100 text-blue-800">
                      + {skill}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {suggestions.skills.priority && suggestions.skills.priority.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Priority Skills</h4>
                <div className="flex flex-wrap gap-2">
                  {suggestions.skills.priority.map((skill: string, index: number) => (
                    <Badge key={index} className="bg-yellow-100 text-yellow-800">
                      ⭐ {skill}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Generate Skills Suggestions */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-gray-900 mb-1">Skills Optimization</h3>
              <p className="text-sm text-gray-600">
                Get AI-powered skill recommendations for your target role
              </p>
            </div>
            <Button
              onClick={() => handleOptimizeSection('skills')}
              disabled={optimizing === 'skills'}
              className="flex items-center gap-2"
            >
              {optimizing === 'skills' ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4" />
                  Optimize Skills
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
