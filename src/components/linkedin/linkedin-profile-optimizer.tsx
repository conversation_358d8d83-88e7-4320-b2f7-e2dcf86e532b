'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Linkedin, 
  TrendingUp, 
  Target, 
  Lightbulb,
  CheckCircle,
  AlertCircle,
  Sparkles,
  BarChart3,
  Users,
  Eye
} from 'lucide-react';
import ProfileEditor from './profile-editor';
import ProfileAnalysis from './profile-analysis';
import OptimizationSuggestions from './optimization-suggestions';
import ProfilePreview from './profile-preview';

interface LinkedInProfile {
  id: string;
  headline?: string;
  industry?: string;
  location?: string;
  summary?: string;
  currentPosition?: string;
  currentCompany?: string;
  connectionsCount?: number;
  profileViews?: number;
  searchAppearances?: number;
  profileStrength?: string;
  profileUrl?: string;
  profileImageUrl?: string;
  isOpenToWork: boolean;
  profileScore?: number;
  completenessScore?: number;
  visibilityScore?: number;
  engagementScore?: number;
  missingFields: string[];
  optimizationTips: string[];
  keywordSuggestions: string[];
  headlineSuggestions: string[];
  summarySuggestions: string[];
  lastAnalyzed?: string;
  lastOptimized?: string;
}

export default function LinkedInProfileOptimizer() {
  const [profile, setProfile] = useState<LinkedInProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [analyzing, setAnalyzing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/linkedin/profile');
      if (response.ok) {
        const data = await response.json();
        setProfile(data);
      }
    } catch (error) {
      console.error('Error fetching LinkedIn profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAnalyzeProfile = async (targetRole?: string, targetIndustry?: string) => {
    setAnalyzing(true);
    try {
      const response = await fetch('/api/linkedin/analyze', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          targetRole,
          targetIndustry,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setProfile(data.profile);
      }
    } catch (error) {
      console.error('Error analyzing profile:', error);
    } finally {
      setAnalyzing(false);
    }
  };

  const handleUpdateProfile = async (updates: Partial<LinkedInProfile>) => {
    try {
      const response = await fetch('/api/linkedin/profile', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates),
      });

      if (response.ok) {
        const updatedProfile = await response.json();
        setProfile(updatedProfile);
      }
    } catch (error) {
      console.error('Error updating profile:', error);
    }
  };

  const getScoreColor = (score?: number) => {
    if (!score) return 'text-gray-500';
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeColor = (score?: number) => {
    if (!score) return 'bg-gray-100 text-gray-800';
    if (score >= 80) return 'bg-green-100 text-green-800';
    if (score >= 60) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Profile Score Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Overall Score</p>
                <p className={`text-2xl font-bold ${getScoreColor(profile?.profileScore)}`}>
                  {profile?.profileScore || 0}%
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <TrendingUp className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completeness</p>
                <p className={`text-2xl font-bold ${getScoreColor(profile?.completenessScore)}`}>
                  {profile?.completenessScore || 0}%
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Visibility</p>
                <p className={`text-2xl font-bold ${getScoreColor(profile?.visibilityScore)}`}>
                  {profile?.visibilityScore || 0}%
                </p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <Eye className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Engagement</p>
                <p className={`text-2xl font-bold ${getScoreColor(profile?.engagementScore)}`}>
                  {profile?.engagementScore || 0}%
                </p>
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <Users className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Profile Optimization
              </h3>
              <p className="text-gray-600">
                {profile?.lastAnalyzed 
                  ? `Last analyzed: ${new Date(profile.lastAnalyzed).toLocaleDateString()}`
                  : 'Run analysis to get personalized optimization recommendations'
                }
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                onClick={() => handleAnalyzeProfile()}
                disabled={analyzing}
                className="flex items-center gap-2"
              >
                {analyzing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Analyzing...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4" />
                    Analyze Profile
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Missing Fields Alert */}
          {profile?.missingFields && profile.missingFields.length > 0 && (
            <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-yellow-800">Complete Your Profile</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    Missing fields: {profile.missingFields.join(', ')}
                  </p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="editor">Profile Editor</TabsTrigger>
          <TabsTrigger value="optimization">Optimization</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <ProfileAnalysis 
            profile={profile}
            onAnalyze={handleAnalyzeProfile}
            analyzing={analyzing}
          />
        </TabsContent>

        <TabsContent value="editor" className="space-y-6">
          <ProfileEditor 
            profile={profile}
            onUpdate={handleUpdateProfile}
          />
        </TabsContent>

        <TabsContent value="optimization" className="space-y-6">
          <OptimizationSuggestions 
            profile={profile}
            onUpdate={handleUpdateProfile}
          />
        </TabsContent>

        <TabsContent value="preview" className="space-y-6">
          <ProfilePreview profile={profile} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
