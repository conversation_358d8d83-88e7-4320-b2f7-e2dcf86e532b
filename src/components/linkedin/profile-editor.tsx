'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Save, 
  Sparkles, 
  Copy,
  RefreshCw,
  User,
  Building,
  MapPin,
  Briefcase,
  FileText,
  Target
} from 'lucide-react';

interface ProfileEditorProps {
  profile: any;
  onUpdate: (updates: any) => void;
}

export default function ProfileEditor({ profile, onUpdate }: ProfileEditorProps) {
  const [formData, setFormData] = useState({
    headline: '',
    industry: '',
    location: '',
    summary: '',
    currentPosition: '',
    currentCompany: '',
    profileUrl: '',
    isOpenToWork: false,
    aboutSection: '',
    ...profile,
  });
  const [saving, setSaving] = useState(false);
  const [optimizing, setOptimizing] = useState<string | null>(null);

  useEffect(() => {
    if (profile) {
      setFormData({ ...formData, ...profile });
    }
  }, [profile]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      await onUpdate(formData);
    } finally {
      setSaving(false);
    }
  };

  const handleOptimizeSection = async (section: string) => {
    setOptimizing(section);
    try {
      const response = await fetch('/api/linkedin/optimize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          section,
          currentContent: formData[section as keyof typeof formData],
          tone: 'professional',
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.optimizedContent?.suggestions?.[0]) {
          handleInputChange(section, data.optimizedContent.suggestions[0]);
        }
      }
    } catch (error) {
      console.error('Error optimizing section:', error);
    } finally {
      setOptimizing(null);
    }
  };

  const sections = [
    {
      id: 'basic',
      title: 'Basic Information',
      icon: User,
      fields: [
        {
          key: 'headline',
          label: 'Professional Headline',
          placeholder: 'e.g., Senior Software Engineer | Full-Stack Developer | Tech Lead',
          maxLength: 220,
          optimizable: true,
        },
        {
          key: 'industry',
          label: 'Industry',
          placeholder: 'e.g., Information Technology and Services',
        },
        {
          key: 'location',
          label: 'Location',
          placeholder: 'e.g., San Francisco, CA',
        },
        {
          key: 'currentPosition',
          label: 'Current Position',
          placeholder: 'e.g., Senior Software Engineer',
        },
        {
          key: 'currentCompany',
          label: 'Current Company',
          placeholder: 'e.g., Google',
        },
        {
          key: 'profileUrl',
          label: 'LinkedIn Profile URL',
          placeholder: 'https://linkedin.com/in/yourname',
        },
      ],
    },
    {
      id: 'content',
      title: 'Profile Content',
      icon: FileText,
      fields: [
        {
          key: 'summary',
          label: 'Professional Summary',
          placeholder: 'Write a compelling summary that highlights your experience, skills, and career goals...',
          type: 'textarea',
          rows: 6,
          optimizable: true,
        },
        {
          key: 'aboutSection',
          label: 'About Section',
          placeholder: 'Tell your professional story in a more personal way...',
          type: 'textarea',
          rows: 4,
          optimizable: true,
        },
      ],
    },
    {
      id: 'settings',
      title: 'Profile Settings',
      icon: Target,
      fields: [
        {
          key: 'isOpenToWork',
          label: 'Open to Work',
          type: 'checkbox',
          description: 'Let recruiters know you\'re open to new opportunities',
        },
      ],
    },
  ];

  return (
    <div className="space-y-6">
      {sections.map((section) => {
        const Icon = section.icon;
        return (
          <Card key={section.id}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon className="h-5 w-5" />
                {section.title}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {section.fields.map((field) => (
                <div key={field.key} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-gray-700">
                      {field.label}
                      {field.maxLength && (
                        <span className="text-xs text-gray-500 ml-2">
                          ({(formData[field.key as keyof typeof formData] as string)?.length || 0}/{field.maxLength})
                        </span>
                      )}
                    </label>
                    {field.optimizable && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleOptimizeSection(field.key)}
                        disabled={optimizing === field.key}
                        className="flex items-center gap-1"
                      >
                        {optimizing === field.key ? (
                          <RefreshCw className="h-3 w-3 animate-spin" />
                        ) : (
                          <Sparkles className="h-3 w-3" />
                        )}
                        Optimize
                      </Button>
                    )}
                  </div>

                  {field.type === 'textarea' ? (
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                      rows={field.rows || 3}
                      placeholder={field.placeholder}
                      value={formData[field.key as keyof typeof formData] as string || ''}
                      onChange={(e) => handleInputChange(field.key, e.target.value)}
                      maxLength={field.maxLength}
                    />
                  ) : field.type === 'checkbox' ? (
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={field.key}
                        checked={formData[field.key as keyof typeof formData] as boolean || false}
                        onChange={(e) => handleInputChange(field.key, e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor={field.key} className="text-sm text-gray-600">
                        {field.description}
                      </label>
                    </div>
                  ) : (
                    <Input
                      placeholder={field.placeholder}
                      value={formData[field.key as keyof typeof formData] as string || ''}
                      onChange={(e) => handleInputChange(field.key, e.target.value)}
                      maxLength={field.maxLength}
                    />
                  )}

                  {field.description && field.type !== 'checkbox' && (
                    <p className="text-xs text-gray-500">{field.description}</p>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
        );
      })}

      {/* AI Suggestions */}
      {profile?.headlineSuggestions && profile.headlineSuggestions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5" />
              AI-Generated Headlines
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {profile.headlineSuggestions.slice(0, 3).map((suggestion: string, index: number) => (
                <div key={index} className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-start justify-between">
                    <p className="text-sm text-gray-700 flex-1">{suggestion}</p>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleInputChange('headline', suggestion)}
                      className="ml-2"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          disabled={saving}
          className="flex items-center gap-2"
        >
          {saving ? (
            <>
              <RefreshCw className="h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4" />
              Save Profile
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
