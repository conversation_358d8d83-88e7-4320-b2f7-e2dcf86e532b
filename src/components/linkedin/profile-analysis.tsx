'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  Target, 
  AlertCircle,
  CheckCircle,
  Eye,
  Users,
  BarChart3,
  Lightbulb,
  Search,
  Sparkles
} from 'lucide-react';

interface ProfileAnalysisProps {
  profile: any;
  onAnalyze: (targetRole?: string, targetIndustry?: string) => void;
  analyzing: boolean;
}

export default function ProfileAnalysis({ profile, onAnalyze, analyzing }: ProfileAnalysisProps) {
  const [targetRole, setTargetRole] = useState('');
  const [targetIndustry, setTargetIndustry] = useState('');

  const handleAnalyze = () => {
    onAnalyze(targetRole || undefined, targetIndustry || undefined);
  };

  const getScoreColor = (score?: number) => {
    if (!score) return 'text-gray-500';
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeColor = (score?: number) => {
    if (!score) return 'bg-gray-100 text-gray-800';
    if (score >= 80) return 'bg-green-100 text-green-800';
    if (score >= 60) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const analysisCards = [
    {
      title: 'Profile Completeness',
      score: profile?.completenessScore || 0,
      icon: CheckCircle,
      description: 'How complete your profile is',
      details: profile?.missingFields?.length 
        ? `Missing: ${profile.missingFields.join(', ')}`
        : 'All key sections completed',
    },
    {
      title: 'Recruiter Visibility',
      score: profile?.visibilityScore || 0,
      icon: Eye,
      description: 'How visible you are to recruiters',
      details: 'Based on keywords, headline, and profile optimization',
    },
    {
      title: 'Engagement Potential',
      score: profile?.engagementScore || 0,
      icon: Users,
      description: 'Potential for profile views and connections',
      details: 'Based on network size and profile activity',
    },
    {
      title: 'Overall Profile Score',
      score: profile?.profileScore || 0,
      icon: TrendingUp,
      description: 'Combined optimization score',
      details: 'Weighted average of all factors',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Analysis Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Profile Analysis
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Target Role (Optional)
              </label>
              <Input
                placeholder="e.g., Senior Software Engineer"
                value={targetRole}
                onChange={(e) => setTargetRole(e.target.value)}
              />
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Target Industry (Optional)
              </label>
              <Input
                placeholder="e.g., Technology"
                value={targetIndustry}
                onChange={(e) => setTargetIndustry(e.target.value)}
              />
            </div>
          </div>
          
          <Button
            onClick={handleAnalyze}
            disabled={analyzing}
            className="w-full flex items-center gap-2"
          >
            {analyzing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Analyzing Profile...
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4" />
                Analyze Profile
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Analysis Results */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {analysisCards.map((card) => {
          const Icon = card.icon;
          return (
            <Card key={card.title}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Icon className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{card.title}</h3>
                      <p className="text-sm text-gray-600">{card.description}</p>
                    </div>
                  </div>
                  <Badge className={getScoreBadgeColor(card.score)}>
                    {card.score}%
                  </Badge>
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
                  <div 
                    className={`h-2 rounded-full transition-all duration-300 ${
                      card.score >= 80 ? 'bg-green-500' :
                      card.score >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${card.score}%` }}
                  ></div>
                </div>
                
                <p className="text-xs text-gray-500">{card.details}</p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Optimization Tips */}
      {profile?.optimizationTips && profile.optimizationTips.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5" />
              Optimization Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {profile.optimizationTips.map((tip: string, index: number) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                  <AlertCircle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-blue-800">{tip}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Keyword Suggestions */}
      {profile?.keywordSuggestions && profile.keywordSuggestions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Recommended Keywords
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {profile.keywordSuggestions.map((keyword: string, index: number) => (
                <Badge key={index} variant="outline" className="text-sm">
                  {keyword}
                </Badge>
              ))}
            </div>
            <p className="text-xs text-gray-500 mt-3">
              Include these keywords in your headline, summary, and experience descriptions to improve visibility.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Profile Metrics */}
      {(profile?.profileViews || profile?.searchAppearances || profile?.connectionsCount) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Profile Metrics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {profile.profileViews && (
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-gray-900">{profile.profileViews}</p>
                  <p className="text-sm text-gray-600">Profile Views</p>
                </div>
              )}
              {profile.searchAppearances && (
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-gray-900">{profile.searchAppearances}</p>
                  <p className="text-sm text-gray-600">Search Appearances</p>
                </div>
              )}
              {profile.connectionsCount && (
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-gray-900">{profile.connectionsCount}</p>
                  <p className="text-sm text-gray-600">Connections</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Profile Strength */}
      {profile?.profileStrength && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-gray-900 mb-1">LinkedIn Profile Strength</h3>
                <p className="text-sm text-gray-600">
                  Your current LinkedIn profile strength level
                </p>
              </div>
              <Badge 
                className={
                  profile.profileStrength === 'All-Star' ? 'bg-green-100 text-green-800' :
                  profile.profileStrength === 'Advanced' ? 'bg-blue-100 text-blue-800' :
                  profile.profileStrength === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }
              >
                {profile.profileStrength}
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
