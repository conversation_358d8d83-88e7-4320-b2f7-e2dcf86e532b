'use client';

import React, { useState } from 'react';
import {
  Palette,
  Type,
  Layout,
  Move3D,
  Save,
  RotateCcw,
  Download,
  Upload,
  Eye,
  EyeOff,
  AlertCircle,
  CheckCircle,
  Settings
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import ColorPicker from './ColorPicker';
import { useTemplateCustomization } from '@/lib/hooks/useTemplateCustomization';
import { TemplateCustomization } from '@/types/customization';
import { COLOR_PALETTES, FONT_OPTIONS, CUSTOMIZATION_PRESETS } from '@/lib/customization/presets';

interface CustomizationPanelProps {
  templateId: string;
  initialCustomization?: Partial<TemplateCustomization>;
  onCustomizationChange?: (customization: TemplateCustomization) => void;
  onSave?: (customization: TemplateCustomization) => Promise<void>;
  className?: string;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

export default function CustomizationPanel({
  templateId,
  initialCustomization,
  onCustomizationChange,
  onSave,
  className = '',
  isCollapsed = false,
  onToggleCollapse
}: CustomizationPanelProps) {
  const [activeTab, setActiveTab] = useState('colors');
  const [showAdvanced, setShowAdvanced] = useState(false);

  const {
    customization,
    isDirty,
    isLoading,
    validation,
    updateColorScheme,
    updateTypography,
    updateLayout,
    updateSpacing,
    applyPreset,
    resetToDefault,
    saveCustomization,
    exportCustomization,
    importCustomization
  } = useTemplateCustomization({
    templateId,
    initialCustomization,
    autoSave: false
  });

  // Notify parent of changes
  React.useEffect(() => {
    onCustomizationChange?.(customization);
  }, [customization, onCustomizationChange]);

  const handleSave = async () => {
    try {
      await saveCustomization();
      if (onSave) {
        await onSave(customization);
      }
    } catch (error) {
      console.error('Failed to save customization:', error);
    }
  };

  const handleExport = () => {
    const data = exportCustomization();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${templateId}-customization.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const data = e.target?.result as string;
      if (importCustomization(data)) {
        // Success feedback could be added here
      } else {
        // Error feedback could be added here
      }
    };
    reader.readAsText(file);
  };

  if (isCollapsed) {
    return (
      <div className={`w-12 ${className}`}>
        <Button
          variant="outline"
          size="sm"
          onClick={onToggleCollapse}
          className="w-full h-12"
        >
          <Settings className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <Card className={`w-80 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Customize
          </CardTitle>
          
          <div className="flex items-center gap-1">
            {onToggleCollapse && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleCollapse}
                className="px-2"
              >
                <EyeOff className="h-4 w-4" />
              </Button>
            )}
            
            {isDirty && (
              <Badge variant="secondary" className="text-xs">
                Unsaved
              </Badge>
            )}
          </div>
        </div>

        {/* Validation Status */}
        {!validation.isValid && (
          <Alert variant="destructive" className="mt-2">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              {validation.errors.length} error(s) found
            </AlertDescription>
          </Alert>
        )}

        {validation.isValid && validation.warnings.length > 0 && (
          <Alert className="mt-2">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              {validation.warnings.length} suggestion(s) available
            </AlertDescription>
          </Alert>
        )}

        {/* ATS Compatibility Score */}
        <div className="space-y-2 mt-2">
          <div className="flex items-center justify-between text-xs">
            <span>ATS Compatibility</span>
            <span className={validation.atsCompatible ? 'text-green-600' : 'text-red-600'}>
              {validation.atsCompatible ? 'Compatible' : 'Issues Found'}
            </span>
          </div>
          <Progress 
            value={validation.accessibilityScore} 
            className="h-1"
          />
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Quick Presets */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Quick Presets</Label>
          <div className="grid grid-cols-2 gap-2">
            {CUSTOMIZATION_PRESETS.slice(0, 4).map((preset) => (
              <Button
                key={preset.id}
                variant="outline"
                size="sm"
                onClick={() => applyPreset(preset)}
                className="text-xs h-8"
              >
                {preset.name}
              </Button>
            ))}
          </div>
        </div>

        {/* Customization Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="colors" className="text-xs">
              <Palette className="h-3 w-3" />
            </TabsTrigger>
            <TabsTrigger value="typography" className="text-xs">
              <Type className="h-3 w-3" />
            </TabsTrigger>
            <TabsTrigger value="layout" className="text-xs">
              <Layout className="h-3 w-3" />
            </TabsTrigger>
            <TabsTrigger value="spacing" className="text-xs">
              <Move3D className="h-3 w-3" />
            </TabsTrigger>
          </TabsList>

          {/* Colors Tab */}
          <TabsContent value="colors" className="space-y-4 mt-4">
            <div className="space-y-3">
              <ColorPicker
                label="Primary Color"
                value={customization.colorScheme.primary}
                onChange={(color) => updateColorScheme({ primary: color })}
              />
              
              <ColorPicker
                label="Secondary Color"
                value={customization.colorScheme.secondary}
                onChange={(color) => updateColorScheme({ secondary: color })}
              />
              
              <ColorPicker
                label="Accent Color"
                value={customization.colorScheme.accent}
                onChange={(color) => updateColorScheme({ accent: color })}
              />

              {showAdvanced && (
                <>
                  <ColorPicker
                    label="Text Color"
                    value={customization.colorScheme.text}
                    onChange={(color) => updateColorScheme({ text: color })}
                  />
                  
                  <ColorPicker
                    label="Background Color"
                    value={customization.colorScheme.background}
                    onChange={(color) => updateColorScheme({ background: color })}
                  />
                </>
              )}
            </div>

            {/* Color Palettes */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Color Palettes</Label>
              <div className="grid grid-cols-2 gap-2">
                {COLOR_PALETTES.slice(0, 6).map((palette) => (
                  <Button
                    key={palette.id}
                    variant="outline"
                    size="sm"
                    onClick={() => updateColorScheme(palette.colors)}
                    className="text-xs h-8 justify-start"
                    style={{ 
                      background: palette.preview,
                      color: 'white',
                      textShadow: '0 1px 2px rgba(0,0,0,0.5)'
                    }}
                  >
                    {palette.name}
                  </Button>
                ))}
              </div>
            </div>
          </TabsContent>

          {/* Typography Tab */}
          <TabsContent value="typography" className="space-y-4 mt-4">
            <div className="space-y-3">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Heading Font</Label>
                <select
                  value={customization.typography.headingFont}
                  onChange={(e) => updateTypography({ headingFont: e.target.value })}
                  className="w-full p-2 border rounded text-sm"
                >
                  {FONT_OPTIONS.map((font) => (
                    <option key={font.id} value={font.family}>
                      {font.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Body Font</Label>
                <select
                  value={customization.typography.bodyFont}
                  onChange={(e) => updateTypography({ bodyFont: e.target.value })}
                  className="w-full p-2 border rounded text-sm"
                >
                  {FONT_OPTIONS.map((font) => (
                    <option key={font.id} value={font.family}>
                      {font.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </TabsContent>

          {/* Layout Tab */}
          <TabsContent value="layout" className="space-y-4 mt-4">
            <div className="text-sm text-gray-600">
              Layout customization options will be available here.
            </div>
          </TabsContent>

          {/* Spacing Tab */}
          <TabsContent value="spacing" className="space-y-4 mt-4">
            <div className="text-sm text-gray-600">
              Spacing customization options will be available here.
            </div>
          </TabsContent>
        </Tabs>

        {/* Advanced Options Toggle */}
        <div className="flex items-center justify-between pt-2 border-t">
          <div className="flex items-center space-x-2">
            <Switch
              id="advanced"
              checked={showAdvanced}
              onCheckedChange={setShowAdvanced}
            />
            <Label htmlFor="advanced" className="text-xs">
              Advanced Options
            </Label>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center gap-2 pt-2 border-t">
          <Button
            onClick={handleSave}
            disabled={!isDirty || isLoading}
            size="sm"
            className="flex-1"
          >
            <Save className="h-3 w-3 mr-1" />
            {isLoading ? 'Saving...' : 'Save'}
          </Button>
          
          <Button
            variant="outline"
            onClick={resetToDefault}
            size="sm"
            className="px-2"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
          
          <Button
            variant="outline"
            onClick={handleExport}
            size="sm"
            className="px-2"
          >
            <Download className="h-3 w-3" />
          </Button>
          
          <label className="cursor-pointer">
            <Button
              variant="outline"
              size="sm"
              className="px-2"
              asChild
            >
              <span>
                <Upload className="h-3 w-3" />
              </span>
            </Button>
            <input
              type="file"
              accept=".json"
              onChange={handleImport}
              className="sr-only"
            />
          </label>
        </div>
      </CardContent>
    </Card>
  );
}
