'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Palette, Check, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';

interface ColorPickerProps {
  label: string;
  value: string;
  onChange: (color: string) => void;
  presetColors?: string[];
  showPresets?: boolean;
  disabled?: boolean;
  className?: string;
}

const DEFAULT_PRESET_COLORS = [
  '#1e40af', '#3b82f6', '#06b6d4', '#0d9488', '#059669',
  '#65a30d', '#ca8a04', '#d97706', '#dc2626', '#e11d48',
  '#c2410c', '#9333ea', '#7c3aed', '#6366f1', '#4f46e5',
  '#374151', '#6b7280', '#9ca3af', '#000000', '#ffffff'
];

export default function ColorPicker({
  label,
  value,
  onChange,
  presetColors = DEFAULT_PRESET_COLORS,
  showPresets = true,
  disabled = false,
  className = ''
}: ColorPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState(value);
  const colorInputRef = useRef<HTMLInputElement>(null);

  // Update input value when prop value changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  const handleColorChange = (newColor: string) => {
    setInputValue(newColor);
    onChange(newColor);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    
    // Validate hex color format
    if (/^#[0-9A-F]{6}$/i.test(newValue)) {
      onChange(newValue);
    }
  };

  const handleInputBlur = () => {
    // If input is not a valid hex color, revert to current value
    if (!/^#[0-9A-F]{6}$/i.test(inputValue)) {
      setInputValue(value);
    }
  };

  const handlePresetClick = (color: string) => {
    handleColorChange(color);
    setIsOpen(false);
  };

  const openNativeColorPicker = () => {
    if (colorInputRef.current) {
      colorInputRef.current.click();
    }
  };

  const resetToDefault = () => {
    handleColorChange('#3b82f6'); // Default blue
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <Label className="text-sm font-medium">{label}</Label>
      
      <div className="flex items-center gap-2">
        {/* Color Preview */}
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="w-12 h-10 p-1 border-2"
              disabled={disabled}
              style={{ backgroundColor: value }}
            >
              <div 
                className="w-full h-full rounded border border-gray-300"
                style={{ backgroundColor: value }}
              />
            </Button>
          </PopoverTrigger>
          
          <PopoverContent className="w-64 p-4" align="start">
            <div className="space-y-4">
              {/* Color Input */}
              <div className="space-y-2">
                <Label className="text-xs font-medium">Hex Color</Label>
                <div className="flex gap-2">
                  <Input
                    value={inputValue}
                    onChange={handleInputChange}
                    onBlur={handleInputBlur}
                    placeholder="#000000"
                    className="font-mono text-sm"
                    maxLength={7}
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={openNativeColorPicker}
                    className="px-3"
                  >
                    <Palette className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Native Color Input (Hidden) */}
              <input
                ref={colorInputRef}
                type="color"
                value={value}
                onChange={(e) => handleColorChange(e.target.value)}
                className="sr-only"
              />

              {/* Preset Colors */}
              {showPresets && (
                <div className="space-y-2">
                  <Label className="text-xs font-medium">Preset Colors</Label>
                  <div className="grid grid-cols-5 gap-2">
                    {presetColors.map((color, index) => (
                      <button
                        key={index}
                        onClick={() => handlePresetClick(color)}
                        className="w-8 h-8 rounded border-2 border-gray-200 hover:border-gray-400 transition-colors relative group"
                        style={{ backgroundColor: color }}
                        title={color}
                      >
                        {color === value && (
                          <Check className="h-4 w-4 text-white absolute inset-0 m-auto drop-shadow-sm" />
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex justify-between pt-2 border-t">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={resetToDefault}
                  className="text-xs"
                >
                  <RotateCcw className="h-3 w-3 mr-1" />
                  Reset
                </Button>
                
                <Button
                  size="sm"
                  onClick={() => setIsOpen(false)}
                  className="text-xs"
                >
                  Done
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* Hex Input */}
        <Input
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleInputBlur}
          placeholder="#000000"
          className="font-mono text-sm flex-1"
          disabled={disabled}
          maxLength={7}
        />

        {/* Quick Actions */}
        <Button
          variant="ghost"
          size="sm"
          onClick={openNativeColorPicker}
          disabled={disabled}
          className="px-2"
        >
          <Palette className="h-4 w-4" />
        </Button>
      </div>

      {/* Color Info */}
      <div className="flex items-center gap-2 text-xs text-gray-500">
        <Badge variant="outline" className="text-xs">
          {value.toUpperCase()}
        </Badge>
        {isValidHexColor(value) && (
          <span>RGB: {hexToRgb(value)}</span>
        )}
      </div>
    </div>
  );
}

// Utility functions
function isValidHexColor(hex: string): boolean {
  return /^#[0-9A-F]{6}$/i.test(hex);
}

function hexToRgb(hex: string): string {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  if (!result) return '';
  
  const r = parseInt(result[1], 16);
  const g = parseInt(result[2], 16);
  const b = parseInt(result[3], 16);
  
  return `${r}, ${g}, ${b}`;
}

// Color palette component for quick selection
export function ColorPalette({
  colors,
  selectedColor,
  onColorSelect,
  className = ''
}: {
  colors: string[];
  selectedColor?: string;
  onColorSelect: (color: string) => void;
  className?: string;
}) {
  return (
    <div className={`grid grid-cols-8 gap-2 ${className}`}>
      {colors.map((color, index) => (
        <button
          key={index}
          onClick={() => onColorSelect(color)}
          className="w-8 h-8 rounded border-2 border-gray-200 hover:border-gray-400 transition-colors relative"
          style={{ backgroundColor: color }}
          title={color}
        >
          {color === selectedColor && (
            <Check className="h-4 w-4 text-white absolute inset-0 m-auto drop-shadow-sm" />
          )}
        </button>
      ))}
    </div>
  );
}
