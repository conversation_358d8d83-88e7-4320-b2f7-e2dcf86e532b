'use client';

import React, { useMemo, useEffect, useRef } from 'react';
import { TemplateCustomization } from '@/types/customization';
import { CustomizationEngine } from '@/lib/customization/customization-engine';

interface CustomizableTemplateProps {
  children: React.ReactNode;
  customization?: TemplateCustomization;
  className?: string;
  style?: React.CSSProperties;
  enableCustomization?: boolean;
}

export default function CustomizableTemplate({
  children,
  customization,
  className = '',
  style = {},
  enableCustomization = true
}: CustomizableTemplateProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Create customization engine
  const engine = useMemo(() => {
    if (!enableCustomization || !customization) return null;
    return new CustomizationEngine(customization);
  }, [customization, enableCustomization]);

  // Get CSS custom properties
  const cssProperties = useMemo(() => {
    if (!engine) return {};
    return engine.getCSSProperties();
  }, [engine]);

  // Apply customization to container element
  useEffect(() => {
    if (!containerRef.current || !engine) return;
    
    engine.applyToElement(containerRef.current);
  }, [engine]);

  // Combine styles
  const combinedStyle = useMemo(() => {
    return {
      ...cssProperties,
      ...style
    };
  }, [cssProperties, style]);

  return (
    <div
      ref={containerRef}
      className={`template-customizable ${className}`}
      style={combinedStyle}
      data-customizable={enableCustomization}
    >
      {children}
      
      {/* Inject custom CSS for template-specific classes */}
      {engine && (
        <style jsx>{`
          .template-customizable {
            /* Color utilities */
            --template-color-primary: ${customization?.colorScheme.primary};
            --template-color-secondary: ${customization?.colorScheme.secondary};
            --template-color-accent: ${customization?.colorScheme.accent};
            --template-color-background: ${customization?.colorScheme.background};
            --template-color-text: ${customization?.colorScheme.text};
            --template-color-text-secondary: ${customization?.colorScheme.textSecondary};
            --template-color-border: ${customization?.colorScheme.border};
            --template-color-surface: ${customization?.colorScheme.surface};
            --template-color-surface-secondary: ${customization?.colorScheme.surfaceSecondary};
            
            /* Typography utilities */
            --template-font-heading: ${customization?.typography.headingFont};
            --template-font-body: ${customization?.typography.bodyFont};
            
            /* Layout utilities */
            --template-section-spacing: ${customization?.layout.sectionSpacing};
            --template-column-gap: ${customization?.layout.columnGap};
            --template-max-width: ${customization?.layout.maxWidth};
            
            /* Spacing utilities */
            --template-section-gap: ${customization?.spacing.sectionGap};
            --template-item-gap: ${customization?.spacing.itemGap};
            --template-paragraph-gap: ${customization?.spacing.paragraphGap};
            --template-list-gap: ${customization?.spacing.listGap};
          }
          
          /* Template-specific utility classes */
          .template-customizable .template-primary {
            color: var(--template-color-primary) !important;
          }
          
          .template-customizable .template-secondary {
            color: var(--template-color-secondary) !important;
          }
          
          .template-customizable .template-accent {
            color: var(--template-color-accent) !important;
          }
          
          .template-customizable .template-text {
            color: var(--template-color-text) !important;
          }
          
          .template-customizable .template-text-secondary {
            color: var(--template-color-text-secondary) !important;
          }
          
          .template-customizable .template-bg-primary {
            background-color: var(--template-color-primary) !important;
          }
          
          .template-customizable .template-bg-secondary {
            background-color: var(--template-color-secondary) !important;
          }
          
          .template-customizable .template-bg-accent {
            background-color: var(--template-color-accent) !important;
          }
          
          .template-customizable .template-bg-surface {
            background-color: var(--template-color-surface) !important;
          }
          
          .template-customizable .template-bg-surface-secondary {
            background-color: var(--template-color-surface-secondary) !important;
          }
          
          .template-customizable .template-border {
            border-color: var(--template-color-border) !important;
          }
          
          .template-customizable .template-font-heading {
            font-family: var(--template-font-heading) !important;
          }
          
          .template-customizable .template-font-body {
            font-family: var(--template-font-body) !important;
          }
          
          .template-customizable .template-spacing-section {
            margin-bottom: var(--template-section-gap) !important;
          }
          
          .template-customizable .template-spacing-item {
            margin-bottom: var(--template-item-gap) !important;
          }
          
          .template-customizable .template-spacing-paragraph {
            margin-bottom: var(--template-paragraph-gap) !important;
          }
          
          .template-customizable .template-spacing-list {
            margin-bottom: var(--template-list-gap) !important;
          }
          
          /* Override common template patterns */
          .template-customizable h1,
          .template-customizable h2,
          .template-customizable h3,
          .template-customizable h4,
          .template-customizable h5,
          .template-customizable h6 {
            font-family: var(--template-font-heading) !important;
          }
          
          .template-customizable p,
          .template-customizable span,
          .template-customizable div {
            font-family: var(--template-font-body) !important;
          }
          
          /* Color overrides for common patterns */
          .template-customizable [class*="text-blue"],
          .template-customizable [class*="bg-blue"] {
            color: var(--template-color-primary) !important;
          }
          
          .template-customizable [class*="bg-blue"] {
            background-color: var(--template-color-primary) !important;
          }
          
          .template-customizable [class*="text-teal"],
          .template-customizable [class*="bg-teal"] {
            color: var(--template-color-accent) !important;
          }
          
          .template-customizable [class*="bg-teal"] {
            background-color: var(--template-color-accent) !important;
          }
          
          .template-customizable [class*="border-blue"],
          .template-customizable [class*="border-teal"] {
            border-color: var(--template-color-primary) !important;
          }
          
          /* Print-specific overrides */
          @media print {
            .template-customizable {
              background: white !important;
            }
            
            .template-customizable * {
              color-adjust: exact !important;
              -webkit-print-color-adjust: exact !important;
            }
          }
        `}</style>
      )}
    </div>
  );
}

// Higher-order component for wrapping existing templates
export function withCustomization<T extends object>(
  WrappedComponent: React.ComponentType<T>
) {
  return function CustomizableTemplateWrapper(
    props: T & { 
      customization?: TemplateCustomization;
      enableCustomization?: boolean;
    }
  ) {
    const { customization, enableCustomization = true, ...restProps } = props;
    
    return (
      <CustomizableTemplate
        customization={customization}
        enableCustomization={enableCustomization}
      >
        <WrappedComponent {...(restProps as T)} />
      </CustomizableTemplate>
    );
  };
}

// Hook for accessing customization context within templates
export function useTemplateCustomizationContext() {
  const containerRef = useRef<HTMLElement>();
  
  useEffect(() => {
    // Find the nearest customizable template container
    const findCustomizableContainer = (element: HTMLElement): HTMLElement | null => {
      if (element.dataset.customizable === 'true') {
        return element;
      }
      if (element.parentElement) {
        return findCustomizableContainer(element.parentElement);
      }
      return null;
    };
    
    if (containerRef.current) {
      const container = findCustomizableContainer(containerRef.current);
      if (container) {
        // Access CSS custom properties from the container
        const styles = getComputedStyle(container);
        // You can now access --template-* CSS variables
      }
    }
  }, []);
  
  return {
    containerRef,
    getCSSVariable: (name: string) => {
      if (containerRef.current) {
        const container = containerRef.current.closest('[data-customizable="true"]');
        if (container) {
          return getComputedStyle(container).getPropertyValue(name);
        }
      }
      return null;
    }
  };
}
