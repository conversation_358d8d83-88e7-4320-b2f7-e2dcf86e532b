/**
 * Onboarding Flow Component
 * Guided user experience with progressive disclosure and contextual help
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  CheckCircle,
  Circle,
  ArrowRight,
  ArrowLeft,
  Skip,
  HelpCircle,
  Clock,
  Target,
  Lightbulb,
  Play,
  X,
} from 'lucide-react';
import { OnboardingFlowType, UserOnboardingProgress, OnboardingStep } from '@/lib/onboarding/onboarding-system';

interface OnboardingFlowProps {
  flowType: OnboardingFlowType;
  userId: string;
  onComplete?: () => void;
  onSkip?: () => void;
}

// Mock onboarding flows - in production these would come from the API
const ONBOARDING_FLOWS = {
  [OnboardingFlowType.FIRST_TIME_USER]: {
    id: 'first_time_user',
    name: 'Welcome to CVLeap',
    description: 'Let\'s get you started with creating your first professional resume',
    steps: [
      {
        id: 'welcome',
        title: 'Welcome to CVLeap',
        description: 'Your AI-powered resume building journey starts here',
        component: 'WelcomeStep',
        optional: false,
        estimatedTime: 2,
        helpContent: {
          text: 'CVLeap helps you create professional resumes that get noticed by employers and pass ATS systems.',
        },
      },
      {
        id: 'profile_setup',
        title: 'Set Up Your Profile',
        description: 'Tell us about yourself to personalize your experience',
        component: 'ProfileSetupStep',
        optional: false,
        estimatedTime: 5,
        helpContent: {
          text: 'Your profile information helps us provide better template recommendations and AI suggestions.',
        },
      },
      {
        id: 'template_selection',
        title: 'Choose Your Template',
        description: 'Select a template that matches your industry and style',
        component: 'TemplateSelectionStep',
        optional: false,
        estimatedTime: 3,
        helpContent: {
          text: 'Different templates work better for different industries. We\'ll recommend the best ones for your field.',
        },
      },
      {
        id: 'ai_features_intro',
        title: 'Discover AI Features',
        description: 'Learn how AI can enhance your resume content',
        component: 'AIFeaturesStep',
        optional: true,
        estimatedTime: 4,
        helpContent: {
          text: 'Our AI can help optimize your content, improve ATS compatibility, and suggest better phrasing.',
        },
      },
      {
        id: 'first_resume',
        title: 'Create Your First Resume',
        description: 'Start building your professional resume',
        component: 'FirstResumeStep',
        optional: false,
        estimatedTime: 15,
        helpContent: {
          text: 'Don\'t worry about making it perfect - you can always edit and improve your resume later.',
        },
      },
    ],
  },
  [OnboardingFlowType.RESUME_CREATION]: {
    id: 'resume_creation',
    name: 'Resume Creation Guide',
    description: 'Step-by-step guide to creating an effective resume',
    steps: [
      {
        id: 'content_strategy',
        title: 'Content Strategy',
        description: 'Learn what makes a resume effective',
        component: 'ContentStrategyStep',
        optional: false,
        estimatedTime: 3,
      },
      {
        id: 'section_by_section',
        title: 'Section-by-Section Guide',
        description: 'Complete each resume section with guidance',
        component: 'SectionGuideStep',
        optional: false,
        estimatedTime: 20,
      },
    ],
  },
};

export default function OnboardingFlow({ flowType, userId, onComplete, onSkip }: OnboardingFlowProps) {
  const [progress, setProgress] = useState<UserOnboardingProgress | null>(null);
  const [currentStep, setCurrentStep] = useState<OnboardingStep | null>(null);
  const [loading, setLoading] = useState(true);
  const [showHelp, setShowHelp] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const flow = ONBOARDING_FLOWS[flowType];

  useEffect(() => {
    initializeOnboarding();
  }, [flowType, userId]);

  useEffect(() => {
    if (progress && flow) {
      const step = flow.steps.find(s => s.id === progress.currentStepId);
      setCurrentStep(step || null);
    }
  }, [progress, flow]);

  const initializeOnboarding = async () => {
    try {
      setLoading(true);
      
      // First try to get existing progress
      const progressResponse = await fetch(`/api/onboarding?action=get-progress&flowType=${flowType}`);
      
      if (progressResponse.ok) {
        const { progress: existingProgress } = await progressResponse.json();
        
        if (existingProgress) {
          setProgress(existingProgress);
          return;
        }
      }

      // Initialize new onboarding
      const initResponse = await fetch('/api/onboarding', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'initialize',
          flowType,
        }),
      });

      if (!initResponse.ok) {
        throw new Error('Failed to initialize onboarding');
      }

      const { progress: newProgress } = await initResponse.json();
      setProgress(newProgress);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to initialize onboarding');
    } finally {
      setLoading(false);
    }
  };

  const completeStep = async (stepId: string) => {
    if (!progress) return;

    try {
      const response = await fetch('/api/onboarding', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'complete-step',
          flowId: progress.flowId,
          stepId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to complete step');
      }

      // Update local progress
      const updatedProgress = {
        ...progress,
        completedSteps: [...progress.completedSteps, stepId],
        currentStepId: getNextStepId(stepId),
      };

      setProgress(updatedProgress);

      // Check if flow is complete
      if (!updatedProgress.currentStepId) {
        onComplete?.();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to complete step');
    }
  };

  const skipStep = async (stepId: string) => {
    if (!progress) return;

    try {
      const response = await fetch('/api/onboarding', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'skip-step',
          flowId: progress.flowId,
          stepId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to skip step');
      }

      // Update local progress
      const updatedProgress = {
        ...progress,
        skippedSteps: [...progress.skippedSteps, stepId],
        currentStepId: getNextStepId(stepId),
      };

      setProgress(updatedProgress);

      // Check if flow is complete
      if (!updatedProgress.currentStepId) {
        onComplete?.();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to skip step');
    }
  };

  const getNextStepId = (currentStepId: string): string | undefined => {
    const currentIndex = flow.steps.findIndex(s => s.id === currentStepId);
    const nextStep = flow.steps[currentIndex + 1];
    return nextStep?.id;
  };

  const getPreviousStepId = (currentStepId: string): string | undefined => {
    const currentIndex = flow.steps.findIndex(s => s.id === currentStepId);
    const previousStep = flow.steps[currentIndex - 1];
    return previousStep?.id;
  };

  const getProgressPercentage = (): number => {
    if (!progress) return 0;
    const totalSteps = flow.steps.length;
    const completedSteps = progress.completedSteps.length + progress.skippedSteps.length;
    return (completedSteps / totalSteps) * 100;
  };

  const goToPreviousStep = () => {
    if (!currentStep || !progress) return;
    
    const previousStepId = getPreviousStepId(currentStep.id);
    if (previousStepId) {
      setProgress({
        ...progress,
        currentStepId: previousStepId,
      });
    }
  };

  const renderStepContent = () => {
    if (!currentStep) return null;

    // In a real implementation, this would render the actual step component
    // For now, we'll render a placeholder
    return (
      <div className="space-y-4">
        <div className="text-center space-y-2">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
            <Target className="h-8 w-8 text-blue-600" />
          </div>
          <h3 className="text-lg font-semibold">{currentStep.title}</h3>
          <p className="text-muted-foreground">{currentStep.description}</p>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <p className="text-sm text-gray-600">
            This is where the {currentStep.component} component would be rendered.
            In a full implementation, each step would have its own interactive component.
          </p>
        </div>

        <div className="flex items-center justify-between pt-4">
          <Button
            variant="outline"
            onClick={goToPreviousStep}
            disabled={!getPreviousStepId(currentStep.id)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          <div className="flex gap-2">
            {currentStep.optional && (
              <Button
                variant="ghost"
                onClick={() => skipStep(currentStep.id)}
              >
                <Skip className="h-4 w-4 mr-2" />
                Skip
              </Button>
            )}
            
            <Button onClick={() => completeStep(currentStep.id)}>
              Continue
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center space-y-4">
        <div className="text-red-600">{error}</div>
        <Button onClick={initializeOnboarding}>Try Again</Button>
      </div>
    );
  }

  if (!progress || !currentStep) {
    return (
      <div className="text-center space-y-4">
        <div className="text-green-600">Onboarding completed!</div>
        <Button onClick={onComplete}>Continue to Dashboard</Button>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-2xl font-bold">{flow.name}</h1>
        <p className="text-muted-foreground">{flow.description}</p>
      </div>

      {/* Progress */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Progress</span>
              <span className="text-sm text-muted-foreground">
                {progress.completedSteps.length + progress.skippedSteps.length} of {flow.steps.length} steps
              </span>
            </div>
            <Progress value={getProgressPercentage()} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Step Navigation */}
      <div className="flex items-center justify-center space-x-2 overflow-x-auto pb-2">
        {flow.steps.map((step, index) => {
          const isCompleted = progress.completedSteps.includes(step.id);
          const isSkipped = progress.skippedSteps.includes(step.id);
          const isCurrent = step.id === currentStep.id;
          
          return (
            <div key={step.id} className="flex items-center">
              <div className={`
                flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium
                ${isCurrent ? 'bg-blue-600 text-white' : ''}
                ${isCompleted ? 'bg-green-600 text-white' : ''}
                ${isSkipped ? 'bg-gray-400 text-white' : ''}
                ${!isCurrent && !isCompleted && !isSkipped ? 'bg-gray-200 text-gray-600' : ''}
              `}>
                {isCompleted ? (
                  <CheckCircle className="h-4 w-4" />
                ) : isSkipped ? (
                  <X className="h-4 w-4" />
                ) : (
                  index + 1
                )}
              </div>
              
              {index < flow.steps.length - 1 && (
                <div className="w-8 h-0.5 bg-gray-200 mx-1" />
              )}
            </div>
          );
        })}
      </div>

      {/* Current Step */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <CardTitle className="flex items-center gap-2">
                {currentStep.title}
                {currentStep.optional && (
                  <Badge variant="secondary">Optional</Badge>
                )}
              </CardTitle>
              <CardDescription className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                {currentStep.estimatedTime} min
              </CardDescription>
            </div>
            
            {currentStep.helpContent && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowHelp(!showHelp)}
              >
                <HelpCircle className="h-4 w-4" />
              </Button>
            )}
          </div>
          
          {showHelp && currentStep.helpContent && (
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="flex items-start gap-2">
                <Lightbulb className="h-4 w-4 text-blue-600 mt-0.5" />
                <div className="text-sm text-blue-800">
                  {currentStep.helpContent.text}
                  {currentStep.helpContent.videoUrl && (
                    <div className="mt-2">
                      <Button size="sm" variant="outline" asChild>
                        <a href={currentStep.helpContent.videoUrl} target="_blank" rel="noopener noreferrer">
                          <Play className="h-3 w-3 mr-1" />
                          Watch Video
                        </a>
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </CardHeader>
        
        <CardContent>
          {renderStepContent()}
        </CardContent>
      </Card>

      {/* Skip All Option */}
      <div className="text-center">
        <Button variant="ghost" onClick={onSkip} className="text-muted-foreground">
          Skip onboarding and go to dashboard
        </Button>
      </div>
    </div>
  );
}
