import { Mail, Phone, MapPin, Linkedin, Calendar, Building2, GraduationCap, Award, BookO<PERSON>, Heart } from 'lucide-react';

export function TechnicalBlueTemplate() {
  return (
    <div className="w-full max-w-4xl mx-auto bg-white shadow-lg min-h-[800px] p-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-blue-600 mb-1">ELLA WHITE</h1>
        <h2 className="text-lg text-gray-600 mb-4">Mechanical Engineer | Aerospace Solutions | CAD Expertise</h2>
        
        {/* Contact Info */}
        <div className="flex flex-wrap gap-6 text-sm text-gray-600 mb-6">
          <div className="flex items-center gap-2">
            <Mail className="w-4 h-4" />
            <span><EMAIL></span>
          </div>
          <div className="flex items-center gap-2">
            <Linkedin className="w-4 h-4" />
            <span>LinkedIn</span>
          </div>
          <div className="flex items-center gap-2">
            <MapPin className="w-4 h-4" />
            <span>Los Angeles, California</span>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column */}
        <div className="lg:col-span-2 space-y-8">
          {/* Summary */}
          <div>
            <h3 className="text-lg font-bold text-gray-800 mb-4 border-b-2 border-blue-600 pb-1">SUMMARY</h3>
            <p className="text-sm text-gray-700 leading-relaxed">
              Mechanical Engineer with over 7 years of experience, specializing in design 
              optimization for aerospace systems and efficiency. Proficient in FEA and CAD 
              software, contributed to significant cost savings of $300,000 through 
              innovative project management strategies.
            </p>
          </div>

          {/* Skills */}
          <div>
            <h3 className="text-lg font-bold text-gray-800 mb-4 border-b-2 border-blue-600 pb-1">SKILLS</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2 text-sm">
                <div className="bg-blue-50 px-3 py-2 rounded font-medium text-gray-800">Mechanical Design</div>
                <div className="bg-blue-50 px-3 py-2 rounded font-medium text-gray-800">SolidWorks</div>
                <div className="bg-blue-50 px-3 py-2 rounded font-medium text-gray-800">Technical Documentation</div>
              </div>
              <div className="space-y-2 text-sm">
                <div className="bg-blue-50 px-3 py-2 rounded font-medium text-gray-800">Finite Element Analysis (FEA)</div>
                <div className="bg-blue-50 px-3 py-2 rounded font-medium text-gray-800">CATIA</div>
                <div className="bg-blue-50 px-3 py-2 rounded font-medium text-gray-800">Project Management</div>
              </div>
            </div>
          </div>

          {/* Experience */}
          <div>
            <h3 className="text-lg font-bold text-gray-800 mb-4 border-b-2 border-blue-600 pb-1">EXPERIENCE</h3>
            
            <div className="space-y-6">
              {/* Senior Mechanical Engineer */}
              <div>
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h4 className="font-bold text-gray-800">Senior Mechanical Engineer</h4>
                    <p className="text-blue-600 font-medium">Northrop Grumman</p>
                  </div>
                  <div className="text-sm text-gray-500 flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    08/2021 - Present
                  </div>
                </div>
                <div className="text-xs text-gray-500 mb-2 flex items-center gap-1">
                  <MapPin className="w-3 h-3" />
                  Los Angeles, CA
                </div>
                <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
                  <li>Designed and optimized mechanical systems for aerospace applications, focusing on weight reduction and industry compliance standards, which improved project success rates</li>
                  <li>Conducted extensive Finite Element Analysis (FEA) on complex structures, identifying design flaws early which led to a 15% reduction in production costs</li>
                  <li>Collaborated with Electrical and Software Engineering teams on integrated solutions, facilitating smooth development processes and maintaining high-quality deliverables and timelines</li>
                  <li>Implemented a new project management strategy for engineering workflows, which reduced average turnaround time by 20% to four months</li>
                  <li>Played a pivotal role in the testing and validation of mechanical components, achieving a 98% pass rate in final assessments across multiple projects</li>
                </ul>
              </div>

              {/* Mechanical Engineer */}
              <div>
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h4 className="font-bold text-gray-800">Mechanical Engineer</h4>
                    <p className="text-blue-600 font-medium">Lockheed Martin</p>
                  </div>
                  <div className="text-sm text-gray-500 flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    05/2018 - 07/2021
                  </div>
                </div>
                <div className="text-xs text-gray-500 mb-2 flex items-center gap-1">
                  <MapPin className="w-3 h-3" />
                  Palmdale, CA
                </div>
                <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
                  <li>Performed intricate design analysis and assessments to guarantee compliance with industry methodologies, achieving a major 30% decrease in design errors</li>
                  <li>Led a team from troubleshooting complex engineering challenges during the production phases, which resulted in a 20% increase in operational efficiency</li>
                  <li>Engaged in comprehensive material selection processes, improving the overall performances and longevity of aerospace components in harsh environments</li>
                  <li>Played a pivotal role in the successful delivery of three aerospace projects that met tight deadlines while ensuring adherence to safety and quality standards</li>
                </ul>
              </div>

              {/* Junior Mechanical Engineer */}
              <div>
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h4 className="font-bold text-gray-800">Junior Mechanical Engineer</h4>
                    <p className="text-blue-600 font-medium">Raytheon Technologies</p>
                  </div>
                  <div className="text-sm text-gray-500 flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    06/2016 - 04/2018
                  </div>
                </div>
                <div className="text-xs text-gray-500 mb-2 flex items-center gap-1">
                  <MapPin className="w-3 h-3" />
                  Tucson, AZ
                </div>
                <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
                  <li>Assisted in the development and construction of mechanical systems and components, contributing to multiple successful defense contracts and collaborating with senior supervisors</li>
                  <li>Worked with CAD software to create and update technical drawings, helping to reduce waste and improve efficiency yields in assembly operations by 15%</li>
                  <li>Generated and maintained detailed technical documentation for designs, facilitating smoother transitions for projects between engineering and production teams</li>
                  <li>Supported senior engineers in stress testing and validation procedures to guarantee final products met the required ASTM standards</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-8">
          {/* Key Achievements */}
          <div>
            <h3 className="text-lg font-bold text-gray-800 mb-4 border-b-2 border-blue-600 pb-1">KEY ACHIEVEMENTS</h3>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center shrink-0 mt-1">
                  <Award className="w-4 h-4 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-bold text-gray-800 text-sm">Efficiency Improvement</h4>
                  <p className="text-xs text-gray-600">Enhanced the mechanical design process, significantly reducing development time by 25% while maintaining compliance with stringent industry standards through innovative approaches.</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center shrink-0 mt-1">
                  <Building2 className="w-4 h-4 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-bold text-gray-800 text-sm">Successful Project Management</h4>
                  <p className="text-xs text-gray-600">Led a cross-departmental team of 10 engineers to complete a critical project ahead of schedule, saving the company an estimated $200,000.</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center shrink-0 mt-1">
                  <Award className="w-4 h-4 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-bold text-gray-800 text-sm">Awards and Recognition</h4>
                  <p className="text-xs text-gray-600">Received "Engineer of the Year" award for outstanding contributions towards the development of a high-performance aerospace component.</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center shrink-0 mt-1">
                  <BookOpen className="w-4 h-4 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-bold text-gray-800 text-sm">Technical Documentation</h4>
                  <p className="text-xs text-gray-600">Authored comprehensive technical reports that improved understanding and tracking of engineering designs, leading to better project outcomes across all teams.</p>
                </div>
              </div>
            </div>
          </div>

          {/* Education */}
          <div>
            <h3 className="text-lg font-bold text-gray-800 mb-4 border-b-2 border-blue-600 pb-1">EDUCATION</h3>
            <div className="space-y-4">
              <div>
                <h4 className="font-bold text-gray-800 text-sm">Master's in Mechanical Engineering</h4>
                <p className="text-blue-600 text-sm font-medium">University of California, Berkeley</p>
                <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                  <span className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    01/2019 - 01/2021
                  </span>
                  <span className="flex items-center gap-1">
                    <MapPin className="w-3 h-3" />
                    Berkeley, CA
                  </span>
                </div>
              </div>
              
              <div>
                <h4 className="font-bold text-gray-800 text-sm">Bachelor's in Aerospace Engineering</h4>
                <p className="text-blue-600 text-sm font-medium">California State University, Los Angeles</p>
                <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                  <span className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    01/2014 - 01/2016
                  </span>
                  <span className="flex items-center gap-1">
                    <MapPin className="w-3 h-3" />
                    Los Angeles, CA
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Training/Courses */}
          <div>
            <h3 className="text-lg font-bold text-gray-800 mb-4 border-b-2 border-blue-600 pb-1">TRAINING / COURSES</h3>
            <div className="space-y-3 text-sm">
              <div>
                <h4 className="font-bold text-gray-800">Advanced CAD Techniques</h4>
                <p className="text-xs text-gray-600">A comprehensive course focusing on advanced features of CAD software, offered by Coursera. It emphasizes practical applications within engineering.</p>
              </div>
              
              <div>
                <h4 className="font-bold text-gray-800">Finite Element Analysis for Aerospace</h4>
                <p className="text-xs text-gray-600">A specialized certification program provided by edX, concentrating on applying FEA methodologies to solve complex aerospace problems.</p>
              </div>
            </div>
          </div>

          {/* Interests */}
          <div>
            <h3 className="text-lg font-bold text-gray-800 mb-4 border-b-2 border-blue-600 pb-1">INTERESTS</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <Heart className="w-4 h-4 text-blue-600" />
                <span className="text-gray-700">Aerospace Innovations</span>
              </div>
              <p className="text-xs text-gray-600 ml-6">Passionate about recent advancements and innovations in aerospace technology and engineering practices.</p>
              
              <div className="flex items-center gap-2 text-sm mt-3">
                <Heart className="w-4 h-4 text-blue-600" />
                <span className="text-gray-700">Sustainable Engineering</span>
              </div>
              <p className="text-xs text-gray-600 ml-6">Passionate about exploring eco-friendly materials and manufacturing processes that reduce environmental impacts in engineering.</p>
            </div>
          </div>

          {/* Languages */}
          <div>
            <h3 className="text-lg font-bold text-gray-800 mb-4 border-b-2 border-blue-600 pb-1">LANGUAGES</h3>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-700">English</span>
                <div className="flex gap-1">
                  <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                  <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                  <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                  <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                  <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                </div>
                <span className="text-xs text-gray-500">Native</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-700">Spanish</span>
                <div className="flex gap-1">
                  <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                  <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                  <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                  <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                  <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
                </div>
                <span className="text-xs text-gray-500">Proficient</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-8 pt-4 border-t border-gray-200 text-center">

      </div>
    </div>
  );
}