import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github } from 'lucide-react';

interface PersonalityTraitsTemplateProps {
  resumeData: ResumeContent;
}

export default function PersonalityTraitsTemplate({ resumeData }: PersonalityTraitsTemplateProps) {
  const { personalInfo, summary, experience, education, skills, languages } = resumeData;

  // Mock personality traits data
  const personalityTraits = [
    { name: 'Leadership', value: 85 },
    { name: 'Creativity', value: 90 },
    { name: 'Communication', value: 80 },
    { name: 'Problem Solving', value: 88 },
    { name: 'Teamwork', value: 92 }
  ];

  const CircularProgress = ({ value, size = 40, strokeWidth = 4 }: { value: number; size?: number; strokeWidth?: number }) => {
    const radius = (size - strokeWidth) / 2;
    const circumference = radius * 2 * Math.PI;
    const offset = circumference - (value / 100) * circumference;

    return (
      <div className="relative" style={{ width: size, height: size }}>
        <svg width={size} height={size} className="transform -rotate-90">
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="#e5e7eb"
            strokeWidth={strokeWidth}
            fill="transparent"
          />
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="#4a90e2"
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={circumference}
            strokeDashoffset={offset}
            strokeLinecap="round"
            className="transition-all duration-300"
          />
        </svg>
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-xs font-semibold text-[#333333]">{value}%</span>
        </div>
      </div>
    );
  };

  return (
    <div 
      className="bg-[#f1f6fa] relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="personality-traits-8"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Header */}
      <div className="absolute top-0 left-0 w-full h-[80px] bg-white shadow-sm">
        <div className="flex items-center justify-between h-full px-8">
          <div>
            <h1 className="text-2xl font-bold text-[#333333]">
              {personalInfo.fullName || 'Your Name'}
            </h1>
            <p className="text-sm text-[#333333]/70">
              {personalInfo.jobTitle || 'Professional Title'}
            </p>
          </div>
          
          {/* Profile Photo */}
          <div className="w-16 h-16 rounded-full bg-[#4a90e2] border-2 border-white overflow-hidden shadow-lg">
            {personalInfo.photo ? (
              <img 
                src={personalInfo.photo} 
                alt="Profile" 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-[#4a90e2] to-[#357abd] flex items-center justify-center">
                <span className="text-white text-lg font-bold">
                  {personalInfo.fullName?.charAt(0) || 'Y'}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content - Two Column Layout */}
      <div className="absolute top-[100px] left-0 w-full h-[742px] flex">
        
        {/* Left Column - Main Content */}
        <div className="w-[370px] px-8 py-6 bg-white">
          
          {/* Professional Summary */}
          {summary && (
            <div className="mb-8">
              <h2 className="text-lg font-semibold text-[#333333] mb-3 flex items-center">
                <div className="w-1 h-6 bg-[#4a90e2] mr-3"></div>
                About Me
              </h2>
              <p className="text-sm text-[#333333] leading-relaxed">
                {summary}
              </p>
            </div>
          )}

          {/* Experience */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-[#333333] mb-4 flex items-center">
              <div className="w-1 h-6 bg-[#4a90e2] mr-3"></div>
              Professional Experience
            </h2>
            <div className="space-y-5">
              {experience.slice(0, 3).map((exp, index) => (
                <div key={index} className="relative">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-semibold text-[#333333] text-sm">
                      {exp.position}
                    </h3>
                    <span className="text-xs text-[#333333]/70 bg-[#f1f6fa] px-2 py-1 rounded">
                      {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                    </span>
                  </div>
                  <p className="text-sm font-medium text-[#4a90e2] mb-2">
                    {exp.company} • {exp.location}
                  </p>
                  <div className="text-xs text-[#333333]/70 space-y-1">
                    {exp.description.slice(0, 2).map((desc, i) => (
                      <p key={i}>• {desc}</p>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Education */}
          <div>
            <h2 className="text-lg font-semibold text-[#333333] mb-4 flex items-center">
              <div className="w-1 h-6 bg-[#4a90e2] mr-3"></div>
              Education
            </h2>
            <div className="space-y-3">
              {education.slice(0, 2).map((edu, index) => (
                <div key={index}>
                  <h3 className="font-semibold text-sm text-[#333333]">
                    {edu.degree} in {edu.field}
                  </h3>
                  <p className="text-sm text-[#4a90e2]">
                    {edu.institution}
                  </p>
                  <p className="text-xs text-[#333333]/70">
                    {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column - Contact & Skills */}
        <div className="w-[224px] bg-[#f1f6fa] px-6 py-6">
          
          {/* Contact Information */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-[#333333] mb-4">
              Contact
            </h2>
            <div className="space-y-3">
              <div className="flex items-center text-xs text-[#333333]">
                <Mail className="w-4 h-4 mr-2 text-[#4a90e2]" />
                <span className="truncate">{personalInfo.email}</span>
              </div>
              <div className="flex items-center text-xs text-[#333333]">
                <Phone className="w-4 h-4 mr-2 text-[#4a90e2]" />
                <span>{personalInfo.phone}</span>
              </div>
              <div className="flex items-center text-xs text-[#333333]">
                <MapPin className="w-4 h-4 mr-2 text-[#4a90e2]" />
                <span>{personalInfo.location}</span>
              </div>
            </div>
          </div>

          {/* Skills */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-[#333333] mb-4">
              Technical Skills
            </h2>
            <div className="space-y-2">
              {skills.slice(0, 6).map((skill, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-2 h-2 bg-[#4a90e2] rounded-full mr-2"></div>
                  <span className="text-xs text-[#333333]">{skill}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Personality Traits with Circular Indicators */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-[#333333] mb-4">
              Personality Traits
            </h2>
            <div className="space-y-4">
              {personalityTraits.map((trait, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-xs font-medium text-[#333333] flex-1">
                    {trait.name}
                  </span>
                  <CircularProgress value={trait.value} size={32} strokeWidth={3} />
                </div>
              ))}
            </div>
          </div>

          {/* Languages */}
          {languages && languages.length > 0 && (
            <div>
              <h2 className="text-lg font-semibold text-[#333333] mb-4">
                Languages
              </h2>
              <div className="space-y-2">
                {languages.slice(0, 4).map((lang, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <span className="text-xs text-[#333333]">{lang.name}</span>
                    <span className="text-xs text-[#333333]/70">{lang.proficiency}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
