import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, Scale, Briefcase, GraduationCap } from 'lucide-react';

interface AttorneyResumeTemplateProps {
  resumeData: ResumeContent;
}

export default function AttorneyResumeTemplate({ resumeData }: AttorneyResumeTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  // Mock professional values for attorney template
  const professionalValues = [
    'Integrity',
    'Client Advocacy',
    'Legal Excellence',
    'Ethical Practice',
    'Justice'
  ];

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="attorney-resume-11"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Left Sidebar */}
      <div className="absolute top-0 left-0 w-[200px] h-full bg-[#1e2027] px-6 py-8">
        
        {/* Profile Photo */}
        <div className="flex justify-center mb-6">
          <div className="w-28 h-28 rounded-full bg-[#dfae4f] border-4 border-[#f2f1ed] overflow-hidden shadow-lg">
            {personalInfo.photo ? (
              <img 
                src={personalInfo.photo} 
                alt="Profile" 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-[#dfae4f] to-[#c49a3f] flex items-center justify-center">
                <span className="text-[#1e2027] text-2xl font-bold">
                  {personalInfo.fullName?.charAt(0) || 'Y'}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Name and Title */}
        <div className="text-center mb-8">
          <h1 className="text-xl font-bold text-[#f2f1ed] mb-2">
            {personalInfo.fullName || 'Your Name'}
          </h1>
          <p className="text-sm text-[#dfae4f] font-medium">
            {personalInfo.jobTitle || 'Attorney at Law'}
          </p>
        </div>

        {/* Contact Information */}
        <div className="mb-8">
          <h2 className="text-sm font-bold text-[#dfae4f] mb-4 uppercase tracking-wide flex items-center">
            <Mail className="w-3 h-3 mr-2" />
            Contact
          </h2>
          <div className="space-y-3">
            <div className="text-xs text-[#f2f1ed]">
              <p className="break-all">{personalInfo.email}</p>
            </div>
            <div className="text-xs text-[#f2f1ed]">
              <p>{personalInfo.phone}</p>
            </div>
            <div className="text-xs text-[#f2f1ed]">
              <p>{personalInfo.location}</p>
            </div>
            {personalInfo.website && (
              <div className="text-xs text-[#f2f1ed]">
                <p className="break-all">{personalInfo.website}</p>
              </div>
            )}
            {personalInfo.linkedinUrl && (
              <div className="text-xs text-[#f2f1ed]">
                <p>LinkedIn Profile</p>
              </div>
            )}
          </div>
        </div>

        {/* Skills */}
        <div className="mb-8">
          <h2 className="text-sm font-bold text-[#dfae4f] mb-4 uppercase tracking-wide flex items-center">
            <Scale className="w-3 h-3 mr-2" />
            Legal Skills
          </h2>
          <div className="space-y-2">
            {skills.slice(0, 8).map((skill, index) => (
              <div key={index} className="flex items-center">
                <div className="w-2 h-2 bg-[#dfae4f] rounded-full mr-2 flex-shrink-0"></div>
                <span className="text-xs text-[#f2f1ed]">{skill}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Professional Values */}
        <div className="mb-8">
          <h2 className="text-sm font-bold text-[#dfae4f] mb-4 uppercase tracking-wide">
            Values
          </h2>
          <div className="space-y-2">
            {professionalValues.map((value, index) => (
              <div key={index} className="flex items-center">
                <div className="w-1 h-4 bg-[#dfae4f] mr-2 flex-shrink-0"></div>
                <span className="text-xs text-[#f2f1ed]">{value}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Education */}
        <div>
          <h2 className="text-sm font-bold text-[#dfae4f] mb-4 uppercase tracking-wide flex items-center">
            <GraduationCap className="w-3 h-3 mr-2" />
            Education
          </h2>
          <div className="space-y-4">
            {education.slice(0, 2).map((edu, index) => (
              <div key={index}>
                <h3 className="font-semibold text-xs text-[#f2f1ed] leading-tight">
                  {edu.degree}
                </h3>
                <p className="text-xs text-[#dfae4f] font-medium">
                  {edu.institution}
                </p>
                <p className="text-xs text-[#f2f1ed]/70">
                  {new Date(edu.startDate).getFullYear()}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="absolute top-0 left-[200px] w-[394px] h-full px-8 py-8 bg-[#f2f1ed]">
        
        {/* Professional Profile */}
        {summary && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-[#353743] mb-4 flex items-center">
              <div className="w-1 h-8 bg-[#dfae4f] mr-3"></div>
              Professional Profile
            </h2>
            <p className="text-sm text-[#353743] leading-relaxed">
              {summary}
            </p>
          </div>
        )}

        {/* Professional Experience */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-[#353743] mb-6 flex items-center">
            <div className="w-1 h-8 bg-[#dfae4f] mr-3"></div>
            Professional Experience
          </h2>
          <div className="space-y-6">
            {experience.slice(0, 4).map((exp, index) => (
              <div key={index} className="relative bg-white p-4 rounded-lg shadow-sm">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-bold text-[#353743] text-base">
                    {exp.position}
                  </h3>
                  <span className="text-xs text-[#353743]/70 bg-[#dfae4f]/20 px-2 py-1 rounded">
                    {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                  </span>
                </div>
                <p className="text-sm font-semibold text-[#dfae4f] mb-1">
                  {exp.company}
                </p>
                <p className="text-xs text-[#353743]/70 mb-3">
                  {exp.location}
                </p>
                <div className="text-xs text-[#353743] space-y-1">
                  {exp.description.slice(0, 3).map((desc, i) => (
                    <p key={i} className="flex items-start">
                      <Briefcase className="w-3 h-3 mr-2 mt-0.5 text-[#dfae4f] flex-shrink-0" />
                      {desc}
                    </p>
                  ))}
                </div>
                {exp.skills && exp.skills.length > 0 && (
                  <div className="mt-3">
                    <div className="flex flex-wrap gap-1">
                      {exp.skills.slice(0, 4).map((skill, i) => (
                        <span key={i} className="text-xs bg-[#dfae4f]/10 text-[#353743] px-2 py-1 rounded border border-[#dfae4f]/20">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Bar Admissions / Certifications */}
        {resumeData.certifications && resumeData.certifications.length > 0 && (
          <div>
            <h2 className="text-2xl font-bold text-[#353743] mb-4 flex items-center">
              <div className="w-1 h-8 bg-[#dfae4f] mr-3"></div>
              Bar Admissions & Certifications
            </h2>
            <div className="space-y-3">
              {resumeData.certifications.slice(0, 4).map((cert, index) => (
                <div key={index} className="bg-white p-3 rounded-lg shadow-sm">
                  <h3 className="font-semibold text-sm text-[#353743]">
                    {cert.name}
                  </h3>
                  <p className="text-xs text-[#dfae4f] font-medium">
                    {cert.issuer}
                  </p>
                  <p className="text-xs text-[#353743]/70">
                    {new Date(cert.issueDate).getFullYear()}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
