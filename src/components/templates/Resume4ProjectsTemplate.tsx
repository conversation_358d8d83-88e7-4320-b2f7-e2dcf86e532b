import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, Folder, ExternalLink, Star } from 'lucide-react';

interface Resume4ProjectsTemplateProps {
  resumeData: ResumeContent;
}

export default function Resume4ProjectsTemplate({ resumeData }: Resume4ProjectsTemplateProps) {
  const { personalInfo, summary, experience, education, skills, projects } = resumeData;

  // Mock projects if none provided
  const displayProjects = projects && projects.length > 0 ? projects : [
    {
      id: '1',
      name: 'E-commerce Platform',
      description: 'Full-stack web application with React and Node.js',
      technologies: ['React', 'Node.js', 'MongoDB'],
      url: 'https://github.com/example/ecommerce',
      startDate: new Date('2023-01-01'),
      endDate: new Date('2023-06-01')
    },
    {
      id: '2',
      name: 'Mobile Task Manager',
      description: 'Cross-platform mobile app built with React Native',
      technologies: ['React Native', 'Firebase', 'Redux'],
      url: 'https://github.com/example/taskmanager',
      startDate: new Date('2023-07-01'),
      endDate: new Date('2023-12-01')
    },
    {
      id: '3',
      name: 'Data Visualization Dashboard',
      description: 'Interactive dashboard for business analytics',
      technologies: ['D3.js', 'Python', 'PostgreSQL'],
      url: 'https://github.com/example/dashboard',
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-03-01')
    },
    {
      id: '4',
      name: 'AI Chat Assistant',
      description: 'Intelligent chatbot using natural language processing',
      technologies: ['Python', 'TensorFlow', 'FastAPI'],
      url: 'https://github.com/example/chatbot',
      startDate: new Date('2024-04-01'),
      endDate: null
    }
  ];

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="resume-4-projects"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Header */}
      <div className="absolute top-0 left-0 w-full h-[100px] bg-gradient-to-r from-[#2c3e50] to-[#3498db] px-8 py-6">
        <div className="flex items-center justify-between h-full">
          <div>
            <h1 className="text-3xl font-bold text-white mb-1">
              {personalInfo.fullName || 'Your Name'}
            </h1>
            <p className="text-lg text-white/90 flex items-center">
              <Folder className="w-5 h-5 mr-2" />
              {personalInfo.jobTitle || 'Full Stack Developer'}
            </p>
          </div>
          
          {/* Contact */}
          <div className="text-right text-white text-sm space-y-1">
            <div className="flex items-center justify-end">
              <Mail className="w-4 h-4 mr-2" />
              <span>{personalInfo.email}</span>
            </div>
            <div className="flex items-center justify-end">
              <Phone className="w-4 h-4 mr-2" />
              <span>{personalInfo.phone}</span>
            </div>
            <div className="flex items-center justify-end">
              <MapPin className="w-4 h-4 mr-2" />
              <span>{personalInfo.location}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[120px] left-0 w-full h-[722px] px-8 py-6">
        
        {/* About */}
        {summary && (
          <div className="mb-6">
            <h2 className="text-xl font-bold text-[#2c3e50] mb-3 border-b-2 border-[#3498db] pb-1">
              About
            </h2>
            <p className="text-sm text-[#2c3e50] leading-relaxed">
              {summary}
            </p>
          </div>
        )}

        {/* Featured Projects Grid */}
        <div className="mb-8">
          <h2 className="text-xl font-bold text-[#2c3e50] mb-4 border-b-2 border-[#3498db] pb-1">
            Featured Projects
          </h2>
          <div className="grid grid-cols-2 gap-4">
            {displayProjects.slice(0, 4).map((project, index) => (
              <div key={index} className="bg-gradient-to-br from-[#f8f9fa] to-white p-4 rounded-lg border border-[#3498db]/20 shadow-sm hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-bold text-[#2c3e50] text-base flex items-center">
                    <Folder className="w-4 h-4 mr-2 text-[#3498db]" />
                    {project.name}
                  </h3>
                  {project.url && (
                    <ExternalLink className="w-4 h-4 text-[#3498db] cursor-pointer" />
                  )}
                </div>
                <p className="text-sm text-[#2c3e50]/80 mb-3 leading-relaxed">
                  {project.description}
                </p>
                <div className="flex flex-wrap gap-1 mb-2">
                  {project.technologies?.slice(0, 3).map((tech, i) => (
                    <span key={i} className="text-xs bg-[#3498db]/10 text-[#3498db] px-2 py-1 rounded border border-[#3498db]/20">
                      {tech}
                    </span>
                  ))}
                </div>
                <div className="text-xs text-[#2c3e50]/60">
                  {new Date(project.startDate).getFullYear()} - {project.endDate ? new Date(project.endDate).getFullYear() : 'Present'}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Experience & Skills Grid */}
        <div className="grid grid-cols-2 gap-8">
          
          {/* Experience */}
          <div>
            <h2 className="text-xl font-bold text-[#2c3e50] mb-4 border-b-2 border-[#3498db] pb-1">
              Experience
            </h2>
            <div className="space-y-4">
              {experience.slice(0, 2).map((exp, index) => (
                <div key={index}>
                  <div className="flex justify-between items-start mb-1">
                    <h3 className="font-bold text-[#2c3e50] text-sm">
                      {exp.position}
                    </h3>
                    <span className="text-xs text-[#2c3e50]/70 bg-[#f8f9fa] px-2 py-1 rounded">
                      {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                    </span>
                  </div>
                  <p className="text-sm font-semibold text-[#3498db] mb-1">
                    {exp.company}
                  </p>
                  <div className="text-xs text-[#2c3e50]/80 space-y-1">
                    {exp.description.slice(0, 2).map((desc, i) => (
                      <p key={i}>• {desc}</p>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Skills & Education */}
          <div>
            <h2 className="text-xl font-bold text-[#2c3e50] mb-4 border-b-2 border-[#3498db] pb-1">
              Skills
            </h2>
            <div className="grid grid-cols-2 gap-2 mb-6">
              {skills.slice(0, 8).map((skill, index) => (
                <div key={index} className="flex items-center">
                  <Star className="w-3 h-3 mr-1 text-[#3498db]" />
                  <span className="text-sm text-[#2c3e50]">{skill}</span>
                </div>
              ))}
            </div>

            <h2 className="text-xl font-bold text-[#2c3e50] mb-4 border-b-2 border-[#3498db] pb-1">
              Education
            </h2>
            <div className="space-y-3">
              {education.slice(0, 2).map((edu, index) => (
                <div key={index}>
                  <h3 className="font-bold text-[#2c3e50] text-sm">
                    {edu.degree}
                  </h3>
                  <p className="text-sm text-[#3498db]">
                    {edu.institution}
                  </p>
                  <p className="text-xs text-[#2c3e50]/70">
                    {new Date(edu.startDate).getFullYear()}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Links */}
        <div className="mt-6 pt-4 border-t border-[#3498db]/20">
          <div className="flex justify-center gap-6">
            {personalInfo.website && (
              <div className="flex items-center text-sm text-[#3498db]">
                <Globe className="w-4 h-4 mr-2" />
                <span>Portfolio</span>
              </div>
            )}
            {personalInfo.githubUrl && (
              <div className="flex items-center text-sm text-[#3498db]">
                <Github className="w-4 h-4 mr-2" />
                <span>GitHub</span>
              </div>
            )}
            {personalInfo.linkedinUrl && (
              <div className="flex items-center text-sm text-[#3498db]">
                <Linkedin className="w-4 h-4 mr-2" />
                <span>LinkedIn</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
