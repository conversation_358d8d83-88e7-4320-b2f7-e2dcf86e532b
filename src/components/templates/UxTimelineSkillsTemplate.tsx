import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, Clock, Star, Award, Zap } from 'lucide-react';

interface UxTimelineSkillsTemplateProps {
  resumeData: ResumeContent;
}

export default function UxTimelineSkillsTemplate({ resumeData }: UxTimelineSkillsTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="john-doe-ux-timeline-2"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Header */}
      <div className="absolute top-0 left-0 w-full h-[100px] bg-gradient-to-r from-[#0088ff] to-[#005ba2] px-8 py-6">
        <div className="flex items-center justify-between h-full">
          <div>
            <h1 className="text-3xl font-bold text-white mb-1">
              {personalInfo.fullName || '<PERSON>'}
            </h1>
            <p className="text-lg text-white/90 flex items-center">
              <Clock className="w-5 h-5 mr-2" />
              {personalInfo.jobTitle || 'UX Designer'}
            </p>
          </div>
          
          {/* Contact Info */}
          <div className="text-right text-white text-sm space-y-1">
            <div className="flex items-center justify-end">
              <Mail className="w-4 h-4 mr-2" />
              <span>{personalInfo.email}</span>
            </div>
            <div className="flex items-center justify-end">
              <Phone className="w-4 h-4 mr-2" />
              <span>{personalInfo.phone}</span>
            </div>
            <div className="flex items-center justify-end">
              <MapPin className="w-4 h-4 mr-2" />
              <span>{personalInfo.location}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[120px] left-0 w-full h-[722px] px-8 py-6">
        
        {/* Professional Summary */}
        {summary && (
          <div className="mb-8">
            <h2 className="text-xl font-bold text-[#005ba2] mb-4 border-b-2 border-[#6683eb] pb-2">
              Professional Summary
            </h2>
            <div className="bg-gradient-to-r from-[#6683eb]/10 to-transparent p-4 rounded-lg border-l-4 border-[#0088ff]">
              <p className="text-sm text-[#005ba2] leading-relaxed">
                {summary}
              </p>
            </div>
          </div>
        )}

        {/* Two Column Layout */}
        <div className="grid grid-cols-3 gap-8">
          
          {/* Left Column - Timeline Experience */}
          <div className="col-span-2">
            
            {/* Experience Timeline */}
            <div className="mb-8">
              <h2 className="text-xl font-bold text-[#005ba2] mb-6 border-b-2 border-[#6683eb] pb-2">
                Professional Experience
              </h2>
              <div className="relative">
                {/* Timeline line */}
                <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-[#6683eb]"></div>
                
                <div className="space-y-8">
                  {experience.slice(0, 4).map((exp, index) => (
                    <div key={index} className="relative pl-12">
                      {/* Timeline dot */}
                      <div className="absolute left-2 top-2 w-4 h-4 bg-[#0088ff] rounded-full border-2 border-white shadow-md"></div>
                      
                      <div className="bg-white p-4 rounded-lg shadow-sm border border-[#6683eb]/20">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-bold text-[#005ba2] text-base">
                            {exp.position}
                          </h3>
                          <span className="text-sm text-white bg-[#0088ff] px-3 py-1 rounded-full">
                            {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                          </span>
                        </div>
                        <p className="text-sm font-semibold text-[#6683eb] mb-2">
                          {exp.company} • {exp.location}
                        </p>
                        <div className="text-sm text-[#005ba2] space-y-1">
                          {exp.description.slice(0, 3).map((desc, i) => (
                            <p key={i} className="flex items-start">
                              <Zap className="w-3 h-3 mr-2 mt-1 text-[#6683eb] flex-shrink-0" />
                              {desc}
                            </p>
                          ))}
                        </div>
                        {exp.skills && exp.skills.length > 0 && (
                          <div className="mt-3">
                            <div className="flex flex-wrap gap-2">
                              {exp.skills.slice(0, 4).map((skill, i) => (
                                <span key={i} className="text-xs bg-[#6683eb]/10 text-[#005ba2] px-2 py-1 rounded border border-[#6683eb]/30">
                                  {skill}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Education */}
            <div>
              <h2 className="text-xl font-bold text-[#005ba2] mb-4 border-b-2 border-[#6683eb] pb-2">
                Education
              </h2>
              <div className="space-y-4">
                {education.slice(0, 2).map((edu, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow-sm border border-[#6683eb]/20">
                    <h3 className="font-bold text-[#005ba2] text-base">
                      {edu.degree}
                    </h3>
                    <p className="text-sm font-semibold text-[#6683eb]">
                      {edu.institution}
                    </p>
                    <p className="text-sm text-[#005ba2]/70">
                      {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                    </p>
                    {edu.gpa && (
                      <p className="text-sm text-[#005ba2]/70">
                        GPA: {edu.gpa}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Skills Focus */}
          <div className="col-span-1">
            
            {/* Core UX Skills */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#005ba2] mb-4">
                UX Skills
              </h2>
              <div className="space-y-4">
                {skills.slice(0, 6).map((skill, index) => {
                  const progress = [95, 90, 85, 88, 82, 87][index] || 80;
                  return (
                    <div key={index}>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-[#005ba2]">{skill}</span>
                        <span className="text-xs text-[#6683eb]">{progress}%</span>
                      </div>
                      <div className="w-full bg-[#6683eb]/20 rounded-full h-2">
                        <div 
                          className="bg-gradient-to-r from-[#0088ff] to-[#6683eb] h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        ></div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Design Tools */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#005ba2] mb-4">
                Design Tools
              </h2>
              <div className="grid grid-cols-1 gap-2">
                {[
                  'Figma',
                  'Sketch',
                  'Adobe XD',
                  'Principle',
                  'InVision',
                  'Miro'
                ].map((tool, index) => (
                  <div key={index} className="bg-[#6683eb]/10 p-2 rounded text-center border border-[#6683eb]/30">
                    <span className="text-sm font-medium text-[#005ba2]">{tool}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Achievements */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#005ba2] mb-4">
                Achievements
              </h2>
              <div className="space-y-3">
                {[
                  'UX Design Award 2023',
                  'Best Mobile App Design',
                  'User Experience Excellence',
                  'Design Innovation Prize'
                ].map((achievement, index) => (
                  <div key={index} className="flex items-center">
                    <Award className="w-4 h-4 mr-2 text-[#6683eb]" />
                    <span className="text-sm text-[#005ba2]">{achievement}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Additional Skills */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#005ba2] mb-4">
                Additional Skills
              </h2>
              <div className="flex flex-wrap gap-2">
                {skills.slice(6, 12).map((skill, index) => (
                  <span key={index} className="text-xs bg-[#6683eb]/10 text-[#005ba2] px-2 py-1 rounded border border-[#6683eb]/30">
                    {skill}
                  </span>
                ))}
              </div>
            </div>

            {/* Links */}
            <div>
              <h2 className="text-lg font-bold text-[#005ba2] mb-4">
                Portfolio Links
              </h2>
              <div className="space-y-2">
                {personalInfo.website && (
                  <div className="flex items-center text-sm text-[#0088ff]">
                    <Globe className="w-4 h-4 mr-2" />
                    <span>Portfolio</span>
                  </div>
                )}
                {personalInfo.linkedinUrl && (
                  <div className="flex items-center text-sm text-[#0088ff]">
                    <Linkedin className="w-4 h-4 mr-2" />
                    <span>LinkedIn</span>
                  </div>
                )}
                {personalInfo.githubUrl && (
                  <div className="flex items-center text-sm text-[#0088ff]">
                    <Github className="w-4 h-4 mr-2" />
                    <span>GitHub</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
