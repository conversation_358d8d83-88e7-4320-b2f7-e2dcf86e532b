import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, Layers, Zap, Target, Award } from 'lucide-react';

interface ProductDesignerTwoColumnTemplateProps {
  resumeData: ResumeContent;
}

export default function ProductDesignerTwoColumnTemplate({ resumeData }: ProductDesignerTwoColumnTemplateProps) {
  const { personalInfo, summary, experience, education, skills, projects } = resumeData;

  // Mock product portfolio if none provided
  const productPortfolio = projects && projects.length > 0 ? projects : [
    { id: '1', name: 'Mobile Banking App', description: 'End-to-end product design for fintech startup' },
    { id: '2', name: 'SaaS Analytics Platform', description: 'Product strategy and design for B2B platform' },
    { id: '3', name: 'E-learning Platform', description: 'User-centered design for educational technology' }
  ];

  return (
    <div 
      className="bg-[#f3f4f6] relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="john-doe-product-two-column"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Modern Header */}
      <div className="absolute top-0 left-0 w-full h-[120px] bg-gradient-to-r from-[#7c3aed] to-[#a78bfa] px-8 py-6">
        {/* Decorative elements */}
        <div className="absolute top-4 right-8 w-12 h-12 bg-white/10 rounded-full"></div>
        <div className="absolute bottom-4 right-16 w-6 h-6 bg-white/20 rounded-full"></div>
        <div className="absolute top-8 right-32 w-4 h-4 bg-white/15 rounded-full"></div>
        
        <div className="flex items-center justify-between h-full">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">
              {personalInfo.fullName || 'John Doe'}
            </h1>
            <p className="text-lg text-white/90 flex items-center">
              <Layers className="w-5 h-5 mr-2" />
              {personalInfo.jobTitle || 'Product Designer'}
            </p>
          </div>
          
          {/* Contact Info */}
          <div className="text-right text-white text-sm space-y-1">
            <div className="flex items-center justify-end">
              <Mail className="w-4 h-4 mr-2" />
              <span>{personalInfo.email}</span>
            </div>
            <div className="flex items-center justify-end">
              <Phone className="w-4 h-4 mr-2" />
              <span>{personalInfo.phone}</span>
            </div>
            <div className="flex items-center justify-end">
              <MapPin className="w-4 h-4 mr-2" />
              <span>{personalInfo.location}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Two Column Layout */}
      <div className="absolute top-[140px] left-0 w-full h-[702px] flex">
        
        {/* Left Column */}
        <div className="w-[297px] bg-white px-6 py-6 shadow-sm">
          
          {/* Professional Summary */}
          {summary && (
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#7c3aed] mb-4 flex items-center">
                <div className="w-1 h-6 bg-[#a78bfa] mr-3"></div>
                About
              </h2>
              <p className="text-sm text-[#333] leading-relaxed">
                {summary}
              </p>
            </div>
          )}

          {/* Core Skills */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#7c3aed] mb-4 flex items-center">
              <div className="w-1 h-6 bg-[#a78bfa] mr-3"></div>
              Core Skills
            </h2>
            <div className="space-y-3">
              {skills.slice(0, 6).map((skill, index) => {
                const progress = [95, 90, 85, 88, 82, 87][index] || 80;
                return (
                  <div key={index}>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-[#333]">{skill}</span>
                      <span className="text-xs text-[#7c3aed]">{progress}%</span>
                    </div>
                    <div className="w-full bg-[#f3f4f6] rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-[#7c3aed] to-[#a78bfa] h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progress}%` }}
                      ></div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Design Tools */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#7c3aed] mb-4 flex items-center">
              <div className="w-1 h-6 bg-[#a78bfa] mr-3"></div>
              Design Tools
            </h2>
            <div className="grid grid-cols-2 gap-2">
              {[
                'Figma',
                'Sketch',
                'Adobe XD',
                'Principle',
                'Framer',
                'InVision'
              ].map((tool, index) => (
                <div key={index} className="bg-[#f3f4f6] p-2 rounded text-center border border-[#7c3aed]/20">
                  <span className="text-xs font-medium text-[#7c3aed]">{tool}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Education */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#7c3aed] mb-4 flex items-center">
              <div className="w-1 h-6 bg-[#a78bfa] mr-3"></div>
              Education
            </h2>
            <div className="space-y-3">
              {education.slice(0, 2).map((edu, index) => (
                <div key={index} className="border-l-3 border-[#a78bfa] pl-3">
                  <h3 className="font-bold text-sm text-[#333]">
                    {edu.degree}
                  </h3>
                  <p className="text-xs text-[#7c3aed] font-medium">
                    {edu.institution}
                  </p>
                  <p className="text-xs text-[#333]/70">
                    {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Links */}
          <div>
            <h2 className="text-lg font-bold text-[#7c3aed] mb-4 flex items-center">
              <div className="w-1 h-6 bg-[#a78bfa] mr-3"></div>
              Portfolio
            </h2>
            <div className="space-y-2">
              {personalInfo.website && (
                <div className="flex items-center text-sm text-[#7c3aed]">
                  <Globe className="w-4 h-4 mr-2" />
                  <span>Portfolio Website</span>
                </div>
              )}
              {personalInfo.linkedinUrl && (
                <div className="flex items-center text-sm text-[#7c3aed]">
                  <Linkedin className="w-4 h-4 mr-2" />
                  <span>LinkedIn</span>
                </div>
              )}
              <div className="flex items-center text-sm text-[#7c3aed]">
                <Layers className="w-4 h-4 mr-2" />
                <span>Dribbble</span>
              </div>
              <div className="flex items-center text-sm text-[#7c3aed]">
                <Target className="w-4 h-4 mr-2" />
                <span>Behance</span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="w-[297px] px-6 py-6">
          
          {/* Professional Experience */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#7c3aed] mb-4 flex items-center">
              <div className="w-1 h-6 bg-[#a78bfa] mr-3"></div>
              Experience
            </h2>
            <div className="space-y-6">
              {experience.slice(0, 4).map((exp, index) => (
                <div key={index} className="bg-white p-4 rounded-lg shadow-sm border border-[#7c3aed]/10">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-bold text-[#333] text-sm">
                      {exp.position}
                    </h3>
                    <span className="text-xs text-white bg-[#7c3aed] px-2 py-1 rounded">
                      {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                    </span>
                  </div>
                  <p className="text-sm font-semibold text-[#a78bfa] mb-1">
                    {exp.company}
                  </p>
                  <p className="text-xs text-[#333]/70 mb-2">
                    {exp.location}
                  </p>
                  <div className="text-xs text-[#333] space-y-1">
                    {exp.description.slice(0, 2).map((desc, i) => (
                      <p key={i} className="flex items-start">
                        <Zap className="w-3 h-3 mr-1 mt-0.5 text-[#a78bfa] flex-shrink-0" />
                        {desc}
                      </p>
                    ))}
                  </div>
                  {exp.skills && exp.skills.length > 0 && (
                    <div className="mt-2">
                      <div className="flex flex-wrap gap-1">
                        {exp.skills.slice(0, 3).map((skill, i) => (
                          <span key={i} className="text-xs bg-[#f3f4f6] text-[#7c3aed] px-2 py-1 rounded border border-[#a78bfa]/20">
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Featured Projects */}
          <div>
            <h2 className="text-lg font-bold text-[#7c3aed] mb-4 flex items-center">
              <div className="w-1 h-6 bg-[#a78bfa] mr-3"></div>
              Featured Projects
            </h2>
            <div className="space-y-4">
              {productPortfolio.slice(0, 3).map((project, index) => (
                <div key={index} className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#7c3aed]">
                  <h3 className="font-bold text-[#333] text-sm flex items-center">
                    <Award className="w-4 h-4 mr-2 text-[#7c3aed]" />
                    {project.name}
                  </h3>
                  <p className="text-xs text-[#333]/80 mt-1">
                    {project.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
