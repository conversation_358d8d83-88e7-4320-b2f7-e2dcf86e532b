import React from 'react';

const imgImage = "http://localhost:3845/assets/782c8fd63dfa9f71a2e4108c0b74ee596d15cc03.png";

interface ResumeData {
  personalInfo: {
    name: string;
    jobTitle: string;
    email: string;
    phone: string;
    photo?: string;
  };
  education: Array<{
    school: string;
    degree: string;
    year: string;
  }>;
  skills: string[];
  certifications: string[];
  software: string[];
  softSkills: string[];
  profile: string;
  experience: Array<{
    company: string;
    companyDescription?: string;
    location: string;
    dates: string;
    jobTitle: string;
    description: string;
  }>;
}

interface ProfessionalSidebarTemplateProps {
  data: ResumeData;
  isPreview?: boolean;
}

export default function ProfessionalSidebarTemplate({ data, isPreview = false }: ProfessionalSidebarTemplateProps) {
  return (
    <div className="bg-white content-stretch flex items-start justify-start relative size-full" data-template-id="professional-sidebar-1">
      {/* Left Sidebar */}
      <div className="bg-[#2d3542] box-border content-stretch flex flex-col h-full items-start justify-start pb-[25.911px] pt-[32.389px] px-[16.194px] relative shrink-0 w-[178.139px]">
        {/* Profile Section */}
        <div className="box-border content-stretch flex flex-col gap-[12.956px] items-center justify-start pb-[32.389px] pt-0 px-0 relative shrink-0 w-full">
          <div className="bg-[position:50%_50%,_0%_0%] bg-size-[cover,auto] bg-white relative rounded-[129.556px] shrink-0 size-[97.167px]" 
               style={{ backgroundImage: `url('${data.personalInfo.photo || imgImage}')` }}>
            <div className="box-border content-stretch flex flex-col gap-[9.069px] items-center justify-start overflow-clip p-[6.478px] size-[97.167px]" />
            <div aria-hidden="true" className="absolute border-[#bd9047] border-[3.239px] border-solid inset-0 pointer-events-none rounded-[129.556px]" />
          </div>
          <div className="content-stretch flex flex-col items-start justify-start leading-[0] not-italic relative shrink-0">
            <div className="font-['Poppins:Bold',_sans-serif] relative shrink-0 text-[#bd9047] text-[14.251px] w-[108.827px]">
              <p className="leading-[normal]">{data.personalInfo.name.toUpperCase()}</p>
            </div>
            <div className="font-['Poppins:Medium',_sans-serif] relative shrink-0 text-[7.773px] text-nowrap text-white tracking-[1.0105px]">
              <p className="leading-[6.478px] whitespace-pre">{data.personalInfo.jobTitle.toUpperCase()}</p>
            </div>
          </div>
        </div>

        {/* Divider */}
        <div className="box-border content-stretch flex flex-col items-start justify-start pb-[12.956px] pt-0 px-0 relative shrink-0 w-full">
          <div className="bg-[rgba(0,0,0,0.4)] h-[0.648px] shrink-0 w-full" />
        </div>

        {/* Contact Information */}
        <div className="box-border content-stretch flex flex-col items-start justify-start pb-[12.956px] pt-0 px-0 relative shrink-0 w-full">
          <div className="content-stretch flex font-['Poppins:Bold',_sans-serif] gap-[6.478px] items-start justify-start leading-[0] not-italic relative shrink-0 text-[7.773px] text-white">
            <div className="relative shrink-0 w-[29.15px]">
              <p className="leading-[normal]">Email:</p>
            </div>
            <div className="relative shrink-0 text-nowrap">
              <p className="leading-[normal] whitespace-pre">{data.personalInfo.email}</p>
            </div>
          </div>
          <div className="content-stretch flex font-['Poppins:Bold',_sans-serif] gap-[6.478px] items-start justify-start leading-[0] not-italic relative shrink-0 text-[7.773px] text-white">
            <div className="relative shrink-0 w-[29.15px]">
              <p className="leading-[normal]">Phone:</p>
            </div>
            <div className="relative shrink-0 text-nowrap">
              <p className="leading-[normal] whitespace-pre">{data.personalInfo.phone}</p>
            </div>
          </div>
        </div>

        {/* Divider */}
        <div className="box-border content-stretch flex flex-col items-start justify-start pb-[25.911px] pt-0 px-0 relative shrink-0 w-full">
          <div className="bg-[rgba(0,0,0,0.4)] h-[0.648px] shrink-0 w-full" />
        </div>

        {/* Education */}
        <div className="content-stretch flex flex-col items-start justify-start relative shrink-0 mb-4">
          <div className="content-stretch flex flex-col items-start justify-start relative shrink-0 w-[161.944px]">
            <div className="font-['Poppins:SemiBold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#bd9047] text-[11.66px] text-nowrap">
              <p className="leading-[normal] whitespace-pre">EDUCATION</p>
            </div>
          </div>
          {data.education.map((edu, index) => (
            <div key={index} className="box-border content-stretch flex flex-col items-start justify-start leading-[0] not-italic pb-[12.956px] pt-0 px-0 relative shrink-0 text-[7.773px] text-white w-[161.944px]">
              <div className="font-['Poppins:Bold',_sans-serif] min-w-full relative shrink-0" style={{ width: "min-content" }}>
                <p className="leading-[normal]">{edu.school}</p>
              </div>
              <div className="font-['Poppins:Regular',_sans-serif] relative shrink-0 text-nowrap">
                <p className="leading-[normal] whitespace-pre">{edu.degree}</p>
              </div>
              <div className="font-['Poppins:Regular',_sans-serif] relative shrink-0 text-nowrap">
                <p className="leading-[normal] whitespace-pre">{edu.year}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Skills */}
        <div className="content-stretch flex flex-col items-start justify-start relative shrink-0 mb-4">
          <div className="content-stretch flex flex-col items-start justify-start relative shrink-0 w-[161.944px]">
            <div className="font-['Poppins:SemiBold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#bd9047] text-[11.66px] text-nowrap">
              <p className="leading-[normal] whitespace-pre">SKILLS</p>
            </div>
          </div>
          <div className="box-border content-stretch flex flex-col items-start justify-start pb-[12.956px] pt-0 px-0 relative shrink-0 w-[161.944px]">
            <div className="font-['Poppins:Regular',_sans-serif] leading-[0] not-italic relative shrink-0 text-[7.126px] text-white w-full">
              <ul className="list-disc">
                {data.skills.map((skill, index) => (
                  <li key={index} className="mb-0 ms-[10.689px]">
                    <span className="leading-[normal]">{skill}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Additional sections can be added here following the same pattern */}
      </div>

      {/* Right Content */}
      <div className="basis-0 box-border content-stretch flex flex-col grow items-start justify-start min-h-px min-w-px relative shrink-0">
        {/* Profile */}
        <div className="box-border content-stretch flex flex-col items-start justify-start pb-[25.911px] pt-[32.389px] px-[25.911px] relative shrink-0 w-full">
          <div className="content-stretch flex flex-col items-start justify-start relative shrink-0 w-full">
            <div className="font-['Poppins:SemiBold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#bd9047] text-[11.66px] text-nowrap">
              <p className="leading-[normal] whitespace-pre">PROFILE</p>
            </div>
          </div>
          <div className="content-stretch flex flex-col items-start justify-start relative shrink-0 w-full">
            <div className="font-['Poppins:Regular',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#444444] text-[7.773px] w-full">
              <p className="leading-[normal]">{data.profile}</p>
            </div>
          </div>
        </div>

        {/* Experience */}
        <div className="box-border content-stretch flex flex-col items-start justify-start pb-[25.911px] pt-0 px-[25.911px] relative shrink-0 w-full">
          <div className="content-stretch flex flex-col items-start justify-start relative shrink-0 w-full">
            <div className="font-['Poppins:SemiBold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#bd9047] text-[11.66px] text-nowrap mb-4">
              <p className="leading-[normal] whitespace-pre">EXPERIENCE</p>
            </div>
          </div>
          
          {data.experience.map((exp, index) => (
            <div key={index} className="content-stretch flex items-start justify-start relative shrink-0 w-full mb-6">
              <div className="box-border content-stretch flex flex-col items-start justify-start leading-[0] pb-[12.956px] pt-0 px-0 relative shrink-0 text-[#444444] w-[97.167px]">
                <div className="font-['Poppins:Bold',_sans-serif] min-w-full not-italic relative shrink-0 text-[7.773px]" style={{ width: "min-content" }}>
                  <p className="leading-[normal]">{exp.company}</p>
                </div>
                {exp.companyDescription && (
                  <div className="font-['Poppins:Medium',_sans-serif] min-w-full not-italic relative shrink-0 text-[7.126px]" style={{ width: "min-content" }}>
                    <p className="leading-[normal]">{exp.companyDescription}</p>
                  </div>
                )}
                <div className="font-['Poppins:Italic',_sans-serif] italic relative shrink-0 text-[7.126px] text-nowrap">
                  <p className="leading-[normal] whitespace-pre">{exp.location}</p>
                </div>
                <div className="font-['Poppins:Italic',_sans-serif] italic relative shrink-0 text-[7.126px] text-nowrap">
                  <p className="leading-[normal] whitespace-pre">{exp.dates}</p>
                </div>
              </div>
              <div className="basis-0 box-border content-stretch flex flex-col grow items-start justify-start leading-[0] min-h-px min-w-px not-italic pb-[12.956px] pt-0 px-0 relative shrink-0 text-[#444444]">
                <div className="font-['Poppins:Bold',_sans-serif] relative shrink-0 text-[7.773px] w-full">
                  <p className="leading-[normal]">{exp.jobTitle}</p>
                </div>
                <div className="font-['Poppins:Regular',_sans-serif] relative shrink-0 text-[7.126px] w-full">
                  <p className="leading-[normal]">{exp.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
