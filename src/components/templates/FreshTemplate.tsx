import { User, Briefcase, GraduationCap, Award, Globe } from 'lucide-react';

export function FreshTemplate() {
  const resumeData = {
    personalInfo: {
      name: "<PERSON>",
      title: "Administrative Assistant",
      contact: {
        phone: "(*************",
        email: "<EMAIL>",
        address: "San Antonio, TX 78023"
      },
    },
    summary: "Administrative assistant with 9+ years of experience organizing presentations, preparing facility reports, and maintaining the utmost confidentiality. Possess a B.A. in history and experience in Microsoft Excel. Looking to leverage my wealth of knowledge and experience into an open administrative assistant role at your organization.",
    skills: [
      "Analytical Thinking",
      "Tolerant & Flexible", 
      "Team Leadership",
      "Organization & Prioritization",
      "Strong Communication",
      "Web app development",
      "Computer engineering",
      "Web security",
      "Critical thinking",
      "Effective communication"
    ],
    certifications: [
      { name: "CPR Certified", year: "2018" },
      { name: "PMP Certified", year: "2009" },
      { name: "HIPAA Certified", year: "2018" },
      { name: "CAA Certified", year: "2015" }
    ],
    languages: [
      { language: "Irish", level: "Native" },
      { language: "English", level: "Native" }
    ],
    experience: [
      {
        title: "Administrative Assistant",
        company: "Redford & Sons",
        location: "Boston, MA",
        dates: "Sep 2017 – Present",
        responsibilities: [
          "Schedule and coordinate meetings, appointments, and travel arrangements for supervisors, managers, and staff",
          "Trained 2 administrative assistants during a period of company expansion to ensure retention to detail and adherence to company practices",
          "Developed new filing and organizational practices, saving the company $3,000 per year in contracted labor expenses"
        ]
      },
      {
        title: "Secretary",
        company: "Bright Spot LTD",
        location: "Boston",
        dates: "Jun 2016 – Aug 2017",
        responsibilities: [
          "Typed documents such as correspondence, drafts, memos, and emails, and prepared 3 reports weekly for management",
          "Opened, sorted, and distributed incoming messages and correspondence to the appropriate personnel",
          "Purchased and maintained office supply inventories, and always careful to adhere to budgeting practices",
          "Greeted visitors and determined to whom and when they could speak with specific individuals"
        ]
      },
      {
        title: "Secretary",
        company: "Winfield & Winfield",
        location: "Boston, MA",
        dates: "Jun 2013 – Aug 2016",
        responsibilities: [
          "Streamlined direct office services such as departmental finances, records, and personnel issues, reducing waiting time",
          "Read and analyzed incoming reports and memos to determine their importance and planned their distribution across staff",
          "Developed and maintained strong relationships with community referral sources, such as schools, churches, and local businesses",
          "Organized a successful fundraiser, bringing in over $20,000 for the community center to upgrade old equipment"
        ]
      }
    ],
    education: [
      {
        degree: "Bachelor of Arts (B.A.) in in Finance",
        university: "Brown University",
        location: "Providence, RI",
        dates: "05/2009"
      },
      {
        degree: "Associate of Arts in Business",
        university: "San Antonio Community College",
        location: "San Antonio, TX",
        dates: "05/2007"
      }
    ]
  };

  const { personalInfo, summary, skills, certifications, languages, experience, education } = resumeData;

  return (
    <div className="bg-white mx-auto w-[900px] print:w-[794px] shadow-[0_4px_16px_rgba(0,0,0,0.08)] border border-gray-200">
      {/* Header */}
      <div className="px-8 pt-8 pb-4">
        <h1 className="text-4xl font-bold text-gray-800 mb-1">{personalInfo.name}</h1>
        <h2 className="text-xl text-teal-600 font-medium">{personalInfo.title}</h2>
      </div>

      {/* Contact Bar */}
      <div className="bg-teal-600 text-white px-8 py-3 flex justify-between items-center text-sm">
        <div>
          <span className="font-semibold">PHONE</span> {personalInfo.contact.phone}
        </div>
        <div>
          <span className="font-semibold">EMAIL</span> {personalInfo.contact.email}
        </div>
        <div>
          <span className="font-semibold">ADDRESS</span> {personalInfo.contact.address}
        </div>
      </div>

      {/* Main Content - Single Column */}
      <div className="px-8 py-6 space-y-6">
        {/* Professional Summary */}
        <div>
          <div className="flex items-center mb-3">
            <User className="text-teal-600 mr-2" size={20} />
            <h3 className="font-bold text-gray-700 tracking-wide">PROFESSIONAL SUMMARY</h3>
          </div>
          <p className="text-sm text-gray-700 leading-relaxed">{summary}</p>
        </div>

        {/* Skills */}
        <div>
          <div className="flex items-center mb-3">
            <Award className="text-teal-600 mr-2" size={20} />
            <h3 className="font-bold text-gray-700 tracking-wide">SKILLS</h3>
          </div>
          <div className="grid grid-cols-2 gap-2">
            {skills.map((skill, index) => (
              <div key={index} className="flex items-center text-sm text-gray-700">
                <span className="w-2 h-2 bg-teal-600 rounded-full mr-2"></span>
                {skill}
              </div>
            ))}
          </div>
        </div>

        {/* Experience */}
        <div>
          <div className="flex items-center mb-4">
            <Briefcase className="text-teal-600 mr-2" size={20} />
            <h3 className="font-bold text-gray-700 tracking-wide">EXPERIENCE</h3>
          </div>
          
          {experience.map((job, index) => (
            <div key={index} className="mb-6">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-semibold text-gray-800 text-sm">{job.title}</h4>
                  <p className="text-sm text-gray-600 font-medium">{job.company}</p>
                </div>
                <div className="text-right text-xs text-gray-500">
                  <div>{job.dates}</div>
                  <div>{job.location}</div>
                </div>
              </div>
              <ul className="text-xs text-gray-700 space-y-1">
                {job.responsibilities.map((resp, i) => (
                  <li key={i} className="flex items-start">
                    <span className="w-1 h-1 bg-gray-400 rounded-full mr-2 mt-2 flex-shrink-0"></span>
                    <span>{resp}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Education */}
        <div>
          <div className="flex items-center mb-4">
            <GraduationCap className="text-teal-600 mr-2" size={20} />
            <h3 className="font-bold text-gray-700 tracking-wide">EDUCATION</h3>
          </div>
          
          {education.map((edu, index) => (
            <div key={index} className="mb-4">
              <div className="flex justify-between items-start">
                <div>
                  <h4 className="font-semibold text-gray-800 text-sm">{edu.degree}</h4>
                  <p className="text-sm text-gray-600 font-medium">{edu.university}</p>
                </div>
                <div className="text-right text-xs text-gray-500">
                  <div>{edu.dates}</div>
                  <div>{edu.location}</div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Two Column Layout for Certifications and Languages */}
        <div className="grid grid-cols-2 gap-8">
          {/* Certifications */}
          <div>
            <div className="flex items-center mb-3">
              <Award className="text-teal-600 mr-2" size={20} />
              <h3 className="font-bold text-gray-700 tracking-wide">CERTIFICATIONS</h3>
            </div>
            <ul className="text-sm text-gray-700 space-y-1">
              {certifications.map((cert, index) => (
                <li key={index} className="flex items-center">
                  <span className="w-2 h-2 bg-teal-600 rounded-full mr-2"></span>
                  {cert.name} / {cert.year}
                </li>
              ))}
            </ul>
          </div>

          {/* Languages */}
          <div>
            <div className="flex items-center mb-3">
              <Globe className="text-teal-600 mr-2" size={20} />
              <h3 className="font-bold text-gray-700 tracking-wide">LANGUAGES</h3>
            </div>
            <div className="text-sm text-gray-700 space-y-1">
              {languages.map((lang, index) => (
                <div key={index}>
                  <span className="text-teal-600 font-medium">{lang.language}</span>
                  <span className="text-gray-600"> | {lang.level}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}