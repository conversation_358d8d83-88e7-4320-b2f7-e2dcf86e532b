import { Mail, Phone, MapPin, Linkedin, Calendar, Building2, GraduationCap, Award, TrendingUp, Users, Target, Heart, Globe, Briefcase } from 'lucide-react';
import { ImageWithFallback } from '../figma/ImageWithFallback';

export function ExecutiveTemplate() {
  return (
    <div className="mx-auto bg-white w-[900px] print:w-[794px] shadow-[0_4px_16px_rgba(0,0,0,0.08)] border border-gray-200 px-8 py-10">
      {/* Header */}
      <div className="flex items-start justify-between mb-8">
        <div className="flex-1">
          <h1 className="text-[36px] font-bold text-black mb-2 tracking-tight">MICHAEL CHEN</h1>
          <p className="text-[16px] text-[#1e88e5] mb-4 font-semibold">
            Senior Operations Executive | Process Excellence | Digital Transformation
          </p>
          
          {/* Contact Info */}
          <div className="text-[14px] text-gray-600 mb-2 leading-relaxed">
            <span>+****************</span>
            <span className="mx-3">|</span>
            <span><EMAIL></span>
            <span className="mx-3">|</span>
            <span>linkedin.com/in/michael-chen</span>
            <span className="mx-3">|</span>
            <span>Chicago, Illinois</span>
          </div>
        </div>
        
        {/* Profile Photo */}
        <div className="w-20 h-20 rounded-full overflow-hidden bg-gray-200 ml-8 flex-shrink-0 border-4 border-gray-100">
          <ImageWithFallback 
            src="https://images.unsplash.com/photo-1739298061757-7a3339cee982?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxwcm9mZXNzaW9uYWwlMjBidXNpbmVzcyUyMGV4ZWN1dGl2ZSUyMGhlYWRzaG90fGVufDF8fHx8MTc1NzIxNzQ3OXww&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral"
            alt="Profile"
            className="w-full h-full object-cover"
          />
        </div>
      </div>

      {/* Main Content - Three Columns */}
      <div className="grid grid-cols-12 gap-8">
        {/* Left Column */}
        <div className="col-span-3 space-y-8">
          {/* Summary */}
          <div>
            <h3 className="text-[14px] font-bold text-black mb-3 uppercase tracking-[1px] border-b border-gray-300 pb-2">SUMMARY</h3>
            <p className="text-[13px] text-gray-700 leading-relaxed">
              Dedicated Senior Operations Executive with 7+ years of experience in B2B strategy development, process optimization, and team leadership. Proven track record in driving operational excellence and fostering lasting client relationships.
            </p>
          </div>

          {/* Key Achievements */}
          <div>
            <h3 className="text-[14px] font-bold text-black mb-3 uppercase tracking-[1px] border-b border-gray-300 pb-2">KEY ACHIEVEMENTS</h3>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-7 h-7 bg-green-100 rounded-full flex items-center justify-center shrink-0 mt-0.5">
                  <TrendingUp className="w-3.5 h-3.5 text-green-600" />
                </div>
                <div>
                  <h4 className="text-[12px] font-bold text-black leading-tight mb-1">Top Regional Sales Performer</h4>
                  <p className="text-[11px] text-gray-600 leading-relaxed">
                    Recognized as top-performing sales executive, achieving 150% of personal sales targets consistently for 2+ years.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-7 h-7 bg-blue-100 rounded-full flex items-center justify-center shrink-0 mt-0.5">
                  <Award className="w-3.5 h-3.5 text-blue-600" />
                </div>
                <div>
                  <h4 className="text-[12px] font-bold text-black leading-tight mb-1">President's Club Award Recipient</h4>
                  <p className="text-[11px] text-gray-600 leading-relaxed">
                    Awarded for exceptional contribution to the company, exceeding revenue targets by 30% at Salesforce.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-7 h-7 bg-orange-100 rounded-full flex items-center justify-center shrink-0 mt-0.5">
                  <Users className="w-3.5 h-3.5 text-orange-600" />
                </div>
                <div>
                  <h4 className="text-[12px] font-bold text-black leading-tight mb-1">Customer Retention Excellence</h4>
                  <p className="text-[11px] text-gray-600 leading-relaxed">
                    Implemented retention strategies that maintained 95% customer satisfaction rate and minimized customer churn.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-7 h-7 bg-purple-100 rounded-full flex items-center justify-center shrink-0 mt-0.5">
                  <Target className="w-3.5 h-3.5 text-purple-600" />
                </div>
                <div>
                  <h4 className="text-[12px] font-bold text-black leading-tight mb-1">CRM Implementation Leader</h4>
                  <p className="text-[11px] text-gray-600 leading-relaxed">
                    Led the successful implementation of a comprehensive CRM system, boosting sales productivity by 25%.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Passions */}
          <div>
            <h3 className="text-[14px] font-bold text-black mb-3 uppercase tracking-[1px] border-b border-gray-300 pb-2">PASSIONS</h3>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <Globe className="w-3.5 h-3.5 text-[#1e88e5] mt-1 flex-shrink-0" />
                <div>
                  <h4 className="text-[12px] font-bold text-black mb-1">Technological Innovations in Sales</h4>
                  <p className="text-[11px] text-gray-600 leading-relaxed">
                    Passionate about leveraging cutting-edge technology to drive sales efficiency and success.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <Heart className="w-3.5 h-3.5 text-[#1e88e5] mt-1 flex-shrink-0" />
                <div>
                  <h4 className="text-[12px] font-bold text-black mb-1">Mentoring Aspiring Sales Professionals</h4>
                  <p className="text-[11px] text-gray-600 leading-relaxed">
                    Enjoy coaching and guiding the next generation of sales leaders, sharing knowledge and expertise.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Middle Column */}
        <div className="col-span-6 space-y-8">
          {/* Experience */}
          <div>
            <h3 className="text-[14px] font-bold text-black mb-3 uppercase tracking-[1px] border-b border-gray-300 pb-2">EXPERIENCE</h3>
            
            <div className="space-y-6">
              {/* Senior Business Development Manager */}
              <div>
                <div className="flex justify-between items-start mb-2">
                  <h4 className="text-[13px] font-bold text-black">Senior Business Development Manager</h4>
                  <span className="text-[12px] text-gray-500">01/2020 - Present</span>
                </div>
                <div className="flex items-center gap-2 mb-3">
                  <Building2 className="w-3 h-3 text-[#1e88e5]" />
                  <span className="text-[12px] font-semibold text-[#1e88e5]">Oracle</span>
                  <span className="text-[12px] text-gray-500">📍 San Antonio, Texas</span>
                </div>
                <div className="text-[12px] text-gray-700 leading-relaxed space-y-1">
                  <p>• Spearheaded a sales team in executing intricate B2B strategies, resulting in a 20% annual increase in new business accounts.</p>
                  <p>• Managed and nurtured relationships of over 150 prospects, consistently meeting or exceeding quarterly targets by a margin of 15%.</p>
                  <p>• Crafted bespoke sales pitches and presentations to meet diverse client needs and satisfaction rates.</p>
                  <p>• Developed strategic partnerships with key stakeholders, enhancing company visibility in the enterprise market segment.</p>
                </div>
              </div>

              {/* Regional Sales Manager */}
              <div>
                <div className="flex justify-between items-start mb-2">
                  <h4 className="text-[13px] font-bold text-black">Regional Sales Manager</h4>
                  <span className="text-[12px] text-gray-500">03/2018 - 12/2019</span>
                </div>
                <div className="flex items-center gap-2 mb-3">
                  <Building2 className="w-3 h-3 text-[#1e88e5]" />
                  <span className="text-[12px] font-semibold text-[#1e88e5]">Salesforce</span>
                  <span className="text-[12px] text-gray-500">📍 Austin, Texas</span>
                </div>
                <div className="text-[12px] text-gray-700 leading-relaxed space-y-1">
                  <p>• Led regional sales initiatives that contributed to a 35% increase in overall revenue within assigned territory.</p>
                  <p>• Collaborated with cross-functional teams to develop and implement comprehensive go-to-market strategies.</p>
                  <p>• Mentored junior sales representatives, resulting in improved team performance and reduced turnover rates.</p>
                  <p>• Established strong client relationships through consistent follow-up and exceptional customer service delivery.</p>
                </div>
              </div>

              {/* Account Executive */}
              <div>
                <div className="flex justify-between items-start mb-2">
                  <h4 className="text-[13px] font-bold text-black">Account Executive</h4>
                  <span className="text-[12px] text-gray-500">06/2016 - 02/2018</span>
                </div>
                <div className="flex items-center gap-2 mb-3">
                  <Building2 className="w-3 h-3 text-[#1e88e5]" />
                  <span className="text-[12px] font-semibold text-[#1e88e5]">HubSpot</span>
                  <span className="text-[12px] text-gray-500">📍 Chicago, Illinois</span>
                </div>
                <div className="text-[12px] text-gray-700 leading-relaxed space-y-1">
                  <p>• Generated qualified leads through strategic prospecting and relationship building activities.</p>
                  <p>• Achieved 120% of sales quota consistently, ranking in top 10% of sales team performance metrics.</p>
                  <p>• Participated in product development discussions, providing valuable customer feedback and insights.</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="col-span-3 space-y-8">
          {/* Core Skills */}
          <div>
            <h3 className="text-[14px] font-bold text-black mb-3 uppercase tracking-[1px] border-b border-gray-300 pb-2">CORE SKILLS</h3>
            <div className="space-y-2">
              <div className="bg-gray-50 px-3 py-2 rounded text-[11px] font-semibold text-gray-800 text-center">B2B Sales Strategy</div>
              <div className="bg-gray-50 px-3 py-2 rounded text-[11px] font-semibold text-gray-800 text-center">CRM Management</div>
              <div className="bg-gray-50 px-3 py-2 rounded text-[11px] font-semibold text-gray-800 text-center">Team Leadership</div>
              <div className="bg-gray-50 px-3 py-2 rounded text-[11px] font-semibold text-gray-800 text-center">Client Relations</div>
              <div className="bg-gray-50 px-3 py-2 rounded text-[11px] font-semibold text-gray-800 text-center">Process Optimization</div>
              <div className="bg-gray-50 px-3 py-2 rounded text-[11px] font-semibold text-gray-800 text-center">Strategic Planning</div>
              <div className="bg-gray-50 px-3 py-2 rounded text-[11px] font-semibold text-gray-800 text-center">Market Analysis</div>
              <div className="bg-gray-50 px-3 py-2 rounded text-[11px] font-semibold text-gray-800 text-center">Revenue Growth</div>
            </div>
          </div>

          {/* Education */}
          <div>
            <h3 className="text-[14px] font-bold text-black mb-3 uppercase tracking-[1px] border-b border-gray-300 pb-2">EDUCATION</h3>
            <div className="space-y-4">
              <div>
                <h4 className="text-[12px] font-bold text-black">Master of Business Administration</h4>
                <p className="text-[11px] font-semibold text-[#1e88e5]">Northwestern University - Kellogg</p>
                <div className="text-[11px] text-gray-500 mt-1">
                  <span>📅 2014 - 2016</span>
                  <span className="mx-2">📍</span>
                  <span>Evanston, IL</span>
                </div>
                <p className="text-[10px] text-gray-600 mt-1">Concentration: Strategic Management & Operations</p>
              </div>
              
              <div>
                <h4 className="text-[12px] font-bold text-black">Bachelor of Science in Business Administration</h4>
                <p className="text-[11px] font-semibold text-[#1e88e5]">University of Illinois at Chicago</p>
                <div className="text-[11px] text-gray-500 mt-1">
                  <span>📅 2010 - 2014</span>
                  <span className="mx-2">📍</span>
                  <span>Chicago, IL</span>
                </div>
                <p className="text-[10px] text-gray-600 mt-1">Major: Marketing & Sales Management</p>
              </div>
            </div>
          </div>

          {/* Certifications */}
          <div>
            <h3 className="text-[14px] font-bold text-black mb-3 uppercase tracking-[1px] border-b border-gray-300 pb-2">CERTIFICATIONS</h3>
            <div className="space-y-3">
              <div>
                <h4 className="text-[12px] font-bold text-black">Salesforce Certified Administrator</h4>
                <p className="text-[10px] text-gray-600">Advanced CRM management and automation</p>
              </div>
              
              <div>
                <h4 className="text-[12px] font-bold text-black">Google Analytics Professional</h4>
                <p className="text-[10px] text-gray-600">Data analysis and business intelligence</p>
              </div>
              
              <div>
                <h4 className="text-[12px] font-bold text-black">HubSpot Sales Software Certified</h4>
                <p className="text-[10px] text-gray-600">Inbound sales methodology and tools</p>
              </div>
            </div>
          </div>

          {/* Languages */}
          <div>
            <h3 className="text-[14px] font-bold text-black mb-3 uppercase tracking-[1px] border-b border-gray-300 pb-2">LANGUAGES</h3>
            <div className="space-y-3">
              <div>
                <span className="text-[12px] font-semibold text-gray-800">English</span>
                <div className="flex gap-1.5 mt-1">
                  <div className="w-2.5 h-2.5 bg-[#1e88e5] rounded-full"></div>
                  <div className="w-2.5 h-2.5 bg-[#1e88e5] rounded-full"></div>
                  <div className="w-2.5 h-2.5 bg-[#1e88e5] rounded-full"></div>
                  <div className="w-2.5 h-2.5 bg-[#1e88e5] rounded-full"></div>
                  <div className="w-2.5 h-2.5 bg-[#1e88e5] rounded-full"></div>
                </div>
                <span className="text-[10px] text-gray-500">Native</span>
              </div>
              
              <div>
                <span className="text-[12px] font-semibold text-gray-800">Mandarin</span>
                <div className="flex gap-1.5 mt-1">
                  <div className="w-2.5 h-2.5 bg-[#1e88e5] rounded-full"></div>
                  <div className="w-2.5 h-2.5 bg-[#1e88e5] rounded-full"></div>
                  <div className="w-2.5 h-2.5 bg-[#1e88e5] rounded-full"></div>
                  <div className="w-2.5 h-2.5 bg-[#1e88e5] rounded-full"></div>
                  <div className="w-2.5 h-2.5 bg-gray-300 rounded-full"></div>
                </div>
                <span className="text-[10px] text-gray-500">Fluent</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}