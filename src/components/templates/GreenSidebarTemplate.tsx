import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github } from 'lucide-react';

interface GreenSidebarTemplateProps {
  resumeData: ResumeContent;
}

export default function GreenSidebarTemplate({ resumeData }: GreenSidebarTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  // Skill progress calculation for circular indicators
  const getSkillProgress = (index: number) => {
    const progressValues = [85, 90, 75, 80, 88, 92, 78, 85];
    return progressValues[index % progressValues.length];
  };

  const CircularProgress = ({ value, size = 50, strokeWidth = 4 }: { value: number; size?: number; strokeWidth?: number }) => {
    const radius = (size - strokeWidth) / 2;
    const circumference = radius * 2 * Math.PI;
    const offset = circumference - (value / 100) * circumference;

    return (
      <div className="relative" style={{ width: size, height: size }}>
        <svg width={size} height={size} className="transform -rotate-90">
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="#e4f2ef"
            strokeWidth={strokeWidth}
            fill="transparent"
          />
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="#7d4701"
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={circumference}
            strokeDashoffset={offset}
            strokeLinecap="round"
            className="transition-all duration-300"
          />
        </svg>
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-xs font-semibold text-[#333333]">{value}%</span>
        </div>
      </div>
    );
  };

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="green-sidebar-9"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Left Sidebar */}
      <div className="absolute top-0 left-0 w-[180px] h-full bg-[#e4f2ef] px-6 py-8">
        
        {/* Profile Photo */}
        <div className="flex justify-center mb-6">
          <div className="w-24 h-24 rounded-full bg-white border-4 border-[#7d4701] overflow-hidden shadow-lg">
            {personalInfo.photo ? (
              <img 
                src={personalInfo.photo} 
                alt="Profile" 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-[#7d4701] to-[#5d3501] flex items-center justify-center">
                <span className="text-white text-xl font-bold">
                  {personalInfo.fullName?.charAt(0) || 'Y'}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Name and Title */}
        <div className="text-center mb-8">
          <h1 className="text-lg font-bold text-[#333333] mb-1">
            {personalInfo.fullName || 'Your Name'}
          </h1>
          <p className="text-sm text-[#7d4701] font-medium">
            {personalInfo.jobTitle || 'Professional Title'}
          </p>
        </div>

        {/* Contact Information */}
        <div className="mb-8">
          <h2 className="text-sm font-bold text-[#333333] mb-4 uppercase tracking-wide">
            Contact
          </h2>
          <div className="space-y-3">
            <div className="flex items-start text-xs text-[#333333]">
              <Mail className="w-3 h-3 mr-2 mt-0.5 text-[#7d4701] flex-shrink-0" />
              <span className="break-all">{personalInfo.email}</span>
            </div>
            <div className="flex items-center text-xs text-[#333333]">
              <Phone className="w-3 h-3 mr-2 text-[#7d4701] flex-shrink-0" />
              <span>{personalInfo.phone}</span>
            </div>
            <div className="flex items-start text-xs text-[#333333]">
              <MapPin className="w-3 h-3 mr-2 mt-0.5 text-[#7d4701] flex-shrink-0" />
              <span>{personalInfo.location}</span>
            </div>
            {personalInfo.website && (
              <div className="flex items-start text-xs text-[#333333]">
                <Globe className="w-3 h-3 mr-2 mt-0.5 text-[#7d4701] flex-shrink-0" />
                <span className="break-all">{personalInfo.website}</span>
              </div>
            )}
            {personalInfo.linkedinUrl && (
              <div className="flex items-center text-xs text-[#333333]">
                <Linkedin className="w-3 h-3 mr-2 text-[#7d4701] flex-shrink-0" />
                <span>LinkedIn Profile</span>
              </div>
            )}
          </div>
        </div>

        {/* Skills with Circular Progress */}
        <div className="mb-8">
          <h2 className="text-sm font-bold text-[#333333] mb-4 uppercase tracking-wide">
            Skills
          </h2>
          <div className="space-y-4">
            {skills.slice(0, 4).map((skill, index) => {
              const progress = getSkillProgress(index);
              return (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-xs font-medium text-[#333333] flex-1 mr-2">
                    {skill}
                  </span>
                  <CircularProgress value={progress} size={40} strokeWidth={3} />
                </div>
              );
            })}
          </div>
        </div>

        {/* Education */}
        <div>
          <h2 className="text-sm font-bold text-[#333333] mb-4 uppercase tracking-wide">
            Education
          </h2>
          <div className="space-y-4">
            {education.slice(0, 2).map((edu, index) => (
              <div key={index}>
                <h3 className="font-semibold text-xs text-[#333333] leading-tight">
                  {edu.degree}
                </h3>
                <p className="text-xs text-[#7d4701] font-medium">
                  {edu.institution}
                </p>
                <p className="text-xs text-[#333333]/70">
                  {new Date(edu.startDate).getFullYear()}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="absolute top-0 left-[180px] w-[414px] h-full px-8 py-8">
        
        {/* Professional Profile */}
        {summary && (
          <div className="mb-8">
            <h2 className="text-xl font-bold text-[#333333] mb-4 border-b-2 border-[#7d4701] pb-2">
              Professional Profile
            </h2>
            <p className="text-sm text-[#333333] leading-relaxed">
              {summary}
            </p>
          </div>
        )}

        {/* Professional Experience */}
        <div className="mb-8">
          <h2 className="text-xl font-bold text-[#333333] mb-4 border-b-2 border-[#7d4701] pb-2">
            Professional Experience
          </h2>
          <div className="space-y-6">
            {experience.slice(0, 4).map((exp, index) => (
              <div key={index} className="relative">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-bold text-[#333333] text-sm">
                    {exp.position}
                  </h3>
                  <span className="text-xs text-[#333333]/70 bg-[#e4f2ef] px-2 py-1 rounded">
                    {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                  </span>
                </div>
                <p className="text-sm font-semibold text-[#7d4701] mb-1">
                  {exp.company}
                </p>
                <p className="text-xs text-[#333333]/70 mb-3">
                  {exp.location}
                </p>
                <div className="text-xs text-[#333333] space-y-1">
                  {exp.description.slice(0, 3).map((desc, i) => (
                    <p key={i} className="flex items-start">
                      <span className="w-1 h-1 bg-[#7d4701] rounded-full mr-2 mt-2 flex-shrink-0"></span>
                      {desc}
                    </p>
                  ))}
                </div>
                {exp.skills && exp.skills.length > 0 && (
                  <div className="mt-2">
                    <div className="flex flex-wrap gap-1">
                      {exp.skills.slice(0, 4).map((skill, i) => (
                        <span key={i} className="text-xs bg-[#e4f2ef] text-[#7d4701] px-2 py-1 rounded">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Additional Skills */}
        {skills.length > 4 && (
          <div>
            <h2 className="text-xl font-bold text-[#333333] mb-4 border-b-2 border-[#7d4701] pb-2">
              Additional Skills
            </h2>
            <div className="flex flex-wrap gap-2">
              {skills.slice(4, 12).map((skill, index) => (
                <span key={index} className="text-xs bg-[#e4f2ef] text-[#7d4701] px-3 py-1 rounded-full">
                  {skill}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
