import { ImageWithFallback } from '../figma/ImageWithFallback';

export function PureTemplate() {
  // Static data for the resume
  const resumeData = {
    name: "ALEX CHEN",
    title: "Solution Architect",
    contact: {
      phone: "(*************",
      email: "<EMAIL>",
      location: "San Francisco, CA 94105",
    },
    summary: "Strategic and results-driven Solution Architect with over 10 years of experience designing and implementing scalable, secure, and robust enterprise solutions. Proven expertise in cloud architecture (AWS, Azure), microservices, API design, and DevOps practices. Adept at translating complex business requirements into technical roadmaps and leading cross-functional teams to successful project completion.",
    experience: [
      {
        company: "Innovatech Solutions",
        location: "San Francisco, CA",
        role: "Lead Solutions Architect",
        duration: "08/2019 - Present",
        responsibilities: [
          "Architected a new microservices platform using Docker and Kubernetes on AWS, improving system performance by 30% and reducing operational costs by 20%.",
          "Led the design and implementation of a company-wide API gateway strategy, standardizing communication protocols and enhancing security across 50+ services.",
          "Collaborated with product teams to define technical requirements and create architectural blueprints for new product features, ensuring alignment with business goals.",
          "Mentored junior engineers and guided best practices in system design, code quality, and cloud-native development.",
        ],
      },
      {
        company: "Tech-Forge Inc.",
        location: "Seattle, WA",
        role: "Cloud Architect",
        duration: "05/2015 - 07/2019",
        responsibilities: [
          "Designed and deployed scalable serverless applications using AWS Lambda and DynamoDB, reducing infrastructure maintenance overhead by 90%.",
          "Developed and managed CI/CD pipelines using Jenkins and GitLab, automating the build and deployment process for multiple applications.",
          "Optimized database performance and data storage strategies for a high-volume e-commerce platform, handling over 1 million transactions per day.",
          "Conducted technical assessments and feasibility studies for migrating on-premises applications to the Azure cloud.",
        ],
      },
    ],
    education: [
      {
        degree: "Master of Science (M.S.)",
        major: "in Computer Science",
        school: "Stanford University",
        location: "Stanford, CA",
        year: "05/2015",
      },
      {
        degree: "Bachelor of Science (B.S.)",
        major: "in Software Engineering",
        school: "University of Washington",
        location: "Seattle, WA",
        year: "05/2013",
      },
    ],
    skills: [
      "Cloud Architecture (AWS, Azure, GCP)",
      "Microservices & Serverless",
      "DevOps & CI/CD (Docker, Kubernetes)",
      "Programming (Python, Java, Node.js)",
      "Databases (SQL, NoSQL, MongoDB)",
      "API Design (REST, GraphQL)",
      "Agile Methodologies",
      "System Design",
    ],
    certifications: [
      "AWS Certified Solutions Architect – Professional (2020)",
      "Google Professional Cloud Architect (2018)",
    ],
  };

  return (
    <div className="bg-white mx-auto w-[900px] print:w-[794px] shadow-[0_4px_16px_rgba(0,0,0,0.08)] border border-gray-200">
      <div className="px-12 py-10">
        {/* Header Section */}
        <div className="flex flex-col items-center text-center mb-10">
          <ImageWithFallback
            alt="Profile picture of Alex Chen"
            className="w-24 h-24 rounded-full mb-6 border-4 border-gray-100"
            src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
          />
          <h1 className="text-[40px] font-bold text-gray-800 mb-2 tracking-tight">{resumeData.name}</h1>
          <h2 className="text-[20px] text-gray-600 font-medium">{resumeData.title}</h2>
        </div>

        {/* Contact Section */}
        <div className="flex justify-center text-[14px] text-gray-600 mb-10 border-b border-gray-200 pb-8">
          <span>{resumeData.contact.phone}</span>
          <span className="mx-4">|</span>
          <span>{resumeData.contact.email}</span>
          <span className="mx-4">|</span>
          <span>{resumeData.contact.location}</span>
        </div>

        {/* Professional Summary Section */}
        <div className="mb-10">
          <h3 className="text-[14px] font-bold text-[#1e40af] border-b-2 border-[#1e40af] inline-block pb-1 mb-5 uppercase tracking-[1px]">PROFESSIONAL SUMMARY</h3>
          <p className="text-[15px] text-gray-700 leading-relaxed">{resumeData.summary}</p>
        </div>

        {/* Experience Section */}
        <div className="mb-10">
          <h3 className="text-[14px] font-bold text-[#1e40af] border-b-2 border-[#1e40af] inline-block pb-1 mb-5 uppercase tracking-[1px]">EXPERIENCE</h3>
          {resumeData.experience.map((job, index) => (
            <div key={index} className={`grid grid-cols-3 gap-10 ${index > 0 ? 'mt-8' : ''}`}>
              <div className="col-span-1">
                <p className="text-[15px] font-bold text-gray-900 mb-1">{job.company} | {job.location}</p>
                <p className="text-[14px] font-semibold text-gray-600 mb-1">{job.role}</p>
                <p className="text-[13px] text-gray-500">{job.duration}</p>
              </div>
              <div className="col-span-2">
                <ul className="list-disc list-inside text-[14px] text-gray-700 space-y-2 leading-relaxed">
                  {job.responsibilities.map((responsibility, i) => (
                    <li key={i}>{responsibility}</li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>

        {/* Education Section */}
        <div className="mb-10">
          <h3 className="text-[14px] font-bold text-[#1e40af] border-b-2 border-[#1e40af] inline-block pb-1 mb-5 uppercase tracking-[1px]">EDUCATION</h3>
          {resumeData.education.map((edu, index) => (
            <div key={index} className={index > 0 ? "mt-5" : "mb-5"}>
              <p className="text-[15px] font-bold text-gray-900">
                {edu.degree} <span className="font-normal">{edu.major}</span>
              </p>
              <p className="text-[14px] text-gray-600 mt-1">{edu.school} | {edu.location}</p>
              <p className="text-[13px] text-gray-500 mt-1">{edu.year}</p>
            </div>
          ))}
        </div>

        {/* Skills Section */}
        <div className="mb-10 border-t border-gray-200 pt-10">
          <h3 className="text-[14px] font-bold text-[#1e40af] border-b-2 border-[#1e40af] inline-block pb-1 mb-5 uppercase tracking-[1px]">SKILLS</h3>
          <ul className="list-disc list-inside grid grid-cols-2 gap-4 text-[14px] text-gray-700">
            {resumeData.skills.map((skill, index) => (
              <li key={index}>{skill}</li>
            ))}
          </ul>
        </div>

        {/* Certifications Section */}
        <div className="border-t border-gray-200 pt-10">
          <h3 className="text-[14px] font-bold text-[#1e40af] border-b-2 border-[#1e40af] inline-block pb-1 mb-5 uppercase tracking-[1px]">CERTIFICATIONS</h3>
          <ul className="list-disc list-inside grid grid-cols-2 gap-4 text-[14px] text-gray-700">
            {resumeData.certifications.map((cert, index) => (
              <li key={index}>{cert}</li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}