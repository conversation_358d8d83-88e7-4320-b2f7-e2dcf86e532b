interface PlaceholderTemplateProps {
  templateName: string;
}

export function PlaceholderTemplate({ templateName }: PlaceholderTemplateProps) {
  return (
    <div className="w-full max-w-4xl mx-auto bg-white min-h-[800px]">
      {/* Header Section */}
      <div className="border-b-4 border-primary pb-6 mb-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h1 className="text-4xl font-bold text-gray-900 mb-2 tracking-wide text-center">JENNIFER DAVIS</h1>
            <p className="text-lg text-primary font-medium mb-4">
              Financial Operations Manager | Strategic Planning | Business Development
            </p>
            
            <div className="space-y-1 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <span>📧</span>
                <span><EMAIL></span>
                <span className="mx-2">•</span>
                <span>📞</span>
                <span>(555) 987-6543</span>
              </div>
              <div className="flex items-center gap-2">
                <span>🔗</span>
                <span>linkedin.com/in/jenniferdavis</span>
                <span className="mx-2">•</span>
                <span>📍</span>
                <span>Seattle, Washington</span>
              </div>
            </div>
          </div>
          
          {/* Profile Photo */}
          <div className="w-24 h-24 rounded-full overflow-hidden border-4 border-primary ml-6 flex-shrink-0">
            <img 
              src="https://images.unsplash.com/photo-1652471949169-9c587e8898cd?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxwcm9mZXNzaW9uYWwlMjBidXNpbmVzcyUyMGhlYWRzaG90JTIwd29tYW58ZW58MXx8fHwxNzU3MjIwMDY1fDA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral"
              alt="Profile"
              className="w-full h-full object-cover"
            />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-3 gap-8">
        {/* Left Column */}
        <div className="col-span-2 space-y-6">
          {/* Professional Summary */}
          <div>
            <h2 className="text-xl font-bold text-primary mb-3 border-b-2 border-primary pb-1">
              PROFESSIONAL SUMMARY
            </h2>
            <p className="text-sm text-gray-700 leading-relaxed">
              Results-driven Financial Operations Manager with 8+ years of experience in strategic 
              financial planning, business development, and process optimization. Proven track record 
              of implementing cost-saving initiatives that reduced operational expenses by 25% while 
              improving efficiency. Expert in financial analysis, budget management, and cross-functional 
              team leadership with a strong focus on data-driven decision making.
            </p>
          </div>

          {/* Work Experience */}
          <div>
            <h2 className="text-xl font-bold text-primary mb-3 border-b-2 border-primary pb-1">
              WORK EXPERIENCE
            </h2>
            
            <div className="space-y-5">
              {/* Senior Financial Operations Manager */}
              <div>
                <div className="flex justify-between items-start mb-1">
                  <h3 className="text-lg font-bold text-gray-900">Senior Financial Operations Manager</h3>
                  <span className="text-sm text-gray-500">Jan 2020 - Present</span>
                </div>
                <p className="text-sm font-medium text-primary mb-2">TechNova Solutions | Seattle, WA</p>
                <ul className="text-sm text-gray-700 leading-relaxed space-y-1 list-disc list-inside">
                  <li>Manage financial operations for a $50M technology company, overseeing budgeting, forecasting, and financial reporting</li>
                  <li>Implemented automated financial processes that reduced month-end closing time by 40% and improved accuracy</li>
                  <li>Led cross-functional teams in developing strategic business plans that resulted in 15% revenue growth year-over-year</li>
                  <li>Collaborated with executive leadership to identify cost-saving opportunities, achieving $2M in annual savings</li>
                  <li>Developed comprehensive financial dashboards and KPI tracking systems for real-time business insights</li>
                </ul>
              </div>

              {/* Financial Analyst */}
              <div>
                <div className="flex justify-between items-start mb-1">
                  <h3 className="text-lg font-bold text-gray-900">Financial Analyst</h3>
                  <span className="text-sm text-gray-500">Mar 2018 - Dec 2019</span>
                </div>
                <p className="text-sm font-medium text-primary mb-2">Growth Dynamics Corp | Portland, OR</p>
                <ul className="text-sm text-gray-700 leading-relaxed space-y-1 list-disc list-inside">
                  <li>Conducted detailed financial analysis and modeling to support strategic business decisions</li>
                  <li>Prepared monthly and quarterly financial reports for senior management and board presentations</li>
                  <li>Analyzed market trends and competitive landscape to identify growth opportunities</li>
                  <li>Supported M&A activities through due diligence and financial evaluation processes</li>
                </ul>
              </div>

              {/* Junior Financial Analyst */}
              <div>
                <div className="flex justify-between items-start mb-1">
                  <h3 className="text-lg font-bold text-gray-900">Junior Financial Analyst</h3>
                  <span className="text-sm text-gray-500">Jun 2016 - Feb 2018</span>
                </div>
                <p className="text-sm font-medium text-primary mb-2">Pacific Financial Group | San Francisco, CA</p>
                <ul className="text-sm text-gray-700 leading-relaxed space-y-1 list-disc list-inside">
                  <li>Assisted in financial planning and analysis activities, including budget preparation and variance analysis</li>
                  <li>Maintained financial databases and ensured data accuracy for reporting purposes</li>
                  <li>Supported senior analysts in preparing client presentations and investment recommendations</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Education */}
          <div>
            <h2 className="text-xl font-bold text-primary mb-3 border-b-2 border-primary pb-1">
              EDUCATION
            </h2>
            <div>
              <h3 className="text-lg font-bold text-gray-900">Master of Business Administration (MBA)</h3>
              <p className="text-sm font-medium text-primary">University of Washington - Foster School of Business</p>
              <p className="text-sm text-gray-500">Graduated: May 2016 | GPA: 3.8/4.0</p>
              <p className="text-sm text-gray-600 mt-1">Concentration: Finance & Strategic Management</p>
            </div>
            
            <div className="mt-3">
              <h3 className="text-lg font-bold text-gray-900">Bachelor of Science in Economics</h3>
              <p className="text-sm font-medium text-primary">University of California, Berkeley</p>
              <p className="text-sm text-gray-500">Graduated: May 2014 | Magna Cum Laude</p>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* Core Competencies */}
          <div>
            <h2 className="text-lg font-bold text-primary mb-3 border-b-2 border-primary pb-1">
              CORE COMPETENCIES
            </h2>
            <div className="space-y-2 text-sm">
              <div className="bg-primary/10 text-primary px-3 py-2 rounded">Financial Planning & Analysis</div>
              <div className="bg-primary/10 text-primary px-3 py-2 rounded">Budget Management</div>
              <div className="bg-primary/10 text-primary px-3 py-2 rounded">Strategic Planning</div>
              <div className="bg-primary/10 text-primary px-3 py-2 rounded">Process Optimization</div>
              <div className="bg-primary/10 text-primary px-3 py-2 rounded">Risk Management</div>
              <div className="bg-primary/10 text-primary px-3 py-2 rounded">Team Leadership</div>
              <div className="bg-primary/10 text-primary px-3 py-2 rounded">Business Development</div>
              <div className="bg-primary/10 text-primary px-3 py-2 rounded">Data Analysis</div>
            </div>
          </div>

          {/* Technical Skills */}
          <div>
            <h2 className="text-lg font-bold text-primary mb-3 border-b-2 border-primary pb-1">
              TECHNICAL SKILLS
            </h2>
            <div className="space-y-2 text-sm">
              <div className="bg-primary/10 text-primary px-3 py-2 rounded">Advanced Excel & VBA</div>
              <div className="bg-primary/10 text-primary px-3 py-2 rounded">SAP & Oracle Systems</div>
              <div className="bg-primary/10 text-primary px-3 py-2 rounded">Tableau & Power BI</div>
              <div className="bg-primary/10 text-primary px-3 py-2 rounded">SQL Database Management</div>
              <div className="bg-primary/10 text-primary px-3 py-2 rounded">QuickBooks & Sage</div>
              <div className="bg-primary/10 text-primary px-3 py-2 rounded">Financial Modeling</div>
            </div>
          </div>

          {/* Certifications */}
          <div>
            <h2 className="text-lg font-bold text-primary mb-3 border-b-2 border-primary pb-1">
              CERTIFICATIONS
            </h2>
            <div className="space-y-3 text-sm">
              <div>
                <h4 className="font-bold text-gray-900">Certified Public Accountant (CPA)</h4>
                <p className="text-gray-600">Washington State Board of Accountancy | 2017</p>
              </div>
              <div>
                <h4 className="font-bold text-gray-900">Financial Risk Manager (FRM)</h4>
                <p className="text-gray-600">Global Association of Risk Professionals | 2018</p>
              </div>
              <div>
                <h4 className="font-bold text-gray-900">Project Management Professional (PMP)</h4>
                <p className="text-gray-600">Project Management Institute | 2019</p>
              </div>
            </div>
          </div>

          {/* Awards & Recognition */}
          <div>
            <h2 className="text-lg font-bold text-primary mb-3 border-b-2 border-primary pb-1">
              AWARDS & RECOGNITION
            </h2>
            <div className="space-y-2 text-sm">
              <div>
                <h4 className="font-bold text-gray-900">Excellence in Financial Operations</h4>
                <p className="text-gray-600">TechNova Solutions | 2022</p>
              </div>
              <div>
                <h4 className="font-bold text-gray-900">Outstanding Leadership Award</h4>
                <p className="text-gray-600">Growth Dynamics Corp | 2019</p>
              </div>
              <div>
                <h4 className="font-bold text-gray-900">Dean's List</h4>
                <p className="text-gray-600">UC Berkeley | 2012-2014</p>
              </div>
            </div>
          </div>

          {/* Languages */}
          <div>
            <h2 className="text-lg font-bold text-primary mb-3 border-b-2 border-primary pb-1">
              LANGUAGES
            </h2>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between items-center py-1">
                <span className="font-medium text-gray-900">English</span>
                <span className="text-gray-600 font-medium">Native</span>
              </div>
              <div className="flex justify-between items-center py-1">
                <span className="font-medium text-gray-900">Spanish</span>
                <span className="text-gray-600 font-medium">Conversational</span>
              </div>
              <div className="flex justify-between items-center py-1">
                <span className="font-medium text-gray-900">French</span>
                <span className="text-gray-600 font-medium">Basic</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}