import { Mail, Phone, MapPin, Linkedin, Globe, Calendar, Building2, GraduationCap } from 'lucide-react';

export function ExecutiveBlueTemplate() {
  return (
    <div className="w-full max-w-4xl mx-auto bg-white shadow-lg flex min-h-[800px]">
      {/* Left Sidebar - Dark Blue */}
      <div className="w-1/3 bg-slate-800 text-white p-8 flex flex-col">
        {/* Profile Image Placeholder */}
        <div className="w-32 h-32 bg-slate-600 rounded-full mx-auto mb-6 flex items-center justify-center">
          <div className="w-16 h-16 bg-slate-500 rounded-full"></div>
        </div>

        {/* Contact Information */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-4 text-blue-200">Contact</h3>
          <div className="space-y-3">
            <div className="flex items-center gap-3 text-sm">
              <Mail className="w-4 h-4 text-blue-300" />
              <span><EMAIL></span>
            </div>
            <div className="flex items-center gap-3 text-sm">
              <Phone className="w-4 h-4 text-blue-300" />
              <span>+****************</span>
            </div>
            <div className="flex items-center gap-3 text-sm">
              <MapPin className="w-4 h-4 text-blue-300" />
              <span>New York, NY</span>
            </div>
            <div className="flex items-center gap-3 text-sm">
              <Linkedin className="w-4 h-4 text-blue-300" />
              <span>linkedin.com/in/elijahbrown</span>
            </div>
            <div className="flex items-center gap-3 text-sm">
              <Globe className="w-4 h-4 text-blue-300" />
              <span>elijahbrown.com</span>
            </div>
          </div>
        </div>

        {/* Skills */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-4 text-blue-200">Skills</h3>
          <div className="space-y-3">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Project Management</span>
                <span className="text-blue-300">95%</span>
              </div>
              <div className="w-full bg-slate-700 rounded-full h-2">
                <div className="bg-blue-400 h-2 rounded-full" style={{ width: '95%' }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Strategic Planning</span>
                <span className="text-blue-300">90%</span>
              </div>
              <div className="w-full bg-slate-700 rounded-full h-2">
                <div className="bg-blue-400 h-2 rounded-full" style={{ width: '90%' }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Team Leadership</span>
                <span className="text-blue-300">85%</span>
              </div>
              <div className="w-full bg-slate-700 rounded-full h-2">
                <div className="bg-blue-400 h-2 rounded-full" style={{ width: '85%' }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Data Analysis</span>
                <span className="text-blue-300">80%</span>
              </div>
              <div className="w-full bg-slate-700 rounded-full h-2">
                <div className="bg-blue-400 h-2 rounded-full" style={{ width: '80%' }}></div>
              </div>
            </div>
          </div>
        </div>

        {/* Languages */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-4 text-blue-200">Languages</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>English</span>
              <span className="text-blue-300">Native</span>
            </div>
            <div className="flex justify-between">
              <span>Spanish</span>
              <span className="text-blue-300">Fluent</span>
            </div>
            <div className="flex justify-between">
              <span>French</span>
              <span className="text-blue-300">Intermediate</span>
            </div>
          </div>
        </div>

        {/* Certifications */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-4 text-blue-200">Certifications</h3>
          <div className="space-y-2 text-sm">
            <div>PMP Certified</div>
            <div>Agile Certified</div>
            <div>Six Sigma Green Belt</div>
          </div>
        </div>
      </div>

      {/* Right Main Content - White */}
      <div className="flex-1 p-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Elijah Brown</h1>
          <h2 className="text-xl text-slate-600 mb-4">Senior Project Manager</h2>
          <p className="text-gray-700 leading-relaxed">
            Results-driven Senior Project Manager with over 8 years of experience leading cross-functional teams 
            to deliver complex projects on time and within budget. Proven track record of implementing strategic 
            initiatives that drive operational efficiency and business growth.
          </p>
        </div>

        {/* Professional Experience */}
        <div className="mb-8">
          <h3 className="text-2xl font-semibold text-gray-900 mb-6 border-b-2 border-slate-800 pb-2">Professional Experience</h3>
          
          <div className="space-y-6">
            <div>
              <div className="flex items-start justify-between mb-2">
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">Senior Project Manager</h4>
                  <div className="flex items-center gap-2 text-slate-600">
                    <Building2 className="w-4 h-4" />
                    <span>TechCorp Solutions</span>
                  </div>
                </div>
                <div className="flex items-center gap-1 text-sm text-slate-500">
                  <Calendar className="w-4 h-4" />
                  <span>2020 - Present</span>
                </div>
              </div>
              <ul className="list-disc list-inside text-sm text-gray-700 space-y-1 ml-4">
                <li>Led 15+ cross-functional teams to deliver $10M+ worth of projects</li>
                <li>Implemented Agile methodologies reducing project delivery time by 30%</li>
                <li>Managed stakeholder relationships and communication across all project phases</li>
                <li>Developed risk mitigation strategies resulting in 95% on-time delivery rate</li>
              </ul>
            </div>

            <div>
              <div className="flex items-start justify-between mb-2">
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">Project Manager</h4>
                  <div className="flex items-center gap-2 text-slate-600">
                    <Building2 className="w-4 h-4" />
                    <span>Innovation Labs Inc.</span>
                  </div>
                </div>
                <div className="flex items-center gap-1 text-sm text-slate-500">
                  <Calendar className="w-4 h-4" />
                  <span>2017 - 2020</span>
                </div>
              </div>
              <ul className="list-disc list-inside text-sm text-gray-700 space-y-1 ml-4">
                <li>Coordinated product development initiatives from conception to launch</li>
                <li>Collaborated with engineering, design, and marketing teams</li>
                <li>Established project governance frameworks and reporting systems</li>
                <li>Achieved 100% project success rate with zero budget overruns</li>
              </ul>
            </div>

            <div>
              <div className="flex items-start justify-between mb-2">
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">Associate Project Manager</h4>
                  <div className="flex items-center gap-2 text-slate-600">
                    <Building2 className="w-4 h-4" />
                    <span>StartupCorp</span>
                  </div>
                </div>
                <div className="flex items-center gap-1 text-sm text-slate-500">
                  <Calendar className="w-4 h-4" />
                  <span>2015 - 2017</span>
                </div>
              </div>
              <ul className="list-disc list-inside text-sm text-gray-700 space-y-1 ml-4">
                <li>Assisted in managing multiple concurrent projects worth $2M+</li>
                <li>Developed project documentation and maintained project schedules</li>
                <li>Facilitated daily standups and sprint planning sessions</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Education */}
        <div className="mb-8">
          <h3 className="text-2xl font-semibold text-gray-900 mb-6 border-b-2 border-slate-800 pb-2">Education</h3>
          
          <div className="space-y-4">
            <div>
              <div className="flex items-start justify-between mb-2">
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">Master of Business Administration</h4>
                  <div className="flex items-center gap-2 text-slate-600">
                    <GraduationCap className="w-4 h-4" />
                    <span>Harvard Business School</span>
                  </div>
                </div>
                <div className="flex items-center gap-1 text-sm text-slate-500">
                  <Calendar className="w-4 h-4" />
                  <span>2013 - 2015</span>
                </div>
              </div>
              <p className="text-sm text-gray-700 ml-4">Focus: Strategic Management & Operations</p>
            </div>

            <div>
              <div className="flex items-start justify-between mb-2">
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">Bachelor of Science in Engineering</h4>
                  <div className="flex items-center gap-2 text-slate-600">
                    <GraduationCap className="w-4 h-4" />
                    <span>MIT</span>
                  </div>
                </div>
                <div className="flex items-center gap-1 text-sm text-slate-500">
                  <Calendar className="w-4 h-4" />
                  <span>2009 - 2013</span>
                </div>
              </div>
              <p className="text-sm text-gray-700 ml-4">Magna Cum Laude, GPA: 3.8/4.0</p>
            </div>
          </div>
        </div>

        {/* Key Achievements */}
        <div>
          <h3 className="text-2xl font-semibold text-gray-900 mb-6 border-b-2 border-slate-800 pb-2">Key Achievements</h3>
          <ul className="list-disc list-inside text-sm text-gray-700 space-y-2">
            <li>Delivered 50+ projects with a combined value of $25M+ across various industries</li>
            <li>Reduced average project delivery time by 35% through process optimization</li>
            <li>Led digital transformation initiative that increased operational efficiency by 40%</li>
            <li>Mentored 20+ junior project managers and team members</li>
            <li>Recipient of "Project Manager of the Year" award (2021, 2022)</li>
          </ul>
        </div>
      </div>
    </div>
  );
}