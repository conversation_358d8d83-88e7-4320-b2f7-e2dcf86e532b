import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, Target, TrendingUp, Award, DollarSign } from 'lucide-react';

interface SalesExecutiveProfessionalTemplateProps {
  resumeData: ResumeContent;
}

export default function SalesExecutiveProfessionalTemplate({ resumeData }: SalesExecutiveProfessionalTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  // Mock sales achievements
  const salesAchievements = [
    { metric: 'Revenue Generated', value: '$2.5M+', icon: DollarSign },
    { metric: 'Quota Achievement', value: '125%', icon: Target },
    { metric: 'Client Retention', value: '95%', icon: Award },
    { metric: 'Team Performance', value: 'Top 5%', icon: TrendingUp }
  ];

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="natasha-wilson-sales"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Dark Blue Sidebar */}
      <div className="absolute top-0 left-0 w-[200px] h-full bg-[#1e40af] px-6 py-8">
        
        {/* Profile Section */}
        <div className="text-center mb-8">
          <div className="w-24 h-24 rounded-full bg-white border-4 border-[#eff6ff] overflow-hidden mx-auto mb-4 shadow-lg">
            {personalInfo.photo ? (
              <img 
                src={personalInfo.photo} 
                alt="Profile" 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-[#3b82f6] to-[#1e40af] flex items-center justify-center">
                <span className="text-white text-2xl font-bold">
                  {personalInfo.fullName?.charAt(0) || 'N'}
                </span>
              </div>
            )}
          </div>
          <h1 className="text-xl font-bold text-white mb-2">
            {personalInfo.fullName || 'Natasha Wilson'}
          </h1>
          <p className="text-sm text-[#eff6ff] font-medium">
            {personalInfo.jobTitle || 'Sales Executive'}
          </p>
        </div>

        {/* Contact Information */}
        <div className="mb-8">
          <h2 className="text-sm font-bold text-[#eff6ff] mb-4 uppercase tracking-wide">
            Contact
          </h2>
          <div className="space-y-3">
            <div className="flex items-start text-xs text-white">
              <Mail className="w-3 h-3 mr-2 mt-0.5 text-[#eff6ff] flex-shrink-0" />
              <span className="break-all">{personalInfo.email}</span>
            </div>
            <div className="flex items-center text-xs text-white">
              <Phone className="w-3 h-3 mr-2 text-[#eff6ff] flex-shrink-0" />
              <span>{personalInfo.phone}</span>
            </div>
            <div className="flex items-start text-xs text-white">
              <MapPin className="w-3 h-3 mr-2 mt-0.5 text-[#eff6ff] flex-shrink-0" />
              <span>{personalInfo.location}</span>
            </div>
            {personalInfo.website && (
              <div className="flex items-start text-xs text-white">
                <Globe className="w-3 h-3 mr-2 mt-0.5 text-[#eff6ff] flex-shrink-0" />
                <span className="break-all">{personalInfo.website}</span>
              </div>
            )}
            {personalInfo.linkedinUrl && (
              <div className="flex items-center text-xs text-white">
                <Linkedin className="w-3 h-3 mr-2 text-[#eff6ff] flex-shrink-0" />
                <span>LinkedIn Profile</span>
              </div>
            )}
          </div>
        </div>

        {/* Core Skills */}
        <div className="mb-8">
          <h2 className="text-sm font-bold text-[#eff6ff] mb-4 uppercase tracking-wide">
            Core Skills
          </h2>
          <div className="space-y-2">
            {skills.slice(0, 8).map((skill, index) => (
              <div key={index} className="flex items-center">
                <div className="w-2 h-2 bg-[#eff6ff] rounded-full mr-2 flex-shrink-0"></div>
                <span className="text-xs text-white">{skill}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Key Achievements */}
        <div className="mb-8">
          <h2 className="text-sm font-bold text-[#eff6ff] mb-4 uppercase tracking-wide">
            Key Metrics
          </h2>
          <div className="space-y-3">
            {salesAchievements.map((achievement, index) => (
              <div key={index} className="bg-[#3b82f6] p-3 rounded-lg">
                <div className="flex items-center mb-1">
                  <achievement.icon className="w-3 h-3 mr-2 text-[#eff6ff]" />
                  <span className="text-xs text-[#eff6ff] font-medium">{achievement.metric}</span>
                </div>
                <span className="text-lg font-bold text-white">{achievement.value}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Education */}
        <div>
          <h2 className="text-sm font-bold text-[#eff6ff] mb-4 uppercase tracking-wide">
            Education
          </h2>
          <div className="space-y-3">
            {education.slice(0, 2).map((edu, index) => (
              <div key={index}>
                <h3 className="font-semibold text-xs text-white leading-tight">
                  {edu.degree}
                </h3>
                <p className="text-xs text-[#eff6ff] font-medium">
                  {edu.institution}
                </p>
                <p className="text-xs text-[#eff6ff]/70">
                  {new Date(edu.startDate).getFullYear()}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="absolute top-0 left-[200px] w-[394px] h-full px-8 py-8 bg-[#eff6ff]">
        
        {/* Professional Summary */}
        {summary && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-[#1e40af] mb-4 flex items-center">
              <div className="w-1 h-8 bg-[#3b82f6] mr-3"></div>
              Executive Summary
            </h2>
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <p className="text-sm text-[#1e40af] leading-relaxed">
                {summary}
              </p>
            </div>
          </div>
        )}

        {/* Professional Experience */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-[#1e40af] mb-6 flex items-center">
            <div className="w-1 h-8 bg-[#3b82f6] mr-3"></div>
            Professional Experience
          </h2>
          <div className="space-y-6">
            {experience.slice(0, 4).map((exp, index) => (
              <div key={index} className="bg-white p-4 rounded-lg shadow-sm">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-bold text-[#1e40af] text-base">
                    {exp.position}
                  </h3>
                  <span className="text-xs text-white bg-[#1e40af] px-2 py-1 rounded">
                    {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                  </span>
                </div>
                <p className="text-sm font-semibold text-[#3b82f6] mb-1">
                  {exp.company}
                </p>
                <p className="text-xs text-[#1e40af]/70 mb-3">
                  {exp.location}
                </p>
                <div className="text-xs text-[#1e40af] space-y-1">
                  {exp.description.slice(0, 3).map((desc, i) => (
                    <p key={i} className="flex items-start">
                      <Target className="w-3 h-3 mr-2 mt-0.5 text-[#3b82f6] flex-shrink-0" />
                      {desc}
                    </p>
                  ))}
                </div>
                {exp.skills && exp.skills.length > 0 && (
                  <div className="mt-3">
                    <div className="flex flex-wrap gap-1">
                      {exp.skills.slice(0, 4).map((skill, i) => (
                        <span key={i} className="text-xs bg-[#eff6ff] text-[#1e40af] px-2 py-1 rounded border border-[#3b82f6]/20">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Sales Methodology & Certifications */}
        <div>
          <h2 className="text-2xl font-bold text-[#1e40af] mb-4 flex items-center">
            <div className="w-1 h-8 bg-[#3b82f6] mr-3"></div>
            Sales Expertise
          </h2>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-white p-3 rounded-lg shadow-sm">
              <h3 className="font-semibold text-sm text-[#1e40af] mb-2">Methodologies</h3>
              <div className="space-y-1">
                {[
                  'SPIN Selling',
                  'Challenger Sale',
                  'Solution Selling',
                  'Consultative Selling'
                ].map((method, index) => (
                  <div key={index} className="flex items-center">
                    <Award className="w-3 h-3 mr-2 text-[#3b82f6]" />
                    <span className="text-xs text-[#1e40af]">{method}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="bg-white p-3 rounded-lg shadow-sm">
              <h3 className="font-semibold text-sm text-[#1e40af] mb-2">Certifications</h3>
              <div className="space-y-1">
                {[
                  'Salesforce Certified',
                  'HubSpot Sales Pro',
                  'LinkedIn Sales Navigator',
                  'Sales Management'
                ].map((cert, index) => (
                  <div key={index} className="flex items-center">
                    <TrendingUp className="w-3 h-3 mr-2 text-[#3b82f6]" />
                    <span className="text-xs text-[#1e40af]">{cert}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
