import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, User, Code } from 'lucide-react';

interface JohnDoeUxuiTemplateProps {
  resumeData: ResumeContent;
}

export default function JohnDoeUxuiTemplate({ resumeData }: JohnDoeUxuiTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  return (
    <div 
      className="bg-[#f8f9fa] relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="john-doe-uxui"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Header */}
      <div className="absolute top-0 left-0 w-full h-[100px] bg-white shadow-sm px-8 py-6">
        <div className="flex items-center justify-between h-full">
          <div>
            <h1 className="text-3xl font-bold text-[#2c3e50] mb-1">
              {personalInfo.fullName || '<PERSON>'}
            </h1>
            <p className="text-lg text-[#3498db] flex items-center">
              <User className="w-5 h-5 mr-2" />
              {personalInfo.jobTitle || 'UX/UI Designer'}
            </p>
          </div>
          
          {/* Contact Info */}
          <div className="text-right text-sm text-[#2c3e50] space-y-1">
            <div>{personalInfo.email}</div>
            <div>{personalInfo.phone}</div>
            <div>{personalInfo.location}</div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[120px] left-0 w-full h-[722px] flex">
        
        {/* Left Column */}
        <div className="w-[370px] bg-white px-8 py-6 shadow-sm">
          
          {/* About */}
          {summary && (
            <div className="mb-8">
              <h2 className="text-xl font-bold text-[#2c3e50] mb-4 flex items-center">
                <div className="w-4 h-4 bg-[#3498db] rounded-full mr-3"></div>
                About Me
              </h2>
              <p className="text-sm text-[#2c3e50] leading-relaxed">
                {summary}
              </p>
            </div>
          )}

          {/* Experience */}
          <div className="mb-8">
            <h2 className="text-xl font-bold text-[#2c3e50] mb-4 flex items-center">
              <div className="w-4 h-4 bg-[#3498db] rounded-full mr-3"></div>
              Experience
            </h2>
            <div className="space-y-6">
              {experience.slice(0, 4).map((exp, index) => (
                <div key={index} className="border-l-3 border-[#3498db] pl-4">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-bold text-[#2c3e50] text-base">
                      {exp.position}
                    </h3>
                    <span className="text-xs text-[#2c3e50]/70 bg-[#ecf0f1] px-2 py-1 rounded">
                      {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                    </span>
                  </div>
                  <p className="text-sm font-semibold text-[#3498db] mb-2">
                    {exp.company} • {exp.location}
                  </p>
                  <div className="text-sm text-[#2c3e50] space-y-1">
                    {exp.description.slice(0, 3).map((desc, i) => (
                      <p key={i}>• {desc}</p>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Education */}
          <div>
            <h2 className="text-xl font-bold text-[#2c3e50] mb-4 flex items-center">
              <div className="w-4 h-4 bg-[#3498db] rounded-full mr-3"></div>
              Education
            </h2>
            <div className="space-y-4">
              {education.slice(0, 2).map((edu, index) => (
                <div key={index} className="border-l-3 border-[#3498db] pl-4">
                  <h3 className="font-bold text-[#2c3e50] text-base">
                    {edu.degree}
                  </h3>
                  <p className="text-sm text-[#3498db] font-medium">
                    {edu.institution}
                  </p>
                  <p className="text-sm text-[#2c3e50]/70">
                    {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="w-[224px] px-6 py-6">
          
          {/* Skills */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#2c3e50] mb-4">
              Skills
            </h2>
            <div className="space-y-3">
              {skills.slice(0, 8).map((skill, index) => {
                const progress = [90, 85, 88, 82, 87, 80, 85, 83][index] || 80;
                return (
                  <div key={index}>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-[#2c3e50]">{skill}</span>
                      <span className="text-xs text-[#2c3e50]/70">{progress}%</span>
                    </div>
                    <div className="w-full bg-[#ecf0f1] rounded-full h-2">
                      <div 
                        className="bg-[#3498db] h-2 rounded-full"
                        style={{ width: `${progress}%` }}
                      ></div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Tools */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#2c3e50] mb-4">
              Tools
            </h2>
            <div className="space-y-2">
              {[
                'Figma',
                'Sketch',
                'Adobe XD',
                'Photoshop',
                'HTML/CSS',
                'JavaScript'
              ].map((tool, index) => (
                <div key={index} className="flex items-center">
                  <Code className="w-3 h-3 mr-2 text-[#3498db]" />
                  <span className="text-sm text-[#2c3e50]">{tool}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Contact */}
          <div>
            <h2 className="text-lg font-bold text-[#2c3e50] mb-4">
              Contact
            </h2>
            <div className="space-y-2">
              {personalInfo.website && (
                <div className="flex items-center text-sm text-[#3498db]">
                  <Globe className="w-4 h-4 mr-2" />
                  <span>Portfolio</span>
                </div>
              )}
              {personalInfo.linkedinUrl && (
                <div className="flex items-center text-sm text-[#3498db]">
                  <Linkedin className="w-4 h-4 mr-2" />
                  <span>LinkedIn</span>
                </div>
              )}
              {personalInfo.githubUrl && (
                <div className="flex items-center text-sm text-[#3498db]">
                  <Github className="w-4 h-4 mr-2" />
                  <span>GitHub</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
