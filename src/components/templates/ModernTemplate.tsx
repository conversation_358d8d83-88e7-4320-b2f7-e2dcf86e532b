import { Phone, Mail, MapPin, Globe } from 'lucide-react';
import { ImageWithFallback } from '../figma/ImageWithFallback';

export function ModernTemplate() {
  const resumeData = {
    personalInfo: {
      name: "JANE DOE",
      title: "Software Developer",
      photoUrl: "https://placehold.co/128x128/d1d5db/4b5563?text=JD",
      contact: {
        phone: "(*************",
        email: "<EMAIL>",
        location: "San Francisco, CA",
        website: "www.janedoe.dev"
      },
    },
    summary: "Highly motivated and results-oriented Software Developer with a passion for building robust and scalable web applications. Experienced in full-stack development using modern technologies and frameworks. Eager to leverage strong problem-solving skills and a collaborative spirit to contribute to innovative projects.",
    skills: [
      "JavaScript",
      "React",
      "Node.js",
      "Tailwind CSS",
      "Python",
      "SQL",
      "Git",
      "REST APIs",
      "Agile Methodologies",
      "Problem-Solving",
      "Teamwork",
    ],
    certifications: [
      {
        name: "Certified ScrumMaster",
        issuer: "Scrum Alliance",
      },
      {
        name: "AWS Certified Developer – Associate",
        issuer: "Amazon Web Services",
      },
      {
        name: "Google Project Management Certificate",
        issuer: "Coursera",
      },
    ],
    experience: [
      {
        title: "Software Engineer Intern",
        company: "Innovate Solutions Inc.",
        location: "Seattle, WA",
        dates: "Jun 2019 – Aug 2019",
        responsibilities: [
          "Assisted senior engineers in the development of a new microservice.",
          "Wrote unit and integration tests to ensure code quality and stability.",
          "Participated in daily stand-ups and sprint planning sessions.",
        ],
      },
      {
        title: "Software Developer",
        company: "Innovate Solutions Inc.",
        location: "San Francisco, CA",
        dates: "Jan 2022 – Present",
        responsibilities: [
          "Developed and maintained responsive web applications using React and Node.js.",
          "Collaborated with a team of developers and designers to build new features and improve user experience.",
          "Implemented RESTful APIs for data communication between front-end and back-end.",
          "Participated in code reviews and mentored junior developers.",
        ],
      },
      {
        title: "Junior Developer",
        company: "Tech Startups LLC",
        location: "San Francisco, CA",
        dates: "Jun 2020 – Dec 2021",
        responsibilities: [
          "Assisted in the development of a new e-commerce platform.",
          "Wrote clean, efficient, and well-documented code.",
          "Debugged and resolved software defects and issues.",
        ],
      },
    ],
    education: [
      {
        degree: "Master of Science in Computer Science",
        university: "University of Technology",
        location: "Berkeley, CA",
        dates: "May 2020",
      },
      {
        degree: "Bachelor of Science in Information Technology",
        university: "State University",
        location: "San Jose, CA",
        dates: "May 2018",
      },
    ],
  };

  const { personalInfo, summary, skills, certifications, experience, education } = resumeData;

  return (
    <div className="bg-white mx-auto w-[900px] print:w-[794px] shadow-[0_4px_16px_rgba(0,0,0,0.08)] border border-gray-200 flex min-h-[1123px]">
      {/* Left Column */}
      <div className="w-1/3 bg-teal-50 p-8 text-gray-700">
        <div className="mb-8">
          <p className="flex items-center mb-2 text-sm">
            <Phone className="text-teal-600 mr-2" size={20} />
            {personalInfo.contact.phone}
          </p>
          <p className="flex items-center mb-2 text-sm">
            <Mail className="text-teal-600 mr-2" size={20} />
            {personalInfo.contact.email}
          </p>
          <p className="flex items-center mb-2 text-sm">
            <MapPin className="text-teal-600 mr-2" size={20} />
            {personalInfo.contact.location}
          </p>
          {personalInfo.contact.website && (
            <p className="flex items-center text-sm">
              <Globe className="text-teal-600 mr-2" size={20} />
              {personalInfo.contact.website}
            </p>
          )}
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-bold text-teal-700 border-b-2 border-teal-700 pb-2 mb-4">PROFESSIONAL SUMMARY</h2>
          <p className="text-sm leading-relaxed">{summary}</p>
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-bold text-teal-700 border-b-2 border-teal-700 pb-2 mb-4">SKILLS</h2>
          <div className="flex flex-wrap gap-2 text-sm">
            {skills.map((skill, index) => (
              <span key={index} className="bg-teal-200 text-teal-800 px-3 py-1 rounded-full">
                {skill}
              </span>
            ))}
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-bold text-teal-700 border-b-2 border-teal-700 pb-2 mb-4">CERTIFICATIONS</h2>
          <div className="text-sm leading-relaxed">
            {certifications.map((cert, index) => (
              <div key={index} className="mb-2">
                <p className="font-semibold">{cert.name}</p>
                <p className="text-gray-600">{cert.issuer}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Right Column */}
      <div className="w-2/3 p-8 bg-white">
        <div className="flex items-center mb-8">
          <div className="flex-grow">
            <h1 className="text-5xl font-bold text-teal-700 leading-tight">{personalInfo.name.split(' ')[0]}</h1>
            <h1 className="text-5xl font-bold text-teal-700 leading-tight">{personalInfo.name.split(' ')[1]}</h1>
            <p className="text-2xl text-gray-600 mt-2">{personalInfo.title}</p>
          </div>
          <div className="flex-shrink-0">
            <ImageWithFallback
              alt="Profile picture of Jane Doe"
              className="w-32 h-32 rounded-full object-cover"
              src={personalInfo.photoUrl}
            />
          </div>
        </div>
        <div className="border-t-4 border-gray-200 mb-8"></div>

        <div className="mb-8">
          <h2 className="text-2xl font-bold text-teal-700 mb-4">EXPERIENCE</h2>
          {experience.map((job, index) => (
            <div key={index} className="mb-6">
              <div className="flex justify-between items-baseline">
                <h3 className="text-lg font-semibold">{job.title}</h3>
                <p className="text-sm text-gray-500">{job.dates}</p>
              </div>
              <div className="flex justify-between items-baseline">
                <p className="text-md font-medium text-gray-600">{job.company}</p>
                <p className="text-sm text-gray-500">{job.location}</p>
              </div>
              <ul className="list-disc list-inside mt-2 text-sm text-gray-700 space-y-1">
                {job.responsibilities.map((resp, i) => (
                  <li key={i}>{resp}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div>
          <h2 className="text-2xl font-bold text-teal-700 mb-4">EDUCATION</h2>
          {education.map((edu, index) => (
            <div key={index} className="mb-4">
              <div className="flex justify-between items-baseline">
                <h3 className="text-lg font-semibold">{edu.degree}</h3>
                <p className="text-sm text-gray-500">{edu.dates}</p>
              </div>
              <div className="flex justify-between items-baseline">
                <p className="text-md font-medium text-gray-600">{edu.university}</p>
                <p className="text-sm text-gray-500">{edu.location}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}