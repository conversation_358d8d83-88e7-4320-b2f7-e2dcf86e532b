import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, BarChart3, Database, TrendingUp, PieChart } from 'lucide-react';

interface DataAnalystProfessionalTemplateProps {
  resumeData: ResumeContent;
}

export default function DataAnalystProfessionalTemplate({ resumeData }: DataAnalystProfessionalTemplateProps) {
  const { personalInfo, summary, experience, education, skills, projects } = resumeData;

  // Mock data projects if none provided
  const dataProjects = projects && projects.length > 0 ? projects : [
    { id: '1', name: 'Sales Performance Dashboard', description: 'Interactive dashboard analyzing quarterly sales trends' },
    { id: '2', name: 'Customer Segmentation Analysis', description: 'Machine learning model for customer behavior prediction' },
    { id: '3', name: 'Financial Risk Assessment', description: 'Statistical model for credit risk evaluation' }
  ];

  return (
    <div 
      className="bg-[#f9fafb] relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="jason-reyes-analyst"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Clean Header */}
      <div className="absolute top-0 left-0 w-full h-[100px] bg-white shadow-sm border-b-2 border-[#1f2937] px-8 py-6">
        <div className="flex items-center justify-between h-full">
          <div>
            <h1 className="text-3xl font-bold text-[#1f2937] mb-1">
              {personalInfo.fullName || 'Jason Reyes'}
            </h1>
            <p className="text-lg text-[#6b7280] flex items-center">
              <BarChart3 className="w-5 h-5 mr-2" />
              {personalInfo.jobTitle || 'Data Analyst'}
            </p>
          </div>
          
          {/* Contact Info */}
          <div className="text-right text-[#1f2937] text-sm space-y-1">
            <div className="flex items-center justify-end">
              <Mail className="w-4 h-4 mr-2 text-[#6b7280]" />
              <span>{personalInfo.email}</span>
            </div>
            <div className="flex items-center justify-end">
              <Phone className="w-4 h-4 mr-2 text-[#6b7280]" />
              <span>{personalInfo.phone}</span>
            </div>
            <div className="flex items-center justify-end">
              <MapPin className="w-4 h-4 mr-2 text-[#6b7280]" />
              <span>{personalInfo.location}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[120px] left-0 w-full h-[722px] px-8 py-6">
        
        {/* Professional Summary */}
        {summary && (
          <div className="mb-8">
            <h2 className="text-xl font-bold text-[#1f2937] mb-4 border-b-2 border-[#6b7280] pb-2">
              Professional Summary
            </h2>
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <p className="text-sm text-[#1f2937] leading-relaxed">
                {summary}
              </p>
            </div>
          </div>
        )}

        {/* Two Column Layout */}
        <div className="grid grid-cols-3 gap-8">
          
          {/* Left Column - Experience & Projects */}
          <div className="col-span-2">
            
            {/* Professional Experience */}
            <div className="mb-8">
              <h2 className="text-xl font-bold text-[#1f2937] mb-4 border-b-2 border-[#6b7280] pb-2">
                Professional Experience
              </h2>
              <div className="space-y-6">
                {experience.slice(0, 3).map((exp, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow-sm">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-bold text-[#1f2937] text-base">
                        {exp.position}
                      </h3>
                      <span className="text-sm text-white bg-[#1f2937] px-3 py-1 rounded">
                        {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                      </span>
                    </div>
                    <p className="text-sm font-semibold text-[#6b7280] mb-2">
                      {exp.company} • {exp.location}
                    </p>
                    <div className="text-sm text-[#1f2937] space-y-1">
                      {exp.description.slice(0, 3).map((desc, i) => (
                        <p key={i} className="flex items-start">
                          <TrendingUp className="w-3 h-3 mr-2 mt-1 text-[#6b7280] flex-shrink-0" />
                          {desc}
                        </p>
                      ))}
                    </div>
                    {exp.skills && exp.skills.length > 0 && (
                      <div className="mt-3">
                        <div className="flex flex-wrap gap-2">
                          {exp.skills.slice(0, 4).map((skill, i) => (
                            <span key={i} className="text-xs bg-[#f9fafb] text-[#1f2937] px-2 py-1 rounded border border-[#6b7280]/20">
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Data Projects */}
            <div>
              <h2 className="text-xl font-bold text-[#1f2937] mb-4 border-b-2 border-[#6b7280] pb-2">
                Key Projects
              </h2>
              <div className="space-y-4">
                {dataProjects.slice(0, 3).map((project, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#1f2937]">
                    <h3 className="font-bold text-[#1f2937] text-sm flex items-center">
                      <PieChart className="w-4 h-4 mr-2 text-[#6b7280]" />
                      {project.name}
                    </h3>
                    <p className="text-sm text-[#6b7280] mt-1">
                      {project.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Skills & Education */}
          <div className="col-span-1">
            
            {/* Technical Skills */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#1f2937] mb-4">
                Technical Skills
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="space-y-3">
                  {skills.slice(0, 8).map((skill, index) => (
                    <div key={index} className="flex items-center">
                      <Database className="w-3 h-3 mr-2 text-[#6b7280]" />
                      <span className="text-sm text-[#1f2937]">{skill}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Tools & Technologies */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#1f2937] mb-4">
                Tools & Technologies
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="grid grid-cols-1 gap-2">
                  {[
                    'Python',
                    'R',
                    'SQL',
                    'Tableau',
                    'Power BI',
                    'Excel',
                    'SPSS',
                    'SAS'
                  ].map((tool, index) => (
                    <div key={index} className="text-center py-2 bg-[#f9fafb] rounded border border-[#6b7280]/20">
                      <span className="text-sm font-medium text-[#1f2937]">{tool}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Education */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#1f2937] mb-4">
                Education
              </h2>
              <div className="space-y-3">
                {education.slice(0, 2).map((edu, index) => (
                  <div key={index} className="bg-white p-3 rounded-lg shadow-sm">
                    <h3 className="font-bold text-sm text-[#1f2937]">
                      {edu.degree}
                    </h3>
                    <p className="text-xs text-[#6b7280] font-medium">
                      {edu.institution}
                    </p>
                    <p className="text-xs text-[#1f2937]/70">
                      {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                    </p>
                    {edu.gpa && (
                      <p className="text-xs text-[#1f2937]/70">
                        GPA: {edu.gpa}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Certifications */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#1f2937] mb-4">
                Certifications
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="space-y-2">
                  {[
                    'Google Analytics Certified',
                    'Microsoft Excel Expert',
                    'Tableau Desktop Specialist',
                    'AWS Cloud Practitioner'
                  ].map((cert, index) => (
                    <div key={index} className="flex items-center">
                      <BarChart3 className="w-3 h-3 mr-2 text-[#6b7280]" />
                      <span className="text-sm text-[#1f2937]">{cert}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Links */}
            <div>
              <h2 className="text-lg font-bold text-[#1f2937] mb-4">
                Professional Links
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="space-y-2">
                  {personalInfo.website && (
                    <div className="flex items-center text-sm text-[#6b7280]">
                      <Globe className="w-4 h-4 mr-2" />
                      <span>Portfolio</span>
                    </div>
                  )}
                  {personalInfo.linkedinUrl && (
                    <div className="flex items-center text-sm text-[#6b7280]">
                      <Linkedin className="w-4 h-4 mr-2" />
                      <span>LinkedIn</span>
                    </div>
                  )}
                  {personalInfo.githubUrl && (
                    <div className="flex items-center text-sm text-[#6b7280]">
                      <Github className="w-4 h-4 mr-2" />
                      <span>GitHub</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
