import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, Briefcase, Award, Star, BookOpen } from 'lucide-react';

interface ExperiencePortfolioTemplateProps {
  resumeData: ResumeContent;
}

export default function ExperiencePortfolioTemplate({ resumeData }: ExperiencePortfolioTemplateProps) {
  const { personalInfo, summary, experience, education, skills, projects } = resumeData;

  // Mock portfolio projects if none provided
  const portfolioProjects = projects && projects.length > 0 ? projects : [
    { id: '1', name: 'E-commerce Platform Redesign', description: 'Complete UX overhaul resulting in 40% conversion increase' },
    { id: '2', name: 'Mobile Banking App', description: 'User-centered design for fintech startup with 500K+ users' },
    { id: '3', name: 'SaaS Dashboard', description: 'B2B analytics platform with advanced data visualization' }
  ];

  // Mock certifications
  const certifications = [
    'Google UX Design Certificate',
    'Adobe Certified Expert',
    'Figma Advanced Certification',
    'Scrum Master Certified'
  ];

  return (
    <div 
      className="bg-[#f9fafb] relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="james-smith-experience-portfolio"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Modern Header */}
      <div className="absolute top-0 left-0 w-full h-[120px] bg-[#393e4d] px-8 py-6">
        <div className="flex items-center justify-between h-full">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">
              {personalInfo.fullName || 'James Smith'}
            </h1>
            <p className="text-lg text-[#f9c068] flex items-center">
              <Briefcase className="w-5 h-5 mr-2" />
              {personalInfo.jobTitle || 'Senior Designer'}
            </p>
          </div>
          
          {/* Contact Info */}
          <div className="text-right text-white text-sm space-y-1">
            <div className="flex items-center justify-end">
              <Mail className="w-4 h-4 mr-2 text-[#f9c068]" />
              <span>{personalInfo.email}</span>
            </div>
            <div className="flex items-center justify-end">
              <Phone className="w-4 h-4 mr-2 text-[#f9c068]" />
              <span>{personalInfo.phone}</span>
            </div>
            <div className="flex items-center justify-end">
              <MapPin className="w-4 h-4 mr-2 text-[#f9c068]" />
              <span>{personalInfo.location}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[140px] left-0 w-full h-[702px] px-8 py-6">
        
        {/* Professional Summary */}
        {summary && (
          <div className="mb-8">
            <h2 className="text-xl font-bold text-[#393e4d] mb-4 border-b-2 border-[#f9c068] pb-2">
              Professional Summary
            </h2>
            <div className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#f9c068]">
              <p className="text-sm text-[#393e4d] leading-relaxed">
                {summary}
              </p>
            </div>
          </div>
        )}

        {/* Two Column Layout */}
        <div className="grid grid-cols-3 gap-8">
          
          {/* Left Column - Experience & Portfolio */}
          <div className="col-span-2">
            
            {/* Professional Experience */}
            <div className="mb-8">
              <h2 className="text-xl font-bold text-[#393e4d] mb-4 border-b-2 border-[#f9c068] pb-2">
                Professional Experience
              </h2>
              <div className="space-y-6">
                {experience.slice(0, 3).map((exp, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow-sm border border-[#404553]/20">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-bold text-[#393e4d] text-base">
                        {exp.position}
                      </h3>
                      <span className="text-sm text-white bg-[#393e4d] px-3 py-1 rounded">
                        {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                      </span>
                    </div>
                    <p className="text-sm font-semibold text-[#f9c068] mb-2">
                      {exp.company} • {exp.location}
                    </p>
                    <div className="text-sm text-[#393e4d] space-y-1">
                      {exp.description.slice(0, 3).map((desc, i) => (
                        <p key={i} className="flex items-start">
                          <Star className="w-3 h-3 mr-2 mt-1 text-[#f9c068] flex-shrink-0" />
                          {desc}
                        </p>
                      ))}
                    </div>
                    {exp.skills && exp.skills.length > 0 && (
                      <div className="mt-3">
                        <div className="flex flex-wrap gap-2">
                          {exp.skills.slice(0, 4).map((skill, i) => (
                            <span key={i} className="text-xs bg-[#f9c068]/20 text-[#393e4d] px-2 py-1 rounded border border-[#f9c068]/30">
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Portfolio Section */}
            <div>
              <h2 className="text-xl font-bold text-[#393e4d] mb-4 border-b-2 border-[#f9c068] pb-2">
                Featured Portfolio
              </h2>
              <div className="space-y-4">
                {portfolioProjects.slice(0, 3).map((project, index) => (
                  <div key={index} className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#393e4d]">
                    <h3 className="font-bold text-[#393e4d] text-sm flex items-center">
                      <Award className="w-4 h-4 mr-2 text-[#f9c068]" />
                      {project.name}
                    </h3>
                    <p className="text-sm text-[#404553] mt-1">
                      {project.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Skills, Education, Certifications */}
          <div className="col-span-1">
            
            {/* Core Skills */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#393e4d] mb-4">
                Core Skills
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="space-y-3">
                  {skills.slice(0, 6).map((skill, index) => {
                    const progress = [95, 90, 85, 88, 82, 87][index] || 80;
                    return (
                      <div key={index}>
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-sm font-medium text-[#393e4d]">{skill}</span>
                          <span className="text-xs text-[#f9c068]">{progress}%</span>
                        </div>
                        <div className="w-full bg-[#f9fafb] rounded-full h-2">
                          <div 
                            className="bg-[#f9c068] h-2 rounded-full transition-all duration-300"
                            style={{ width: `${progress}%` }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Education */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#393e4d] mb-4">
                Education
              </h2>
              <div className="space-y-3">
                {education.slice(0, 2).map((edu, index) => (
                  <div key={index} className="bg-white p-3 rounded-lg shadow-sm">
                    <h3 className="font-bold text-sm text-[#393e4d] flex items-center">
                      <BookOpen className="w-4 h-4 mr-2 text-[#f9c068]" />
                      {edu.degree}
                    </h3>
                    <p className="text-xs text-[#404553] font-medium">
                      {edu.institution}
                    </p>
                    <p className="text-xs text-[#393e4d]/70">
                      {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                    </p>
                    {edu.gpa && (
                      <p className="text-xs text-[#393e4d]/70">
                        GPA: {edu.gpa}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Certifications */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#393e4d] mb-4">
                Certifications
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="space-y-2">
                  {certifications.map((cert, index) => (
                    <div key={index} className="flex items-center">
                      <Award className="w-3 h-3 mr-2 text-[#f9c068]" />
                      <span className="text-sm text-[#393e4d]">{cert}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Additional Skills */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#393e4d] mb-4">
                Additional Skills
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="flex flex-wrap gap-2">
                  {skills.slice(6, 12).map((skill, index) => (
                    <span key={index} className="text-xs bg-[#f9c068]/20 text-[#393e4d] px-2 py-1 rounded border border-[#f9c068]/30">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Links */}
            <div>
              <h2 className="text-lg font-bold text-[#393e4d] mb-4">
                Professional Links
              </h2>
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="space-y-2">
                  {personalInfo.website && (
                    <div className="flex items-center text-sm text-[#f9c068]">
                      <Globe className="w-4 h-4 mr-2" />
                      <span>Portfolio</span>
                    </div>
                  )}
                  {personalInfo.linkedinUrl && (
                    <div className="flex items-center text-sm text-[#f9c068]">
                      <Linkedin className="w-4 h-4 mr-2" />
                      <span>LinkedIn</span>
                    </div>
                  )}
                  {personalInfo.githubUrl && (
                    <div className="flex items-center text-sm text-[#f9c068]">
                      <Github className="w-4 h-4 mr-2" />
                      <span>GitHub</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
