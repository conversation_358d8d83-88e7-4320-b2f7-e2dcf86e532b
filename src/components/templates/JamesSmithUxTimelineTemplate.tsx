import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, Calendar, Award } from 'lucide-react';

interface JamesSmithUxTimelineTemplateProps {
  resumeData: ResumeContent;
}

export default function JamesSmithUxTimelineTemplate({ resumeData }: JamesSmithUxTimelineTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="james-smith-ux-timeline"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Header */}
      <div className="absolute top-0 left-0 w-full h-[120px] bg-gradient-to-r from-[#667eea] to-[#764ba2] px-8 py-6">
        <div className="flex items-center h-full">
          <div className="flex-1">
            <h1 className="text-4xl font-bold text-white mb-2">
              {personalInfo.fullName || '<PERSON>'}
            </h1>
            <p className="text-xl text-white/90 flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              {personalInfo.jobTitle || 'UX Designer'}
            </p>
          </div>
          
          {/* Contact */}
          <div className="text-right text-white text-sm space-y-1">
            <div>{personalInfo.email}</div>
            <div>{personalInfo.phone}</div>
            <div>{personalInfo.location}</div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[140px] left-0 w-full h-[702px] px-8 py-6">
        
        {/* About */}
        {summary && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-[#333] mb-4 border-b-2 border-[#667eea] pb-2">
              About
            </h2>
            <p className="text-sm text-[#333] leading-relaxed">
              {summary}
            </p>
          </div>
        )}

        {/* Timeline Experience */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-[#333] mb-6 border-b-2 border-[#667eea] pb-2">
            Experience Timeline
          </h2>
          
          {/* Timeline Container */}
          <div className="relative">
            {/* Vertical Timeline Line */}
            <div className="absolute left-8 top-0 bottom-0 w-1 bg-gradient-to-b from-[#667eea] to-[#764ba2]"></div>
            
            <div className="space-y-8">
              {experience.slice(0, 4).map((exp, index) => (
                <div key={index} className="relative flex items-start">
                  {/* Timeline Node */}
                  <div className="absolute left-6 w-5 h-5 bg-white border-4 border-[#667eea] rounded-full z-10 shadow-lg"></div>
                  
                  {/* Content Card */}
                  <div className="ml-16 bg-gradient-to-r from-[#f8f9ff] to-white p-6 rounded-lg shadow-md border-l-4 border-[#667eea] w-full">
                    <div className="flex justify-between items-start mb-3">
                      <h3 className="font-bold text-[#333] text-xl">
                        {exp.position}
                      </h3>
                      <div className="text-right">
                        <span className="text-sm text-white bg-gradient-to-r from-[#667eea] to-[#764ba2] px-3 py-1 rounded-full">
                          {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                        </span>
                      </div>
                    </div>
                    <p className="text-lg font-semibold text-[#667eea] mb-2">
                      {exp.company}
                    </p>
                    <p className="text-sm text-[#333]/70 mb-4">
                      {exp.location}
                    </p>
                    <div className="text-sm text-[#333] space-y-2">
                      {exp.description.slice(0, 3).map((desc, i) => (
                        <p key={i} className="flex items-start">
                          <Award className="w-4 h-4 mr-2 mt-0.5 text-[#667eea] flex-shrink-0" />
                          {desc}
                        </p>
                      ))}
                    </div>
                    {exp.skills && exp.skills.length > 0 && (
                      <div className="mt-4">
                        <div className="flex flex-wrap gap-2">
                          {exp.skills.slice(0, 5).map((skill, i) => (
                            <span key={i} className="text-xs bg-gradient-to-r from-[#667eea] to-[#764ba2] text-white px-3 py-1 rounded-full">
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Skills & Education Grid */}
        <div className="grid grid-cols-2 gap-8">
          
          {/* Skills */}
          <div>
            <h2 className="text-xl font-bold text-[#333] mb-4 border-b-2 border-[#667eea] pb-2">
              Core Skills
            </h2>
            <div className="grid grid-cols-2 gap-3">
              {skills.slice(0, 8).map((skill, index) => (
                <div key={index} className="bg-gradient-to-r from-[#f8f9ff] to-white p-3 rounded-lg border border-[#667eea]/20 text-center">
                  <span className="text-sm font-medium text-[#333]">{skill}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Education */}
          <div>
            <h2 className="text-xl font-bold text-[#333] mb-4 border-b-2 border-[#667eea] pb-2">
              Education
            </h2>
            <div className="space-y-4">
              {education.slice(0, 2).map((edu, index) => (
                <div key={index} className="bg-gradient-to-r from-[#f8f9ff] to-white p-4 rounded-lg border border-[#667eea]/20">
                  <h3 className="font-bold text-[#333] text-base">
                    {edu.degree}
                  </h3>
                  <p className="text-sm text-[#667eea] font-medium">
                    {edu.institution}
                  </p>
                  <p className="text-sm text-[#333]/70">
                    {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
