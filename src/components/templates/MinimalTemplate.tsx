// Placeholder for Figma assets - replace with actual images in production
const imgMemoji = "/placeholder-avatar.png";
const imgMemoji1 = "/placeholder-avatar.png";
const imgMemoji2 = "/placeholder-avatar.png";
// import { imgIcon, imgIcon1, imgDivider } from "../../imports/svg-7ldyi";
import { Mail, Phone, MapPin, ExternalLink } from 'lucide-react';

interface MinimalTemplateProps {
  templateName?: string;
}

export function MinimalTemplate({ templateName = "Minimal" }: MinimalTemplateProps) {
  return (
    <div className="bg-[#fcfbf7] min-h-[842px] w-[595px] mx-auto px-10 py-12 relative overflow-hidden shadow-[0_4px_16px_rgba(0,0,0,0.08)] border border-gray-200" data-name="Resume">
      <div className="flex gap-8">
        {/* Left Column */}
        <div className="flex flex-col gap-8 shrink-0 w-48">
          {/* Profile Card */}
          <div className="bg-white rounded-3xl p-8 shadow-[0px_6px_50px_0px_rgba(0,0,0,0.04)]">
            {/* Profile Section */}
            <div className="flex flex-col gap-4 mb-6">
              {/* Avatar */}
              <div 
                className="w-16 h-16 rounded-full bg-cover bg-center bg-no-repeat relative overflow-hidden"
                style={{ 
                  backgroundImage: `url('${imgMemoji}'), url('${imgMemoji1}')`,
                  backgroundPosition: '50% 50%, 0% 0%',
                  backgroundSize: 'cover, 100% 100%'
                }}
              >
                <div 
                  className="absolute inset-0 bg-cover bg-center bg-no-repeat"
                  style={{ 
                    backgroundImage: `url('${imgMemoji2}')`,
                    left: '1.17%',
                    right: '-6.25%',
                    top: '0%',
                    bottom: '-5.08%'
                  }}
                />
              </div>
              
              {/* Name & Title */}
              <div>
                <h1 className="text-[20px] font-semibold text-[#323232] tracking-[-1px] leading-normal mb-2">
                  Andrew Mallen
                </h1>
                <p className="text-[16px] font-semibold text-[#f1b43e] tracking-[-0.8px] leading-normal">
                  Design Lead
                </p>
              </div>
            </div>
            
            {/* Contact Links */}
            <div className="flex flex-col gap-3 text-[14px] font-medium text-[#727272] tracking-[-0.7px]">
              <div className="flex items-center gap-3">
                <Mail size={14} className="text-[#f1b43e] shrink-0" />
                <a href="mailto:<EMAIL>" className="hover:text-[#f1b43e] transition-colors text-[13px]">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center gap-3">
                <Phone size={14} className="text-[#f1b43e] shrink-0" />
                <span className="text-[13px]">(*************</span>
              </div>
              <div className="flex items-center gap-3">
                <MapPin size={14} className="text-[#f1b43e] shrink-0" />
                <span className="text-[13px]">San Francisco, CA</span>
              </div>
              <div className="flex items-center gap-3">
                <ExternalLink size={14} className="text-[#f1b43e] shrink-0" />
                <a href="http://www.andrewmallen.com" className="hover:text-[#f1b43e] transition-colors text-[13px]">
                  andrewmallen.com
                </a>
              </div>
            </div>
          </div>

          {/* Skills & Interests Section */}
          <div className="px-2 space-y-8">
            {/* Skills */}
            <div>
              <h3 className="text-[20px] font-semibold text-[#323232] tracking-[-1px] mb-4">Skills</h3>
              <div className="space-y-4">
                <div className="flex flex-col gap-3 text-[14px] font-medium text-[#727272] tracking-[-0.7px]">
                  <span>Web Design</span>
                  <span>UX/UI Design</span>
                  <span>Usability Testing</span>
                  <span>Prototyping</span>
                  <span>Design Systems</span>
                  <span>User Research</span>
                </div>
                <div className="border-t border-[#e0dfdc] pt-3">
                  <div className="flex flex-col gap-3 text-[14px] font-medium text-[#727272] tracking-[-0.7px]">
                    <span>Figma</span>
                    <span>Framer</span>
                    <span>Webflow</span>
                    <span>After Effects</span>
                    <span>Sketch</span>
                    <span>InVision</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Languages */}
            <div>
              <h3 className="text-[20px] font-semibold text-[#323232] tracking-[-1px] mb-4">Languages</h3>
              <div className="flex flex-col gap-3 text-[14px] font-medium text-[#727272] tracking-[-0.7px]">
                <div className="flex justify-between items-center">
                  <span>English</span>
                  <span className="text-[#f1b43e] text-[13px] font-semibold">Native</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Spanish</span>
                  <span className="text-[#f1b43e] text-[13px] font-semibold">Fluent</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>French</span>
                  <span className="text-[#f1b43e] text-[13px] font-semibold">Basic</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="flex-1 space-y-10">
          {/* About Me */}
          <div>
            <h2 className="text-[26px] font-semibold text-[#323232] tracking-[-1px] mb-5">About me</h2>
            <p className="text-[16px] font-medium text-[#727272] tracking-[-0.7px] leading-relaxed">
              I'm a multi-disciplinary Product Designer, specialising in no-code and visual development. Currently Design Lead @ Revolution. Take a look below!
            </p>
          </div>

          {/* Experience */}
          <div>
            <h2 className="text-[22px] font-semibold text-[#323232] tracking-[-1px] mb-6">Experience</h2>
            <div className="space-y-8">
              {/* Experience 1 */}
              <div>
                <div className="mb-3">
                  <h4 className="text-[18px] font-semibold text-[#f1b43e] tracking-[-0.8px] leading-normal">
                    Design Lead @ Revolution
                  </h4>
                  <p className="text-[14px] font-medium text-[#727272] tracking-[-0.6px] leading-normal">
                    Sep 2019 - Present
                  </p>
                </div>
                <p className="text-[16px] font-medium text-[#727272] tracking-[-0.7px] leading-relaxed mb-4">
                  Leading a team of 5 designers in creating user-centered digital experiences for enterprise clients. Spearheaded the design system initiative that reduced development time by 40% and improved design consistency across all products.
                </p>
                <ul className="text-[15px] font-medium text-[#727272] tracking-[-0.7px] leading-relaxed space-y-2">
                  <li>• Established design processes that improved team velocity by 60%</li>
                  <li>• Led user research initiatives resulting in 35% increase in user satisfaction</li>
                  <li>• Mentored junior designers and fostered collaborative design culture</li>
                </ul>
              </div>

              {/* Experience 2 */}
              <div>
                <div className="mb-3">
                  <h4 className="text-[18px] font-semibold text-[#f1b43e] tracking-[-0.8px] leading-normal">
                    Senior Product Designer @ TechCorp
                  </h4>
                  <p className="text-[14px] font-medium text-[#727272] tracking-[-0.6px] leading-normal">
                    Jan 2018 - Aug 2019
                  </p>
                </div>
                <p className="text-[16px] font-medium text-[#727272] tracking-[-0.7px] leading-relaxed mb-4">
                  Designed and shipped end-to-end digital experiences for B2B SaaS platform serving 50,000+ users. Collaborated closely with engineering and product teams to deliver pixel-perfect implementations.
                </p>
                <ul className="text-[15px] font-medium text-[#727272] tracking-[-0.7px] leading-relaxed space-y-2">
                  <li>• Redesigned core user workflows reducing task completion time by 50%</li>
                  <li>• Conducted usability testing sessions with 200+ participants</li>
                  <li>• Created comprehensive component library in Figma</li>
                </ul>
              </div>

              {/* Experience 3 */}
              <div>
                <div className="mb-3">
                  <h4 className="text-[18px] font-semibold text-[#f1b43e] tracking-[-0.8px] leading-normal">
                    UX Designer @ StartupLab
                  </h4>
                  <p className="text-[14px] font-medium text-[#727272] tracking-[-0.6px] leading-normal">
                    Mar 2016 - Dec 2017
                  </p>
                </div>
                <p className="text-[16px] font-medium text-[#727272] tracking-[-0.7px] leading-relaxed mb-4">
                  First design hire at early-stage startup. Built design practice from ground up and established user-centered design methodology across all product initiatives.
                </p>
                <ul className="text-[15px] font-medium text-[#727272] tracking-[-0.7px] leading-relaxed space-y-2">
                  <li>• Designed MVP that attracted first 10,000 users in 6 months</li>
                  <li>• Implemented user feedback loops and analytics tracking</li>
                  <li>• Collaborated with founders on product strategy and roadmap</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Education */}
          <div>
            <h2 className="text-[22px] font-semibold text-[#323232] tracking-[-1px] mb-6">Education</h2>
            <div className="space-y-6">
              <div>
                <h4 className="text-[18px] font-semibold text-[#f1b43e] tracking-[-0.8px] leading-normal">
                  Bachelor of Fine Arts in Graphic Design
                </h4>
                <p className="text-[14px] font-medium text-[#727272] tracking-[-0.6px] leading-normal">
                  California College of the Arts • 2012 - 2016
                </p>
                <p className="text-[15px] font-medium text-[#727272] tracking-[-0.7px] leading-relaxed mt-2">
                  Magna Cum Laude • Dean's List • Thesis: "The Future of Digital Interaction Design"
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}