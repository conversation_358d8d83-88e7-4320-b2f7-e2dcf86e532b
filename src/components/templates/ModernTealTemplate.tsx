import { Mail, Phone, MapPin, Linkedin, Calendar, Building2, GraduationCap, Award, BookOpen, Target, Star } from 'lucide-react';

export function ModernTealTemplate() {
  return (
    <div className="w-full max-w-4xl mx-auto bg-white shadow-lg min-h-[800px] flex">
      {/* Main Content - Left Side */}
      <div className="flex-1 p-8 pr-6">
        {/* Summary */}
        <div className="mb-8">
          <h3 className="text-xl font-bold text-gray-800 mb-4 uppercase tracking-wide">Summary</h3>
          <p className="text-sm text-gray-700 leading-relaxed">
            Dynamic technology professional with 6+ years of experience in software development and team leadership. 
            Proven track record of delivering innovative solutions that drive business growth and improve operational efficiency. 
            Expertise in full-stack development, agile methodologies, and cross-functional collaboration.
          </p>
        </div>

        {/* Experience */}
        <div className="mb-8">
          <h3 className="text-xl font-bold text-gray-800 mb-4 uppercase tracking-wide">Experience</h3>
          
          <div className="space-y-6">
            {/* Senior Software Engineer */}
            <div>
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-bold text-gray-800">Senior Software Engineer</h4>
                  <p className="text-teal-600 font-medium">TechFlow Solutions</p>
                </div>
                <div className="text-sm text-gray-500">03/2021 - Present</div>
              </div>
              <div className="text-xs text-gray-500 mb-2 flex items-center gap-1">
                <MapPin className="w-3 h-3" />
                San Francisco, CA
              </div>
              <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
                <li>Led development of microservices architecture, reducing system downtime by 40%</li>
                <li>Mentored junior developers and established code review best practices</li>
                <li>Collaborated with product teams to deliver features serving 100K+ daily active users</li>
                <li>Implemented automated testing frameworks, improving deployment confidence by 60%</li>
              </ul>
            </div>

            {/* Software Developer */}
            <div>
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-bold text-gray-800">Software Developer</h4>
                  <p className="text-teal-600 font-medium">InnovateCorp</p>
                </div>
                <div className="text-sm text-gray-500">06/2019 - 02/2021</div>
              </div>
              <div className="text-xs text-gray-500 mb-2 flex items-center gap-1">
                <MapPin className="w-3 h-3" />
                Seattle, WA
              </div>
              <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
                <li>Developed responsive web applications using React and Node.js</li>
                <li>Optimized database queries, improving application performance by 35%</li>
                <li>Participated in agile development processes and sprint planning</li>
                <li>Built RESTful APIs serving enterprise clients with 99.9% uptime</li>
              </ul>
            </div>

            {/* Junior Developer */}
            <div>
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-bold text-gray-800">Junior Developer</h4>
                  <p className="text-teal-600 font-medium">StartupHub</p>
                </div>
                <div className="text-sm text-gray-500">01/2018 - 05/2019</div>
              </div>
              <div className="text-xs text-gray-500 mb-2 flex items-center gap-1">
                <MapPin className="w-3 h-3" />
                Portland, OR
              </div>
              <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
                <li>Built front-end components using modern JavaScript frameworks</li>
                <li>Collaborated on mobile-first responsive design implementations</li>
                <li>Contributed to open-source projects and internal tooling</li>
                <li>Assisted in QA processes and bug tracking systems</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Education */}
        <div className="mb-8">
          <h3 className="text-xl font-bold text-gray-800 mb-4 uppercase tracking-wide">Education</h3>
          <div>
            <h4 className="font-bold text-gray-800">Bachelor of Science in Computer Science</h4>
            <p className="text-teal-600 font-medium">University of Washington</p>
            <div className="flex items-center gap-4 text-sm text-gray-500 mt-1">
              <span className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                2014 - 2018
              </span>
              <span className="flex items-center gap-1">
                <MapPin className="w-3 h-3" />
                Seattle, WA
              </span>
            </div>
          </div>
        </div>

        {/* Interests */}
        <div>
          <h3 className="text-xl font-bold text-gray-800 mb-4 uppercase tracking-wide">Interests</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="font-bold text-gray-800 text-sm mb-1">Open Source Contribution</h4>
              <p className="text-xs text-gray-600">Active contributor to React and Vue.js communities</p>
            </div>
            <div>
              <h4 className="font-bold text-gray-800 text-sm mb-1">Technology Innovation</h4>
              <p className="text-xs text-gray-600">Exploring AI/ML applications in web development</p>
            </div>
          </div>
        </div>
      </div>

      {/* Sidebar - Right Side */}
      <div className="w-80 bg-teal-700 text-white p-8">
        {/* Name and Title */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold mb-2">ALEX THOMPSON</h1>
          <p className="text-teal-100 text-sm mb-4">Full Stack Developer | Tech Lead | Innovation Driver</p>
          
          {/* Contact Info */}
          <div className="space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4" />
              <span><EMAIL></span>
            </div>
            <div className="flex items-center gap-2">
              <Linkedin className="w-4 h-4" />
              <span>linkedin.com/in/alexthompson</span>
            </div>
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              <span>San Francisco, CA</span>
            </div>
          </div>
        </div>

        {/* Key Achievements */}
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-4 uppercase tracking-wide">Key Achievements</h3>
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-teal-500 rounded-full flex items-center justify-center shrink-0 mt-1">
                <Star className="w-3 h-3" />
              </div>
              <div>
                <h4 className="font-bold text-sm">Performance Leader 2023</h4>
                <p className="text-xs text-teal-100">Exceeded project delivery targets by 25% while maintaining code quality standards</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-teal-500 rounded-full flex items-center justify-center shrink-0 mt-1">
                <Target className="w-3 h-3" />
              </div>
              <div>
                <h4 className="font-bold text-sm">System Architecture Redesign</h4>
                <p className="text-xs text-teal-100">Led migration to cloud-native architecture, reducing operational costs by $200K annually</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-teal-500 rounded-full flex items-center justify-center shrink-0 mt-1">
                <Award className="w-3 h-3" />
              </div>
              <div>
                <h4 className="font-bold text-sm">Innovation Award Winner</h4>
                <p className="text-xs text-teal-100">Recognized for developing automated deployment pipeline used across 5 teams</p>
              </div>
            </div>
          </div>
        </div>

        {/* Skills */}
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-4 uppercase tracking-wide">Skills</h3>
          <div className="space-y-3">
            <div>
              <span className="text-sm font-medium">JavaScript & TypeScript</span>
              <div className="w-full bg-teal-800 h-2 rounded mt-1">
                <div className="bg-white h-2 rounded w-11/12"></div>
              </div>
            </div>
            
            <div>
              <span className="text-sm font-medium">React & Vue.js</span>
              <div className="w-full bg-teal-800 h-2 rounded mt-1">
                <div className="bg-white h-2 rounded w-5/6"></div>
              </div>
            </div>
            
            <div>
              <span className="text-sm font-medium">Node.js & Python</span>
              <div className="w-full bg-teal-800 h-2 rounded mt-1">
                <div className="bg-white h-2 rounded w-4/5"></div>
              </div>
            </div>
            
            <div>
              <span className="text-sm font-medium">AWS & Docker</span>
              <div className="w-full bg-teal-800 h-2 rounded mt-1">
                <div className="bg-white h-2 rounded w-3/4"></div>
              </div>
            </div>
            
            <div>
              <span className="text-sm font-medium">Database Design</span>
              <div className="w-full bg-teal-800 h-2 rounded mt-1">
                <div className="bg-white h-2 rounded w-4/5"></div>
              </div>
            </div>
            
            <div>
              <span className="text-sm font-medium">Team Leadership</span>
              <div className="w-full bg-teal-800 h-2 rounded mt-1">
                <div className="bg-white h-2 rounded w-5/6"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Training/Courses */}
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-4 uppercase tracking-wide">Training / Courses</h3>
          <div className="space-y-3 text-sm">
            <div>
              <h4 className="font-bold">AWS Solutions Architect</h4>
              <p className="text-xs text-teal-100">Comprehensive certification covering cloud architecture best practices and scalable system design</p>
            </div>
            
            <div>
              <h4 className="font-bold">Advanced React Patterns</h4>
              <p className="text-xs text-teal-100">Specialized course focusing on performance optimization and modern React development techniques</p>
            </div>
          </div>
        </div>

        {/* Languages */}
        <div>
          <h3 className="text-lg font-bold mb-4 uppercase tracking-wide">Languages</h3>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm">English</span>
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm">Spanish</span>
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <div className="w-2 h-2 bg-teal-500 rounded-full"></div>
                <div className="w-2 h-2 bg-teal-500 rounded-full"></div>
              </div>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm">Mandarin</span>
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <div className="w-2 h-2 bg-teal-500 rounded-full"></div>
                <div className="w-2 h-2 bg-teal-500 rounded-full"></div>
                <div className="w-2 h-2 bg-teal-500 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}