import { ImageWithFallback } from '../figma/ImageWithFallback';

interface Classic001TemplateProps {
  templateName?: string;
}

export function Classic001Template({ templateName = "Classic 001" }: Classic001TemplateProps) {
  return (
    <div className="w-full max-w-4xl mx-auto bg-white p-8">
      {/* Header Section */}
      <div className="mb-6">
        <h1 className="text-5xl font-bold text-gray-900 mb-2 tracking-wide">DAVID CHEN</h1>
        <div className="text-lg text-teal-600 font-medium mb-4">
          Senior Software Engineer | Cloud Architecture | Technical Leadership
        </div>
        
        <div className="flex items-center gap-6 text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <span>📞</span>
            <span>+1 (415) 555-7890</span>
          </div>
          <div className="flex items-center gap-1">
            <span>✉️</span>
            <span><EMAIL></span>
          </div>
          <div className="flex items-center gap-1">
            <span>🔗</span>
            <span>linkedin.com/in/davidchen</span>
          </div>
          <div className="flex items-center gap-1">
            <span>📍</span>
            <span>San Francisco, California</span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-3 gap-8">
        {/* Left Column - Experience */}
        <div className="col-span-2">
          <div className="border-b-2 border-gray-300 pb-2 mb-6">
            <h2 className="text-lg font-bold text-gray-900 uppercase tracking-wide">EXPERIENCE</h2>
          </div>

          {/* Senior Software Engineer */}
          <div className="mb-8">
            <div className="mb-4">
              <h3 className="text-lg font-bold text-gray-900">Senior Software Engineer</h3>
              <div className="text-teal-600 font-medium mb-2">CloudTech Solutions</div>
              <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                <div className="flex items-center gap-1">
                  <span>📅</span>
                  <span>01/2021 - Present</span>
                </div>
                <div className="flex items-center gap-1">
                  <span>📍</span>
                  <span>San Francisco, California</span>
                </div>
              </div>
              
              <p className="text-sm text-gray-700 mb-4 leading-relaxed">
                Led the development of a $20M cloud infrastructure platform, aligning with 
                organizational goals to enhance system scalability and performance. Achieved 
                a 40% improvement in deployment efficiency through automated CI/CD pipeline 
                implementation, reducing manual intervention by 60%.
              </p>
              
              <p className="text-sm text-gray-700 mb-4 leading-relaxed">
                Managed relationships with cross-functional teams across 8 departments, 
                resulting in a 25% increase in project delivery speed. Led a team of 12 
                engineers across 4 countries, improving team collaboration and technical 
                expertise through mentorship programs.
              </p>
              
              <p className="text-sm text-gray-700 mb-4 leading-relaxed">
                Implemented microservices architecture that influenced technology adoption 
                company-wide, achieving 99.9% system uptime and enhanced financial 
                accountability through rigorous performance monitoring.
              </p>
              
              <p className="text-sm text-gray-700 mb-4 leading-relaxed">
                Spearheaded DevOps initiatives that influenced technology modernization 
                across six product lines, improving deployment reliability by 45%.
              </p>
            </div>

            {/* Projects Table */}
            <div className="mb-6">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-300">
                    <th className="text-left py-2 text-teal-600 font-medium">Project name</th>
                    <th className="text-left py-2 text-teal-600 font-medium">Description</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-gray-200">
                    <td className="py-3 pr-4 font-medium text-teal-600">Cloud Infrastructure Platform</td>
                    <td className="py-3 text-gray-700">
                      Enhanced system scalability across 8 regions by 
                      implementing cloud-native solutions, leading to 
                      improved performance and cost optimization.
                    </td>
                  </tr>
                  <tr className="border-b border-gray-200">
                    <td className="py-3 pr-4 font-medium text-teal-600">Microservices Migration</td>
                    <td className="py-3 text-gray-700">
                      Managed a $5M microservices architecture project, 
                      achieving 30% better system performance through 
                      strategic service decomposition.
                    </td>
                  </tr>
                  <tr className="border-b border-gray-200">
                    <td className="py-3 pr-4 font-medium text-teal-600">DevOps Automation Suite</td>
                    <td className="py-3 text-gray-700">
                      Launched comprehensive automation solutions, 
                      increasing deployment frequency by 200% while 
                      reducing deployment-related issues by 80%.
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Software Engineering Lead */}
          <div className="mb-8">
            <div className="mb-4">
              <h3 className="text-lg font-bold text-gray-900">Software Engineering Lead</h3>
              <div className="text-teal-600 font-medium mb-2">TechFlow Innovations</div>
              <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                <div className="flex items-center gap-1">
                  <span>📅</span>
                  <span>06/2019 - 12/2020</span>
                </div>
                <div className="flex items-center gap-1">
                  <span>📍</span>
                  <span>San Jose, California</span>
                </div>
              </div>
              
              <p className="text-sm text-gray-700 mb-4 leading-relaxed">
                Administered a portfolio of enterprise applications worth $15M, focused on 
                enhancing system reliability and exceeding performance targets by 20%. 
                Cultivated strategic partnerships that brought in additional $8M in project 
                funding. Enhanced organizational capacity through talent acquisition and 
                training for a team of 80+ engineering staff.
              </p>
              
              <p className="text-sm text-gray-700 mb-4 leading-relaxed">
                Orchestrated end-to-end implementation of agile methodologies, improving 
                development workflow efficiency by 35%. Negotiated and secured development 
                partnerships for five major international technology integrations.
              </p>
            </div>

            {/* Projects Table */}
            <div className="mb-6">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-300">
                    <th className="text-left py-2 text-teal-600 font-medium">Project name</th>
                    <th className="text-left py-2 text-teal-600 font-medium">Description</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-gray-200">
                    <td className="py-3 pr-4 font-medium text-teal-600">Enterprise API Gateway</td>
                    <td className="py-3 text-gray-700">
                      Negotiated five key technology partnership 
                      agreements for API development, enhancing data 
                      integration capabilities and improving service delivery.
                    </td>
                  </tr>
                  <tr className="border-b border-gray-200">
                    <td className="py-3 pr-4 font-medium text-teal-600">Performance Optimization Initiative</td>
                    <td className="py-3 text-gray-700">
                      Secured an additional $3M in performance improvement 
                      funding by establishing and nurturing key 
                      stakeholder relationships across departments.
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Full Stack Developer */}
          <div className="mb-8">
            <div className="mb-4">
              <h3 className="text-lg font-bold text-gray-900">Full Stack Developer</h3>
              <div className="text-teal-600 font-medium mb-2">Digital Solutions Inc</div>
              <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                <div className="flex items-center gap-1">
                  <span>📅</span>
                  <span>03/2017 - 05/2019</span>
                </div>
                <div className="flex items-center gap-1">
                  <span>📍</span>
                  <span>Palo Alto, California</span>
                </div>
              </div>
              
              <p className="text-sm text-gray-700 mb-4 leading-relaxed">
                Developed and maintained multiple web applications serving 50,000+ users daily, 
                utilizing modern JavaScript frameworks and cloud technologies. Collaborated with 
                UX/UI designers to create responsive interfaces that improved user engagement by 40%. 
                Implemented automated testing frameworks reducing bug reports by 60%.
              </p>
              
              <p className="text-sm text-gray-700 mb-4 leading-relaxed">
                Built RESTful APIs and microservices using Node.js and Python, improving system 
                modularity and maintainability. Optimized database queries and implemented caching 
                strategies that reduced page load times by 50%. Mentored 3 junior developers on 
                coding best practices and modern development workflows.
              </p>
            </div>

            {/* Projects Table */}
            <div className="mb-6">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-300">
                    <th className="text-left py-2 text-teal-600 font-medium">Project name</th>
                    <th className="text-left py-2 text-teal-600 font-medium">Description</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-gray-200">
                    <td className="py-3 pr-4 font-medium text-teal-600">Customer Portal Platform</td>
                    <td className="py-3 text-gray-700">
                      Built comprehensive customer management system 
                      serving 50K+ users, implementing real-time data 
                      synchronization and advanced search capabilities.
                    </td>
                  </tr>
                  <tr className="border-b border-gray-200">
                    <td className="py-3 pr-4 font-medium text-teal-600">Mobile App Backend</td>
                    <td className="py-3 text-gray-700">
                      Developed scalable REST API infrastructure 
                      supporting iOS and Android applications with 
                      99.8% uptime and sub-200ms response times.
                    </td>
                  </tr>
                  <tr className="border-b border-gray-200">
                    <td className="py-3 pr-4 font-medium text-teal-600">E-commerce Integration</td>
                    <td className="py-3 text-gray-700">
                      Integrated payment processing and inventory 
                      management systems, resulting in 25% increase 
                      in transaction completion rates.
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Skills Section */}
          <div className="border-b-2 border-gray-300 pb-2 mb-4">
            <h2 className="text-lg font-bold text-gray-900 uppercase tracking-wide">SKILLS</h2>
          </div>
          
          <div className="grid grid-cols-3 gap-x-8 gap-y-2 text-sm">
            <div className="font-medium text-teal-600">Cloud Architecture</div>
            <div className="font-medium text-teal-600">Microservices Design</div>
            <div className="font-medium text-teal-600">DevOps Engineering</div>
            <div className="font-medium text-teal-600">Team Leadership</div>
            <div className="font-medium text-teal-600">System Optimization</div>
            <div className="font-medium text-teal-600">Agile Methodologies</div>
            <div className="font-medium text-teal-600">Docker & Containers</div>
            <div className="font-medium text-teal-600">Kubernetes Orchestration</div>
            <div className="font-medium text-teal-600">CI/CD Pipelines</div>
            <div className="font-medium text-teal-600">Performance Monitoring</div>
            <div className="font-medium text-teal-600">Code Reviews</div>
            <div className="font-medium text-teal-600">Technical Architecture</div>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* Summary */}
          <div>
            <div className="border-b-2 border-gray-300 pb-2 mb-4">
              <h2 className="text-lg font-bold text-gray-900 uppercase tracking-wide">SUMMARY</h2>
            </div>
            <p className="text-sm text-gray-700 leading-relaxed">
              Experienced software engineer with a proven track 
              record in cloud architecture, driven by a passion for 
              enhancing system performance and scalability. With 
              expertise in microservices design and team leadership, 
              I have successfully managed multimillion-dollar 
              technology initiatives across 10+ countries. My focus 
              on strategic partnerships and performance optimization 
              has consistently delivered innovative solutions that 
              positively impact enterprise-level outcomes. I bring 
              extensive experience in DevOps practices and agile 
              methodologies, focusing on operational excellence and 
              continuous improvement to achieve organizational goals.
            </p>
          </div>

          {/* Key Achievements */}
          <div>
            <div className="border-b-2 border-gray-300 pb-2 mb-4">
              <h2 className="text-lg font-bold text-gray-900 uppercase tracking-wide">KEY ACHIEVEMENTS</h2>
            </div>
            
            <div className="space-y-4 text-sm">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-teal-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-teal-600 font-bold text-xs">🚀</span>
                </div>
                <div>
                  <div className="font-bold text-gray-900 mb-1">System Performance Enhancement</div>
                  <div className="text-gray-700">
                    Spearheaded cloud architecture improvements 
                    across 8 regions, implementing solutions and 
                    improving system performance.
                  </div>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-teal-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-teal-600 font-bold text-xs">⚡</span>
                </div>
                <div>
                  <div className="font-bold text-gray-900 mb-1">Deployment Efficiency Optimization</div>
                  <div className="text-gray-700">
                    Directed a technology initiative valued at $5M, 
                    increasing deployment efficiency while 
                    maintaining high operational standards.
                  </div>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-teal-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-teal-600 font-bold text-xs">🤝</span>
                </div>
                <div>
                  <div className="font-bold text-gray-900 mb-1">Strategic Partnership Development</div>
                  <div className="text-gray-700">
                    Secured an additional $8M in partnership 
                    funding by establishing and nurturing key 
                    technology relationships.
                  </div>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-teal-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-teal-600 font-bold text-xs">📊</span>
                </div>
                <div>
                  <div className="font-bold text-gray-900 mb-1">Performance Monitoring Excellence</div>
                  <div className="text-gray-700">
                    Revised monitoring practices, significantly 
                    improving system reliability and 
                    bolstering organizational decision-making.
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Education */}
          <div>
            <div className="border-b-2 border-gray-300 pb-2 mb-4">
              <h2 className="text-lg font-bold text-gray-900 uppercase tracking-wide">EDUCATION</h2>
            </div>
            
            <div className="space-y-4 text-sm">
              <div>
                <div className="font-bold text-gray-900">Master's Degree in Computer Science</div>
                <div className="text-teal-600 font-medium">Stanford University</div>
                <div className="flex items-center gap-1 text-gray-600 mt-1">
                  <span>📅</span>
                  <span>09/2017 - 06/2019</span>
                </div>
              </div>
              
              <div>
                <div className="font-bold text-gray-900">Bachelor of Science in Software Engineering</div>
                <div className="text-teal-600 font-medium">University of California, Berkeley</div>
                <div className="flex items-center gap-1 text-gray-600 mt-1">
                  <span>📅</span>
                  <span>09/2013 - 06/2017</span>
                </div>
              </div>
            </div>
          </div>

          {/* Courses */}
          <div>
            <div className="border-b-2 border-gray-300 pb-2 mb-4">
              <h2 className="text-lg font-bold text-gray-900 uppercase tracking-wide">CERTIFICATIONS</h2>
            </div>
            
            <div className="space-y-3 text-sm">
              <div>
                <div className="font-bold text-teal-600">AWS Solutions Architect Professional</div>
                <div className="text-gray-700">
                  Advanced cloud architecture certification 
                  focused on complex system design and 
                  optimization strategies, provided by Amazon 
                  Web Services.
                </div>
              </div>
              
              <div>
                <div className="font-bold text-teal-600">Kubernetes Administrator Certification</div>
                <div className="text-gray-700">
                  Covering container orchestration and 
                  microservices management in enterprise 
                  environments, provided by the Cloud Native 
                  Computing Foundation.
                </div>
              </div>
              
              <div>
                <div className="font-bold text-teal-600">Google Cloud Professional Developer</div>
                <div className="text-gray-700">
                  Comprehensive certification in cloud-native 
                  application development, focusing on scalable 
                  solution design and implementation.
                </div>
              </div>
              
              <div>
                <div className="font-bold text-teal-600">Certified Scrum Master (CSM)</div>
                <div className="text-gray-700">
                  Agile project management certification 
                  emphasizing team leadership and iterative 
                  development methodologies.
                </div>
              </div>
            </div>
          </div>

          {/* Technical Skills */}
          <div>
            <div className="border-b-2 border-gray-300 pb-2 mb-4">
              <h2 className="text-lg font-bold text-gray-900 uppercase tracking-wide">TECHNICAL SKILLS</h2>
            </div>
            
            <div className="space-y-3 text-sm">
              <div>
                <div className="font-bold text-gray-900 mb-2">Programming Languages</div>
                <div className="text-gray-700">
                  Python, JavaScript/TypeScript, Java, Go, 
                  C++, SQL, Bash/Shell Scripting
                </div>
              </div>
              
              <div>
                <div className="font-bold text-gray-900 mb-2">Cloud Platforms</div>
                <div className="text-gray-700">
                  AWS (EC2, S3, Lambda, RDS), Google Cloud 
                  Platform, Microsoft Azure, Docker, Kubernetes
                </div>
              </div>
              
              <div>
                <div className="font-bold text-gray-900 mb-2">Frameworks & Tools</div>
                <div className="text-gray-700">
                  React, Node.js, Django, Spring Boot, 
                  Jenkins, GitLab CI, Terraform, Ansible
                </div>
              </div>
              
              <div>
                <div className="font-bold text-gray-900 mb-2">Databases</div>
                <div className="text-gray-700">
                  PostgreSQL, MongoDB, Redis, Elasticsearch, 
                  MySQL, DynamoDB
                </div>
              </div>
            </div>
          </div>


        </div>
      </div>


    </div>
  );
}