import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, Circle, Star, Award } from 'lucide-react';

interface CreativeCircularPhotoTemplateProps {
  resumeData: ResumeContent;
}

export default function CreativeCircularPhotoTemplate({ resumeData }: CreativeCircularPhotoTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  // Circular skill level indicators
  const CircularSkillIndicator = ({ skill, level }: { skill: string; level: number }) => {
    const circumference = 2 * Math.PI * 20;
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (level / 100) * circumference;

    return (
      <div className="flex items-center justify-between mb-3">
        <span className="text-sm font-medium text-[#333] flex-1">{skill}</span>
        <div className="relative w-12 h-12">
          <svg className="w-12 h-12 transform -rotate-90" viewBox="0 0 44 44">
            <circle
              cx="22"
              cy="22"
              r="20"
              stroke="#dbeafe"
              strokeWidth="3"
              fill="transparent"
            />
            <circle
              cx="22"
              cy="22"
              r="20"
              stroke="#3b82f6"
              strokeWidth="3"
              fill="transparent"
              strokeDasharray={strokeDasharray}
              strokeDashoffset={strokeDashoffset}
              strokeLinecap="round"
              className="transition-all duration-300"
            />
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-xs font-bold text-[#3b82f6]">{level}%</span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="creative-circular-photo"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Header */}
      <div className="absolute top-0 left-0 w-full h-[120px] bg-gradient-to-r from-[#3b82f6] to-[#60a5fa] px-8 py-6">
        <div className="flex items-center justify-between h-full">
          <div>
            <h1 className="text-3xl font-bold text-white mb-1">
              {personalInfo.fullName || 'Your Name'}
            </h1>
            <p className="text-lg text-white/90 flex items-center">
              <Circle className="w-5 h-5 mr-2" />
              {personalInfo.jobTitle || 'Creative Professional'}
            </p>
          </div>
          
          {/* Large Circular Photo */}
          <div className="w-24 h-24 rounded-full bg-white border-4 border-[#dbeafe] overflow-hidden shadow-xl">
            {personalInfo.photo ? (
              <img 
                src={personalInfo.photo} 
                alt="Profile" 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-[#60a5fa] to-[#3b82f6] flex items-center justify-center">
                <span className="text-white text-2xl font-bold">
                  {personalInfo.fullName?.charAt(0) || 'Y'}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Contact Info Strip */}
      <div className="absolute top-[120px] left-0 w-full h-[40px] bg-[#dbeafe] px-8 flex items-center justify-center">
        <div className="flex gap-8 text-[#3b82f6] text-sm font-medium">
          <div className="flex items-center">
            <Mail className="w-4 h-4 mr-2" />
            <span>{personalInfo.email}</span>
          </div>
          <div className="flex items-center">
            <Phone className="w-4 h-4 mr-2" />
            <span>{personalInfo.phone}</span>
          </div>
          <div className="flex items-center">
            <MapPin className="w-4 h-4 mr-2" />
            <span>{personalInfo.location}</span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[180px] left-0 w-full h-[662px] flex">
        
        {/* Left Column */}
        <div className="w-[370px] px-8 py-6">
          
          {/* About */}
          {summary && (
            <div className="mb-8">
              <h2 className="text-xl font-bold text-[#333] mb-4 flex items-center">
                <div className="w-6 h-6 bg-[#3b82f6] rounded-full mr-3 flex items-center justify-center">
                  <Star className="w-4 h-4 text-white" />
                </div>
                About Me
              </h2>
              <p className="text-sm text-[#333] leading-relaxed">
                {summary}
              </p>
            </div>
          )}

          {/* Experience */}
          <div className="mb-8">
            <h2 className="text-xl font-bold text-[#333] mb-4 flex items-center">
              <div className="w-6 h-6 bg-[#3b82f6] rounded-full mr-3 flex items-center justify-center">
                <Award className="w-4 h-4 text-white" />
              </div>
              Experience
            </h2>
            <div className="space-y-6">
              {experience.slice(0, 4).map((exp, index) => (
                <div key={index} className="relative">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-bold text-[#333] text-base">
                      {exp.position}
                    </h3>
                    <span className="text-sm text-white bg-[#3b82f6] px-3 py-1 rounded-full">
                      {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                    </span>
                  </div>
                  <p className="text-sm font-semibold text-[#60a5fa] mb-2">
                    {exp.company} • {exp.location}
                  </p>
                  <div className="text-sm text-[#333] space-y-1">
                    {exp.description.slice(0, 3).map((desc, i) => (
                      <p key={i} className="flex items-start">
                        <div className="w-2 h-2 bg-[#3b82f6] rounded-full mr-2 mt-2 flex-shrink-0"></div>
                        {desc}
                      </p>
                    ))}
                  </div>
                  {exp.skills && exp.skills.length > 0 && (
                    <div className="mt-3">
                      <div className="flex flex-wrap gap-2">
                        {exp.skills.slice(0, 4).map((skill, i) => (
                          <span key={i} className="text-xs bg-[#dbeafe] text-[#3b82f6] px-2 py-1 rounded-full border border-[#3b82f6]/20">
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="w-[224px] bg-[#f8fafc] px-6 py-6">
          
          {/* Skills with Circular Indicators */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#333] mb-4 flex items-center">
              <Circle className="w-4 h-4 mr-2 text-[#3b82f6]" />
              Skills
            </h2>
            <div className="space-y-1">
              {skills.slice(0, 6).map((skill, index) => {
                const levels = [95, 90, 85, 88, 82, 87];
                return (
                  <CircularSkillIndicator 
                    key={index} 
                    skill={skill} 
                    level={levels[index] || 80} 
                  />
                );
              })}
            </div>
          </div>

          {/* Education */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#333] mb-4">
              Education
            </h2>
            <div className="space-y-4">
              {education.slice(0, 2).map((edu, index) => (
                <div key={index} className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#3b82f6]">
                  <h3 className="font-bold text-sm text-[#333]">
                    {edu.degree}
                  </h3>
                  <p className="text-xs text-[#3b82f6] font-medium">
                    {edu.institution}
                  </p>
                  <p className="text-xs text-[#333]/70">
                    {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                  </p>
                  {edu.gpa && (
                    <p className="text-xs text-[#333]/70">
                      GPA: {edu.gpa}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Additional Skills */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#333] mb-4">
              Additional Skills
            </h2>
            <div className="flex flex-wrap gap-2">
              {skills.slice(6, 12).map((skill, index) => (
                <span key={index} className="text-xs bg-white text-[#3b82f6] px-2 py-1 rounded-full border border-[#3b82f6]/20">
                  {skill}
                </span>
              ))}
            </div>
          </div>

          {/* Contact Links */}
          <div>
            <h2 className="text-lg font-bold text-[#333] mb-4">
              Links
            </h2>
            <div className="space-y-3">
              {personalInfo.website && (
                <div className="flex items-center text-sm text-[#3b82f6]">
                  <div className="w-6 h-6 bg-[#3b82f6] rounded-full flex items-center justify-center mr-2">
                    <Globe className="w-3 h-3 text-white" />
                  </div>
                  <span>Portfolio</span>
                </div>
              )}
              {personalInfo.linkedinUrl && (
                <div className="flex items-center text-sm text-[#3b82f6]">
                  <div className="w-6 h-6 bg-[#3b82f6] rounded-full flex items-center justify-center mr-2">
                    <Linkedin className="w-3 h-3 text-white" />
                  </div>
                  <span>LinkedIn</span>
                </div>
              )}
              {personalInfo.githubUrl && (
                <div className="flex items-center text-sm text-[#3b82f6]">
                  <div className="w-6 h-6 bg-[#3b82f6] rounded-full flex items-center justify-center mr-2">
                    <Github className="w-3 h-3 text-white" />
                  </div>
                  <span>GitHub</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
