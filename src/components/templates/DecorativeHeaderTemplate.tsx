import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github } from 'lucide-react';

interface DecorativeHeaderTemplateProps {
  resumeData: ResumeContent;
}

export default function DecorativeHeaderTemplate({ resumeData }: DecorativeHeaderTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="decorative-header-5"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Decorative Header */}
      <div className="absolute top-0 left-0 w-full h-[120px] bg-gradient-to-r from-[#b8d5e7] to-[#a8c5d7] overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute top-4 right-8 w-16 h-16 bg-white/20 rounded-full"></div>
        <div className="absolute top-8 right-24 w-8 h-8 bg-white/30 rounded-full"></div>
        <div className="absolute bottom-4 left-8 w-12 h-12 bg-white/25 rounded-full"></div>
        
        {/* Header content */}
        <div className="absolute bottom-6 left-8 text-white">
          <h1 className="text-3xl font-bold text-[#333333] mb-1">
            {personalInfo.fullName || 'Your Name'}
          </h1>
          <p className="text-lg text-[#333333]/80">
            {personalInfo.jobTitle || 'Professional Title'}
          </p>
        </div>
      </div>

      {/* Main Content Area - Two Column Layout */}
      <div className="absolute top-[140px] left-0 w-full h-[702px] flex">
        
        {/* Left Column - Main Content */}
        <div className="w-[370px] px-8 py-6">
          
          {/* Professional Summary */}
          {summary && (
            <div className="mb-8">
              <h2 className="text-lg font-semibold text-[#333333] mb-3 border-b-2 border-[#b8d5e7] pb-1">
                Professional Summary
              </h2>
              <p className="text-sm text-[#333333] leading-relaxed">
                {summary}
              </p>
            </div>
          )}

          {/* Experience */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-[#333333] mb-4 border-b-2 border-[#b8d5e7] pb-1">
              Professional Experience
            </h2>
            <div className="space-y-4">
              {experience.slice(0, 3).map((exp, index) => (
                <div key={index} className="border-l-3 border-[#b8d5e7] pl-4">
                  <div className="flex justify-between items-start mb-1">
                    <h3 className="font-semibold text-[#333333] text-sm">
                      {exp.position}
                    </h3>
                    <span className="text-xs text-[#333333]/70">
                      {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                    </span>
                  </div>
                  <p className="text-sm font-medium text-[#333333]/80 mb-2">
                    {exp.company} • {exp.location}
                  </p>
                  <div className="text-xs text-[#333333]/70 space-y-1">
                    {exp.description.slice(0, 2).map((desc, i) => (
                      <p key={i}>• {desc}</p>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column - Contact & Additional Info */}
        <div className="w-[224px] bg-[#f8f9fa] px-6 py-6">
          
          {/* Contact Information */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-[#333333] mb-4">
              Contact
            </h2>
            <div className="space-y-3">
              <div className="flex items-center text-xs text-[#333333]">
                <Mail className="w-3 h-3 mr-2 text-[#b8d5e7]" />
                <span>{personalInfo.email}</span>
              </div>
              <div className="flex items-center text-xs text-[#333333]">
                <Phone className="w-3 h-3 mr-2 text-[#b8d5e7]" />
                <span>{personalInfo.phone}</span>
              </div>
              <div className="flex items-center text-xs text-[#333333]">
                <MapPin className="w-3 h-3 mr-2 text-[#b8d5e7]" />
                <span>{personalInfo.location}</span>
              </div>
              {personalInfo.website && (
                <div className="flex items-center text-xs text-[#333333]">
                  <Globe className="w-3 h-3 mr-2 text-[#b8d5e7]" />
                  <span className="truncate">{personalInfo.website}</span>
                </div>
              )}
              {personalInfo.linkedinUrl && (
                <div className="flex items-center text-xs text-[#333333]">
                  <Linkedin className="w-3 h-3 mr-2 text-[#b8d5e7]" />
                  <span className="truncate">LinkedIn</span>
                </div>
              )}
            </div>
          </div>

          {/* Education */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-[#333333] mb-4">
              Education
            </h2>
            <div className="space-y-3">
              {education.slice(0, 2).map((edu, index) => (
                <div key={index}>
                  <h3 className="font-semibold text-sm text-[#333333]">
                    {edu.degree}
                  </h3>
                  <p className="text-xs text-[#333333]/80">
                    {edu.institution}
                  </p>
                  <p className="text-xs text-[#333333]/70">
                    {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Skills */}
          <div>
            <h2 className="text-lg font-semibold text-[#333333] mb-4">
              Skills
            </h2>
            <div className="space-y-2">
              {skills.slice(0, 8).map((skill, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-2 h-2 bg-[#b8d5e7] rounded-full mr-2"></div>
                  <span className="text-xs text-[#333333]">{skill}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
