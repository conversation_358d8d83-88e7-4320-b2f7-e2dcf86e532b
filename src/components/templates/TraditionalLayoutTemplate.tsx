import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, Award } from 'lucide-react';

interface TraditionalLayoutTemplateProps {
  resumeData: ResumeContent;
}

export default function TraditionalLayoutTemplate({ resumeData }: TraditionalLayoutTemplateProps) {
  const { personalInfo, summary, experience, education, skills, certifications } = resumeData;

  // Skill progress calculation
  const getSkillProgress = (index: number) => {
    const progressValues = [90, 85, 80, 88, 75, 92, 78, 85, 82, 87];
    return progressValues[index % progressValues.length];
  };

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="traditional-layout-13"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Header */}
      <div className="absolute top-0 left-0 w-full h-[120px] bg-[#212121] px-8 py-6">
        <div className="flex items-center h-full">
          {/* Profile Photo */}
          <div className="w-20 h-20 rounded-full bg-[#3e6af2] border-3 border-white overflow-hidden mr-6">
            {personalInfo.photo ? (
              <img 
                src={personalInfo.photo} 
                alt="Profile" 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-[#3e6af2] to-[#2d4fd1] flex items-center justify-center">
                <span className="text-white text-2xl font-bold">
                  {personalInfo.fullName?.charAt(0) || 'Y'}
                </span>
              </div>
            )}
          </div>
          
          {/* Name and Title */}
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-white mb-2">
              {personalInfo.fullName || 'Your Name'}
            </h1>
            <p className="text-lg text-white/90">
              {personalInfo.jobTitle || 'Professional Title'}
            </p>
          </div>
        </div>
      </div>

      {/* Main Content - Two Column Layout */}
      <div className="absolute top-[140px] left-0 w-full h-[702px] flex">
        
        {/* Left Column - Main Content */}
        <div className="w-[370px] px-8 py-6">
          
          {/* Contact Information */}
          <div className="mb-6">
            <h2 className="text-lg font-bold text-[#212121] mb-3 border-b-2 border-[#3e6af2] pb-1">
              Contact Information
            </h2>
            <div className="grid grid-cols-1 gap-2 text-sm text-[#212121]">
              <div className="flex items-center">
                <Mail className="w-4 h-4 mr-2 text-[#3e6af2]" />
                <span>{personalInfo.email}</span>
              </div>
              <div className="flex items-center">
                <Phone className="w-4 h-4 mr-2 text-[#3e6af2]" />
                <span>{personalInfo.phone}</span>
              </div>
              <div className="flex items-center">
                <MapPin className="w-4 h-4 mr-2 text-[#3e6af2]" />
                <span>{personalInfo.location}</span>
              </div>
              {personalInfo.website && (
                <div className="flex items-center">
                  <Globe className="w-4 h-4 mr-2 text-[#3e6af2]" />
                  <span className="truncate">{personalInfo.website}</span>
                </div>
              )}
              {personalInfo.linkedinUrl && (
                <div className="flex items-center">
                  <Linkedin className="w-4 h-4 mr-2 text-[#3e6af2]" />
                  <span>LinkedIn Profile</span>
                </div>
              )}
            </div>
          </div>

          {/* Professional Profile */}
          {summary && (
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#212121] mb-3 border-b-2 border-[#3e6af2] pb-1">
                Professional Profile
              </h2>
              <p className="text-sm text-[#212121] leading-relaxed">
                {summary}
              </p>
            </div>
          )}

          {/* Employment History */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#212121] mb-4 border-b-2 border-[#3e6af2] pb-1">
              Employment History
            </h2>
            <div className="space-y-5">
              {experience.slice(0, 4).map((exp, index) => (
                <div key={index}>
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-bold text-[#212121] text-base">
                      {exp.position}
                    </h3>
                    <span className="text-sm text-[#212121]/70 bg-gray-100 px-2 py-1 rounded">
                      {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                    </span>
                  </div>
                  <p className="text-sm font-semibold text-[#3e6af2] mb-1">
                    {exp.company} • {exp.location}
                  </p>
                  <div className="text-sm text-[#212121] space-y-1">
                    {exp.description.slice(0, 3).map((desc, i) => (
                      <p key={i}>• {desc}</p>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column - Skills & Education */}
        <div className="w-[224px] bg-gray-50 px-6 py-6">
          
          {/* Education */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#212121] mb-4">
              Education
            </h2>
            <div className="space-y-4">
              {education.slice(0, 3).map((edu, index) => (
                <div key={index} className="bg-white p-3 rounded-lg shadow-sm">
                  <h3 className="font-bold text-sm text-[#212121]">
                    {edu.degree}
                  </h3>
                  <p className="text-xs text-[#3e6af2] font-medium">
                    {edu.institution}
                  </p>
                  <p className="text-xs text-[#212121]/70">
                    {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                  </p>
                  {edu.gpa && (
                    <p className="text-xs text-[#212121]/70">
                      GPA: {edu.gpa}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Skills with Progress Bars */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#212121] mb-4">
              Skills
            </h2>
            <div className="space-y-3">
              {skills.slice(0, 8).map((skill, index) => {
                const progress = getSkillProgress(index);
                return (
                  <div key={index}>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-[#212121]">{skill}</span>
                      <span className="text-xs text-[#212121]/70">{progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-[#3e6af2] h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progress}%` }}
                      ></div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Certifications */}
          {certifications && certifications.length > 0 && (
            <div>
              <h2 className="text-lg font-bold text-[#212121] mb-4 flex items-center">
                <Award className="w-4 h-4 mr-2 text-[#3e6af2]" />
                Certifications
              </h2>
              <div className="space-y-3">
                {certifications.slice(0, 4).map((cert, index) => (
                  <div key={index} className="bg-white p-3 rounded-lg shadow-sm border-l-3 border-[#3e6af2]">
                    <h3 className="font-semibold text-sm text-[#212121]">
                      {cert.name}
                    </h3>
                    <p className="text-xs text-[#3e6af2] font-medium">
                      {cert.issuer}
                    </p>
                    <p className="text-xs text-[#212121]/70">
                      {new Date(cert.issueDate).getFullYear()}
                    </p>
                    {cert.credentialId && (
                      <p className="text-xs text-[#212121]/60 mt-1">
                        ID: {cert.credentialId}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
