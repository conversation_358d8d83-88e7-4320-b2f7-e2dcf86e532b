import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, Figma, Zap } from 'lucide-react';

interface DianneRussellUiuxTemplateProps {
  resumeData: ResumeContent;
}

export default function DianneRussellUiuxTemplate({ resumeData }: DianneRussellUiuxTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="dianne-russell-uiux"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Modern Header */}
      <div className="absolute top-0 left-0 w-full h-[120px] bg-gradient-to-r from-[#ff6b6b] to-[#ee5a24] px-8 py-6">
        <div className="flex items-center justify-between h-full">
          <div>
            <h1 className="text-3xl font-bold text-white mb-1">
              {personalInfo.fullName || '<PERSON><PERSON>'}
            </h1>
            <p className="text-lg text-white/90 flex items-center">
              <Zap className="w-5 h-5 mr-2" />
              {personalInfo.jobTitle || 'UI/UX Designer'}
            </p>
          </div>
          
          {/* Contact badges */}
          <div className="flex gap-2">
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <Mail className="w-5 h-5 text-white" />
            </div>
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <Phone className="w-5 h-5 text-white" />
            </div>
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <MapPin className="w-5 h-5 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Contact Info Bar */}
      <div className="absolute top-[120px] left-0 w-full h-[40px] bg-[#2d3436] px-8 flex items-center justify-center">
        <div className="flex gap-8 text-white text-sm">
          <span>{personalInfo.email}</span>
          <span>{personalInfo.phone}</span>
          <span>{personalInfo.location}</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[180px] left-0 w-full h-[662px] flex">
        
        {/* Left Column */}
        <div className="w-[370px] px-8 py-6">
          
          {/* About */}
          {summary && (
            <div className="mb-8">
              <h2 className="text-xl font-bold text-[#2d3436] mb-4 border-b-2 border-[#ff6b6b] pb-2">
                About Me
              </h2>
              <p className="text-sm text-[#2d3436] leading-relaxed">
                {summary}
              </p>
            </div>
          )}

          {/* Experience */}
          <div className="mb-8">
            <h2 className="text-xl font-bold text-[#2d3436] mb-4 border-b-2 border-[#ff6b6b] pb-2">
              Work Experience
            </h2>
            <div className="space-y-6">
              {experience.slice(0, 4).map((exp, index) => (
                <div key={index}>
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-bold text-[#2d3436] text-base">
                      {exp.position}
                    </h3>
                    <span className="text-sm text-white bg-[#ff6b6b] px-3 py-1 rounded-full">
                      {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                    </span>
                  </div>
                  <p className="text-sm font-semibold text-[#ff6b6b] mb-2">
                    {exp.company} • {exp.location}
                  </p>
                  <div className="text-sm text-[#2d3436] space-y-1">
                    {exp.description.slice(0, 3).map((desc, i) => (
                      <p key={i}>• {desc}</p>
                    ))}
                  </div>
                  {exp.skills && exp.skills.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-1">
                      {exp.skills.slice(0, 4).map((skill, i) => (
                        <span key={i} className="text-xs bg-[#ff6b6b]/10 text-[#ff6b6b] px-2 py-1 rounded">
                          {skill}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="w-[224px] bg-[#f8f9fa] px-6 py-6">
          
          {/* Skills */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#2d3436] mb-4">
              Skills
            </h2>
            <div className="space-y-3">
              {skills.slice(0, 8).map((skill, index) => {
                const progress = [95, 90, 85, 88, 82, 87, 80, 85][index] || 80;
                return (
                  <div key={index}>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-[#2d3436]">{skill}</span>
                      <span className="text-xs text-[#2d3436]/70">{progress}%</span>
                    </div>
                    <div className="w-full bg-white rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-[#ff6b6b] to-[#ee5a24] h-2 rounded-full"
                        style={{ width: `${progress}%` }}
                      ></div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Education */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#2d3436] mb-4">
              Education
            </h2>
            <div className="space-y-3">
              {education.slice(0, 2).map((edu, index) => (
                <div key={index} className="bg-white p-3 rounded-lg shadow-sm">
                  <h3 className="font-semibold text-sm text-[#2d3436]">
                    {edu.degree}
                  </h3>
                  <p className="text-xs text-[#ff6b6b] font-medium">
                    {edu.institution}
                  </p>
                  <p className="text-xs text-[#2d3436]/70">
                    {new Date(edu.startDate).getFullYear()}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Tools */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#2d3436] mb-4">
              Design Tools
            </h2>
            <div className="grid grid-cols-2 gap-2">
              {[
                'Figma',
                'Sketch',
                'Adobe XD',
                'Photoshop',
                'Illustrator',
                'InVision'
              ].map((tool, index) => (
                <div key={index} className="bg-white p-2 rounded text-center">
                  <span className="text-xs font-medium text-[#2d3436]">{tool}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Links */}
          <div>
            <h2 className="text-lg font-bold text-[#2d3436] mb-4">
              Links
            </h2>
            <div className="space-y-2">
              {personalInfo.website && (
                <div className="flex items-center text-sm text-[#ff6b6b]">
                  <Globe className="w-4 h-4 mr-2" />
                  <span>Portfolio</span>
                </div>
              )}
              {personalInfo.linkedinUrl && (
                <div className="flex items-center text-sm text-[#ff6b6b]">
                  <Linkedin className="w-4 h-4 mr-2" />
                  <span>LinkedIn</span>
                </div>
              )}
              <div className="flex items-center text-sm text-[#ff6b6b]">
                <Figma className="w-4 h-4 mr-2" />
                <span>Figma</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
