import { Mail, Phone, Linkedin, Github, Calendar, Briefcase, FileText, GraduationCap } from 'lucide-react';

interface ElegantTemplateProps {
  templateName?: string;
}

export function ElegantTemplate({ templateName = "Elegant" }: ElegantTemplateProps) {
  return (
    <div className="bg-gray-50 min-h-[842px] max-w-[595px] mx-auto relative">
      {/* Header Section */}
      <div className="bg-white px-10 py-6">
        <div className="flex justify-between items-start">
          {/* Name and Title */}
          <div className="flex flex-col">
            <h1 className="text-2xl font-bold text-gray-900 mb-1"><PERSON></h1>
            <h2 className="text-lg font-semibold text-purple-600">Senior Product Designer</h2>
          </div>
          
          {/* Contact Info */}
          <div className="text-right space-y-2 text-sm">
            <div className="flex items-center justify-end gap-2">
              <Mail size={14} className="text-purple-600" />
              <span className="text-gray-700"><EMAIL></span>
            </div>
            <div className="flex items-center justify-end gap-2">
              <Phone size={14} className="text-purple-600" />
              <span className="text-gray-700">(555) 987 - 6543</span>
            </div>
            <div className="flex items-center justify-end gap-2">
              <Linkedin size={14} className="text-purple-600" />
              <span className="text-gray-700">LinkedIn</span>
            </div>
            <div className="flex items-center justify-end gap-2">
              <Github size={14} className="text-purple-600" />
              <span className="text-gray-700">Portfolio</span>
            </div>
          </div>
        </div>
        
        {/* Divider Line */}
        <div className="w-full h-px bg-gray-300 mt-6 mb-4"></div>
        
        {/* Summary */}
        <p className="text-sm text-gray-700 leading-relaxed">
          Experienced Product Designer with a passion for creating intuitive user experiences and building scalable design systems that bridge the gap between user needs and business objectives.
        </p>
      </div>

      {/* Main Content */}
      <div className="flex bg-white">
        {/* Left Sidebar */}
        <div className="w-48 bg-white px-6 py-6 space-y-8">
          {/* Skills */}
          <div>
            <h3 className="text-lg font-bold text-gray-900 mb-4 tracking-wide">SKILLS</h3>
            <div className="border-b border-purple-600 w-full mb-4"></div>
            <div className="space-y-2">
              <div className="flex flex-wrap gap-1 mb-2">
                <span className="bg-gray-100 border border-gray-200 px-3 py-1 rounded-full text-xs font-medium text-gray-800">Figma</span>
                <span className="bg-gray-100 border border-gray-200 px-3 py-1 rounded-full text-xs font-medium text-gray-800">Sketch</span>
              </div>
              <div className="flex flex-wrap gap-1 mb-2">
                <span className="bg-gray-100 border border-gray-200 px-3 py-1 rounded-full text-xs font-medium text-gray-800">Prototyping</span>
                <span className="bg-gray-100 border border-gray-200 px-3 py-1 rounded-full text-xs font-medium text-gray-800">User Research</span>
              </div>
              <div className="flex flex-wrap gap-1 mb-2">
                <span className="bg-gray-100 border border-gray-200 px-3 py-1 rounded-full text-xs font-medium text-gray-800">Design Systems</span>
                <span className="bg-gray-100 border border-gray-200 px-3 py-1 rounded-full text-xs font-medium text-gray-800">UX Strategy</span>
                <span className="bg-gray-100 border border-gray-200 px-3 py-1 rounded-full text-xs font-medium text-gray-800">Accessibility</span>
              </div>
              <div className="flex flex-wrap gap-1">
                <span className="bg-gray-100 border border-gray-200 px-3 py-1 rounded-full text-xs font-medium text-gray-800">Usability Testing</span>
                <span className="bg-gray-100 border border-gray-200 px-3 py-1 rounded-full text-xs font-medium text-gray-800">Adobe Creative</span>
              </div>
            </div>
          </div>

          {/* Education */}
          <div>
            <h3 className="text-lg font-bold text-gray-900 mb-4 tracking-wide">EDUCATION</h3>
            <div className="border-b border-purple-600 w-full mb-4"></div>
            <div className="space-y-4">
              <div className="flex gap-3">
                <GraduationCap size={14} className="text-purple-600 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 text-sm">M.A. Interaction Design</h4>
                  <p className="text-sm text-gray-700 font-medium">Design Institute</p>
                  <div className="flex items-center gap-1 mt-1">
                    <Calendar size={12} className="text-gray-500" />
                    <span className="text-xs text-gray-600">2018-2020</span>
                  </div>
                </div>
              </div>
              
              <div className="flex gap-3">
                <GraduationCap size={14} className="text-purple-600 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 text-sm">B.A. Graphic Design</h4>
                  <p className="text-sm text-gray-700 font-medium">State University</p>
                  <div className="flex items-center gap-1 mt-1">
                    <Calendar size={12} className="text-gray-500" />
                    <span className="text-xs text-gray-600">2014-2018</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Content */}
        <div className="flex-1 px-6 py-6">
          {/* Experience */}
          <div className="mb-8">
            <h3 className="text-lg font-bold text-gray-900 mb-4 tracking-wide">EXPERIENCE</h3>
            <div className="border-b border-purple-600 w-full mb-6"></div>
            
            <div className="space-y-6">
              {/* Experience Item 1 */}
              <div className="flex gap-3">
                <Briefcase size={14} className="text-purple-600 mt-1 flex-shrink-0" />
                <div className="flex-1">
                  <div className="flex justify-between items-start mb-1">
                    <h4 className="font-semibold text-gray-900 text-sm">Senior Product Designer</h4>
                    <div className="flex items-center gap-1">
                      <Calendar size={12} className="text-gray-500" />
                      <span className="text-xs text-gray-600">2022-Present</span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-700 font-medium mb-2">Design Systems Co.</p>
                  <ul className="text-xs text-gray-600 space-y-1 list-disc list-inside">
                    <li>Led design system initiatives across 15+ product teams, establishing unified design patterns and component libraries.</li>
                    <li>Collaborated with engineering teams to implement design tokens, reducing development time by 40%.</li>
                    <li>Mentored junior designers and conducted design critiques to maintain high-quality standards across projects.</li>
                  </ul>
                </div>
              </div>

              {/* Experience Item 2 */}
              <div className="flex gap-3">
                <Briefcase size={14} className="text-purple-600 mt-1 flex-shrink-0" />
                <div className="flex-1">
                  <div className="flex justify-between items-start mb-1">
                    <h4 className="font-semibold text-gray-900 text-sm">Product Designer</h4>
                    <div className="flex items-center gap-1">
                      <Calendar size={12} className="text-gray-500" />
                      <span className="text-xs text-gray-600">2020-2022</span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-700 font-medium mb-2">Creative Studio Inc</p>
                  <ul className="text-xs text-gray-600 space-y-1 list-disc list-inside">
                    <li>Designed user-centered interfaces for mobile and web applications, improving user satisfaction scores by 35%.</li>
                    <li>Conducted user research and usability testing sessions to validate design decisions and iterate on solutions.</li>
                    <li>Collaborated closely with product managers to define feature requirements and user stories.</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Projects */}
          <div>
            <h3 className="text-lg font-bold text-gray-900 mb-4 tracking-wide">PROJECTS</h3>
            <div className="border-b border-purple-600 w-full mb-6"></div>
            
            <div className="space-y-4">
              {/* Project 1 */}
              <div className="flex gap-3">
                <FileText size={12} className="text-purple-600 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 text-sm mb-1">Design System Platform</h4>
                  <p className="text-xs text-gray-600 leading-relaxed">
                    Built a comprehensive design system platform with component documentation, usage guidelines, and automated design token generation for multi-brand consistency.
                  </p>
                </div>
              </div>

              {/* Project 2 */}
              <div className="flex gap-3">
                <FileText size={12} className="text-purple-600 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 text-sm mb-1">Mobile App Redesign</h4>
                  <p className="text-xs text-gray-600 leading-relaxed">
                    Led complete redesign of mobile application interface, focusing on accessibility and user engagement, resulting in 50% increase in daily active users.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}