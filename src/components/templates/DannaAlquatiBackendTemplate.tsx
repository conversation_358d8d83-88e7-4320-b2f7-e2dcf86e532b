import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, Server, Database, Code2 } from 'lucide-react';

interface DannaAlquatiBackendTemplateProps {
  resumeData: ResumeContent;
}

export default function DannaAlquatiBackendTemplate({ resumeData }: DannaAlquatiBackendTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  const techStack = [
    { name: 'Node.js', level: 95 },
    { name: 'Python', level: 90 },
    { name: 'PostgreSQL', level: 88 },
    { name: 'MongoDB', level: 85 },
    { name: 'Docker', level: 82 },
    { name: 'AWS', level: 87 }
  ];

  return (
    <div 
      className="bg-[#0d1117] relative w-[594px] h-[842px] overflow-hidden text-white"
      data-template-id="danna-alquati-backend"
      style={{ fontFamily: 'JetBrains Mono, monospace' }}
    >
      {/* Terminal-style Header */}
      <div className="absolute top-0 left-0 w-full h-[80px] bg-[#161b22] border-b border-[#30363d] px-6 py-4">
        <div className="flex items-center justify-between h-full">
          <div className="flex items-center">
            <div className="flex gap-2 mr-4">
              <div className="w-3 h-3 bg-[#ff5f56] rounded-full"></div>
              <div className="w-3 h-3 bg-[#ffbd2e] rounded-full"></div>
              <div className="w-3 h-3 bg-[#27ca3f] rounded-full"></div>
            </div>
            <span className="text-[#8b949e] text-sm">~/developer/</span>
            <span className="text-[#58a6ff] text-sm font-bold ml-1">
              {personalInfo.fullName?.toLowerCase().replace(' ', '-') || 'danna-alquati'}
            </span>
          </div>
          <div className="text-[#8b949e] text-sm">
            {personalInfo.jobTitle || 'Backend Developer'}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[100px] left-0 w-full h-[742px] flex">
        
        {/* Left Column - Code Style */}
        <div className="w-[370px] px-6 py-6">
          
          {/* About Section */}
          {summary && (
            <div className="mb-8">
              <div className="text-[#ff7b72] text-sm mb-2">// About Me</div>
              <div className="bg-[#161b22] p-4 rounded border border-[#30363d]">
                <span className="text-[#79c0ff]">const</span>{' '}
                <span className="text-[#ffa657]">aboutMe</span>{' '}
                <span className="text-[#ff7b72]">=</span>{' '}
                <span className="text-[#a5d6ff]">"{summary}"</span>
              </div>
            </div>
          )}

          {/* Experience */}
          <div className="mb-8">
            <div className="text-[#ff7b72] text-sm mb-2">// Work Experience</div>
            <div className="space-y-4">
              {experience.slice(0, 3).map((exp, index) => (
                <div key={index} className="bg-[#161b22] p-4 rounded border border-[#30363d]">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <span className="text-[#79c0ff]">class</span>{' '}
                      <span className="text-[#ffa657] font-bold">{exp.position.replace(/\s+/g, '')}</span>{' '}
                      <span className="text-[#ff7b72]">extends</span>{' '}
                      <span className="text-[#79c0ff]">Developer</span>
                    </div>
                    <span className="text-[#8b949e] text-xs">
                      {new Date(exp.startDate).getFullYear()}-{exp.current ? 'present' : new Date(exp.endDate!).getFullYear()}
                    </span>
                  </div>
                  <div className="text-[#8b949e] text-sm mb-2">
                    <span className="text-[#ff7b72]">//</span> {exp.company} • {exp.location}
                  </div>
                  <div className="text-sm space-y-1">
                    {exp.description.slice(0, 2).map((desc, i) => (
                      <div key={i} className="text-[#8b949e]">
                        <span className="text-[#ff7b72]">//</span> {desc}
                      </div>
                    ))}
                  </div>
                  {exp.skills && exp.skills.length > 0 && (
                    <div className="mt-2">
                      <span className="text-[#79c0ff]">technologies:</span>{' '}
                      <span className="text-[#a5d6ff]">[</span>
                      {exp.skills.slice(0, 3).map((skill, i) => (
                        <span key={i}>
                          <span className="text-[#a5d6ff]">"{skill}"</span>
                          {i < exp.skills.slice(0, 3).length - 1 && <span className="text-[#ff7b72]">, </span>}
                        </span>
                      ))}
                      <span className="text-[#a5d6ff]">]</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column - Sidebar */}
        <div className="w-[224px] bg-[#161b22] px-6 py-6 border-l border-[#30363d]">
          
          {/* Contact */}
          <div className="mb-8">
            <div className="text-[#ff7b72] text-sm mb-3">// Contact Info</div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center">
                <Mail className="w-4 h-4 mr-2 text-[#58a6ff]" />
                <span className="text-[#8b949e] truncate">{personalInfo.email}</span>
              </div>
              <div className="flex items-center">
                <Phone className="w-4 h-4 mr-2 text-[#58a6ff]" />
                <span className="text-[#8b949e]">{personalInfo.phone}</span>
              </div>
              <div className="flex items-center">
                <MapPin className="w-4 h-4 mr-2 text-[#58a6ff]" />
                <span className="text-[#8b949e]">{personalInfo.location}</span>
              </div>
            </div>
          </div>

          {/* Tech Stack */}
          <div className="mb-8">
            <div className="text-[#ff7b72] text-sm mb-3">// Tech Stack</div>
            <div className="space-y-3">
              {techStack.map((tech, index) => (
                <div key={index}>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-[#ffa657] text-sm">{tech.name}</span>
                    <span className="text-[#8b949e] text-xs">{tech.level}%</span>
                  </div>
                  <div className="w-full bg-[#21262d] rounded h-1">
                    <div 
                      className="bg-[#58a6ff] h-1 rounded"
                      style={{ width: `${tech.level}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Skills */}
          <div className="mb-8">
            <div className="text-[#ff7b72] text-sm mb-3">// Core Skills</div>
            <div className="space-y-2">
              {skills.slice(0, 6).map((skill, index) => (
                <div key={index} className="flex items-center">
                  <Server className="w-3 h-3 mr-2 text-[#58a6ff]" />
                  <span className="text-[#8b949e] text-sm">{skill}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Education */}
          <div className="mb-8">
            <div className="text-[#ff7b72] text-sm mb-3">// Education</div>
            <div className="space-y-3">
              {education.slice(0, 2).map((edu, index) => (
                <div key={index} className="bg-[#0d1117] p-3 rounded border border-[#30363d]">
                  <div className="text-[#ffa657] text-sm font-medium">
                    {edu.degree}
                  </div>
                  <div className="text-[#8b949e] text-xs">
                    {edu.institution}
                  </div>
                  <div className="text-[#8b949e] text-xs">
                    {new Date(edu.startDate).getFullYear()}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Links */}
          <div>
            <div className="text-[#ff7b72] text-sm mb-3">// Links</div>
            <div className="space-y-2">
              {personalInfo.githubUrl && (
                <div className="flex items-center text-sm text-[#58a6ff]">
                  <Github className="w-4 h-4 mr-2" />
                  <span>GitHub</span>
                </div>
              )}
              {personalInfo.linkedinUrl && (
                <div className="flex items-center text-sm text-[#58a6ff]">
                  <Linkedin className="w-4 h-4 mr-2" />
                  <span>LinkedIn</span>
                </div>
              )}
              {personalInfo.website && (
                <div className="flex items-center text-sm text-[#58a6ff]">
                  <Globe className="w-4 h-4 mr-2" />
                  <span>Portfolio</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
