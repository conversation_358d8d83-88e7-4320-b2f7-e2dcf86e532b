import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github } from 'lucide-react';

interface CreativeDesignTemplateProps {
  resumeData: ResumeContent;
}

export default function CreativeDesignTemplate({ resumeData }: CreativeDesignTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  // Skill progress calculation (mock data for visual effect)
  const getSkillProgress = (index: number) => {
    const progressValues = [90, 85, 80, 75, 70, 85, 90, 80];
    return progressValues[index % progressValues.length];
  };

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="creative-design-6"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Creative Header with Decorative Elements */}
      <div className="absolute top-0 left-0 w-full h-[100px] bg-gradient-to-r from-[#4a90e2] to-[#357abd] overflow-hidden">
        {/* Decorative geometric shapes */}
        <div className="absolute top-2 right-12 w-8 h-8 bg-[#e74c3c] transform rotate-45"></div>
        <div className="absolute top-6 right-32 w-6 h-6 bg-white/30 rounded-full"></div>
        <div className="absolute bottom-2 left-16 w-10 h-10 bg-[#e74c3c]/70 transform rotate-12"></div>
        
        <div className="absolute bottom-4 left-8 text-white">
          <h1 className="text-2xl font-bold mb-1">
            {personalInfo.fullName || 'Your Name'}
          </h1>
          <p className="text-sm opacity-90">
            {personalInfo.jobTitle || 'Creative Professional'}
          </p>
        </div>
      </div>

      {/* Main Content - Two Column Layout */}
      <div className="absolute top-[120px] left-0 w-full h-[722px] flex">
        
        {/* Left Column - Main Content */}
        <div className="w-[370px] px-8 py-6">
          
          {/* Profile Photo */}
          <div className="flex justify-center mb-6">
            <div className="w-24 h-24 rounded-full bg-[#f5f5f5] border-4 border-[#4a90e2] overflow-hidden">
              {personalInfo.photo ? (
                <img 
                  src={personalInfo.photo} 
                  alt="Profile" 
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-[#4a90e2] to-[#357abd] flex items-center justify-center">
                  <span className="text-white text-xl font-bold">
                    {personalInfo.fullName?.charAt(0) || 'Y'}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Professional Summary */}
          {summary && (
            <div className="mb-6">
              <h2 className="text-lg font-bold text-[#333333] mb-3 flex items-center">
                <div className="w-4 h-4 bg-[#e74c3c] mr-2"></div>
                About Me
              </h2>
              <p className="text-sm text-[#333333] leading-relaxed">
                {summary}
              </p>
            </div>
          )}

          {/* Experience */}
          <div className="mb-6">
            <h2 className="text-lg font-bold text-[#333333] mb-4 flex items-center">
              <div className="w-4 h-4 bg-[#e74c3c] mr-2"></div>
              Experience
            </h2>
            <div className="space-y-4">
              {experience.slice(0, 3).map((exp, index) => (
                <div key={index} className="relative pl-6">
                  <div className="absolute left-0 top-1 w-3 h-3 bg-[#4a90e2] rounded-full"></div>
                  <div className="absolute left-1.5 top-4 w-0.5 h-16 bg-[#4a90e2]/30"></div>
                  
                  <div className="flex justify-between items-start mb-1">
                    <h3 className="font-semibold text-[#333333] text-sm">
                      {exp.position}
                    </h3>
                    <span className="text-xs text-[#333333]/70 bg-[#f5f5f5] px-2 py-1 rounded">
                      {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Now' : new Date(exp.endDate!).getFullYear()}
                    </span>
                  </div>
                  <p className="text-sm font-medium text-[#4a90e2] mb-2">
                    {exp.company}
                  </p>
                  <div className="text-xs text-[#333333]/70 space-y-1">
                    {exp.description.slice(0, 2).map((desc, i) => (
                      <p key={i}>• {desc}</p>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column - Contact & Skills */}
        <div className="w-[224px] bg-[#f5f5f5] px-6 py-6">
          
          {/* Contact Information */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#333333] mb-4 flex items-center">
              <div className="w-3 h-3 bg-[#e74c3c] mr-2"></div>
              Contact
            </h2>
            <div className="space-y-3">
              <div className="flex items-center text-xs text-[#333333]">
                <div className="w-6 h-6 bg-[#4a90e2] rounded-full flex items-center justify-center mr-2">
                  <Mail className="w-3 h-3 text-white" />
                </div>
                <span className="truncate">{personalInfo.email}</span>
              </div>
              <div className="flex items-center text-xs text-[#333333]">
                <div className="w-6 h-6 bg-[#4a90e2] rounded-full flex items-center justify-center mr-2">
                  <Phone className="w-3 h-3 text-white" />
                </div>
                <span>{personalInfo.phone}</span>
              </div>
              <div className="flex items-center text-xs text-[#333333]">
                <div className="w-6 h-6 bg-[#4a90e2] rounded-full flex items-center justify-center mr-2">
                  <MapPin className="w-3 h-3 text-white" />
                </div>
                <span>{personalInfo.location}</span>
              </div>
            </div>
          </div>

          {/* Skills with Progress Bars */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#333333] mb-4 flex items-center">
              <div className="w-3 h-3 bg-[#e74c3c] mr-2"></div>
              Skills
            </h2>
            <div className="space-y-3">
              {skills.slice(0, 6).map((skill, index) => {
                const progress = getSkillProgress(index);
                return (
                  <div key={index}>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-xs font-medium text-[#333333]">{skill}</span>
                      <span className="text-xs text-[#333333]/70">{progress}%</span>
                    </div>
                    <div className="w-full bg-white rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-[#4a90e2] to-[#e74c3c] h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progress}%` }}
                      ></div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Education */}
          <div>
            <h2 className="text-lg font-bold text-[#333333] mb-4 flex items-center">
              <div className="w-3 h-3 bg-[#e74c3c] mr-2"></div>
              Education
            </h2>
            <div className="space-y-3">
              {education.slice(0, 2).map((edu, index) => (
                <div key={index} className="bg-white p-3 rounded-lg">
                  <h3 className="font-semibold text-sm text-[#333333]">
                    {edu.degree}
                  </h3>
                  <p className="text-xs text-[#4a90e2] font-medium">
                    {edu.institution}
                  </p>
                  <p className="text-xs text-[#333333]/70">
                    {new Date(edu.startDate).getFullYear()}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
