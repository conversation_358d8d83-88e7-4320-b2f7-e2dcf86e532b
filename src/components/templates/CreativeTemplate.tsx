import { User, Briefcase, GraduationCap, Award, Globe } from 'lucide-react';

export function CreativeTemplate() {
  const resumeData = {
    personalInfo: {
      name: "<PERSON>",
      title: "Senior Administrative Specialist & Operations Coordinator",
      contact: {
        phone: "(*************",
        email: "<EMAIL>",
        address: "San Antonio, TX 78023"
      },
    },
    summary: "Accomplished administrative professional with 9+ years of progressive experience in office management, team coordination, and operational excellence. Proven track record of streamlining processes, managing cross-functional teams, and implementing cost-saving initiatives that resulted in over $50,000 in annual savings. Expert in Microsoft Office Suite, project management methodologies, and stakeholder communication. Seeking to leverage extensive administrative expertise and leadership capabilities to drive operational efficiency in a dynamic organizational environment.",
    skills: [
      "Advanced Project Management",
      "Team Leadership & Training", 
      "Process Optimization",
      "Stakeholder Communication",
      "Budget Management & Analysis",
      "Microsoft Office Suite Expert",
      "Database Management",
      "Event Planning & Coordination",
      "Conflict Resolution",
      "Vendor Relations Management",
      "Compliance & Risk Assessment",
      "Quality Assurance"
    ],
    education: [
      {
        degree: "Bachelor of Arts in Business Administration",
        university: "Brown University",
        location: "Providence, RI",
        dates: "09/2005 – 05/2009",
        details: "Magna Cum Laude, GPA: 3.8/4.0"
      },
      {
        degree: "Associate of Arts in Business Management",
        university: "San Antonio Community College",
        location: "San Antonio, TX",
        dates: "09/2003 – 05/2007",
        details: "Dean's List (6 semesters)"
      },
      {
        degree: "Professional Certificate in Project Management",
        university: "University of Texas at San Antonio",
        location: "San Antonio, TX",
        dates: "01/2018 – 06/2018",
        details: "PMI-aligned curriculum"
      }
    ],
    certifications: [
      { name: "Project Management Professional (PMP)", year: "2019", issuer: "PMI" },
      { name: "Certified Administrative Professional (CAP)", year: "2018", issuer: "IAAP" },
      { name: "Microsoft Office Specialist Expert", year: "2017", issuer: "Microsoft" },
      { name: "HIPAA Privacy & Security", year: "2020", issuer: "HHS" },
      { name: "Six Sigma Yellow Belt", year: "2019", issuer: "IASSC" },
      { name: "CPR/AED Certified", year: "2021", issuer: "American Red Cross" }
    ],
    languages: [
      { language: "English", level: "Native" },
      { language: "Spanish", level: "Professional Working" },
      { language: "French", level: "Conversational" }
    ],
    experience: [
      {
        title: "Senior Administrative Specialist",
        company: "Redford & Associates",
        location: "Boston, MA",
        dates: "Sep 2017 – Present",
        responsibilities: [
          "Lead administrative operations for 45+ person consulting firm, managing executive calendars, travel coordination, and high-priority project documentation",
          "Developed and implemented new client onboarding process, reducing processing time by 40% and improving client satisfaction scores by 25%",
          "Trained and supervised team of 4 administrative assistants, establishing standardized procedures and quality metrics",
          "Managed annual operating budget of $2.5M, identifying cost-saving opportunities that reduced expenses by $75,000 annually",
          "Coordinated major corporate events and conferences for 200+ attendees, managing all logistics, vendor relations, and on-site execution"
        ]
      },
      {
        title: "Executive Assistant & Office Manager",
        company: "Bright Spot Consulting",
        location: "Boston, MA",
        dates: "Jun 2016 – Aug 2017",
        responsibilities: [
          "Provided comprehensive executive support to C-suite leadership team of 3 executives in fast-paced consulting environment",
          "Implemented digital filing system and document management protocols, improving information retrieval efficiency by 60%",
          "Managed complex travel arrangements for international business trips, negotiating corporate rates with vendors",
          "Prepared detailed financial reports and expense analysis for quarterly board meetings and investor presentations",
          "Coordinated cross-departmental communications and served as primary liaison between executive team and department heads"
        ]
      },
      {
        title: "Administrative Coordinator",
        company: "Winfield & Partners",
        location: "Boston, MA",
        dates: "Jun 2013 – May 2016",
        responsibilities: [
          "Streamlined office operations and administrative processes for 25-person professional services firm",
          "Developed comprehensive onboarding program for new employees, reducing time-to-productivity by 30%",
          "Managed vendor relationships and office supply procurement, implementing cost-control measures that saved $15,000 annually",
          "Created and maintained client database system, improving data accuracy and accessibility for business development team",
          "Organized successful company retreat and quarterly team-building events, boosting employee engagement scores by 20%"
        ]
      },
      {
        title: "Administrative Assistant",
        company: "Sterling Professional Services",
        location: "Boston, MA",
        dates: "Aug 2011 – May 2013",
        responsibilities: [
          "Provided administrative support to senior management team in busy professional services environment",
          "Managed multi-line phone system and directed calls to appropriate personnel, maintaining professional client communication",
          "Processed invoices, expense reports, and maintained accurate filing systems for client documentation",
          "Coordinated scheduling for 15+ staff members, optimizing calendar management and reducing scheduling conflicts by 35%",
          "Assisted with special projects including data entry, document preparation, and client correspondence"
        ]
      }
    ],
    projects: [
      {
        title: "Digital Transformation Initiative",
        organization: "Redford & Associates",
        dates: "Jan 2020 – Jun 2020",
        description: "Led company-wide transition to cloud-based document management and collaboration platform, training 45+ employees and establishing new workflows."
      },
      {
        title: "Process Standardization Project",
        organization: "Bright Spot Consulting",
        dates: "Mar 2017 – Jul 2017",
        description: "Designed and implemented standardized administrative procedures across all departments, creating comprehensive documentation and training materials."
      }
    ],
    achievements: [
      {
        title: "Employee of the Year",
        organization: "Redford & Associates",
        year: "2019",
        description: "Recognized for outstanding performance and leadership in process improvement initiatives"
      },
      {
        title: "Innovation Award",
        organization: "Bright Spot Consulting", 
        year: "2017",
        description: "Awarded for developing automated reporting system that improved efficiency by 45%"
      },
      {
        title: "Leadership Excellence Award",
        organization: "Boston Business Women's Association",
        year: "2020", 
        description: "Honored for mentorship and professional development contributions to local business community"
      }
    ]
  };

  const { personalInfo, summary, skills, education, certifications, languages, experience, projects, achievements } = resumeData;

  return (
    <div className="bg-white mx-auto w-[900px] print:w-[794px] shadow-[0_4px_16px_rgba(0,0,0,0.08)] border border-gray-200">
      {/* Header */}
      <div className="px-8 pt-8 pb-4">
        <h1 className="text-4xl font-bold text-gray-800 mb-1">{personalInfo.name}</h1>
        <h2 className="text-xl text-teal-600 font-medium">{personalInfo.title}</h2>
      </div>

      {/* Contact Bar */}
      <div className="bg-teal-600 text-white px-8 py-3 flex justify-between items-center text-sm">
        <div>
          <span className="font-semibold">PHONE</span> {personalInfo.contact.phone}
        </div>
        <div>
          <span className="font-semibold">EMAIL</span> {personalInfo.contact.email}
        </div>
        <div>
          <span className="font-semibold">ADDRESS</span> {personalInfo.contact.address}
        </div>
      </div>

      {/* Main Content - Two Columns */}
      <div className="flex px-8 py-6 gap-8">
        {/* Left Column */}
        <div className="w-1/2 space-y-4">
          {/* Professional Summary */}
          <div>
            <div className="flex items-center mb-2">
              <User className="text-teal-600 mr-2" size={18} />
              <h3 className="font-bold text-gray-700 tracking-wide">PROFESSIONAL SUMMARY</h3>
            </div>
            <p className="text-xs text-gray-700 leading-tight text-justify">{summary}</p>
          </div>

          {/* Skills */}
          <div>
            <div className="flex items-center mb-2">
              <Award className="text-teal-600 mr-2" size={18} />
              <h3 className="font-bold text-gray-700 tracking-wide">CORE COMPETENCIES</h3>
            </div>
            <div className="grid grid-cols-1 gap-1">
              {skills.slice(0, 8).map((skill, index) => (
                <div key={index} className="flex items-center text-xs text-gray-700">
                  <span className="w-1.5 h-1.5 bg-teal-600 rounded-full mr-2 flex-shrink-0"></span>
                  {skill}
                </div>
              ))}
            </div>
          </div>

          {/* Education */}
          <div>
            <div className="flex items-center mb-2">
              <GraduationCap className="text-teal-600 mr-2" size={18} />
              <h3 className="font-bold text-gray-700 tracking-wide">EDUCATION</h3>
            </div>
            
            {education.slice(0, 2).map((edu, index) => (
              <div key={index} className="mb-3">
                <h4 className="font-semibold text-gray-800 text-xs leading-tight">{edu.degree}</h4>
                <p className="text-xs text-gray-600 font-medium">{edu.university}</p>
                <div className="flex justify-between text-xs text-gray-500">
                  <span>{edu.dates}</span>
                  <span>{edu.location}</span>
                </div>
                {edu.details && (
                  <p className="text-xs text-teal-600 font-medium">{edu.details}</p>
                )}
              </div>
            ))}
          </div>

          {/* Certifications */}
          <div>
            <div className="flex items-center mb-2">
              <Award className="text-teal-600 mr-2" size={18} />
              <h3 className="font-bold text-gray-700 tracking-wide">CERTIFICATIONS</h3>
            </div>
            <div className="space-y-1">
              {certifications.slice(0, 4).map((cert, index) => (
                <div key={index} className="border-l-2 border-teal-200 pl-2">
                  <div className="font-medium text-gray-800 text-xs leading-tight">{cert.name}</div>
                  <div className="text-xs text-gray-600">
                    {cert.issuer} • {cert.year}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Languages */}
          <div>
            <div className="flex items-center mb-2">
              <Globe className="text-teal-600 mr-2" size={18} />
              <h3 className="font-bold text-gray-700 tracking-wide">LANGUAGES</h3>
            </div>
            <div className="space-y-1">
              {languages.map((lang, index) => (
                <div key={index} className="flex justify-between items-center text-xs">
                  <span className="text-gray-800 font-medium">{lang.language}</span>
                  <span className="text-teal-600 font-medium">{lang.level}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Awards & Recognition */}
          <div>
            <div className="flex items-center mb-2">
              <Award className="text-teal-600 mr-2" size={18} />
              <h3 className="font-bold text-gray-700 tracking-wide">AWARDS & RECOGNITION</h3>
            </div>
            <div className="space-y-2">
              {achievements.slice(0, 2).map((achievement, index) => (
                <div key={index} className="border-l-2 border-teal-200 pl-2">
                  <div className="flex justify-between items-start mb-1">
                    <h4 className="font-medium text-gray-800 text-xs leading-tight">{achievement.title}</h4>
                    <span className="text-xs text-teal-600 font-medium">{achievement.year}</span>
                  </div>
                  <p className="text-xs text-gray-600 font-medium">{achievement.organization}</p>
                  <p className="text-xs text-gray-700 leading-tight">{achievement.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Key Projects */}
          <div>
            <div className="flex items-center mb-2">
              <Award className="text-teal-600 mr-2" size={18} />
              <h3 className="font-bold text-gray-700 tracking-wide">KEY PROJECTS</h3>
            </div>
            <div className="space-y-2">
              {projects.map((project, index) => (
                <div key={index} className="bg-gray-50 p-2 rounded">
                  <div className="flex justify-between items-start mb-1">
                    <h4 className="font-medium text-gray-800 text-xs leading-tight">{project.title}</h4>
                    <span className="text-xs text-gray-500 font-medium">{project.dates}</span>
                  </div>
                  <p className="text-xs text-gray-600 mb-1 font-medium">{project.organization}</p>
                  <p className="text-xs text-gray-700 leading-tight">{project.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column - Experience, Projects & Achievements */}
        <div className="w-1/2 space-y-6">
          {/* Experience Section */}
          <div>
            <div className="flex items-center mb-4">
              <Briefcase className="text-teal-600 mr-2" size={20} />
              <h3 className="font-bold text-gray-700 tracking-wide">PROFESSIONAL EXPERIENCE</h3>
            </div>
            
            {experience.map((job, index) => (
              <div key={index} className="mb-6">
                <div className="flex justify-between items-start mb-2">
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-800 text-sm">{job.title}</h4>
                    <p className="text-sm text-gray-600 font-medium">{job.company}</p>
                  </div>
                  <div className="text-right text-xs text-gray-500 ml-4">
                    <div className="font-medium">{job.dates}</div>
                    <div>{job.location}</div>
                  </div>
                </div>
                <ul className="text-xs text-gray-700 space-y-1">
                  {job.responsibilities.map((resp, i) => (
                    <li key={i} className="flex items-start">
                      <span className="w-1 h-1 bg-teal-600 rounded-full mr-2 mt-2 flex-shrink-0"></span>
                      <span>{resp}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>




        </div>
      </div>
    </div>
  );
}