import React from 'react';
import { ResumeData } from '@/lib/templates/template-registry';

interface TwoColumnSidebarTemplateProps {
  data: ResumeData;
}

export default function TwoColumnSidebarTemplate({ data }: TwoColumnSidebarTemplateProps) {
  return (
    <div className="bg-white relative size-full" data-name="Resume" data-node-id="132:2056">
      <div className="absolute bg-[#e4f2ef] h-[710px] top-[132px] translate-x-[-50%] w-[169px]" style={{ left: "calc(50% - 195px)" }} />
      
      {/* Header Section */}
      <div className="absolute font-['Inter:Extra_Bold',_sans-serif] font-extrabold leading-[0] left-[153px] not-italic text-[#333333] text-[29.793px] text-nowrap top-7 tracking-[1.7876px]">
        <p className="leading-[normal] whitespace-pre">{data.personalInfo.fullName}</p>
      </div>
      
      {/* Contact Information */}
      <div className="absolute font-['Inter:Regular',_sans-serif] font-normal leading-[normal] left-[153px] not-italic text-[#333333] text-[10px] top-[75px] tracking-[0.6px] whitespace-pre">
        <p className="mb-0">{data.personalInfo.email}</p>
        <p className="mb-0">{data.personalInfo.phone}</p>
        <p className="mb-0">{data.personalInfo.location}</p>
        <p className="">{data.personalInfo.website}</p>
      </div>

      {/* Left Sidebar */}
      <div className="absolute h-[710px] left-[32.8px] top-[132px] w-[136.2px]">
        {/* Profile Section */}
        <div className="absolute content-stretch flex flex-col gap-2.5 items-start justify-start left-0 top-[120px]">
          <div className="font-['Playfair_Display:Regular',_sans-serif] font-normal leading-[0] text-[#f2f1ed] text-[13px] text-nowrap tracking-[1.3px] uppercase">
            <p className="leading-[14px] whitespace-pre">Profile</p>
          </div>
          <div className="bg-[#dfae4f] h-px w-[23px]" />
          <div className="font-['Mulish:Regular',_sans-serif] font-normal leading-[1.4] text-[#f2f1ed] text-[10px] tracking-[-0.1px] w-[154px]">
            <p>{data.summary}</p>
          </div>
        </div>

        {/* Contact Section */}
        <div className="absolute content-stretch flex flex-col gap-1.5 items-start justify-start left-0 top-[252px]">
          <div className="content-stretch flex gap-1.5 items-center justify-start">
            <div className="bg-[#dfae4f] h-[1.357px] w-[22.62px]" />
            <div className="font-['Playfair_Display:Regular',_sans-serif] font-normal leading-[0] text-[#f2f1ed] text-[13px] text-nowrap tracking-[1.3px] uppercase">
              <p className="leading-[14px] whitespace-pre">Contact</p>
            </div>
          </div>
          <div className="font-['Mulish:Regular',_sans-serif] font-normal leading-[0] text-[#f2f1ed] text-[10px] tracking-[-0.1px]">
            <p className="leading-[10px] mb-2">{data.personalInfo.phone}</p>
            <p className="leading-[10px]">{data.personalInfo.email}</p>
          </div>
        </div>

        {/* Skills Section */}
        <div className="absolute content-stretch flex flex-col gap-2.5 items-start justify-start left-0 top-[383.62px]">
          <div className="grid-cols-[max-content] grid-rows-[max-content] inline-grid leading-[0] place-items-start">
            <div className="font-['Playfair_Display:Regular',_sans-serif] font-normal text-[#f2f1ed] text-[13px] text-nowrap tracking-[1.3px] uppercase">
              <p className="leading-[14px] whitespace-pre">Skills</p>
            </div>
            <div className="bg-[#dfae4f] h-px w-[23px]" />
          </div>
          <div className="font-['Mulish:Regular',_sans-serif] font-normal leading-[1.4] text-[#f2f1ed] text-[10px] tracking-[-0.1px]">
            {data.skills.map((skill, index) => (
              <p key={index} className="leading-[12px] mb-1">{skill.name}</p>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="absolute left-[240px] top-[150px] w-[320px]">
        {/* Experience Section */}
        <div className="mb-8">
          <div className="font-['Mulish:Regular',_sans-serif] font-normal leading-[0] text-[#dfae4f] text-[11px] text-nowrap tracking-[1.1px] uppercase mb-4">
            <p className="leading-[15px] whitespace-pre">Experience</p>
          </div>
          {data.experience.map((exp, index) => (
            <div key={index} className="mb-6">
              <div className="content-stretch flex gap-[13px] items-start justify-start mb-2">
                <div className="font-['Mulish:Regular',_sans-serif] font-normal leading-[0] text-[#353743] text-[9px] text-nowrap tracking-[0.9px] uppercase">
                  <p className="leading-[12px] whitespace-pre">{exp.position}</p>
                </div>
                <div className="font-['Mulish:Regular',_sans-serif] font-normal leading-[0] text-[9px] text-[rgba(53,55,67,0.5)] text-nowrap tracking-[0.9px] uppercase">
                  <p className="leading-[12px] whitespace-pre">{exp.startDate} - {exp.endDate}</p>
                </div>
              </div>
              <div className="font-['Mulish:Regular',_sans-serif] font-normal leading-[0] text-[#666d79] text-[9px] tracking-[-0.09px]">
                <p className="leading-[15px]">{exp.description}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Education Section */}
        <div className="mb-8">
          <div className="font-['Mulish:Regular',_sans-serif] font-normal leading-[0] text-[#dfae4f] text-[11px] text-nowrap tracking-[1.1px] uppercase mb-4">
            <p className="leading-[15px] whitespace-pre">Education</p>
          </div>
          {data.education.map((edu, index) => (
            <div key={index} className="mb-4">
              <div className="font-['Mulish:Regular',_sans-serif] font-normal leading-[0] text-[#353743] text-[9px] text-nowrap tracking-[0.9px] uppercase">
                <p className="leading-[12px] whitespace-pre">{edu.degree}</p>
              </div>
              <div className="font-['Mulish:Regular',_sans-serif] font-normal leading-[0] text-[#666d79] text-[9px] tracking-[-0.09px]">
                <p className="leading-[15px]">{edu.institution} - {edu.graduationDate}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
