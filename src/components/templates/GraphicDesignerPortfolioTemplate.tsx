import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, <PERSON>lette, <PERSON>, Star } from 'lucide-react';

interface GraphicDesignerPortfolioTemplateProps {
  resumeData: ResumeContent;
}

export default function GraphicDesignerPortfolioTemplate({ resumeData }: GraphicDesignerPortfolioTemplateProps) {
  const { personalInfo, summary, experience, education, skills, projects } = resumeData;

  // Mock portfolio projects if none provided
  const portfolioProjects = projects && projects.length > 0 ? projects : [
    { id: '1', name: 'Brand Identity Design', description: 'Complete brand package for tech startup' },
    { id: '2', name: 'Website Redesign', description: 'E-commerce platform visual overhaul' },
    { id: '3', name: 'Print Campaign', description: 'Marketing materials for product launch' }
  ];

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="anne-harris-graphic"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Purple Header */}
      <div className="absolute top-0 left-0 w-full h-[140px] bg-gradient-to-r from-[#7c3aed] to-[#a78bfa] overflow-hidden">
        {/* Creative decorative elements */}
        <div className="absolute top-4 right-8 w-16 h-16 bg-white/10 transform rotate-12"></div>
        <div className="absolute top-8 right-32 w-8 h-8 bg-white/20 rounded-full"></div>
        <div className="absolute bottom-4 left-12 w-12 h-12 bg-white/15 transform -rotate-12"></div>
        
        <div className="flex items-center h-full px-8">
          <div className="flex-1">
            <h1 className="text-4xl font-bold text-white mb-2">
              {personalInfo.fullName || 'Anne Harris'}
            </h1>
            <p className="text-xl text-white/90 flex items-center">
              <Palette className="w-6 h-6 mr-2" />
              {personalInfo.jobTitle || 'Graphic Designer'}
            </p>
          </div>
          
          {/* Contact Icons */}
          <div className="flex gap-3">
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <Mail className="w-5 h-5 text-white" />
            </div>
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <Phone className="w-5 h-5 text-white" />
            </div>
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <Globe className="w-5 h-5 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Contact Info Bar */}
      <div className="absolute top-[140px] left-0 w-full h-[30px] bg-[#6d28d9] px-8 flex items-center justify-center">
        <div className="flex gap-8 text-white text-sm">
          <span>{personalInfo.email}</span>
          <span>{personalInfo.phone}</span>
          <span>{personalInfo.location}</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[190px] left-0 w-full h-[652px] flex">
        
        {/* Left Column */}
        <div className="w-[370px] px-8 py-6">
          
          {/* About */}
          {summary && (
            <div className="mb-8">
              <h2 className="text-xl font-bold text-[#333] mb-4 flex items-center">
                <Eye className="w-5 h-5 mr-2 text-[#7c3aed]" />
                About Me
              </h2>
              <p className="text-sm text-[#333] leading-relaxed">
                {summary}
              </p>
            </div>
          )}

          {/* Experience */}
          <div className="mb-8">
            <h2 className="text-xl font-bold text-[#333] mb-4 border-b-2 border-[#7c3aed] pb-2">
              Professional Experience
            </h2>
            <div className="space-y-6">
              {experience.slice(0, 3).map((exp, index) => (
                <div key={index}>
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-bold text-[#333] text-base">
                      {exp.position}
                    </h3>
                    <span className="text-sm text-white bg-[#7c3aed] px-3 py-1 rounded-full">
                      {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                    </span>
                  </div>
                  <p className="text-sm font-semibold text-[#a78bfa] mb-2">
                    {exp.company} • {exp.location}
                  </p>
                  <div className="text-sm text-[#333] space-y-1">
                    {exp.description.slice(0, 3).map((desc, i) => (
                      <p key={i}>• {desc}</p>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Portfolio Projects */}
          <div>
            <h2 className="text-xl font-bold text-[#333] mb-4 border-b-2 border-[#7c3aed] pb-2">
              Featured Projects
            </h2>
            <div className="space-y-4">
              {portfolioProjects.slice(0, 3).map((project, index) => (
                <div key={index} className="bg-gradient-to-r from-[#f3f4f6] to-white p-4 rounded-lg border-l-4 border-[#7c3aed]">
                  <h3 className="font-bold text-[#333] text-sm flex items-center">
                    <Star className="w-4 h-4 mr-2 text-[#7c3aed]" />
                    {project.name}
                  </h3>
                  <p className="text-sm text-[#333]/80 mt-1">
                    {project.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="w-[224px] bg-[#f8f9fa] px-6 py-6">
          
          {/* Skills with Green Progress Bars */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#333] mb-4">
              Design Skills
            </h2>
            <div className="space-y-3">
              {skills.slice(0, 6).map((skill, index) => {
                const progress = [95, 90, 85, 88, 82, 87][index] || 80;
                return (
                  <div key={index}>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-[#333]">{skill}</span>
                      <span className="text-xs text-[#333]/70">{progress}%</span>
                    </div>
                    <div className="w-full bg-white rounded-full h-2">
                      <div 
                        className="bg-[#10b981] h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progress}%` }}
                      ></div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Software Proficiency */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#333] mb-4">
              Software
            </h2>
            <div className="grid grid-cols-2 gap-2">
              {[
                'Photoshop',
                'Illustrator',
                'InDesign',
                'Figma',
                'Sketch',
                'After Effects'
              ].map((software, index) => (
                <div key={index} className="bg-white p-2 rounded text-center border border-[#7c3aed]/20">
                  <span className="text-xs font-medium text-[#333]">{software}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Education */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#333] mb-4">
              Education
            </h2>
            <div className="space-y-3">
              {education.slice(0, 2).map((edu, index) => (
                <div key={index} className="bg-white p-3 rounded-lg shadow-sm">
                  <h3 className="font-semibold text-sm text-[#333]">
                    {edu.degree}
                  </h3>
                  <p className="text-xs text-[#7c3aed] font-medium">
                    {edu.institution}
                  </p>
                  <p className="text-xs text-[#333]/70">
                    {new Date(edu.startDate).getFullYear()}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Links */}
          <div>
            <h2 className="text-lg font-bold text-[#333] mb-4">
              Portfolio Links
            </h2>
            <div className="space-y-2">
              {personalInfo.website && (
                <div className="flex items-center text-sm text-[#7c3aed]">
                  <Globe className="w-4 h-4 mr-2" />
                  <span>Portfolio Website</span>
                </div>
              )}
              {personalInfo.linkedinUrl && (
                <div className="flex items-center text-sm text-[#7c3aed]">
                  <Linkedin className="w-4 h-4 mr-2" />
                  <span>LinkedIn</span>
                </div>
              )}
              <div className="flex items-center text-sm text-[#7c3aed]">
                <Palette className="w-4 h-4 mr-2" />
                <span>Behance</span>
              </div>
              <div className="flex items-center text-sm text-[#7c3aed]">
                <Eye className="w-4 h-4 mr-2" />
                <span>Dribbble</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
