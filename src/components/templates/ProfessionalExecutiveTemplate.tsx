import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, User, Briefcase } from 'lucide-react';

interface ProfessionalExecutiveTemplateProps {
  resumeData: ResumeContent;
}

export default function ProfessionalExecutiveTemplate({ resumeData }: ProfessionalExecutiveTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="professional-template-21"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Header */}
      <div className="absolute top-0 left-0 w-full h-[120px] bg-[#2563eb] px-8 py-6">
        <div className="flex items-center h-full">
          {/* Profile Photo */}
          <div className="w-20 h-20 rounded-full bg-white border-3 border-[#f1f5f9] overflow-hidden mr-6 shadow-lg">
            {personalInfo.photo ? (
              <img 
                src={personalInfo.photo} 
                alt="Profile" 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-[#64748b] to-[#475569] flex items-center justify-center">
                <User className="w-8 h-8 text-white" />
              </div>
            )}
          </div>
          
          {/* Name and Title */}
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-white mb-2">
              {personalInfo.fullName || 'Your Name'}
            </h1>
            <p className="text-lg text-white/90 flex items-center">
              <Briefcase className="w-5 h-5 mr-2" />
              {personalInfo.jobTitle || 'Professional Executive'}
            </p>
          </div>
          
          {/* Contact Info */}
          <div className="text-right text-white text-sm space-y-1">
            <div className="flex items-center justify-end">
              <Mail className="w-4 h-4 mr-2" />
              <span>{personalInfo.email}</span>
            </div>
            <div className="flex items-center justify-end">
              <Phone className="w-4 h-4 mr-2" />
              <span>{personalInfo.phone}</span>
            </div>
            <div className="flex items-center justify-end">
              <MapPin className="w-4 h-4 mr-2" />
              <span>{personalInfo.location}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[140px] left-0 w-full h-[702px] flex">
        
        {/* Left Column - Main Content */}
        <div className="w-[370px] px-8 py-6">
          
          {/* Professional Summary */}
          {summary && (
            <div className="mb-8">
              <h2 className="text-xl font-bold text-[#64748b] mb-4 border-b-2 border-[#2563eb] pb-2">
                Professional Summary
              </h2>
              <p className="text-sm text-[#64748b] leading-relaxed">
                {summary}
              </p>
            </div>
          )}

          {/* Professional Experience */}
          <div className="mb-8">
            <h2 className="text-xl font-bold text-[#64748b] mb-4 border-b-2 border-[#2563eb] pb-2">
              Professional Experience
            </h2>
            <div className="space-y-6">
              {experience.slice(0, 4).map((exp, index) => (
                <div key={index}>
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-bold text-[#64748b] text-base">
                      {exp.position}
                    </h3>
                    <span className="text-sm text-white bg-[#2563eb] px-3 py-1 rounded-full">
                      {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                    </span>
                  </div>
                  <p className="text-sm font-semibold text-[#2563eb] mb-2">
                    {exp.company} • {exp.location}
                  </p>
                  <div className="text-sm text-[#64748b] space-y-1">
                    {exp.description.slice(0, 3).map((desc, i) => (
                      <p key={i}>• {desc}</p>
                    ))}
                  </div>
                  {exp.skills && exp.skills.length > 0 && (
                    <div className="mt-3">
                      <div className="flex flex-wrap gap-2">
                        {exp.skills.slice(0, 4).map((skill, i) => (
                          <span key={i} className="text-xs bg-[#f1f5f9] text-[#64748b] px-2 py-1 rounded border border-[#2563eb]/20">
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column - Skills & Education */}
        <div className="w-[224px] bg-[#f1f5f9] px-6 py-6">
          
          {/* Core Skills */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#64748b] mb-4">
              Core Skills
            </h2>
            <div className="space-y-3">
              {skills.slice(0, 8).map((skill, index) => {
                const progress = [95, 90, 85, 88, 82, 87, 80, 85][index] || 80;
                return (
                  <div key={index}>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-[#64748b]">{skill}</span>
                      <span className="text-xs text-[#64748b]/70">{progress}%</span>
                    </div>
                    <div className="w-full bg-white rounded-full h-2">
                      <div 
                        className="bg-[#2563eb] h-2 rounded-full"
                        style={{ width: `${progress}%` }}
                      ></div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Education */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#64748b] mb-4">
              Education
            </h2>
            <div className="space-y-4">
              {education.slice(0, 3).map((edu, index) => (
                <div key={index} className="bg-white p-3 rounded-lg shadow-sm">
                  <h3 className="font-bold text-sm text-[#64748b]">
                    {edu.degree}
                  </h3>
                  <p className="text-xs text-[#2563eb] font-medium">
                    {edu.institution}
                  </p>
                  <p className="text-xs text-[#64748b]/70">
                    {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                  </p>
                  {edu.gpa && (
                    <p className="text-xs text-[#64748b]/70">
                      GPA: {edu.gpa}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Contact Links */}
          <div>
            <h2 className="text-lg font-bold text-[#64748b] mb-4">
              Professional Links
            </h2>
            <div className="space-y-3">
              {personalInfo.website && (
                <div className="flex items-center text-sm text-[#2563eb]">
                  <Globe className="w-4 h-4 mr-2" />
                  <span>Portfolio</span>
                </div>
              )}
              {personalInfo.linkedinUrl && (
                <div className="flex items-center text-sm text-[#2563eb]">
                  <Linkedin className="w-4 h-4 mr-2" />
                  <span>LinkedIn</span>
                </div>
              )}
              {personalInfo.githubUrl && (
                <div className="flex items-center text-sm text-[#2563eb]">
                  <Github className="w-4 h-4 mr-2" />
                  <span>GitHub</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
