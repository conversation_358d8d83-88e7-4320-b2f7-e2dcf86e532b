import React from 'react';

const imgLine1 = "http://localhost:3845/assets/793c2b4c03c960d91afd870af58812048ddbe071.svg";
const imgFrame = "http://localhost:3845/assets/103b3edf3de9741fea3c4b9048c3b0c768f61114.svg";
const imgFrame1 = "http://localhost:3845/assets/cb71357504dbc97b73c197c8398091ffccdd4676.svg";
const imgStreamlineLinkedin = "http://localhost:3845/assets/ed85a2541fa83146a7fe80a3a38379ea388cfbf5.svg";
const imgMeteorIconsGithub = "http://localhost:3845/assets/bc06fa3e28b58c4441cfaa9a85193cc7a771b4ca.svg";
const imgBxsCalendar = "http://localhost:3845/assets/dc0b846d709ed0b0cfb869761dd2506a421c18cf.svg";
const imgTdesignEducationFilled = "http://localhost:3845/assets/da29f6e22ccb98d4b14d69370ca2c00784ecc226.svg";

interface ResumeData {
  personalInfo: {
    name: string;
    jobTitle: string;
    email: string;
    phone: string;
    linkedin?: string;
    github?: string;
  };
  summary: string;
  experience: Array<{
    jobTitle: string;
    company: string;
    dates: string;
    description: string;
  }>;
  projects: Array<{
    name: string;
    description: string;
  }>;
  skills: string[];
  education: Array<{
    degree: string;
    institution: string;
    dates: string;
  }>;
}

interface CleanProfessionalTemplateProps {
  data: ResumeData;
  isPreview?: boolean;
}

export default function CleanProfessionalTemplate({ data, isPreview = false }: CleanProfessionalTemplateProps) {
  return (
    <div className="bg-[#f8f9fa] relative size-full" data-template-id="clean-professional-3">
      {/* Header Line */}
      <div className="absolute h-0 left-[27px] top-[111px] w-[530px]">
        <div className="absolute bottom-[-0.5px] left-0 right-0 top-[-0.5px]">
          <img alt className="block max-w-none size-full" src={imgLine1} />
        </div>
      </div>

      {/* Header */}
      <div className="absolute box-border content-stretch flex gap-[103px] items-center justify-start left-1/2 pl-10 pr-0 py-0 top-4 translate-x-[-50%] w-[595px]">
        {/* Name and Title */}
        <div className="content-stretch flex flex-col gap-2.5 items-start justify-start leading-[0] not-italic relative shrink-0 w-[222px]">
          <div className="font-['Inter:Bold',_sans-serif] font-bold relative shrink-0 text-[24px] text-[rgba(0,0,0,0.85)] w-full">
            <p className="leading-[22px]">{data.personalInfo.name}</p>
          </div>
          <div className="font-['Inter:Semi_Bold',_sans-serif] font-semibold relative shrink-0 text-[#473bba] text-[18px] w-full">
            <p className="leading-[22px]">{data.personalInfo.jobTitle}</p>
          </div>
        </div>

        {/* Contact Info */}
        <div className="content-stretch flex flex-col gap-2 items-start justify-start relative shrink-0 w-[197px]">
          <div className="content-stretch flex gap-1 items-center justify-start relative shrink-0 w-full">
            <div className="relative shrink-0 size-4">
              <img alt className="block max-w-none size-full" src={imgFrame} />
            </div>
            <div className="font-['Inter:Medium',_sans-serif] font-medium leading-[0] not-italic relative shrink-0 text-[12px] text-[rgba(0,0,0,0.85)] text-nowrap">
              <p className="leading-[normal] whitespace-pre">{data.personalInfo.email}</p>
            </div>
          </div>
          <div className="content-stretch flex gap-1 items-center justify-start relative shrink-0">
            <div className="relative shrink-0 size-4">
              <img alt className="block max-w-none size-full" src={imgFrame1} />
            </div>
            <div className="font-['Inter:Medium',_sans-serif] font-medium leading-[0] not-italic relative shrink-0 text-[#252525] text-[12px] text-nowrap">
              <p className="leading-[normal] whitespace-pre">{data.personalInfo.phone}</p>
            </div>
          </div>
          {data.personalInfo.linkedin && (
            <div className="content-stretch flex gap-1 h-[15px] items-end justify-start relative shrink-0 w-[69px]">
              <div className="content-stretch flex gap-1 items-end justify-start relative shrink-0">
                <div className="relative shrink-0 size-3.5">
                  <img alt className="block max-w-none size-full" src={imgStreamlineLinkedin} />
                </div>
              </div>
              <div className="grid-cols-[max-content] grid-rows-[max-content] inline-grid leading-[0] place-items-start relative shrink-0">
                <div className="[grid-area:1_/_1] font-['Inter:Medium',_sans-serif] font-medium ml-0 mt-0 not-italic relative text-[#252525] text-[12px] text-nowrap">
                  <p className="leading-[normal] whitespace-pre">LinkedIn</p>
                </div>
              </div>
            </div>
          )}
          {data.personalInfo.github && (
            <div className="content-stretch flex gap-1 h-[15px] items-end justify-start relative shrink-0 w-[60px]">
              <div className="relative shrink-0 size-3.5">
                <img alt className="block max-w-none size-full" src={imgMeteorIconsGithub} />
              </div>
              <div className="grid-cols-[max-content] grid-rows-[max-content] inline-grid leading-[0] place-items-start relative shrink-0">
                <div className="[grid-area:1_/_1] font-['Inter:Medium',_sans-serif] font-medium ml-0 mt-0 not-italic relative text-[#252525] text-[12px] text-nowrap">
                  <p className="leading-[normal] whitespace-pre">GitHub</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Summary */}
      <div className="absolute font-['Inter:Regular',_sans-serif] font-normal leading-[0] left-[14px] not-italic text-[14px] text-[#252525] top-[120px] tracking-[-0.25px] w-[567px]">
        <p className="leading-[18px]">{data.summary}</p>
      </div>

      {/* Main Content */}
      <div className="absolute left-0 top-[165px] w-[595px] h-[687px]">
        {/* Right Column - Experience and Projects */}
        <div className="absolute left-[213px] top-[18px] w-[368px] h-[588px]">
          {/* Experience Section */}
          <div className="content-stretch flex flex-col gap-[46px] items-start justify-start relative shrink-0 w-[368px] h-[394px]">
            <div className="content-stretch flex flex-col gap-[26px] items-start justify-start relative shrink-0 w-[269px]">
              <div className="font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic relative shrink-0 text-[18px] text-[#252525] tracking-[-0.25px] w-full">
                <p className="leading-[normal]">Experience</p>
              </div>
            </div>
            
            <div className="content-stretch flex flex-col gap-[177px] items-start justify-start relative shrink-0 w-[368px]">
              {data.experience.map((exp, index) => (
                <div key={index} className="content-stretch flex flex-col gap-[41px] items-start justify-start relative shrink-0 w-[368px]">
                  <div className="content-stretch flex gap-4 items-start justify-start relative shrink-0 w-[354px]">
                    <div className="box-border content-stretch flex gap-2.5 items-center justify-start pb-0 pt-1 px-0 relative shrink-0">
                      <div className="bg-[#473bba] relative rounded-full shrink-0 size-2.5" />
                    </div>
                    <div className="basis-0 content-stretch flex flex-col gap-[18px] grow items-start justify-start min-h-px min-w-px relative shrink-0">
                      <div className="font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic relative shrink-0 text-[15px] text-[#252525] tracking-[-0.25px] w-full">
                        <p className="leading-[normal]">{exp.jobTitle}</p>
                      </div>
                      <div className="content-stretch flex gap-[17px] items-center justify-start relative shrink-0 w-full">
                        <div className="font-['Inter:Medium',_sans-serif] font-medium leading-[0] not-italic relative shrink-0 text-[14px] text-[#252525] tracking-[-0.25px]">
                          <p className="leading-[normal]">{exp.company}</p>
                        </div>
                        <div className="content-stretch flex gap-0.5 items-center justify-start relative shrink-0">
                          <div className="relative shrink-0 size-3">
                            <img alt className="block max-w-none size-full" src={imgBxsCalendar} />
                          </div>
                          <div className="font-['Inter:Regular',_sans-serif] font-normal leading-[0] not-italic relative shrink-0 text-[13px] text-[rgba(37,37,37,0.8)] text-nowrap tracking-[-0.25px]">
                            <p className="leading-[normal] whitespace-pre">{exp.dates}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="font-['Inter:Regular',_sans-serif] font-normal leading-[0] not-italic relative shrink-0 text-[13px] text-[rgba(37,37,37,0.8)] tracking-[-0.25px] w-[368px]">
                    <p className="leading-[15px]">{exp.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Projects Section */}
          {data.projects && data.projects.length > 0 && (
            <div className="absolute top-[406px] content-stretch flex flex-col gap-[46px] items-start justify-start relative shrink-0 w-[368px]">
              <div className="content-stretch flex flex-col gap-[26px] items-start justify-start relative shrink-0 w-[269px]">
                <div className="font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic relative shrink-0 text-[18px] text-[#252525] tracking-[-0.25px] w-full">
                  <p className="leading-[normal]">Projects</p>
                </div>
              </div>
              
              <div className="content-stretch flex flex-col gap-[76px] items-start justify-start relative shrink-0 w-[368px]">
                {data.projects.map((project, index) => (
                  <div key={index} className="content-stretch flex flex-col gap-[20px] items-start justify-start relative shrink-0 w-[368px]">
                    <div className="content-stretch flex gap-[15px] items-start justify-start relative shrink-0 w-[368px]">
                      <div className="content-stretch flex gap-[15px] items-start justify-start relative shrink-0 w-[353px]">
                        <div className="relative shrink-0 size-3">
                          <div className="bg-[#473bba] relative rounded-sm shrink-0 size-2" />
                        </div>
                        <div className="basis-0 content-stretch flex gap-[15px] grow items-start justify-start min-h-px min-w-px relative shrink-0">
                          <div className="font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic relative shrink-0 text-[15px] text-[#252525] tracking-[-0.25px] w-full">
                            <p className="leading-[normal]">{project.name}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="font-['Inter:Regular',_sans-serif] font-normal leading-[0] not-italic relative shrink-0 text-[13px] text-[rgba(37,37,37,0.8)] tracking-[-0.25px] w-[350px] ml-[9px]">
                      <p className="leading-[15px]">{project.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Left Column - Skills and Education */}
        <div className="absolute left-5 top-[22px] w-[185px] h-[393px]">
          {/* Skills Section */}
          <div className="content-stretch flex flex-col gap-[43px] items-start justify-start relative shrink-0 w-[166px]">
            <div className="content-stretch flex flex-col gap-[26px] items-start justify-start relative shrink-0 w-[137px]">
              <div className="font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic relative shrink-0 text-[18px] text-[#252525] tracking-[-0.25px] w-full">
                <p className="leading-[normal]">Skills</p>
              </div>
            </div>
            
            <div className="content-stretch flex flex-col gap-[33px] items-start justify-start relative shrink-0 w-[166px]">
              {/* Skills Grid */}
              <div className="grid grid-cols-2 gap-2 w-full">
                {data.skills.map((skill, index) => (
                  <div key={index} className="bg-[#f0f0f0] px-3 py-1.5 rounded-md">
                    <div className="font-['Inter:Medium',_sans-serif] font-medium leading-[0] not-italic text-[12px] text-[#252525] text-center">
                      <p className="leading-[normal]">{skill}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Education Section */}
          <div className="absolute top-[207px] content-stretch flex flex-col gap-[41px] items-start justify-start relative shrink-0 w-[185px]">
            <div className="content-stretch flex flex-col gap-[26px] items-start justify-start relative shrink-0 w-[166px] ml-[9.5px]">
              <div className="content-stretch flex flex-col gap-[26px] items-start justify-start relative shrink-0 w-[137px]">
                <div className="font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic relative shrink-0 text-[18px] text-[#252525] tracking-[-0.25px] w-full">
                  <p className="leading-[normal]">Education</p>
                </div>
              </div>
            </div>
            
            {data.education.map((edu, index) => (
              <div key={index} className="content-stretch flex flex-col gap-1.5 h-[65px] items-start justify-start relative shrink-0 w-full">
                <div className="content-stretch flex gap-1.5 h-[65px] items-start justify-start relative shrink-0 w-[185px]">
                  <div className="box-border content-stretch flex gap-2.5 items-center justify-start pb-0 pt-1 px-0 relative shrink-0">
                    <div className="relative shrink-0 size-3.5">
                      <img alt className="block max-w-none size-full" src={imgTdesignEducationFilled} />
                    </div>
                  </div>
                  <div className="basis-0 content-stretch flex flex-col grow items-start justify-start min-h-px min-w-px relative shrink-0">
                    <div className="font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic relative shrink-0 text-[#252525] text-[15px] tracking-[-0.25px] w-full">
                      <p className="leading-[normal]">{edu.degree}</p>
                    </div>
                    <div className="content-stretch flex flex-col gap-[7px] items-start justify-start relative shrink-0 w-full">
                      <div className="font-['Inter:Medium',_sans-serif] font-medium leading-[0] not-italic relative shrink-0 text-[#252525] text-[14px] tracking-[-0.25px] w-[170px]">
                        <p className="leading-[normal]">{edu.institution}</p>
                      </div>
                      <div className="content-stretch flex gap-0.5 items-center justify-start relative shrink-0">
                        <div className="relative shrink-0 size-3">
                          <img alt className="block max-w-none size-full" src={imgBxsCalendar} />
                        </div>
                        <div className="font-['Inter:Regular',_sans-serif] font-normal leading-[0] not-italic relative shrink-0 text-[13px] text-[rgba(37,37,37,0.8)] text-nowrap tracking-[-0.25px]">
                          <p className="leading-[normal] whitespace-pre">{edu.dates}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
