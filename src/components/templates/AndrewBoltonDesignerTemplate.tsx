import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, Palette, Eye } from 'lucide-react';

interface AndrewBoltonDesignerTemplateProps {
  resumeData: ResumeContent;
}

export default function AndrewBoltonDesignerTemplate({ resumeData }: AndrewBoltonDesignerTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="andrew-bolton-designer"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Creative Header */}
      <div className="absolute top-0 left-0 w-full h-[140px] bg-gradient-to-br from-[#667eea] to-[#764ba2] overflow-hidden">
        {/* Geometric decorations */}
        <div className="absolute top-4 right-8 w-12 h-12 bg-white/20 transform rotate-45"></div>
        <div className="absolute bottom-4 left-12 w-8 h-8 bg-white/30 rounded-full"></div>
        
        <div className="flex items-center h-full px-8">
          <div className="flex-1">
            <h1 className="text-4xl font-bold text-white mb-2">
              {personalInfo.fullName || 'Andrew Bolton'}
            </h1>
            <p className="text-xl text-white/90 flex items-center">
              <Palette className="w-5 h-5 mr-2" />
              {personalInfo.jobTitle || 'Creative Designer'}
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[160px] left-0 w-full h-[682px] flex">
        
        {/* Left Column */}
        <div className="w-[370px] px-8 py-6">
          
          {/* About */}
          {summary && (
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-[#333] mb-4 flex items-center">
                <Eye className="w-6 h-6 mr-2 text-[#667eea]" />
                About Me
              </h2>
              <p className="text-sm text-[#333] leading-relaxed">
                {summary}
              </p>
            </div>
          )}

          {/* Experience */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-[#333] mb-4">
              Experience
            </h2>
            <div className="space-y-6">
              {experience.slice(0, 3).map((exp, index) => (
                <div key={index} className="relative pl-6">
                  <div className="absolute left-0 top-2 w-4 h-4 bg-[#667eea] rounded-full"></div>
                  <div className="absolute left-2 top-6 w-0.5 h-16 bg-[#667eea]/30"></div>
                  
                  <div className="bg-gradient-to-r from-[#f8f9ff] to-white p-4 rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-bold text-[#333] text-lg">
                        {exp.position}
                      </h3>
                      <span className="text-sm text-white bg-[#667eea] px-3 py-1 rounded-full">
                        {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Now' : new Date(exp.endDate!).getFullYear()}
                      </span>
                    </div>
                    <p className="text-base font-semibold text-[#764ba2] mb-2">
                      {exp.company}
                    </p>
                    <div className="text-sm text-[#333] space-y-1">
                      {exp.description.slice(0, 2).map((desc, i) => (
                        <p key={i}>• {desc}</p>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="w-[224px] bg-gradient-to-b from-[#f8f9ff] to-[#f0f2ff] px-6 py-6">
          
          {/* Contact */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#333] mb-4">
              Contact
            </h2>
            <div className="space-y-3">
              <div className="flex items-center text-sm text-[#333]">
                <div className="w-8 h-8 bg-[#667eea] rounded-full flex items-center justify-center mr-3">
                  <Mail className="w-4 h-4 text-white" />
                </div>
                <span className="truncate">{personalInfo.email}</span>
              </div>
              <div className="flex items-center text-sm text-[#333]">
                <div className="w-8 h-8 bg-[#667eea] rounded-full flex items-center justify-center mr-3">
                  <Phone className="w-4 h-4 text-white" />
                </div>
                <span>{personalInfo.phone}</span>
              </div>
              <div className="flex items-center text-sm text-[#333]">
                <div className="w-8 h-8 bg-[#667eea] rounded-full flex items-center justify-center mr-3">
                  <MapPin className="w-4 h-4 text-white" />
                </div>
                <span>{personalInfo.location}</span>
              </div>
            </div>
          </div>

          {/* Skills */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#333] mb-4">
              Design Skills
            </h2>
            <div className="space-y-3">
              {skills.slice(0, 6).map((skill, index) => {
                const progress = [95, 90, 85, 88, 82, 87][index] || 80;
                return (
                  <div key={index}>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-[#333]">{skill}</span>
                      <span className="text-xs text-[#333]/70">{progress}%</span>
                    </div>
                    <div className="w-full bg-white rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-[#667eea] to-[#764ba2] h-2 rounded-full"
                        style={{ width: `${progress}%` }}
                      ></div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Education */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#333] mb-4">
              Education
            </h2>
            <div className="space-y-3">
              {education.slice(0, 2).map((edu, index) => (
                <div key={index} className="bg-white p-3 rounded-lg shadow-sm">
                  <h3 className="font-semibold text-sm text-[#333]">
                    {edu.degree}
                  </h3>
                  <p className="text-xs text-[#667eea] font-medium">
                    {edu.institution}
                  </p>
                  <p className="text-xs text-[#333]/70">
                    {new Date(edu.startDate).getFullYear()}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Portfolio */}
          <div>
            <h2 className="text-lg font-bold text-[#333] mb-4">
              Portfolio
            </h2>
            <div className="space-y-2">
              {personalInfo.website && (
                <div className="flex items-center text-sm text-[#667eea]">
                  <Globe className="w-4 h-4 mr-2" />
                  <span>Portfolio Website</span>
                </div>
              )}
              {personalInfo.linkedinUrl && (
                <div className="flex items-center text-sm text-[#667eea]">
                  <Linkedin className="w-4 h-4 mr-2" />
                  <span>LinkedIn</span>
                </div>
              )}
              <div className="flex items-center text-sm text-[#667eea]">
                <Palette className="w-4 h-4 mr-2" />
                <span>Behance</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
