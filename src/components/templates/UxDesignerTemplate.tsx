import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, Figma, Palette, Monitor, Smartphone } from 'lucide-react';

interface UxDesignerTemplateProps {
  resumeData: ResumeContent;
}

export default function UxDesignerTemplate({ resumeData }: UxDesignerTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  // Mock design tools for UX Designer template
  const designTools = [
    { name: 'Figma', level: 95 },
    { name: 'Sketch', level: 90 },
    { name: 'Adobe XD', level: 85 },
    { name: 'Photoshop', level: 80 },
    { name: 'Illustrator', level: 75 },
    { name: 'InVision', level: 85 },
    { name: 'Principle', level: 70 },
    { name: 'Framer', level: 80 }
  ];

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="ux-designer-12"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Header */}
      <div className="absolute top-0 left-0 w-full h-[100px] bg-[#005ba2] px-8 py-6">
        <div className="flex justify-between items-center h-full">
          <div>
            <h1 className="text-3xl font-bold text-white mb-1">
              {personalInfo.fullName || 'Your Name'}
            </h1>
            <p className="text-lg text-white/90">
              {personalInfo.jobTitle || 'UX/UI Designer'}
            </p>
          </div>
          
          {/* Contact Info in Header */}
          <div className="text-right text-white text-sm space-y-1">
            <div className="flex items-center justify-end">
              <Mail className="w-4 h-4 mr-2" />
              <span>{personalInfo.email}</span>
            </div>
            <div className="flex items-center justify-end">
              <Phone className="w-4 h-4 mr-2" />
              <span>{personalInfo.phone}</span>
            </div>
            <div className="flex items-center justify-end">
              <MapPin className="w-4 h-4 mr-2" />
              <span>{personalInfo.location}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - Two Column Layout */}
      <div className="absolute top-[120px] left-0 w-full h-[722px] flex">
        
        {/* Left Column - Main Content */}
        <div className="w-[370px] px-8 py-6">
          
          {/* Professional Summary */}
          {summary && (
            <div className="mb-8">
              <h2 className="text-xl font-bold text-[#454545] mb-4 border-b-2 border-[#005ba2] pb-2">
                About Me
              </h2>
              <p className="text-sm text-[#000000] leading-relaxed">
                {summary}
              </p>
            </div>
          )}

          {/* Professional Experience */}
          <div className="mb-8">
            <h2 className="text-xl font-bold text-[#454545] mb-4 border-b-2 border-[#005ba2] pb-2">
              Experience
            </h2>
            <div className="space-y-6">
              {experience.slice(0, 4).map((exp, index) => (
                <div key={index} className="relative">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-bold text-[#000000] text-base">
                      {exp.position}
                    </h3>
                    <span className="text-xs text-[#454545] bg-gray-100 px-2 py-1 rounded">
                      {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                    </span>
                  </div>
                  <p className="text-sm font-semibold text-[#005ba2] mb-1">
                    {exp.company}
                  </p>
                  <p className="text-xs text-[#454545] mb-3">
                    {exp.location}
                  </p>
                  <div className="text-sm text-[#000000] space-y-1">
                    {exp.description.slice(0, 3).map((desc, i) => (
                      <p key={i} className="flex items-start">
                        <span className="w-1 h-1 bg-[#005ba2] rounded-full mr-2 mt-2 flex-shrink-0"></span>
                        {desc}
                      </p>
                    ))}
                  </div>
                  {exp.skills && exp.skills.length > 0 && (
                    <div className="mt-3">
                      <div className="flex flex-wrap gap-1">
                        {exp.skills.slice(0, 5).map((skill, i) => (
                          <span key={i} className="text-xs bg-[#005ba2]/10 text-[#005ba2] px-2 py-1 rounded border border-[#005ba2]/20">
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Education */}
          <div>
            <h2 className="text-xl font-bold text-[#454545] mb-4 border-b-2 border-[#005ba2] pb-2">
              Education
            </h2>
            <div className="space-y-4">
              {education.slice(0, 2).map((edu, index) => (
                <div key={index}>
                  <h3 className="font-bold text-[#000000] text-base">
                    {edu.degree} in {edu.field}
                  </h3>
                  <p className="text-sm text-[#005ba2] font-medium">
                    {edu.institution}
                  </p>
                  <p className="text-sm text-[#454545]">
                    {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column - Skills & Tools */}
        <div className="w-[224px] bg-gray-50 px-6 py-6">
          
          {/* Core Skills */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#454545] mb-4 flex items-center">
              <Palette className="w-4 h-4 mr-2 text-[#005ba2]" />
              Core Skills
            </h2>
            <div className="space-y-2">
              {skills.slice(0, 8).map((skill, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-2 h-2 bg-[#005ba2] rounded-full mr-2"></div>
                  <span className="text-sm text-[#000000]">{skill}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Design Tools */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#454545] mb-4 flex items-center">
              <Monitor className="w-4 h-4 mr-2 text-[#005ba2]" />
              Design Tools
            </h2>
            <div className="space-y-3">
              {designTools.slice(0, 6).map((tool, index) => (
                <div key={index}>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium text-[#000000]">{tool.name}</span>
                    <span className="text-xs text-[#454545]">{tool.level}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-[#005ba2] h-2 rounded-full transition-all duration-300"
                      style={{ width: `${tool.level}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Specializations */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#454545] mb-4 flex items-center">
              <Smartphone className="w-4 h-4 mr-2 text-[#005ba2]" />
              Specializations
            </h2>
            <div className="space-y-2">
              {[
                'User Research',
                'Wireframing',
                'Prototyping',
                'Usability Testing',
                'Information Architecture',
                'Interaction Design'
              ].map((spec, index) => (
                <div key={index} className="bg-white p-2 rounded border-l-3 border-[#005ba2]">
                  <span className="text-sm text-[#000000]">{spec}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Contact Links */}
          <div>
            <h2 className="text-lg font-bold text-[#454545] mb-4">
              Links
            </h2>
            <div className="space-y-2">
              {personalInfo.website && (
                <div className="flex items-center text-sm text-[#005ba2]">
                  <Globe className="w-3 h-3 mr-2" />
                  <span>Portfolio</span>
                </div>
              )}
              {personalInfo.linkedinUrl && (
                <div className="flex items-center text-sm text-[#005ba2]">
                  <Linkedin className="w-3 h-3 mr-2" />
                  <span>LinkedIn</span>
                </div>
              )}
              {personalInfo.githubUrl && (
                <div className="flex items-center text-sm text-[#005ba2]">
                  <Github className="w-3 h-3 mr-2" />
                  <span>GitHub</span>
                </div>
              )}
              <div className="flex items-center text-sm text-[#005ba2]">
                <Figma className="w-3 h-3 mr-2" />
                <span>Figma Profile</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
