import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github } from 'lucide-react';

interface ContactBadgesTemplateProps {
  resumeData: ResumeContent;
}

export default function ContactBadgesTemplate({ resumeData }: ContactBadgesTemplateProps) {
  const { personalInfo, summary, experience, education, skills, languages } = resumeData;

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden p-8"
      data-template-id="contact-badges-10"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Header Section */}
      <div className="mb-8">
        {/* Profile Photo */}
        <div className="flex justify-center mb-6">
          <div className="w-24 h-24 rounded-full bg-[#e4f2ef] border-3 border-[#333333] overflow-hidden">
            {personalInfo.photo ? (
              <img 
                src={personalInfo.photo} 
                alt="Profile" 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-[#e4f2ef] to-[#d4e2df] flex items-center justify-center">
                <span className="text-[#333333] text-2xl font-bold">
                  {personalInfo.fullName?.charAt(0) || 'Y'}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Name and Title */}
        <div className="text-center mb-6">
          <h1 className="text-3xl font-bold text-[#333333] mb-2">
            {personalInfo.fullName || 'Your Name'}
          </h1>
          <p className="text-lg text-[#333333]/80">
            {personalInfo.jobTitle || 'Professional Title'}
          </p>
        </div>

        {/* Contact Badges */}
        <div className="flex flex-wrap justify-center gap-3 mb-6">
          <div className="flex items-center bg-[#e4f2ef] px-3 py-2 rounded-full">
            <Mail className="w-4 h-4 mr-2 text-[#333333]" />
            <span className="text-sm text-[#333333]">{personalInfo.email}</span>
          </div>
          <div className="flex items-center bg-[#e4f2ef] px-3 py-2 rounded-full">
            <Phone className="w-4 h-4 mr-2 text-[#333333]" />
            <span className="text-sm text-[#333333]">{personalInfo.phone}</span>
          </div>
          <div className="flex items-center bg-[#e4f2ef] px-3 py-2 rounded-full">
            <MapPin className="w-4 h-4 mr-2 text-[#333333]" />
            <span className="text-sm text-[#333333]">{personalInfo.location}</span>
          </div>
          {personalInfo.website && (
            <div className="flex items-center bg-[#e4f2ef] px-3 py-2 rounded-full">
              <Globe className="w-4 h-4 mr-2 text-[#333333]" />
              <span className="text-sm text-[#333333]">Website</span>
            </div>
          )}
          {personalInfo.linkedinUrl && (
            <div className="flex items-center bg-[#e4f2ef] px-3 py-2 rounded-full">
              <Linkedin className="w-4 h-4 mr-2 text-[#333333]" />
              <span className="text-sm text-[#333333]">LinkedIn</span>
            </div>
          )}
        </div>
      </div>

      {/* Professional Summary */}
      {summary && (
        <div className="mb-8">
          <h2 className="text-xl font-bold text-[#333333] mb-4 border-b-2 border-[#e4f2ef] pb-2">
            Professional Summary
          </h2>
          <p className="text-sm text-[#333333] leading-relaxed">
            {summary}
          </p>
        </div>
      )}

      {/* Professional Experience */}
      <div className="mb-8">
        <h2 className="text-xl font-bold text-[#333333] mb-4 border-b-2 border-[#e4f2ef] pb-2">
          Professional Experience
        </h2>
        <div className="space-y-6">
          {experience.slice(0, 4).map((exp, index) => (
            <div key={index}>
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-bold text-[#333333] text-lg">
                  {exp.position}
                </h3>
                <span className="text-sm text-[#333333]/70 bg-[#e4f2ef] px-3 py-1 rounded-full">
                  {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                </span>
              </div>
              <p className="text-base font-semibold text-[#333333]/80 mb-1">
                {exp.company} • {exp.location}
              </p>
              <div className="text-sm text-[#333333] space-y-1 mb-3">
                {exp.description.slice(0, 3).map((desc, i) => (
                  <p key={i}>• {desc}</p>
                ))}
              </div>
              {exp.skills && exp.skills.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {exp.skills.slice(0, 5).map((skill, i) => (
                    <span key={i} className="text-xs bg-[#e4f2ef] text-[#333333] px-2 py-1 rounded-full">
                      {skill}
                    </span>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Education */}
      <div className="mb-8">
        <h2 className="text-xl font-bold text-[#333333] mb-4 border-b-2 border-[#e4f2ef] pb-2">
          Education
        </h2>
        <div className="space-y-4">
          {education.slice(0, 2).map((edu, index) => (
            <div key={index}>
              <h3 className="font-bold text-[#333333] text-base">
                {edu.degree} in {edu.field}
              </h3>
              <p className="text-sm text-[#333333]/80 font-medium">
                {edu.institution} • {edu.location}
              </p>
              <p className="text-sm text-[#333333]/70">
                {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Skills Tags */}
      <div className="mb-8">
        <h2 className="text-xl font-bold text-[#333333] mb-4 border-b-2 border-[#e4f2ef] pb-2">
          Skills
        </h2>
        <div className="flex flex-wrap gap-2">
          {skills.map((skill, index) => (
            <span key={index} className="bg-[#e4f2ef] text-[#333333] px-3 py-2 rounded-full text-sm font-medium">
              {skill}
            </span>
          ))}
        </div>
      </div>

      {/* Languages */}
      {languages && languages.length > 0 && (
        <div>
          <h2 className="text-xl font-bold text-[#333333] mb-4 border-b-2 border-[#e4f2ef] pb-2">
            Languages
          </h2>
          <div className="flex flex-wrap gap-2">
            {languages.map((lang, index) => (
              <div key={index} className="bg-[#e4f2ef] text-[#333333] px-3 py-2 rounded-full text-sm">
                <span className="font-medium">{lang.name}</span>
                <span className="text-[#333333]/70 ml-1">({lang.proficiency})</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
