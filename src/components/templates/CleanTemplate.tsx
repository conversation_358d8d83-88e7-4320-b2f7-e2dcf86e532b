import React from "react";
import { Mail, Linkedin, MapPin, CheckCircle2, ChevronRight, BookOpen, Award, Target, Users, Calendar, Building2, Star, Phone } from "lucide-react";

// Rule component for section dividers
const Rule = ({ dark = false }) => (
  <div className={`flex-1 h-px ${dark ? 'bg-white/20' : 'bg-gray-300'}`}></div>
);

export default function CleanTemplate() {
  return (
    <div className="max-w-4xl mx-auto bg-white shadow-lg border border-gray-200 overflow-hidden">
      <div className="flex min-h-screen">
        {/* Left Sidebar - Dark */}
        <div className="w-1/3 bg-gradient-to-b from-slate-800 to-slate-900 text-white p-8">
          {/* Profile Section */}
          <div className="text-center mb-8">
            <div className="w-24 h-24 mx-auto bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mb-4">
              <Users size={36} className="text-white" />
            </div>
            <h1 className="font-bold mb-2">DAVID CHEN</h1>
            <p className="text-blue-300 text-sm">PROJECT MANAGER</p>
          </div>

          {/* Contact Information */}
          <div className="mb-8">
            <div className="flex items-end gap-3 mb-4">
              <h3 className="text-[12px] tracking-[0.2em] font-semibold">CONTACT</h3>
              <Rule dark />
            </div>
            <div className="space-y-3 text-sm">
              <div className="flex items-center gap-3">
                <Phone size={14} className="text-blue-400" />
                <span>+****************</span>
              </div>
              <div className="flex items-center gap-3">
                <Mail size={14} className="text-blue-400" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-3">
                <MapPin size={14} className="text-blue-400" />
                <span>Seattle, WA</span>
              </div>
              <div className="flex items-center gap-3">
                <Linkedin size={14} className="text-blue-400" />
                <span>linkedin.com/in/davidchen</span>
              </div>
            </div>
          </div>

          {/* Skills */}
          <div className="mb-8">
            <div className="flex items-end gap-3 mb-4">
              <h3 className="text-[12px] tracking-[0.2em] font-semibold">SKILLS</h3>
              <Rule dark />
            </div>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between items-center">
                <span>Project Planning</span>
                <div className="flex gap-1">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="w-2 h-2 rounded-full bg-blue-400"></div>
                  ))}
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span>Team Leadership</span>
                <div className="flex gap-1">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="w-2 h-2 rounded-full bg-blue-400"></div>
                  ))}
                  <div className="w-2 h-2 rounded-full bg-white/30"></div>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span>Risk Management</span>
                <div className="flex gap-1">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="w-2 h-2 rounded-full bg-blue-400"></div>
                  ))}
                  <div className="w-2 h-2 rounded-full bg-white/30"></div>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span>Agile & Scrum</span>
                <div className="flex gap-1">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="w-2 h-2 rounded-full bg-blue-400"></div>
                  ))}
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span>Stakeholder Management</span>
                <div className="flex gap-1">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="w-2 h-2 rounded-full bg-blue-400"></div>
                  ))}
                  <div className="w-2 h-2 rounded-full bg-white/30"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Training / Courses */}
          <div className="mb-10">
            <div className="flex items-end gap-3 mb-4">
              <h3 className="text-[12px] tracking-[0.2em] font-semibold">TRAINING / COURSES</h3>
              <Rule dark />
            </div>
            <div className="space-y-4 text-sm">
              <div>
                <h4 className="font-medium text-blue-300">Project Management Professional (PMP)</h4>
                <p className="text-white/70 text-xs">Project Management Institute • 2023</p>
              </div>
              <div>
                <h4 className="font-medium text-blue-300">Certified Scrum Master (CSM)</h4>
                <p className="text-white/70 text-xs">Scrum Alliance • 2022</p>
              </div>
              <div>
                <h4 className="font-medium text-blue-300">Advanced Risk Management</h4>
                <p className="text-white/70 text-xs">Stanford Online • 2022</p>
              </div>
              <div>
                <h4 className="font-medium text-blue-300">Leadership in Project Management</h4>
                <p className="text-white/70 text-xs">MIT Professional Education • 2021</p>
              </div>
            </div>
          </div>

          {/* Achievements */}
          <div className="mb-8">
            <div className="flex items-end gap-3 mb-4">
              <h3 className="text-[12px] tracking-[0.2em] font-semibold">ACHIEVEMENTS</h3>
              <Rule dark />
            </div>
            <div className="space-y-3 text-sm">
              <div className="flex items-start gap-2">
                <Award size={12} className="text-blue-400 mt-0.5 shrink-0" />
                <span>Project Manager of the Year 2023</span>
              </div>
              <div className="flex items-start gap-2">
                <Target size={12} className="text-blue-400 mt-0.5 shrink-0" />
                <span>Delivered 15+ projects under budget</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle2 size={12} className="text-blue-400 mt-0.5 shrink-0" />
                <span>98% on-time project delivery rate</span>
              </div>
              <div className="flex items-start gap-2">
                <Star size={12} className="text-blue-400 mt-0.5 shrink-0" />
                <span>Led teams of up to 25 members</span>
              </div>
            </div>
          </div>

          {/* Interests */}
          <div>
            <div className="flex items-end gap-3 mb-4">
              <h3 className="text-[12px] tracking-[0.2em] font-semibold">INTERESTS</h3>
              <Rule dark />
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <ChevronRight size={12} className="text-blue-400" />
                <span>Technology Innovation</span>
              </div>
              <div className="flex items-center gap-2">
                <ChevronRight size={12} className="text-blue-400" />
                <span>Team Building</span>
              </div>
              <div className="flex items-center gap-2">
                <ChevronRight size={12} className="text-blue-400" />
                <span>Process Optimization</span>
              </div>
              <div className="flex items-center gap-2">
                <ChevronRight size={12} className="text-blue-400" />
                <span>Continuous Learning</span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Content Area */}
        <div className="flex-1 p-10">
          {/* Summary */}
          <section className="mb-8">
            <h2 className="font-bold text-gray-800 mb-4">PROFESSIONAL SUMMARY</h2>
            <p className="text-gray-600 leading-relaxed">
              Results-driven Project Manager with over 7 years of experience leading cross-functional teams 
              and delivering complex projects on time and within budget. Proven expertise in Agile methodologies, 
              risk management, and stakeholder communication. Successfully managed projects worth over $10M in 
              total value across technology and healthcare sectors. Passionate about driving operational 
              excellence and fostering collaborative team environments.
            </p>
          </section>

          {/* Experience */}
          <section className="mb-8">
            <h2 className="font-bold text-gray-800 mb-4">EXPERIENCE</h2>
            
            {/* Senior Project Manager */}
            <div className="mb-6">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h3 className="font-semibold text-gray-800">Senior Project Manager</h3>
                  <p className="text-blue-600 font-medium">TechFlow Solutions</p>
                </div>
                <div className="text-right text-sm text-gray-500">
                  <div className="flex items-center gap-1">
                    <Calendar size={12} />
                    <span>2021 - Present</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <MapPin size={12} />
                    <span>Seattle, WA</span>
                  </div>
                </div>
              </div>
              <ul className="text-gray-600 space-y-1 list-disc list-inside">
                <li>Led implementation of enterprise software solutions for Fortune 500 clients</li>
                <li>Managed project budgets ranging from $500K to $2.5M with 98% on-time delivery</li>
                <li>Coordinated cross-functional teams of 15-25 members across multiple time zones</li>
                <li>Implemented Agile practices that reduced project delivery time by 30%</li>
                <li>Maintained stakeholder satisfaction score of 4.8/5.0 across all projects</li>
              </ul>
            </div>

            {/* Project Manager */}
            <div className="mb-6">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h3 className="font-semibold text-gray-800">Project Manager</h3>
                  <p className="text-blue-600 font-medium">HealthTech Innovations</p>
                </div>
                <div className="text-right text-sm text-gray-500">
                  <div className="flex items-center gap-1">
                    <Calendar size={12} />
                    <span>2019 - 2021</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <MapPin size={12} />
                    <span>Portland, OR</span>
                  </div>
                </div>
              </div>
              <ul className="text-gray-600 space-y-1 list-disc list-inside">
                <li>Managed healthcare software development projects from concept to deployment</li>
                <li>Collaborated with medical professionals to ensure regulatory compliance</li>
                <li>Reduced project costs by 20% through improved resource allocation</li>
                <li>Led user acceptance testing with hospital staff across 12 facilities</li>
                <li>Mentored 3 junior project coordinators in Agile methodologies</li>
              </ul>
            </div>

            {/* Associate Project Manager */}
            <div className="mb-6">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h3 className="font-semibold text-gray-800">Associate Project Manager</h3>
                  <p className="text-blue-600 font-medium">Digital Solutions Corp</p>
                </div>
                <div className="text-right text-sm text-gray-500">
                  <div className="flex items-center gap-1">
                    <Calendar size={12} />
                    <span>2017 - 2019</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <MapPin size={12} />
                    <span>San Francisco, CA</span>
                  </div>
                </div>
              </div>
              <ul className="text-gray-600 space-y-1 list-disc list-inside">
                <li>Supported senior project managers on web application development projects</li>
                <li>Maintained project documentation and tracked deliverable milestones</li>
                <li>Facilitated daily standups and sprint planning meetings</li>
                <li>Coordinated with QA teams to ensure quality standards were met</li>
                <li>Managed project communications with clients and internal stakeholders</li>
              </ul>
            </div>
          </section>

          {/* Education */}
          <section className="mb-8">
            <h2 className="font-bold text-gray-800 mb-4">EDUCATION</h2>
            <div>
              <h3 className="font-semibold text-gray-800">Master of Business Administration (MBA)</h3>
              <p className="text-blue-600 font-medium">University of Washington</p>
              <div className="flex items-center gap-4 text-sm text-gray-500 mt-1">
                <span className="flex items-center gap-1">
                  <Calendar size={12} />
                  2015 - 2017
                </span>
                <span className="flex items-center gap-1">
                  <MapPin size={12} />
                  Seattle, WA
                </span>
              </div>
              <p className="text-xs text-gray-600 mt-1">Concentration: Operations Management & Technology</p>
            </div>
          </section>

          {/* Tools & Technologies */}
          <section>
            <h2 className="font-bold text-gray-800 mb-4">TOOLS & TECHNOLOGIES</h2>
            <div className="grid grid-cols-3 gap-3">
              <div className="bg-gray-50 px-4 py-2 rounded-lg border border-gray-200 text-center">
                <span className="text-sm font-medium text-gray-700">Jira</span>
              </div>
              <div className="bg-gray-50 px-4 py-2 rounded-lg border border-gray-200 text-center">
                <span className="text-sm font-medium text-gray-700">Confluence</span>
              </div>
              <div className="bg-gray-50 px-4 py-2 rounded-lg border border-gray-200 text-center">
                <span className="text-sm font-medium text-gray-700">Microsoft Project</span>
              </div>
              <div className="bg-gray-50 px-4 py-2 rounded-lg border border-gray-200 text-center">
                <span className="text-sm font-medium text-gray-700">Slack</span>
              </div>
              <div className="bg-gray-50 px-4 py-2 rounded-lg border border-gray-200 text-center">
                <span className="text-sm font-medium text-gray-700">Tableau</span>
              </div>
              <div className="bg-gray-50 px-4 py-2 rounded-lg border border-gray-200 text-center">
                <span className="text-sm font-medium text-gray-700">Salesforce</span>
              </div>
              <div className="bg-gray-50 px-4 py-2 rounded-lg border border-gray-200 text-center">
                <span className="text-sm font-medium text-gray-700">Azure DevOps</span>
              </div>
              <div className="bg-gray-50 px-4 py-2 rounded-lg border border-gray-200 text-center">
                <span className="text-sm font-medium text-gray-700">Smartsheet</span>
              </div>
              <div className="bg-gray-50 px-4 py-2 rounded-lg border border-gray-200 text-center">
                <span className="text-sm font-medium text-gray-700">Trello</span>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}