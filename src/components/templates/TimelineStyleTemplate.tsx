import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, Twitter, Instagram } from 'lucide-react';

interface TimelineStyleTemplateProps {
  resumeData: ResumeContent;
}

export default function TimelineStyleTemplate({ resumeData }: TimelineStyleTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden p-8"
      data-template-id="timeline-style-14"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Header Section */}
      <div className="mb-8">
        <div className="flex items-center mb-6">
          {/* Profile Photo */}
          <div className="w-24 h-24 rounded-full bg-[#4b76c4] border-4 border-white shadow-lg overflow-hidden mr-6">
            {personalInfo.photo ? (
              <img 
                src={personalInfo.photo} 
                alt="Profile" 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-[#4b76c4] to-[#3a5ba3] flex items-center justify-center">
                <span className="text-white text-2xl font-bold">
                  {personalInfo.fullName?.charAt(0) || 'Y'}
                </span>
              </div>
            )}
          </div>
          
          {/* Name and Title */}
          <div className="flex-1">
            <h1 className="text-4xl font-bold text-[#2c2c2c] mb-2">
              {personalInfo.fullName || 'Your Name'}
            </h1>
            <p className="text-xl text-[#4b76c4] font-medium">
              {personalInfo.jobTitle || 'Professional Title'}
            </p>
          </div>
        </div>

        {/* Contact Information */}
        <div className="flex flex-wrap gap-4 mb-6">
          <div className="flex items-center text-sm text-[#2c2c2c]">
            <Mail className="w-4 h-4 mr-2 text-[#4b76c4]" />
            <span>{personalInfo.email}</span>
          </div>
          <div className="flex items-center text-sm text-[#2c2c2c]">
            <Phone className="w-4 h-4 mr-2 text-[#4b76c4]" />
            <span>{personalInfo.phone}</span>
          </div>
          <div className="flex items-center text-sm text-[#2c2c2c]">
            <MapPin className="w-4 h-4 mr-2 text-[#4b76c4]" />
            <span>{personalInfo.location}</span>
          </div>
          {personalInfo.website && (
            <div className="flex items-center text-sm text-[#2c2c2c]">
              <Globe className="w-4 h-4 mr-2 text-[#4b76c4]" />
              <span>Portfolio</span>
            </div>
          )}
        </div>

        {/* Social Links */}
        <div className="flex gap-3">
          {personalInfo.linkedinUrl && (
            <div className="w-8 h-8 bg-[#4b76c4] rounded-full flex items-center justify-center">
              <Linkedin className="w-4 h-4 text-white" />
            </div>
          )}
          {personalInfo.githubUrl && (
            <div className="w-8 h-8 bg-[#4b76c4] rounded-full flex items-center justify-center">
              <Github className="w-4 h-4 text-white" />
            </div>
          )}
          <div className="w-8 h-8 bg-[#4b76c4] rounded-full flex items-center justify-center">
            <Twitter className="w-4 h-4 text-white" />
          </div>
          <div className="w-8 h-8 bg-[#4b76c4] rounded-full flex items-center justify-center">
            <Instagram className="w-4 h-4 text-white" />
          </div>
        </div>
      </div>

      {/* Professional Profile */}
      {summary && (
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-[#2c2c2c] mb-4 flex items-center">
            <div className="w-1 h-8 bg-[#4b76c4] mr-3"></div>
            Professional Profile
          </h2>
          <p className="text-sm text-[#2c2c2c] leading-relaxed">
            {summary}
          </p>
        </div>
      )}

      {/* Timeline Experience */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-[#2c2c2c] mb-6 flex items-center">
          <div className="w-1 h-8 bg-[#4b76c4] mr-3"></div>
          Professional Experience
        </h2>
        
        {/* Timeline Container */}
        <div className="relative">
          {/* Timeline Line */}
          <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-[#4b76c4]/30"></div>
          
          <div className="space-y-8">
            {experience.slice(0, 4).map((exp, index) => (
              <div key={index} className="relative flex items-start">
                {/* Timeline Dot */}
                <div className="absolute left-4 w-4 h-4 bg-[#4b76c4] rounded-full border-4 border-white shadow-lg z-10"></div>
                
                {/* Content */}
                <div className="ml-12 bg-white border border-gray-200 rounded-lg p-4 shadow-sm w-full">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-bold text-[#2c2c2c] text-lg">
                      {exp.position}
                    </h3>
                    <span className="text-sm text-white bg-[#4b76c4] px-3 py-1 rounded-full">
                      {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                    </span>
                  </div>
                  <p className="text-base font-semibold text-[#4b76c4] mb-1">
                    {exp.company}
                  </p>
                  <p className="text-sm text-[#2c2c2c]/70 mb-3">
                    {exp.location}
                  </p>
                  <div className="text-sm text-[#2c2c2c] space-y-1">
                    {exp.description.slice(0, 3).map((desc, i) => (
                      <p key={i}>• {desc}</p>
                    ))}
                  </div>
                  {exp.skills && exp.skills.length > 0 && (
                    <div className="mt-3">
                      <div className="flex flex-wrap gap-2">
                        {exp.skills.slice(0, 5).map((skill, i) => (
                          <span key={i} className="text-xs bg-[#4b76c4]/10 text-[#4b76c4] px-2 py-1 rounded-full border border-[#4b76c4]/20">
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Skills Section */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-[#2c2c2c] mb-4 flex items-center">
          <div className="w-1 h-8 bg-[#4b76c4] mr-3"></div>
          Core Skills
        </h2>
        <div className="grid grid-cols-3 gap-3">
          {skills.slice(0, 12).map((skill, index) => (
            <div key={index} className="bg-gray-50 border border-gray-200 rounded-lg p-3 text-center">
              <span className="text-sm font-medium text-[#2c2c2c]">{skill}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Education */}
      <div>
        <h2 className="text-2xl font-bold text-[#2c2c2c] mb-4 flex items-center">
          <div className="w-1 h-8 bg-[#4b76c4] mr-3"></div>
          Education
        </h2>
        <div className="grid grid-cols-1 gap-4">
          {education.slice(0, 2).map((edu, index) => (
            <div key={index} className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="font-bold text-[#2c2c2c] text-base">
                {edu.degree} in {edu.field}
              </h3>
              <p className="text-sm text-[#4b76c4] font-medium">
                {edu.institution} • {edu.location}
              </p>
              <p className="text-sm text-[#2c2c2c]/70">
                {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
              </p>
              {edu.gpa && (
                <p className="text-sm text-[#2c2c2c]/70">
                  GPA: {edu.gpa}
                </p>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
