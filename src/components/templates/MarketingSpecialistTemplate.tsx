import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, TrendingUp, Target, Users, Megaphone } from 'lucide-react';

interface MarketingSpecialistTemplateProps {
  resumeData: ResumeContent;
}

export default function MarketingSpecialistTemplate({ resumeData }: MarketingSpecialistTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  // Marketing-specific certifications
  const marketingCertifications = [
    'Google Ads Certified',
    'Facebook Blueprint',
    'HubSpot Content Marketing',
    'Google Analytics Certified',
    'Hootsuite Social Media',
    'Salesforce Marketing Cloud'
  ];

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="rana-muktyomber-marketing"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Blue Sidebar */}
      <div className="absolute top-0 left-0 w-[180px] h-full bg-[#2563eb] px-5 py-8">
        
        {/* Profile Section */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 rounded-full bg-white border-3 border-[#dbeafe] overflow-hidden mx-auto mb-4 shadow-lg">
            {personalInfo.photo ? (
              <img 
                src={personalInfo.photo} 
                alt="Profile" 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-[#3b82f6] to-[#2563eb] flex items-center justify-center">
                <span className="text-white text-xl font-bold">
                  {personalInfo.fullName?.charAt(0) || 'R'}
                </span>
              </div>
            )}
          </div>
          <h1 className="text-lg font-bold text-white mb-1">
            {personalInfo.fullName || 'Rana Muktyomber'}
          </h1>
          <p className="text-sm text-[#dbeafe] font-medium">
            {personalInfo.jobTitle || 'Marketing Specialist'}
          </p>
        </div>

        {/* Contact Information */}
        <div className="mb-8">
          <h2 className="text-sm font-bold text-[#dbeafe] mb-4 uppercase tracking-wide">
            Contact
          </h2>
          <div className="space-y-3">
            <div className="flex items-start text-xs text-white">
              <Mail className="w-3 h-3 mr-2 mt-0.5 text-[#dbeafe] flex-shrink-0" />
              <span className="break-all">{personalInfo.email}</span>
            </div>
            <div className="flex items-center text-xs text-white">
              <Phone className="w-3 h-3 mr-2 text-[#dbeafe] flex-shrink-0" />
              <span>{personalInfo.phone}</span>
            </div>
            <div className="flex items-start text-xs text-white">
              <MapPin className="w-3 h-3 mr-2 mt-0.5 text-[#dbeafe] flex-shrink-0" />
              <span>{personalInfo.location}</span>
            </div>
            {personalInfo.website && (
              <div className="flex items-start text-xs text-white">
                <Globe className="w-3 h-3 mr-2 mt-0.5 text-[#dbeafe] flex-shrink-0" />
                <span className="break-all">{personalInfo.website}</span>
              </div>
            )}
            {personalInfo.linkedinUrl && (
              <div className="flex items-center text-xs text-white">
                <Linkedin className="w-3 h-3 mr-2 text-[#dbeafe] flex-shrink-0" />
                <span>LinkedIn Profile</span>
              </div>
            )}
          </div>
        </div>

        {/* Core Skills */}
        <div className="mb-8">
          <h2 className="text-sm font-bold text-[#dbeafe] mb-4 uppercase tracking-wide">
            Marketing Skills
          </h2>
          <div className="space-y-2">
            {skills.slice(0, 8).map((skill, index) => (
              <div key={index} className="flex items-center">
                <div className="w-2 h-2 bg-[#dbeafe] rounded-full mr-2 flex-shrink-0"></div>
                <span className="text-xs text-white">{skill}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Certifications */}
        <div className="mb-8">
          <h2 className="text-sm font-bold text-[#dbeafe] mb-4 uppercase tracking-wide">
            Certifications
          </h2>
          <div className="space-y-2">
            {marketingCertifications.slice(0, 6).map((cert, index) => (
              <div key={index} className="bg-[#3b82f6] p-2 rounded text-center">
                <span className="text-xs text-white font-medium">{cert}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Education */}
        <div>
          <h2 className="text-sm font-bold text-[#dbeafe] mb-4 uppercase tracking-wide">
            Education
          </h2>
          <div className="space-y-3">
            {education.slice(0, 2).map((edu, index) => (
              <div key={index}>
                <h3 className="font-semibold text-xs text-white leading-tight">
                  {edu.degree}
                </h3>
                <p className="text-xs text-[#dbeafe] font-medium">
                  {edu.institution}
                </p>
                <p className="text-xs text-[#dbeafe]/70">
                  {new Date(edu.startDate).getFullYear()}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="absolute top-0 left-[180px] w-[414px] h-full px-8 py-8 bg-[#dbeafe]">
        
        {/* Professional Summary */}
        {summary && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-[#2563eb] mb-4 flex items-center">
              <div className="w-1 h-8 bg-[#3b82f6] mr-3"></div>
              Professional Summary
            </h2>
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <p className="text-sm text-[#2563eb] leading-relaxed">
                {summary}
              </p>
            </div>
          </div>
        )}

        {/* Professional Experience */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-[#2563eb] mb-6 flex items-center">
            <div className="w-1 h-8 bg-[#3b82f6] mr-3"></div>
            Professional Experience
          </h2>
          <div className="space-y-6">
            {experience.slice(0, 4).map((exp, index) => (
              <div key={index} className="bg-white p-4 rounded-lg shadow-sm">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-bold text-[#2563eb] text-base">
                    {exp.position}
                  </h3>
                  <span className="text-xs text-white bg-[#2563eb] px-2 py-1 rounded">
                    {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                  </span>
                </div>
                <p className="text-sm font-semibold text-[#3b82f6] mb-1">
                  {exp.company}
                </p>
                <p className="text-xs text-[#2563eb]/70 mb-3">
                  {exp.location}
                </p>
                <div className="text-xs text-[#2563eb] space-y-1">
                  {exp.description.slice(0, 3).map((desc, i) => (
                    <p key={i} className="flex items-start">
                      <TrendingUp className="w-3 h-3 mr-2 mt-0.5 text-[#3b82f6] flex-shrink-0" />
                      {desc}
                    </p>
                  ))}
                </div>
                {exp.skills && exp.skills.length > 0 && (
                  <div className="mt-3">
                    <div className="flex flex-wrap gap-1">
                      {exp.skills.slice(0, 4).map((skill, i) => (
                        <span key={i} className="text-xs bg-[#dbeafe] text-[#2563eb] px-2 py-1 rounded border border-[#3b82f6]/20">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Marketing Expertise */}
        <div>
          <h2 className="text-2xl font-bold text-[#2563eb] mb-4 flex items-center">
            <div className="w-1 h-8 bg-[#3b82f6] mr-3"></div>
            Marketing Expertise
          </h2>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-white p-3 rounded-lg shadow-sm">
              <h3 className="font-semibold text-sm text-[#2563eb] mb-2 flex items-center">
                <Megaphone className="w-4 h-4 mr-2" />
                Channels
              </h3>
              <div className="space-y-1">
                {[
                  'Digital Marketing',
                  'Social Media',
                  'Email Marketing',
                  'Content Marketing'
                ].map((channel, index) => (
                  <div key={index} className="flex items-center">
                    <Target className="w-3 h-3 mr-2 text-[#3b82f6]" />
                    <span className="text-xs text-[#2563eb]">{channel}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="bg-white p-3 rounded-lg shadow-sm">
              <h3 className="font-semibold text-sm text-[#2563eb] mb-2 flex items-center">
                <Users className="w-4 h-4 mr-2" />
                Specialties
              </h3>
              <div className="space-y-1">
                {[
                  'Campaign Management',
                  'Lead Generation',
                  'Brand Strategy',
                  'Analytics & ROI'
                ].map((specialty, index) => (
                  <div key={index} className="flex items-center">
                    <TrendingUp className="w-3 h-3 mr-2 text-[#3b82f6]" />
                    <span className="text-xs text-[#2563eb]">{specialty}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
