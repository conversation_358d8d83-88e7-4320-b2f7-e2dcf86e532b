import { Phone, Mail, MapPin, Globe } from 'lucide-react';

export function DynamicTemplate() {
  const resumeData = {
    personalInfo: {
      name: "JANE DOE",
      title: "Software Developer",
      contact: {
        phone: "(*************",
        email: "<EMAIL>",
        location: "San Francisco, CA",
        website: "www.janedoe.dev"
      },
    },
    summary: "Highly motivated and results-oriented Software Developer with a passion for building robust and scalable web applications. Experienced in full-stack development using modern technologies and frameworks. Eager to leverage strong problem-solving skills and a collaborative spirit to contribute to innovative projects.",
    skills: [
      "JavaScript",
      "React",
      "Node.js",
      "Tailwind CSS",
      "Python",
      "SQL",
      "Git",
      "REST APIs",
      "Agile Methodologies",
      "Problem-Solving",
      "Teamwork",
    ],
    experience: [
      {
        title: "Software Developer",
        company: "Innovate Solutions Inc.",
        location: "San Francisco, CA",
        dates: "Jan 2022 – Present",
        responsibilities: [
          "Developed and maintained responsive web applications using React and Node.js.",
          "Collaborated with a team of developers and designers to build new features and improve user experience.",
          "Implemented RESTful APIs for data communication between front-end and back-end.",
          "Participated in code reviews and mentored junior developers.",
        ],
      },
      {
        title: "Junior Developer",
        company: "Tech Startups LLC",
        location: "San Francisco, CA",
        dates: "Jun 2020 – Dec 2021",
        responsibilities: [
          "Assisted in the development of a new e-commerce platform.",
          "Wrote clean, efficient, and well-documented code.",
          "Debugged and resolved software defects and issues.",
        ],
      },
    ],
    education: [
      {
        degree: "Master of Science in Computer Science",
        university: "University of Technology",
        location: "Berkeley, CA",
        dates: "May 2020",
      },
      {
        degree: "Bachelor of Science in Information Technology",
        university: "State University",
        location: "San Jose, CA",
        dates: "May 2018",
      },
    ],
  };

  const { personalInfo, summary, skills, experience, education } = resumeData;

  return (
    <div className="bg-white mx-auto w-[900px] print:w-[794px] shadow-[0_4px_16px_rgba(0,0,0,0.08)] border border-gray-200">
      <div className="px-12 py-10">
        <div className="flex flex-col items-center text-center mb-8">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 tracking-wide mb-2">{personalInfo.name}</h1>
          <p className="text-xl text-teal-600 mt-2 font-medium">{personalInfo.title}</p>
          <div className="flex flex-wrap justify-center items-center mt-4 text-gray-600 text-sm md:text-base space-x-2 md:space-x-4">
            <p className="flex items-center">
              <Phone size={16} className="mr-1 text-teal-600" />
              {personalInfo.contact.phone}
            </p>
            <p className="flex items-center">
              <Mail size={16} className="mr-1 text-teal-600" />
              {personalInfo.contact.email}
            </p>
            <p className="flex items-center">
              <MapPin size={16} className="mr-1 text-teal-600" />
              {personalInfo.contact.location}
            </p>
            {personalInfo.contact.website && (
              <p className="flex items-center">
                <Globe size={16} className="mr-1 text-teal-600" />
                {personalInfo.contact.website}
              </p>
            )}
          </div>
        </div>

        {/* Section: Professional Summary */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-700 border-b-2 border-gray-300 pb-2 mb-4 tracking-wide">PROFESSIONAL SUMMARY</h2>
          <p className="text-sm leading-relaxed text-gray-800">{summary}</p>
        </div>

        {/* Section: Skills */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-700 border-b-2 border-gray-300 pb-2 mb-4 tracking-wide">SKILLS</h2>
          <div className="flex flex-wrap gap-2 text-sm">
            {skills.map((skill, index) => (
              <span key={index} className="bg-gray-200 text-gray-800 px-3 py-1 rounded-full font-medium">
                {skill}
              </span>
            ))}
          </div>
        </div>

        {/* Section: Experience */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-700 border-b-2 border-gray-300 pb-2 mb-4 tracking-wide">EXPERIENCE</h2>
          {experience.map((job, index) => (
            <div key={index} className="mb-6">
              <div className="flex justify-between items-baseline mb-1">
                <h3 className="text-lg font-semibold text-gray-800">{job.title}</h3>
                <p className="text-sm text-gray-500">{job.dates}</p>
              </div>
              <div className="flex justify-between items-baseline">
                <p className="text-md font-medium text-gray-700">{job.company}</p>
                <p className="text-sm text-gray-500">{job.location}</p>
              </div>
              <ul className="list-disc list-inside mt-2 text-sm text-gray-800 space-y-1">
                {job.responsibilities.map((resp, i) => (
                  <li key={i}>{resp}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Section: Education */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-700 border-b-2 border-gray-300 pb-2 mb-4 tracking-wide">EDUCATION</h2>
          {education.map((edu, index) => (
            <div key={index} className="mb-4">
              <div className="flex justify-between items-baseline mb-1">
                <h3 className="text-lg font-semibold text-gray-800">{edu.degree}</h3>
                <p className="text-sm text-gray-500">{edu.dates}</p>
              </div>
              <div className="flex justify-between items-baseline">
                <p className="text-md font-medium text-gray-700">{edu.university}</p>
                <p className="text-sm text-gray-500">{edu.location}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}