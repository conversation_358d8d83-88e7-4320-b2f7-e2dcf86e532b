import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, Palette, Figma, Monitor, Smartphone } from 'lucide-react';

interface UxDesignerBlueAccentsTemplateProps {
  resumeData: ResumeContent;
}

export default function UxDesignerBlueAccentsTemplate({ resumeData }: UxDesignerBlueAccentsTemplateProps) {
  const { personalInfo, summary, experience, education, skills, projects } = resumeData;

  // Mock UX projects if none provided
  const uxProjects = projects && projects.length > 0 ? projects : [
    { id: '1', name: 'E-commerce Mobile App', description: 'Complete UX redesign increasing conversion by 35%' },
    { id: '2', name: 'SaaS Dashboard', description: 'User-centered design for analytics platform' },
    { id: '3', name: 'Healthcare Portal', description: 'Accessible design for patient management system' }
  ];

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="john-doe-ux-blue"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Header with Blue Accents */}
      <div className="absolute top-0 left-0 w-full h-[100px] bg-gradient-to-r from-[#2563eb] to-[#3b82f6] px-8 py-6">
        <div className="flex items-center justify-between h-full">
          <div>
            <h1 className="text-3xl font-bold text-white mb-1">
              {personalInfo.fullName || 'John Doe'}
            </h1>
            <p className="text-lg text-white/90 flex items-center">
              <Palette className="w-5 h-5 mr-2" />
              {personalInfo.jobTitle || 'UX Designer'}
            </p>
          </div>
          
          {/* Contact Icons with Blue Accents */}
          <div className="flex gap-3">
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <Mail className="w-5 h-5 text-white" />
            </div>
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <Phone className="w-5 h-5 text-white" />
            </div>
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <Globe className="w-5 h-5 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Contact Info Strip */}
      <div className="absolute top-[100px] left-0 w-full h-[30px] bg-[#dbeafe] px-8 flex items-center justify-center">
        <div className="flex gap-8 text-[#2563eb] text-sm font-medium">
          <span>{personalInfo.email}</span>
          <span>{personalInfo.phone}</span>
          <span>{personalInfo.location}</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[150px] left-0 w-full h-[692px] px-8 py-6">
        
        {/* Professional Summary */}
        {summary && (
          <div className="mb-8">
            <h2 className="text-xl font-bold text-[#2563eb] mb-4 border-b-2 border-[#3b82f6] pb-2">
              About Me
            </h2>
            <div className="bg-[#dbeafe] p-4 rounded-lg">
              <p className="text-sm text-[#2563eb] leading-relaxed">
                {summary}
              </p>
            </div>
          </div>
        )}

        {/* Two Column Layout */}
        <div className="grid grid-cols-3 gap-8">
          
          {/* Left Column - Experience & Projects */}
          <div className="col-span-2">
            
            {/* Professional Experience */}
            <div className="mb-8">
              <h2 className="text-xl font-bold text-[#2563eb] mb-4 border-b-2 border-[#3b82f6] pb-2">
                Professional Experience
              </h2>
              <div className="space-y-6">
                {experience.slice(0, 3).map((exp, index) => (
                  <div key={index} className="relative">
                    {/* Blue accent line */}
                    <div className="absolute left-0 top-0 w-1 h-full bg-[#3b82f6] rounded"></div>
                    <div className="pl-6">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-bold text-[#2563eb] text-base">
                          {exp.position}
                        </h3>
                        <span className="text-sm text-white bg-[#2563eb] px-3 py-1 rounded-full">
                          {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                        </span>
                      </div>
                      <p className="text-sm font-semibold text-[#3b82f6] mb-2">
                        {exp.company} • {exp.location}
                      </p>
                      <div className="text-sm text-[#333] space-y-1">
                        {exp.description.slice(0, 3).map((desc, i) => (
                          <p key={i} className="flex items-start">
                            <div className="w-2 h-2 bg-[#3b82f6] rounded-full mr-2 mt-2 flex-shrink-0"></div>
                            {desc}
                          </p>
                        ))}
                      </div>
                      {exp.skills && exp.skills.length > 0 && (
                        <div className="mt-3">
                          <div className="flex flex-wrap gap-2">
                            {exp.skills.slice(0, 4).map((skill, i) => (
                              <span key={i} className="text-xs bg-[#dbeafe] text-[#2563eb] px-2 py-1 rounded border border-[#3b82f6]/20">
                                {skill}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* UX Projects */}
            <div>
              <h2 className="text-xl font-bold text-[#2563eb] mb-4 border-b-2 border-[#3b82f6] pb-2">
                Featured Projects
              </h2>
              <div className="space-y-4">
                {uxProjects.slice(0, 3).map((project, index) => (
                  <div key={index} className="bg-[#dbeafe] p-4 rounded-lg border-l-4 border-[#2563eb]">
                    <h3 className="font-bold text-[#2563eb] text-sm flex items-center">
                      <Monitor className="w-4 h-4 mr-2" />
                      {project.name}
                    </h3>
                    <p className="text-sm text-[#2563eb]/80 mt-1">
                      {project.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Skills & Tools */}
          <div className="col-span-1">
            
            {/* UX Skills */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#2563eb] mb-4">
                UX Skills
              </h2>
              <div className="space-y-3">
                {skills.slice(0, 6).map((skill, index) => {
                  const progress = [95, 90, 85, 88, 82, 87][index] || 80;
                  return (
                    <div key={index}>
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-sm font-medium text-[#333]">{skill}</span>
                        <span className="text-xs text-[#2563eb]">{progress}%</span>
                      </div>
                      <div className="w-full bg-[#dbeafe] rounded-full h-2">
                        <div 
                          className="bg-[#2563eb] h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        ></div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Design Tools */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#2563eb] mb-4">
                Design Tools
              </h2>
              <div className="grid grid-cols-2 gap-2">
                {[
                  { name: 'Figma', icon: Figma },
                  { name: 'Sketch', icon: Palette },
                  { name: 'Adobe XD', icon: Monitor },
                  { name: 'Principle', icon: Smartphone },
                  { name: 'InVision', icon: Globe },
                  { name: 'Miro', icon: Palette }
                ].map((tool, index) => (
                  <div key={index} className="bg-[#dbeafe] p-2 rounded text-center border border-[#2563eb]/20">
                    <tool.icon className="w-4 h-4 mx-auto mb-1 text-[#2563eb]" />
                    <span className="text-xs font-medium text-[#2563eb]">{tool.name}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Education */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#2563eb] mb-4">
                Education
              </h2>
              <div className="space-y-3">
                {education.slice(0, 2).map((edu, index) => (
                  <div key={index} className="bg-[#dbeafe] p-3 rounded-lg">
                    <h3 className="font-bold text-sm text-[#2563eb]">
                      {edu.degree}
                    </h3>
                    <p className="text-xs text-[#3b82f6] font-medium">
                      {edu.institution}
                    </p>
                    <p className="text-xs text-[#2563eb]/70">
                      {new Date(edu.startDate).getFullYear()} - {edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* UX Methodologies */}
            <div className="mb-8">
              <h2 className="text-lg font-bold text-[#2563eb] mb-4">
                UX Methods
              </h2>
              <div className="space-y-2">
                {[
                  'User Research',
                  'Wireframing',
                  'Prototyping',
                  'Usability Testing',
                  'Design Systems',
                  'A/B Testing'
                ].map((method, index) => (
                  <div key={index} className="flex items-center">
                    <div className="w-2 h-2 bg-[#2563eb] rounded-full mr-2"></div>
                    <span className="text-sm text-[#333]">{method}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Links */}
            <div>
              <h2 className="text-lg font-bold text-[#2563eb] mb-4">
                Portfolio Links
              </h2>
              <div className="space-y-2">
                {personalInfo.website && (
                  <div className="flex items-center text-sm text-[#2563eb]">
                    <Globe className="w-4 h-4 mr-2" />
                    <span>Portfolio</span>
                  </div>
                )}
                {personalInfo.linkedinUrl && (
                  <div className="flex items-center text-sm text-[#2563eb]">
                    <Linkedin className="w-4 h-4 mr-2" />
                    <span>LinkedIn</span>
                  </div>
                )}
                <div className="flex items-center text-sm text-[#2563eb]">
                  <Palette className="w-4 h-4 mr-2" />
                  <span>Dribbble</span>
                </div>
                <div className="flex items-center text-sm text-[#2563eb]">
                  <Figma className="w-4 h-4 mr-2" />
                  <span>Behance</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
