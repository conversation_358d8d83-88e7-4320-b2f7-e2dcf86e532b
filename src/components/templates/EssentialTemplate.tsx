import React from "react";
import { Mail, Linkedin, MapPin, CheckCircle2, ChevronRight, BookOpen, Award, Target, Users } from "lucide-react";

// Small helper to render dot proficiency like in the image
const Dots = ({ filled = 5, total = 6 }: { filled?: number; total?: number }) => (
  <div className="flex gap-1 mt-1">
    {Array.from({ length: total }).map((_, i) => (
      <span
        key={i}
        className={
          "inline-block h-2.5 w-2.5 rounded-full " +
          (i < filled ? "bg-[#1E88E5]" : "bg-gray-200")
        }
      />
    ))}
  </div>
);

const Rule = ({ dark = false }: { dark?: boolean }) => (
  <div className={dark ? "h-px bg-white/15 w-full" : "h-px bg-gray-200 w-full"} />
);

// === PIXEL-FAITHFUL TEMPLATE ===
// Designed to mirror the provided image: left light column + right dark sidebar,
// uppercase section headers with thin rules, blue accents, compact vertical rhythm.

export default function EssentialTemplate() {
  return (
    <div className="mx-auto bg-white w-[900px] print:w-[794px] shadow-[0_4px_16px_rgba(0,0,0,0.08)] border border-gray-200">
      <div className="flex">
        {/* LEFT COLUMN (≈66%) */}
        <div className="w-2/3 px-10 pt-10 pb-8">
          {/* Header */}
          <header>
            <h1 className="text-[34px] leading-none tracking-tight font-extrabold text-gray-900">SOPHIA MARTINEZ</h1>
            <p className="mt-1 text-[14px] text-[#1E88E5] font-semibold">
              Senior Project Manager | Digital Transformation | Process Optimization | Scrum Master
            </p>
            <div className="mt-2 flex items-center gap-3 text-[12px] text-gray-600">
              <span className="inline-flex items-center gap-1"><Mail size={14} /> <EMAIL></span>
              <span className="text-gray-300">•</span>
              <span className="inline-flex items-center gap-1"><Linkedin size={14} /> LinkedIn</span>
              <span className="text-gray-300">•</span>
              <span className="inline-flex items-center gap-1"><MapPin size={14} /> Denver, Colorado</span>
            </div>
          </header>

          {/* SUMMARY */}
          <section className="mt-7">
            <div className="flex items-end gap-3">
              <h2 className="text-[12px] tracking-[0.18em] text-gray-800 font-semibold">SUMMARY</h2>
              <Rule />
            </div>
            <p className="mt-2 text-[13px] leading-relaxed text-gray-700">
              With over 6 years of project management experience, I excel in leading digital transformation initiatives and process optimization projects. My expertise in Scrum methodologies and cross-functional team leadership has resulted in a 35% improvement in project efficiency and enhanced stakeholder satisfaction across diverse industries.
            </p>
          </section>

          {/* EXPERIENCE */}
          <section className="mt-7">
            <div className="flex items-end gap-3">
              <h2 className="text-[12px] tracking-[0.18em] text-gray-800 font-semibold">EXPERIENCE</h2>
              <Rule />
            </div>

            {/* Role 1 */}
            <div className="mt-4">
              <div className="flex items-baseline justify-between">
                <h3 className="text-[15px] font-bold text-gray-900">Senior Project Manager</h3>
                <span className="text-[12px] text-gray-600">03/2022 - Present</span>
              </div>
              <div className="flex items-baseline justify-between">
                <a className="text-[13px] font-semibold text-[#1E88E5]">NexaDigital Solutions</a>
                <span className="text-[12px] text-gray-600">Denver, Colorado</span>
              </div>
              <ul className="mt-2 space-y-1.5 text-[13px] text-gray-700 list-disc list-inside">
                <li>Led digital transformation initiatives for enterprise clients, managing complex projects with budgets exceeding $3M while maintaining strict timelines.</li>
                <li>Implemented comprehensive project governance frameworks and conducted weekly stakeholder meetings, improving project transparency by 40%.</li>
                <li>Deployed advanced project management platforms resulting in a 35% enhancement in team collaboration and resource visibility.</li>
                <li>Executed proactive risk management strategies that eliminated 85% of potential project roadblocks and ensured on-time delivery.</li>
                <li>Built strategic partnerships with key stakeholders, achieving a 98% client retention rate and securing $8M in additional project contracts.</li>
              </ul>
            </div>

            {/* Role 2 */}
            <div className="mt-5">
              <div className="flex items-baseline justify-between">
                <h3 className="text-[15px] font-bold text-gray-900">Project Manager</h3>
                <span className="text-[12px] text-gray-600">06/2019 - 02/2022</span>
              </div>
              <div className="flex items-baseline justify-between">
                <a className="text-[13px] font-semibold text-[#1E88E5]">Alpine Tech Partners</a>
                <span className="text-[12px] text-gray-600">Denver, Colorado</span>
              </div>
              <ul className="mt-2 space-y-1.5 text-[13px] text-gray-700 list-disc list-inside">
                <li>Managed end-to-end software development projects for healthcare and finance sectors, ensuring regulatory compliance and quality standards.</li>
                <li>Developed comprehensive project proposals and business cases that secured $4.2M in new client contracts with projected ROI of 28% over 3 years.</li>
                <li>Implemented Jira and Confluence workflows that increased project visibility and reduced missed deliverables by 60%.</li>
                <li>Established agile retrospective processes and team communication protocols that accelerated issue resolution by 50%.</li>
                <li>Created standardized project documentation templates and best practices that became company-wide standards for project delivery.</li>
              </ul>
            </div>

            {/* Role 3 */}
            <div className="mt-5">
              <div className="flex items-baseline justify-between">
                <h3 className="text-[15px] font-bold text-gray-900">Junior Project Coordinator</h3>
                <span className="text-[12px] text-gray-600">08/2017 - 05/2019</span>
              </div>
              <div className="flex items-baseline justify-between">
                <a className="text-[13px] font-semibold text-[#1E88E5]">CloudWave Technologies</a>
                <span className="text-[12px] text-gray-600">Boulder, Colorado</span>
              </div>
              <ul className="mt-2 space-y-1.5 text-[13px] text-gray-700 list-disc list-inside">
                <li>Facilitated communication across development, design, and QA teams to ensure project objectives were clearly understood and executed.</li>
                <li>Collaborated on project timeline development and milestone tracking, contributing to a 25% improvement in on-time delivery rates.</li>
                <li>Tracked project expenses and generated detailed financial reports for stakeholders, identifying cost-saving opportunities worth $150K annually.</li>
                <li>Completed Scrum Master certification and introduced agile practices that improved team velocity and sprint completion rates by 30%.</li>
                <li>Supported business development efforts through project demonstrations and client presentations, contributing to $1.8M in new business.</li>
              </ul>
            </div>
          </section>

          {/* EDUCATION */}
          <section className="mt-7">
            <div className="flex items-end gap-3">
              <h2 className="text-[12px] tracking-[0.18em] text-gray-800 font-semibold">EDUCATION</h2>
              <Rule />
            </div>
            <div className="mt-3">
              <p className="text-[13px] font-semibold text-gray-900">Master of Science in Information Systems</p>
              <p className="text-[12px] text-gray-600">University of Colorado Denver</p>
              <p className="text-[12px] text-gray-500">08/2019 - 05/2021 · Denver, Colorado</p>
            </div>
            <div className="mt-3">
              <p className="text-[13px] font-semibold text-gray-900">Bachelor of Science in Business Administration</p>
              <p className="text-[12px] text-gray-600">Colorado State University</p>
              <p className="text-[12px] text-gray-500">08/2013 - 05/2017 · Fort Collins, Colorado</p>
            </div>
          </section>

          {/* LANGUAGES — stays in LEFT column in this design */}
          <section className="mt-7">
            <div className="flex items-end gap-3">
              <h2 className="text-[12px] tracking-[0.18em] text-gray-800 font-semibold">LANGUAGES</h2>
              <Rule />
            </div>
            <div className="mt-3 grid gap-3">
              <div>
                <div className="flex items-center justify-between">
                  <p className="text-[13px] text-gray-900">English</p>
                  <span className="text-[12px] text-gray-500">Native</span>
                </div>
                <Dots filled={6} total={6} />
              </div>
              <div>
                <div className="flex items-center justify-between">
                  <p className="text-[13px] text-gray-900">French</p>
                  <span className="text-[12px] text-gray-500">Conversational</span>
                </div>
                <Dots filled={3} total={6} />
              </div>
            </div>
          </section>
        </div>

        {/* RIGHT SIDEBAR (≈34%) */}
        <aside className="w-1/3 bg-[#1F2B3B] text-white px-7 pt-10 pb-8 border-l border-gray-200/20">
          {/* TRAINING/COURSES */}
          <section>
            <div className="flex items-end gap-3">
              <h3 className="text-[12px] tracking-[0.2em] font-semibold">TRAINING / COURSES</h3>
              <Rule dark />
            </div>
            <ul className="mt-3 space-y-3 text-[13px] leading-relaxed">
              <li>
                <p className="font-semibold">Advanced Certified Scrum Master (A-CSM)</p>
                <p className="text-white/80">Achieved advanced certification through Scrum Alliance, demonstrating expertise in scaling agile practices across enterprise organizations.</p>
              </li>
              <li>
                <p className="font-semibold">Project Management Professional (PMP)</p>
                <p className="text-white/80">Earned PMP certification from PMI, validating comprehensive knowledge of project management principles and best practices.</p>
              </li>
            </ul>
          </section>

          {/* SKILLS */}
          <section className="mt-7">
            <div className="flex items-end gap-3">
              <h3 className="text-[12px] tracking-[0.2em] font-semibold">SKILLS</h3>
              <Rule dark />
            </div>
            <div className="mt-3 text-[13px] text-white/90">
              Digital Transformation • Scrum & Kanban • Jira & Confluence • Monday.com • Team Leadership • Stakeholder Management
            </div>
          </section>

          {/* KEY ACHIEVEMENTS */}
          <section className="mt-7">
            <div className="flex items-end gap-3">
              <h3 className="text-[12px] tracking-[0.2em] font-semibold">KEY ACHIEVEMENTS</h3>
              <Rule dark />
            </div>
            <ul className="mt-3 space-y-3">
              <li className="flex gap-3">
                <div className="mt-0.5"><CheckCircle2 className="h-4 w-4 text-[#1E88E5]"/></div>
                <div className="text-[13px]">
                  <p className="font-semibold">Digital Transformation Excellence</p>
                  <p className="text-white/80">Orchestrated digital transformation initiatives at NexaDigital, achieving 35% improvement in project efficiency and reducing delivery timelines.</p>
                </div>
              </li>
              <li className="flex gap-3">
                <div className="mt-0.5"><Award className="h-4 w-4 text-[#1E88E5]"/></div>
                <div className="text-[13px]">
                  <p className="font-semibold">Project Management Innovation Award</p>
                  <p className="text-white/80">Recognized with company-wide excellence award for implementing innovative project governance frameworks that improved transparency by 40%.</p>
                </div>
              </li>
              <li className="flex gap-3">
                <div className="mt-0.5"><Target className="h-4 w-4 text-[#1E88E5]"/></div>
                <div className="text-[13px]">
                  <p className="font-semibold">Agile Transformation Leadership</p>
                  <p className="text-white/80">Successfully led enterprise-wide agile adoption at Alpine Tech Partners, resulting in 50% faster issue resolution and improved team velocity.</p>
                </div>
              </li>
              <li className="flex gap-3">
                <div className="mt-0.5"><Users className="h-4 w-4 text-[#1E88E5]"/></div>
                <div className="text-[13px]">
                  <p className="font-semibold">Stakeholder Engagement Excellence</p>
                  <p className="text-white/80">Maintained 98% client retention rate through strategic stakeholder management and proactive communication, securing $8M in additional contracts.</p>
                </div>
              </li>
            </ul>
          </section>

          {/* INTERESTS */}
          <section className="mt-7">
            <div className="flex items-end gap-3">
              <h3 className="text-[12px] tracking-[0.2em] font-semibold">INTERESTS</h3>
              <Rule dark />
            </div>
            <ul className="mt-3 space-y-3 text-[13px]">
              <li className="flex gap-3">
                <div className="mt-0.5"><BookOpen className="h-4 w-4 text-[#1E88E5]"/></div>
                <div>
                  <p className="font-semibold">Digital Innovation & AI</p>
                  <p className="text-white/80">Passionate about exploring artificial intelligence and machine learning applications in project management to drive automation and predictive analytics.</p>
                </div>
              </li>
              <li className="flex gap-3">
                <div className="mt-0.5"><Users className="h-4 w-4 text-[#1E88E5]"/></div>
                <div>
                  <p className="font-semibold">Mentorship & Leadership Development</p>
                  <p className="text-white/80">Dedicated to mentoring emerging project managers and developing leadership capabilities within cross-functional teams to drive organizational success.</p>
                </div>
              </li>
            </ul>
          </section>
        </aside>
      </div>
    </div>
  );
}