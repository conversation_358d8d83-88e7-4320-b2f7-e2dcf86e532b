import React from 'react';

const imgBcfec5A3A40C46228BccB839C1Fae37BRemovebg1 = "http://localhost:3845/assets/ac07f4d3f05c24467857e512fcc45fca8e1844d1.png";
const imgEllipse6 = "http://localhost:3845/assets/3b82bc90f838407f4db5f90b7f5434baaf2312ff.svg";
const imgBcfec5A3A40C46228BccB839C1Fae37BRemovebg2 = "http://localhost:3845/assets/1018d69684b8e180e7a4ef8f3782ec6a2d3d67fe.svg";
const imgSimpleIconsLinkedin = "http://localhost:3845/assets/5b83e034e36b176500a7f9170399ffacba810e88.svg";
const imgSimpleIconsGithub = "http://localhost:3845/assets/8dd090a450dc7a702bcd76e39e28537ed7f5f37e.svg";

interface ResumeData {
  personalInfo: {
    name: string;
    jobTitle: string;
    birthdate: string;
    phone: string;
    email: string;
    location: string;
    photo?: string;
  };
  skills: string[];
  awards: Array<{
    title: string;
    date: string;
    description: string;
  }>;
  hobbies: string[];
  links: {
    linkedin?: string;
    github?: string;
    dribbble?: string;
    instagram?: string;
  };
  experience: Array<{
    company: string;
    jobTitle: string;
    dates: string;
    description: string;
    logo?: string;
  }>;
  education: Array<{
    degree: string;
    institution: string;
    dates: string;
  }>;
  volunteer?: Array<{
    organization: string;
    role: string;
    dates: string;
    description: string;
  }>;
}

interface ModernPhotoCVTemplateProps {
  data: ResumeData;
  isPreview?: boolean;
}

export default function ModernPhotoCVTemplate({ data, isPreview = false }: ModernPhotoCVTemplateProps) {
  return (
    <div className="relative size-full" data-template-id="modern-photo-cv">
      <div className="absolute bg-white h-[842px] left-0 overflow-clip top-0 w-[595px]">
        {/* Sidebar */}
        <div className="absolute h-[842px] left-0 top-0 w-[210px]">
          <div className="absolute bg-[#2e235f] h-[842px] left-0 top-0 w-[210px]" />
          <div className="absolute content-stretch flex flex-col gap-5 items-start justify-start left-3 top-[31px]">
            
            {/* Profile Picture */}
            <div className="grid-cols-[max-content] grid-rows-[max-content] inline-grid leading-[0] place-items-start relative shrink-0">
              <div className="[grid-area:1_/_1] ml-0 mt-0 relative size-[100px]">
                <img alt className="block max-w-none size-full" src={imgEllipse6} />
              </div>
              <div className="[grid-area:1_/_1] grid-cols-[max-content] grid-rows-[max-content] inline-grid ml-0 mt-0 place-items-start relative">
                <div className="[grid-area:1_/_1] bg-center bg-cover bg-no-repeat h-[150.146px] mask-alpha mask-intersect mask-no-clip mask-no-repeat mask-position-[14.869px_47.522px] mask-size-[100px_100px] ml-[-14.869px] mt-[-47.522px] w-[202.915px]" 
                     style={{ 
                       backgroundImage: `url('${data.personalInfo.photo || imgBcfec5A3A40C46228BccB839C1Fae37BRemovebg1}')`, 
                       maskImage: `url('${imgBcfec5A3A40C46228BccB839C1Fae37BRemovebg2}')` 
                     }} />
              </div>
            </div>

            {/* Profile Info */}
            <div className="content-stretch flex flex-col items-start justify-start leading-[0] not-italic relative shrink-0 text-center text-nowrap w-[186px]">
              <div className="font-['Poppins:SemiBold',_sans-serif] relative shrink-0 text-[18px] text-white">
                <p className="leading-[normal] text-nowrap whitespace-pre">{data.personalInfo.name}</p>
              </div>
              <div className="font-['Poppins:Regular',_sans-serif] relative shrink-0 text-[#c3c2c2] text-[10px]">
                <p className="leading-[normal] text-nowrap whitespace-pre">{data.personalInfo.jobTitle}</p>
              </div>
            </div>

            {/* Contact Info */}
            <div className="content-stretch flex flex-col gap-2 items-start justify-start relative shrink-0 w-full">
              <div className="bg-neutral-200 box-border content-stretch flex gap-2.5 items-start justify-start px-2 py-1 relative rounded-[4px] shrink-0 w-full">
                <div className="font-['Poppins:Regular',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#2e235f] text-[10px] text-center text-nowrap">
                  <p className="leading-[normal] whitespace-pre">{data.personalInfo.birthdate}</p>
                </div>
              </div>
              <div className="bg-neutral-200 box-border content-stretch flex gap-2.5 items-start justify-start px-2 py-1 relative rounded-[4px] shrink-0 w-full">
                <div className="font-['Poppins:Regular',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#2e235f] text-[10px] text-center text-nowrap">
                  <p className="leading-[normal] whitespace-pre">{data.personalInfo.phone}</p>
                </div>
              </div>
              <div className="bg-neutral-200 box-border content-stretch flex gap-2.5 items-start justify-start px-2 py-1 relative rounded-[4px] shrink-0 w-full">
                <div className="font-['Poppins:Regular',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#2e235f] text-[10px] text-center text-nowrap">
                  <p className="leading-[normal] whitespace-pre">{data.personalInfo.email}</p>
                </div>
              </div>
              <div className="bg-neutral-200 box-border content-stretch flex gap-2.5 items-start justify-start px-2 py-1 relative rounded-[4px] shrink-0 w-full">
                <div className="font-['Poppins:Regular',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#2e235f] text-[10px] text-center text-nowrap">
                  <p className="leading-[normal] whitespace-pre">{data.personalInfo.location}</p>
                </div>
              </div>
            </div>

            {/* Skills Section */}
            <div className="content-stretch flex flex-col gap-3 items-start justify-start relative shrink-0 w-full">
              <div className="font-['Poppins:SemiBold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[12px] text-center text-nowrap text-white">
                <p className="leading-[normal] whitespace-pre">Skills</p>
              </div>
              <div className="font-['Poppins:Regular',_sans-serif] leading-[0] not-italic relative shrink-0 text-[10px] text-white w-[186px]">
                {data.skills.map((skill, index) => (
                  <p key={index} className="leading-[normal] mb-1">{skill}</p>
                ))}
              </div>
            </div>

            {/* Awards Section */}
            {data.awards && data.awards.length > 0 && (
              <div className="content-stretch flex flex-col gap-3 items-start justify-start relative shrink-0 w-full">
                <div className="font-['Poppins:SemiBold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[12px] text-center text-nowrap text-white">
                  <p className="leading-[normal] whitespace-pre">Awards & Achievements</p>
                </div>
                {data.awards.map((award, index) => (
                  <div key={index} className="content-stretch flex flex-col gap-1 items-start justify-start relative shrink-0 w-full">
                    <div className="content-stretch flex items-start justify-between relative shrink-0 w-full">
                      <div className="font-['Poppins:SemiBold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[10px] text-white">
                        <p className="leading-[normal]">{award.title}</p>
                      </div>
                      <div className="font-['Poppins:Regular',_sans-serif] leading-[0] not-italic relative shrink-0 text-[10px] text-white text-nowrap">
                        <p className="leading-[normal] whitespace-pre">{award.date}</p>
                      </div>
                    </div>
                    <div className="font-['Poppins:Regular',_sans-serif] leading-[0] not-italic relative shrink-0 text-[9px] text-white w-full">
                      <p className="leading-[normal]">{award.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Hobbies Section */}
            {data.hobbies && data.hobbies.length > 0 && (
              <div className="content-stretch flex flex-col gap-3 items-start justify-start relative shrink-0 w-full">
                <div className="font-['Poppins:SemiBold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[12px] text-center text-nowrap text-white">
                  <p className="leading-[normal] whitespace-pre">Hobbies & Interests</p>
                </div>
                <div className="font-['Poppins:Regular',_sans-serif] leading-[0] not-italic relative shrink-0 text-[10px] text-white w-full">
                  <p className="leading-[normal]">{data.hobbies.join(', ')}</p>
                </div>
              </div>
            )}

            {/* Links Section */}
            <div className="content-stretch flex flex-col gap-3 items-start justify-start relative shrink-0 w-full">
              <div className="font-['Poppins:SemiBold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[12px] text-center text-nowrap text-white">
                <p className="leading-[normal] whitespace-pre">Professional Links</p>
              </div>
              <div className="content-stretch flex flex-col gap-2 items-start justify-start relative shrink-0 w-full">
                {data.links.linkedin && (
                  <div className="content-stretch flex gap-2 items-center justify-start relative shrink-0">
                    <div className="relative shrink-0 size-3.5">
                      <img alt className="block max-w-none size-full" src={imgSimpleIconsLinkedin} />
                    </div>
                    <div className="font-['Poppins:Regular',_sans-serif] leading-[0] not-italic relative shrink-0 text-[10px] text-white text-nowrap">
                      <p className="leading-[normal] whitespace-pre">LinkedIn</p>
                    </div>
                  </div>
                )}
                {data.links.github && (
                  <div className="content-stretch flex gap-2 items-center justify-start relative shrink-0">
                    <div className="relative shrink-0 size-3.5">
                      <img alt className="block max-w-none size-full" src={imgSimpleIconsGithub} />
                    </div>
                    <div className="font-['Poppins:Regular',_sans-serif] leading-[0] not-italic relative shrink-0 text-[10px] text-white text-nowrap">
                      <p className="leading-[normal] whitespace-pre">GitHub</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="absolute h-[842px] left-[210px] top-0 w-[385px] bg-white p-6">
          {/* Experience Section */}
          <div className="mb-8">
            <div className="content-stretch flex flex-col gap-1 items-start justify-start relative shrink-0 mb-4">
              <div className="font-['Poppins:SemiBold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#2e235f] text-[12px] text-center text-nowrap">
                <p className="leading-[normal] whitespace-pre">Work Experience</p>
              </div>
              <div className="bg-[#2e235f] h-0.5 rounded-[4px] shrink-0 w-8" />
            </div>
            
            {data.experience.map((exp, index) => (
              <div key={index} className="content-stretch flex gap-6 items-start justify-start relative shrink-0 w-full mb-6">
                {exp.logo && (
                  <div className="overflow-clip relative shrink-0 size-8">
                    <div className="absolute bg-center bg-cover bg-no-repeat inset-0" style={{ backgroundImage: `url('${exp.logo}')` }} />
                  </div>
                )}
                <div className="basis-0 content-stretch flex flex-col gap-1 grow items-start justify-start leading-[0] min-h-px min-w-px not-italic relative shrink-0">
                  <div className="font-['Poppins:SemiBold',_sans-serif] relative shrink-0 text-[#2a2a2a] text-[10px] text-nowrap">
                    <p className="leading-[normal] whitespace-pre">{exp.company}</p>
                  </div>
                  <div className="content-stretch flex font-['Poppins:Regular',_sans-serif] gap-1 items-start justify-start relative shrink-0 text-[#2a2a2a] text-[8px] text-nowrap">
                    <div className="relative shrink-0">
                      <p className="leading-[normal] text-nowrap whitespace-pre">{exp.jobTitle}</p>
                    </div>
                    <div className="relative shrink-0">
                      <p className="leading-[normal] text-nowrap whitespace-pre">|</p>
                    </div>
                    <div className="relative shrink-0">
                      <p className="leading-[normal] text-nowrap whitespace-pre">{exp.dates}</p>
                    </div>
                  </div>
                  <div className="font-['Poppins:Regular',_sans-serif] min-w-full relative shrink-0 text-[#878787] text-[8px]" style={{ width: "min-content" }}>
                    <p className="leading-[normal]">{exp.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Education Section */}
          {data.education && data.education.length > 0 && (
            <div className="mb-8">
              <div className="content-stretch flex flex-col gap-1 items-start justify-start relative shrink-0 mb-4">
                <div className="font-['Poppins:SemiBold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#2e235f] text-[12px] text-center text-nowrap">
                  <p className="leading-[normal] whitespace-pre">Education</p>
                </div>
                <div className="bg-[#2e235f] h-0.5 rounded-[4px] shrink-0 w-8" />
              </div>
              
              {data.education.map((edu, index) => (
                <div key={index} className="content-stretch flex flex-col gap-1 items-start justify-start leading-[0] not-italic relative shrink-0 mb-4">
                  <div className="font-['Poppins:SemiBold',_sans-serif] relative shrink-0 text-[#2a2a2a] text-[10px] text-nowrap">
                    <p className="leading-[normal] whitespace-pre">{edu.degree}</p>
                  </div>
                  <div className="font-['Poppins:Regular',_sans-serif] relative shrink-0 text-[#2a2a2a] text-[9px] text-nowrap">
                    <p className="leading-[normal] whitespace-pre">{edu.institution}</p>
                  </div>
                  <div className="font-['Poppins:Regular',_sans-serif] relative shrink-0 text-[#878787] text-[8px] text-nowrap">
                    <p className="leading-[normal] whitespace-pre">{edu.dates}</p>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Volunteer Experience */}
          {data.volunteer && data.volunteer.length > 0 && (
            <div>
              <div className="content-stretch flex flex-col gap-1 items-start justify-start relative shrink-0 mb-4">
                <div className="font-['Poppins:SemiBold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#2e235f] text-[12px] text-center text-nowrap">
                  <p className="leading-[normal] whitespace-pre">Volunteer Experience</p>
                </div>
                <div className="bg-[#2e235f] h-0.5 rounded-[4px] shrink-0 w-8" />
              </div>
              
              {data.volunteer.map((vol, index) => (
                <div key={index} className="content-stretch flex flex-col gap-1 items-start justify-start leading-[0] not-italic relative shrink-0 mb-4">
                  <div className="font-['Poppins:SemiBold',_sans-serif] relative shrink-0 text-[#2a2a2a] text-[10px] text-nowrap">
                    <p className="leading-[normal] whitespace-pre">{vol.organization}</p>
                  </div>
                  <div className="content-stretch flex font-['Poppins:Regular',_sans-serif] gap-1 items-start justify-start relative shrink-0 text-[#2a2a2a] text-[8px] text-nowrap">
                    <div className="relative shrink-0">
                      <p className="leading-[normal] text-nowrap whitespace-pre">{vol.role}</p>
                    </div>
                    <div className="relative shrink-0">
                      <p className="leading-[normal] text-nowrap whitespace-pre">|</p>
                    </div>
                    <div className="relative shrink-0">
                      <p className="leading-[normal] text-nowrap whitespace-pre">{vol.dates}</p>
                    </div>
                  </div>
                  <div className="font-['Poppins:Regular',_sans-serif] min-w-full relative shrink-0 text-[#878787] text-[8px]" style={{ width: "min-content" }}>
                    <p className="leading-[normal]">{vol.description}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
