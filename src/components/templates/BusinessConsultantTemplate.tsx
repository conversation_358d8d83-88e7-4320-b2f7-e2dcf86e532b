import React from 'react';
import { ResumeContent } from '@/types';
import { Mail, Phone, MapPin, Globe, Linkedin, Github, Briefcase, TrendingUp } from 'lucide-react';

interface BusinessConsultantTemplateProps {
  resumeData: ResumeContent;
}

export default function BusinessConsultantTemplate({ resumeData }: BusinessConsultantTemplateProps) {
  const { personalInfo, summary, experience, education, skills } = resumeData;

  return (
    <div 
      className="bg-white relative w-[594px] h-[842px] overflow-hidden"
      data-template-id="business-consultant"
      style={{ fontFamily: 'Inter, sans-serif' }}
    >
      {/* Header */}
      <div className="absolute top-0 left-0 w-full h-[120px] bg-gradient-to-r from-[#2c3e50] to-[#34495e] px-8 py-6">
        <div className="flex items-center h-full">
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-white mb-2">
              {personalInfo.fullName || 'Your Name'}
            </h1>
            <p className="text-lg text-white/90 flex items-center">
              <Briefcase className="w-5 h-5 mr-2" />
              {personalInfo.jobTitle || 'Business Consultant'}
            </p>
          </div>
          
          {/* Contact Info */}
          <div className="text-right text-white text-sm space-y-1">
            <div className="flex items-center justify-end">
              <Mail className="w-4 h-4 mr-2" />
              <span>{personalInfo.email}</span>
            </div>
            <div className="flex items-center justify-end">
              <Phone className="w-4 h-4 mr-2" />
              <span>{personalInfo.phone}</span>
            </div>
            <div className="flex items-center justify-end">
              <MapPin className="w-4 h-4 mr-2" />
              <span>{personalInfo.location}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="absolute top-[140px] left-0 w-full h-[702px] flex">
        
        {/* Left Column */}
        <div className="w-[370px] px-8 py-6">
          
          {/* Executive Summary */}
          {summary && (
            <div className="mb-8">
              <h2 className="text-xl font-bold text-[#2c3e50] mb-4 border-b-2 border-[#3498db] pb-2">
                Executive Summary
              </h2>
              <p className="text-sm text-[#2c3e50] leading-relaxed">
                {summary}
              </p>
            </div>
          )}

          {/* Professional Experience */}
          <div className="mb-8">
            <h2 className="text-xl font-bold text-[#2c3e50] mb-4 border-b-2 border-[#3498db] pb-2">
              Professional Experience
            </h2>
            <div className="space-y-6">
              {experience.slice(0, 4).map((exp, index) => (
                <div key={index} className="relative">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-bold text-[#2c3e50] text-base">
                      {exp.position}
                    </h3>
                    <span className="text-xs text-[#2c3e50]/70 bg-[#ecf0f1] px-2 py-1 rounded">
                      {new Date(exp.startDate).getFullYear()} - {exp.current ? 'Present' : new Date(exp.endDate!).getFullYear()}
                    </span>
                  </div>
                  <p className="text-sm font-semibold text-[#3498db] mb-1">
                    {exp.company} • {exp.location}
                  </p>
                  <div className="text-sm text-[#2c3e50] space-y-1">
                    {exp.description.slice(0, 3).map((desc, i) => (
                      <p key={i} className="flex items-start">
                        <TrendingUp className="w-3 h-3 mr-2 mt-1 text-[#3498db] flex-shrink-0" />
                        {desc}
                      </p>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="w-[224px] bg-[#ecf0f1] px-6 py-6">
          
          {/* Core Competencies */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#2c3e50] mb-4">
              Core Competencies
            </h2>
            <div className="space-y-2">
              {skills.slice(0, 8).map((skill, index) => (
                <div key={index} className="bg-white p-2 rounded border-l-3 border-[#3498db]">
                  <span className="text-sm text-[#2c3e50]">{skill}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Education */}
          <div className="mb-8">
            <h2 className="text-lg font-bold text-[#2c3e50] mb-4">
              Education
            </h2>
            <div className="space-y-3">
              {education.slice(0, 2).map((edu, index) => (
                <div key={index} className="bg-white p-3 rounded shadow-sm">
                  <h3 className="font-semibold text-sm text-[#2c3e50]">
                    {edu.degree}
                  </h3>
                  <p className="text-xs text-[#3498db] font-medium">
                    {edu.institution}
                  </p>
                  <p className="text-xs text-[#2c3e50]/70">
                    {new Date(edu.startDate).getFullYear()}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Industry Focus */}
          <div>
            <h2 className="text-lg font-bold text-[#2c3e50] mb-4">
              Industry Focus
            </h2>
            <div className="space-y-2">
              {[
                'Strategy Development',
                'Process Optimization',
                'Change Management',
                'Market Analysis',
                'Financial Planning'
              ].map((focus, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-2 h-2 bg-[#3498db] rounded-full mr-2"></div>
                  <span className="text-sm text-[#2c3e50]">{focus}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
