import { Mail, Phone, MapPin, Globe, Award, User, Briefcase, GraduationCap } from 'lucide-react';

export function Fresh001Template() {
  const personalInfo = {
    name: "<PERSON><PERSON>",
    title: "Strategic Sourcing Leader | Procurement Specialist | Team Management",
    email: "<EMAIL>",
    phone: "+****************",
    location: "Charlotte, North Carolina",
    website: "www.linkedin.com/in/maevedelaney"
  };

  const summary = "Dynamic procurement specialist with over 8 years of experience in strategic sourcing and team management. Highly skilled in supply chain optimization and developing category strategies. Proven leader with an MBA and a solid track record of implementing value-creating initiatives, demonstrating exceptional management, and driving key cost-saving projects.";

  const experience = [
    {
      title: "Senior Sourcing Manager",
      company: "Premier Inc.",
      location: "Charlotte, NC",
      dates: "04/2018 - Present",
      responsibilities: [
        "Developed and executed category strategy for medical supplies, reducing supply costs by 12% through strategic negotiations and innovative sourcing methods",
        "Led cross-functional teams to optimize vendor relationships and improve service contracts, yielding a 3% improvement in service level agreements",
        "Managed activities of $500M in spend scope, driving top adoption of cost-saving initiatives and maintaining competitive supplier base",
        "Executed comprehensive RFP processes to secure favorable terms and drive continuous improvement initiatives"
      ]
    },
    {
      title: "Procurement Category Manager",
      company: "First City Bank",
      location: "Raleigh, NC", 
      dates: "01/2015 - 03/2018",
      responsibilities: [
        "Established year-over growth plans for the electronics category - delivering superior financial performance and exceeding targets by 15%",
        "Contributed extensive market knowledge leading to the early identification of cost optimization opportunities",
        "Collaborated with internal business partners to identify key category management requirements",
        "Created financial models and business cases for senior leadership, supporting effective supplier relationship management",
        "Delivered financial market and business cases for senior leadership, supporting strategic business development initiatives",
        "Generated $2M+ in savings by restructuring key vendor relationships and optimizing contract terms"
      ]
    }
  ];

  const keyAchievements = [
    {
      title: "Implemented Supplier Performance Management System",
      description: "Led development of comprehensive supplier assessment framework, improving vendor performance tracking and accountability across organization."
    },
    {
      title: "Managed $500M Indirect Spend Portfolio", 
      description: "Developed and executed sourcing strategies for diverse business services categories, optimizing operational efficiency and generating substantial cost savings by 15%."
    },
    {
      title: "Achieved 95% Annual Cost Savings",
      description: "Led implementation of strategic sourcing initiatives across multiple categories, utilizing comprehensive category management and effective supplier relationship management to deliver measurable savings that exceeded annual targets through innovative procurement strategies."
    }
  ];

  const certifications = [
    "Certified Professional in Supply Management",
    "ISM member course covering strategic sourcing and supply chain management best practices for Supply Management"
  ];

  const education = [
    {
      degree: "Master of Business Administration",
      school: "Duke University",
      location: "Durham, NC",
      dates: "01/2007 - 01/2009"
    },
    {
      degree: "Bachelor of Science in Supply Chain Management", 
      school: "North Carolina State University",
      location: "Raleigh, NC",
      dates: "01/2003 - 01/2007"
    }
  ];

  return (
    <div className="w-full h-full bg-white p-8 text-gray-800">
      <div className="max-w-4xl mx-auto">
        {/* Header Section */}
        <div className="flex items-start mb-8">
          <div className="relative mr-6">
            <div className="w-24 h-24 bg-green-100 rounded-full p-1">
              <div className="w-full h-full bg-gray-300 rounded-full flex items-center justify-center">
                <User className="w-12 h-12 text-gray-500" />
              </div>
            </div>
          </div>
          <div className="flex-1">
            <h1 className="text-3xl font-normal text-gray-700 mb-2">{personalInfo.name}</h1>
            <div className="inline-block bg-green-500 text-white px-4 py-1 rounded-full text-sm mb-4">
              {personalInfo.title}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-3 gap-8">
          {/* Left Sidebar */}
          <div className="space-y-6">
            {/* Contacts */}
            <div>
              <div className="flex items-center mb-3">
                <div className="w-6 h-6 bg-green-500 rounded flex items-center justify-center mr-2">
                  <Mail className="w-3 h-3 text-white" />
                </div>
                <h3 className="font-semibold text-gray-700">CONTACTS</h3>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  <Mail className="w-4 h-4 text-green-500 mr-2" />
                  <span>{personalInfo.email}</span>
                </div>
                <div className="flex items-center">
                  <Phone className="w-4 h-4 text-green-500 mr-2" />
                  <span>{personalInfo.phone}</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="w-4 h-4 text-green-500 mr-2" />
                  <span>{personalInfo.location}</span>
                </div>
                <div className="flex items-center">
                  <Globe className="w-4 h-4 text-green-500 mr-2" />
                  <span className="text-blue-600">{personalInfo.website}</span>
                </div>
              </div>
            </div>

            {/* Key Achievements */}
            <div>
              <div className="flex items-center mb-3">
                <div className="w-6 h-6 bg-green-500 rounded flex items-center justify-center mr-2">
                  <Award className="w-3 h-3 text-white" />
                </div>
                <h3 className="font-semibold text-gray-700">KEY ACHIEVEMENTS</h3>
              </div>
              <div className="space-y-4">
                {keyAchievements.map((achievement, index) => (
                  <div key={index}>
                    <h4 className="font-semibold text-gray-800 text-sm mb-1">{achievement.title}</h4>
                    <p className="text-xs text-gray-600 leading-relaxed">{achievement.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Certifications */}
            <div>
              <div className="flex items-center mb-3">
                <div className="w-6 h-6 bg-green-500 rounded flex items-center justify-center mr-2">
                  <Award className="w-3 h-3 text-white" />
                </div>
                <h3 className="font-semibold text-gray-700">CERTIFICATIONS</h3>
              </div>
              <div className="space-y-2">
                {certifications.map((cert, index) => (
                  <p key={index} className="text-xs text-gray-700 leading-relaxed">{cert}</p>
                ))}
              </div>
            </div>

            {/* Education */}
            <div>
              <div className="flex items-center mb-3">
                <div className="w-6 h-6 bg-green-500 rounded flex items-center justify-center mr-2">
                  <GraduationCap className="w-3 h-3 text-white" />
                </div>
                <h3 className="font-semibold text-gray-700">EDUCATION</h3>
              </div>
              <div className="space-y-3">
                {education.map((edu, index) => (
                  <div key={index}>
                    <h4 className="font-semibold text-gray-800 text-sm">{edu.degree}</h4>
                    <p className="text-xs text-gray-600">{edu.school}</p>
                    <p className="text-xs text-gray-500">{edu.location}</p>
                    <p className="text-xs text-gray-500">{edu.dates}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Content */}
          <div className="col-span-2 space-y-6">
            {/* Summary */}
            <div>
              <div className="flex items-center mb-3">
                <User className="w-5 h-5 text-green-500 mr-2" />
                <h3 className="font-semibold text-gray-700">SUMMARY</h3>
              </div>
              <p className="text-sm text-gray-700 leading-relaxed">{summary}</p>
            </div>

            {/* Experience */}
            <div>
              <div className="flex items-center mb-3">
                <Briefcase className="w-5 h-5 text-green-500 mr-2" />
                <h3 className="font-semibold text-gray-700">EXPERIENCE</h3>
              </div>
              <div className="space-y-6">
                {experience.map((exp, index) => (
                  <div key={index}>
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-semibold text-gray-800">{exp.title}</h4>
                        <p className="text-sm text-green-600 font-medium">{exp.company}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-600">{exp.location}</p>
                        <p className="text-sm text-gray-500">{exp.dates}</p>
                      </div>
                    </div>
                    <ul className="space-y-1">
                      {exp.responsibilities.map((resp, respIndex) => (
                        <li key={respIndex} className="text-sm text-gray-700 leading-relaxed flex">
                          <span className="text-green-500 mr-2">•</span>
                          <span>{resp}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 pt-4 border-t border-gray-200 text-center">
          <p className="text-xs text-gray-500">www.linkedin.com/in/maevedelaney</p>
          <div className="flex justify-center items-center mt-2">
            <span className="text-xs text-gray-400 mr-2">Powered by</span>
            <span className="text-xs font-semibold text-gray-600">Figma Make</span>
          </div>
        </div>
      </div>
    </div>
  );
}