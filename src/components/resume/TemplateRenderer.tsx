'use client';

import React, { Suspense, useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>ircle, FileText, Loader2 } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { getTemplateComponent, templateRegistry } from '@/lib/templates/template-registry';
import { TEMPLATE_CATALOG } from '@/lib/templates/template-catalog';
import { ResumeContent } from '@/types';
import { TemplateCustomization } from '@/types/customization';
import CustomizableTemplate from '@/components/customization/CustomizableTemplate';

interface TemplateRendererProps {
  templateId: string;
  resumeData: ResumeContent;
  className?: string;
  showMetadata?: boolean;
  onError?: (error: Error) => void;
  customization?: TemplateCustomization;
  enableCustomization?: boolean;
}

interface TemplateErrorBoundaryProps {
  children: React.ReactNode;
  templateId: string;
  onError?: (error: Error) => void;
}

// Error boundary for template rendering
class TemplateErrorBoundary extends React.Component<
  TemplateErrorBoundaryProps,
  { hasError: boolean; error?: Error }
> {
  constructor(props: TemplateErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error(`Template rendering error for ${this.props.templateId}:`, error, errorInfo);
    this.props.onError?.(error);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-6 border-2 border-dashed border-red-300 rounded-lg bg-red-50">
          <div className="flex items-center gap-2 text-red-600 mb-2">
            <AlertCircle size={20} />
            <span className="font-medium">Template Rendering Error</span>
          </div>
          <p className="text-sm text-red-600 mb-3">
            Failed to render template: {this.props.templateId}
          </p>
          <p className="text-xs text-red-500">
            {this.state.error?.message || 'Unknown error occurred'}
          </p>
        </div>
      );
    }

    return this.props.children;
  }
}

// Loading placeholder component
function TemplateLoadingPlaceholder({ templateId }: { templateId: string }) {
  return (
    <Card className="w-full h-[600px] flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
        <p className="text-sm text-gray-600 mb-2">Loading template...</p>
        <p className="text-xs text-gray-500">{templateId}</p>
      </div>
    </Card>
  );
}

// Fallback component for missing templates
function TemplateFallback({ templateId, resumeData }: { templateId: string; resumeData: ResumeContent }) {
  const template = TEMPLATE_CATALOG.find(t => t.id === templateId);
  
  return (
    <Card className="w-full max-w-[595px] mx-auto bg-white shadow-lg">
      <div className="p-8">
        {/* Header */}
        <div className="border-b pb-6 mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {resumeData.personalInfo?.fullName || 'Your Name'}
          </h1>
          <p className="text-lg text-gray-600 mb-4">
            {resumeData.personalInfo?.jobTitle || 'Professional Title'}
          </p>
          <div className="flex flex-wrap gap-4 text-sm text-gray-600">
            {resumeData.personalInfo?.email && (
              <span>{resumeData.personalInfo.email}</span>
            )}
            {resumeData.personalInfo?.phone && (
              <span>{resumeData.personalInfo.phone}</span>
            )}
            {resumeData.personalInfo?.location && (
              <span>{resumeData.personalInfo.location}</span>
            )}
          </div>
        </div>

        {/* Summary */}
        {resumeData.summary && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-3">Summary</h2>
            <p className="text-gray-700 leading-relaxed">{resumeData.summary}</p>
          </div>
        )}

        {/* Experience */}
        {resumeData.experience && resumeData.experience.length > 0 && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Experience</h2>
            <div className="space-y-4">
              {resumeData.experience.map((exp, index) => (
                <div key={index} className="border-l-2 border-blue-200 pl-4">
                  <h3 className="font-semibold text-gray-900">{exp.position}</h3>
                  <p className="text-gray-600">{exp.company} • {exp.location}</p>
                  <p className="text-sm text-gray-500 mb-2">
                    {exp.startDate} - {exp.current ? 'Present' : exp.endDate}
                  </p>
                  {exp.description && exp.description.length > 0 && (
                    <ul className="text-sm text-gray-700 space-y-1">
                      {exp.description.map((desc, i) => (
                        <li key={i}>• {desc}</li>
                      ))}
                    </ul>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Education */}
        {resumeData.education && resumeData.education.length > 0 && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Education</h2>
            <div className="space-y-3">
              {resumeData.education.map((edu, index) => (
                <div key={index}>
                  <h3 className="font-semibold text-gray-900">{edu.degree} in {edu.field}</h3>
                  <p className="text-gray-600">{edu.institution} • {edu.location}</p>
                  <p className="text-sm text-gray-500">
                    {edu.startDate} - {edu.endDate}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Skills */}
        {resumeData.skills && resumeData.skills.length > 0 && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-3">Skills</h2>
            <div className="flex flex-wrap gap-2">
              {resumeData.skills.map((skill, index) => (
                <Badge key={index} variant="secondary" className="text-sm">
                  {skill}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Template Info */}
        <div className="mt-8 pt-4 border-t border-gray-200">
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <FileText size={14} />
            <span>Template: {template?.name || templateId}</span>
            {template?.isPremium && (
              <Badge variant="outline" className="text-xs">Premium</Badge>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
}

export default function TemplateRenderer({
  templateId,
  resumeData,
  className = '',
  showMetadata = false,
  onError,
  customization,
  enableCustomization = true
}: TemplateRendererProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasComponent, setHasComponent] = useState(false);

  const TemplateComponent = getTemplateComponent(templateId);
  const template = TEMPLATE_CATALOG.find(t => t.id === templateId);

  useEffect(() => {
    // Check if template component exists
    setHasComponent(!!TemplateComponent);
    setIsLoading(false);
  }, [templateId, TemplateComponent]);

  if (isLoading) {
    return <TemplateLoadingPlaceholder templateId={templateId} />;
  }

  if (!template) {
    return (
      <Alert className="max-w-md mx-auto">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Template not found: {templateId}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={`template-renderer ${className}`}>
      {showMetadata && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-gray-900">{template.name}</h3>
              <p className="text-sm text-gray-600">{template.description}</p>
            </div>
            <div className="flex gap-2">
              <Badge variant={template.isPremium ? "default" : "secondary"}>
                {template.isPremium ? "Premium" : "Free"}
              </Badge>
              <Badge variant="outline">{template.category}</Badge>
            </div>
          </div>
        </div>
      )}

      <TemplateErrorBoundary templateId={templateId} onError={onError}>
        <Suspense fallback={<TemplateLoadingPlaceholder templateId={templateId} />}>
          <CustomizableTemplate
            customization={customization}
            enableCustomization={enableCustomization}
          >
            {hasComponent && TemplateComponent ? (
              <TemplateComponent resumeData={resumeData} />
            ) : (
              <TemplateFallback templateId={templateId} resumeData={resumeData} />
            )}
          </CustomizableTemplate>
        </Suspense>
      </TemplateErrorBoundary>
    </div>
  );
}
