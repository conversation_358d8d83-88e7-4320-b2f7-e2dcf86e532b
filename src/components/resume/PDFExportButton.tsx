'use client';

import React, { useState } from 'react';
import { Download, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import PDFExportModal from './PDFExportModal';
import { ResumeContent } from '@/types';

interface PDFExportButtonProps {
  resumeData: ResumeContent;
  templateId: string;
  templateName?: string;
  variant?: 'default' | 'outline' | 'ghost' | 'secondary';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
  showIcon?: boolean;
  children?: React.ReactNode;
}

export default function PDFExportButton({
  resumeData,
  templateId,
  templateName,
  variant = 'default',
  size = 'default',
  className = '',
  showIcon = true,
  children
}: PDFExportButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleExportClick = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={handleExportClick}
        className={`flex items-center gap-2 ${className}`}
      >
        {showIcon && <Download className="h-4 w-4" />}
        {children || 'Export PDF'}
      </Button>

      <PDFExportModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        resumeData={resumeData}
        templateId={templateId}
        templateName={templateName}
      />
    </>
  );
}

// Quick export button with minimal UI
export function QuickPDFExportButton({
  resumeData,
  templateId,
  templateName,
  className = ''
}: Omit<PDFExportButtonProps, 'variant' | 'size' | 'children'>) {
  return (
    <PDFExportButton
      resumeData={resumeData}
      templateId={templateId}
      templateName={templateName}
      variant="outline"
      size="sm"
      className={className}
    >
      <FileText className="h-3 w-3" />
      PDF
    </PDFExportButton>
  );
}
