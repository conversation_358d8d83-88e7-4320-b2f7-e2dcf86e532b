'use client';

import React, { useState, useEffect } from 'react';
import { 
  Download, 
  FileText, 
  Settings, 
  AlertCircle, 
  CheckCircle, 
  X,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { usePDFExport, PDFExportOptions, generatePDFFilename, validateResumeForExport } from '@/lib/hooks/usePDFExport';
import { ResumeContent } from '@/types';

interface PDFExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  resumeData: ResumeContent;
  templateId: string;
  templateName?: string;
}

export default function PDFExportModal({
  isOpen,
  onClose,
  resumeData,
  templateId,
  templateName
}: PDFExportModalProps) {
  const { isExporting, progress, error, lastExported, exportToPDF, clearError } = usePDFExport();
  
  const [options, setOptions] = useState<PDFExportOptions>({
    quality: 'high',
    format: 'A4',
    margin: {
      top: '0.5in',
      right: '0.5in',
      bottom: '0.5in',
      left: '0.5in'
    },
    filename: generatePDFFilename(resumeData.personalInfo, templateName)
  });

  const [validation, setValidation] = useState(validateResumeForExport(resumeData));

  // Update validation when resume data changes
  useEffect(() => {
    setValidation(validateResumeForExport(resumeData));
  }, [resumeData]);

  // Update filename when personal info changes
  useEffect(() => {
    setOptions(prev => ({
      ...prev,
      filename: generatePDFFilename(resumeData.personalInfo, templateName)
    }));
  }, [resumeData.personalInfo, templateName]);

  const handleExport = async () => {
    clearError();
    
    if (!validation.isValid) {
      return;
    }

    const result = await exportToPDF(resumeData, templateId, options);
    
    if (result.success) {
      // Close modal after successful export
      setTimeout(() => {
        onClose();
      }, 1500);
    }
  };

  const handleOptionChange = (key: keyof PDFExportOptions, value: any) => {
    setOptions(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleMarginChange = (side: string, value: string) => {
    setOptions(prev => ({
      ...prev,
      margin: {
        ...prev.margin,
        [side]: value
      }
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <FileText className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <CardTitle>Export Resume as PDF</CardTitle>
                <p className="text-sm text-gray-600 mt-1">
                  Download your resume in high-quality PDF format
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              disabled={isExporting}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Validation Status */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Resume Validation
            </h3>
            
            {!validation.isValid && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-1">
                    <p className="font-medium">Missing required information:</p>
                    <ul className="list-disc list-inside text-sm">
                      {validation.missingFields.map((field, index) => (
                        <li key={index}>{field}</li>
                      ))}
                    </ul>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {validation.warnings.length > 0 && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-1">
                    <p className="font-medium">Recommendations:</p>
                    <ul className="list-disc list-inside text-sm">
                      {validation.warnings.map((warning, index) => (
                        <li key={index}>{warning}</li>
                      ))}
                    </ul>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {validation.isValid && validation.warnings.length === 0 && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Your resume is ready for export! All required sections are complete.
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* Export Options */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Export Options
            </h3>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Quality</label>
                <Select
                  value={options.quality}
                  onValueChange={(value) => handleOptionChange('quality', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="high">High (Recommended)</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low (Faster)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Page Format</label>
                <Select
                  value={options.format}
                  onValueChange={(value) => handleOptionChange('format', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="A4">A4 (International)</SelectItem>
                    <SelectItem value="Letter">Letter (US)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Filename</label>
              <Input
                value={options.filename}
                onChange={(e) => handleOptionChange('filename', e.target.value)}
                placeholder="resume.pdf"
              />
            </div>

            {/* Margin Settings */}
            <div className="space-y-3">
              <label className="text-sm font-medium">Page Margins</label>
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-1">
                  <label className="text-xs text-gray-600">Top</label>
                  <Input
                    value={options.margin?.top || '0.5in'}
                    onChange={(e) => handleMarginChange('top', e.target.value)}
                    placeholder="0.5in"
                    className="text-sm"
                  />
                </div>
                <div className="space-y-1">
                  <label className="text-xs text-gray-600">Bottom</label>
                  <Input
                    value={options.margin?.bottom || '0.5in'}
                    onChange={(e) => handleMarginChange('bottom', e.target.value)}
                    placeholder="0.5in"
                    className="text-sm"
                  />
                </div>
                <div className="space-y-1">
                  <label className="text-xs text-gray-600">Left</label>
                  <Input
                    value={options.margin?.left || '0.5in'}
                    onChange={(e) => handleMarginChange('left', e.target.value)}
                    placeholder="0.5in"
                    className="text-sm"
                  />
                </div>
                <div className="space-y-1">
                  <label className="text-xs text-gray-600">Right</label>
                  <Input
                    value={options.margin?.right || '0.5in'}
                    onChange={(e) => handleMarginChange('right', e.target.value)}
                    placeholder="0.5in"
                    className="text-sm"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Export Progress */}
          {isExporting && (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm font-medium">Generating PDF...</span>
                <Badge variant="secondary">{progress}%</Badge>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  <p className="font-medium">Export failed</p>
                  <p className="text-sm">{error}</p>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Success Message */}
          {lastExported && !isExporting && !error && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                PDF exported successfully! Your download should start automatically.
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="text-sm text-gray-600">
              {templateName && (
                <span>Using template: <Badge variant="secondary">{templateName}</Badge></span>
              )}
            </div>
            
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isExporting}
              >
                Cancel
              </Button>
              <Button
                onClick={handleExport}
                disabled={!validation.isValid || isExporting}
                className="flex items-center gap-2"
              >
                {isExporting ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Download className="h-4 w-4" />
                )}
                {isExporting ? 'Exporting...' : 'Export PDF'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
