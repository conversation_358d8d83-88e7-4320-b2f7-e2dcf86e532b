'use client';

import React, { useState, useMemo } from 'react';
import { Search, Filter, Grid, List, Star, Crown, Eye, Check } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TEMPLATE_CATALOG, TEMPLATE_CATEGORIES } from '@/lib/templates/template-catalog';
import { ResumeContent } from '@/types';
import TemplateRenderer from './TemplateRenderer';

interface TemplateSwitcherProps {
  currentTemplateId: string;
  resumeData: ResumeContent;
  onTemplateChange: (templateId: string) => void;
  onPreview?: (templateId: string) => void;
  className?: string;
}

interface TemplateCardProps {
  template: any;
  isSelected: boolean;
  onSelect: () => void;
  onPreview: () => void;
  resumeData: ResumeContent;
}

function TemplateCard({ template, isSelected, onSelect, onPreview, resumeData }: TemplateCardProps) {
  return (
    <Card className={`relative cursor-pointer transition-all duration-200 hover:shadow-lg ${
      isSelected ? 'ring-2 ring-blue-500 shadow-lg' : ''
    }`}>
      <CardHeader className="p-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-sm truncate">{template.name}</h3>
            <p className="text-xs text-gray-500 mt-1 line-clamp-2">{template.description}</p>
          </div>
          <div className="flex items-center gap-1 ml-2">
            {template.isPremium && (
              <Crown className="h-3 w-3 text-yellow-500" />
            )}
            {isSelected && (
              <Check className="h-3 w-3 text-blue-500" />
            )}
          </div>
        </div>
        
        <div className="flex flex-wrap gap-1 mt-2">
          <Badge variant="outline" className="text-xs px-1 py-0">
            {template.category}
          </Badge>
          {template.isPremium && (
            <Badge variant="default" className="text-xs px-1 py-0">
              Premium
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="p-3 pt-0">
        {/* Template Preview */}
        <div className="relative bg-gray-50 rounded border aspect-[3/4] overflow-hidden">
          <div className="absolute inset-0 scale-[0.15] origin-top-left">
            <TemplateRenderer
              templateId={template.id}
              resumeData={resumeData}
              className="pointer-events-none"
            />
          </div>
          
          {/* Overlay with actions */}
          <div className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors duration-200 flex items-center justify-center opacity-0 hover:opacity-100">
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="secondary"
                onClick={(e) => {
                  e.stopPropagation();
                  onPreview();
                }}
                className="h-8 px-3"
              >
                <Eye className="h-3 w-3 mr-1" />
                Preview
              </Button>
              <Button
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onSelect();
                }}
                className="h-8 px-3"
              >
                {isSelected ? 'Selected' : 'Select'}
              </Button>
            </div>
          </div>
        </div>

        {/* Template Tags */}
        <div className="flex flex-wrap gap-1 mt-2">
          {template.tags.slice(0, 3).map((tag: string, index: number) => (
            <Badge key={index} variant="secondary" className="text-xs px-1 py-0">
              {tag}
            </Badge>
          ))}
          {template.tags.length > 3 && (
            <Badge variant="secondary" className="text-xs px-1 py-0">
              +{template.tags.length - 3}
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export default function TemplateSwitcher({
  currentTemplateId,
  resumeData,
  onTemplateChange,
  onPreview,
  className = ''
}: TemplateSwitcherProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedIndustry, setSelectedIndustry] = useState('all');
  const [selectedExperience, setSelectedExperience] = useState('all');
  const [showPremiumOnly, setShowPremiumOnly] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Get unique industries and experience levels
  const industries = useMemo(() => {
    const allIndustries = TEMPLATE_CATALOG.flatMap(t => t.industry || []);
    return ['all', ...Array.from(new Set(allIndustries))];
  }, []);

  const experienceLevels = useMemo(() => {
    const allLevels = TEMPLATE_CATALOG.flatMap(t => t.experience || []);
    return ['all', ...Array.from(new Set(allLevels))];
  }, []);

  // Filter templates based on current filters
  const filteredTemplates = useMemo(() => {
    let templates = TEMPLATE_CATALOG;

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      templates = templates.filter(template =>
        template.name.toLowerCase().includes(query) ||
        template.description.toLowerCase().includes(query) ||
        template.tags.some(tag => tag.toLowerCase().includes(query)) ||
        (template.industry || []).some(ind => ind.toLowerCase().includes(query))
      );
    }

    // Category filter
    if (selectedCategory !== 'all') {
      templates = templates.filter(template => template.category === selectedCategory);
    }

    // Industry filter
    if (selectedIndustry !== 'all') {
      templates = templates.filter(template => 
        template.industry?.includes(selectedIndustry)
      );
    }

    // Experience level filter
    if (selectedExperience !== 'all') {
      templates = templates.filter(template => 
        template.experience?.includes(selectedExperience)
      );
    }

    // Premium filter
    if (showPremiumOnly) {
      templates = templates.filter(template => template.isPremium);
    }

    return templates;
  }, [searchQuery, selectedCategory, selectedIndustry, selectedExperience, showPremiumOnly]);

  const handleTemplateSelect = (templateId: string) => {
    onTemplateChange(templateId);
  };

  const handleTemplatePreview = (templateId: string) => {
    onPreview?.(templateId);
  };

  return (
    <div className={`template-switcher ${className}`}>
      {/* Header */}
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Choose Your Template</h2>
        <p className="text-gray-600">
          Select from {TEMPLATE_CATALOG.length} professionally designed resume templates
        </p>
      </div>

      {/* Filters */}
      <div className="mb-6 space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search templates..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Filter Row */}
        <div className="flex flex-wrap gap-4 items-center">
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              {TEMPLATE_CATEGORIES.map(category => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name} ({category.count})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedIndustry} onValueChange={setSelectedIndustry}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Industry" />
            </SelectTrigger>
            <SelectContent>
              {industries.map(industry => (
                <SelectItem key={industry} value={industry}>
                  {industry === 'all' ? 'All Industries' : industry}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedExperience} onValueChange={setSelectedExperience}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Experience" />
            </SelectTrigger>
            <SelectContent>
              {experienceLevels.map(level => (
                <SelectItem key={level} value={level}>
                  {level === 'all' ? 'All Levels' : level}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button
            variant={showPremiumOnly ? "default" : "outline"}
            size="sm"
            onClick={() => setShowPremiumOnly(!showPremiumOnly)}
            className="flex items-center gap-2"
          >
            <Crown className="h-4 w-4" />
            Premium Only
          </Button>

          <div className="flex items-center gap-1 ml-auto">
            <Button
              variant={viewMode === 'grid' ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Results Count */}
      <div className="mb-4">
        <p className="text-sm text-gray-600">
          Showing {filteredTemplates.length} of {TEMPLATE_CATALOG.length} templates
        </p>
      </div>

      {/* Template Grid */}
      <div className={`grid gap-4 ${
        viewMode === 'grid' 
          ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
          : 'grid-cols-1'
      }`}>
        {filteredTemplates.map(template => (
          <TemplateCard
            key={template.id}
            template={template}
            isSelected={template.id === currentTemplateId}
            onSelect={() => handleTemplateSelect(template.id)}
            onPreview={() => handleTemplatePreview(template.id)}
            resumeData={resumeData}
          />
        ))}
      </div>

      {/* No Results */}
      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Search className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
          <p className="text-gray-600 mb-4">
            Try adjusting your search criteria or filters
          </p>
          <Button
            variant="outline"
            onClick={() => {
              setSearchQuery('');
              setSelectedCategory('all');
              setSelectedIndustry('all');
              setSelectedExperience('all');
              setShowPremiumOnly(false);
            }}
          >
            Clear all filters
          </Button>
        </div>
      )}
    </div>
  );
}
