'use client';

import React, { useEffect, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  GraduationCap, 
  Plus, 
  Trash2, 
  Calendar, 
  MapPin, 
  Building, 
  BookOpen,
  Award,
  Hash
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import FormSection, { FormField, FormRow, FormEntry } from './FormSection';
import { 
  educationFormSchema, 
  EducationFormData,
  defaultEducation 
} from '@/lib/validations/resume-schemas';
import { Education } from '@/types';

interface EducationFormProps {
  data: Education[];
  onChange: (data: Education[]) => void;
  onValidationChange?: (isValid: boolean, errors: Record<string, string>) => void;
}

interface EducationEntryProps {
  education: Education;
  index: number;
  total: number;
  onUpdate: (index: number, data: Education) => void;
  onRemove: (index: number) => void;
  canRemove: boolean;
}

function EducationEntry({
  education,
  index,
  total,
  onUpdate,
  onRemove,
  canRemove
}: EducationEntryProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [achievementInput, setAchievementInput] = useState('');

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    setValue,
    trigger,
    control
  } = useForm<EducationFormData>({
    resolver: zodResolver(educationFormSchema.shape.education.element),
    defaultValues: education,
    mode: 'onChange'
  });

  const { fields: achievementFields, append: appendAchievement, remove: removeAchievement } = useFieldArray({
    control,
    name: 'achievements'
  });

  const watchedValues = watch();

  // Update parent when form changes
  useEffect(() => {
    const subscription = watch((value) => {
      if (value) {
        onUpdate(index, value as Education);
      }
    });
    return () => subscription.unsubscribe();
  }, [watch, onUpdate, index]);

  const handleAddAchievement = () => {
    if (achievementInput.trim()) {
      const currentAchievements = watchedValues.achievements || [];
      const newAchievements = [...currentAchievements, achievementInput.trim()];
      setValue('achievements', newAchievements);
      setAchievementInput('');
      trigger('achievements');
    }
  };

  const handleRemoveAchievement = (achievementIndex: number) => {
    const currentAchievements = watchedValues.achievements || [];
    const newAchievements = currentAchievements.filter((_, i) => i !== achievementIndex);
    setValue('achievements', newAchievements);
    trigger('achievements');
  };

  const formatDate = (date: Date | undefined) => {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  };

  const parseDate = (dateString: string) => {
    return dateString ? new Date(dateString) : undefined;
  };

  return (
    <FormEntry
      title={`${watchedValues.degree || 'Degree'} in ${watchedValues.field || 'Field'}`}
      index={index}
      total={total}
      onRemove={canRemove ? () => onRemove(index) : undefined}
      canRemove={canRemove}
      isCollapsible={true}
      defaultCollapsed={!isExpanded}
      hasErrors={Object.keys(errors).length > 0}
      errorCount={Object.keys(errors).length}
    >
      <div className="space-y-4">
        {/* Basic Information */}
        <FormRow>
          <FormField
            label="Degree"
            required={true}
            error={errors.degree?.message}
            helpText="e.g., Bachelor of Science, Master of Arts, PhD"
          >
            <div className="relative">
              <GraduationCap className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                {...register('degree')}
                placeholder="Bachelor of Science"
                className="pl-10"
              />
            </div>
          </FormField>

          <FormField
            label="Field of Study"
            required={true}
            error={errors.field?.message}
            helpText="Your major or area of specialization"
          >
            <div className="relative">
              <BookOpen className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                {...register('field')}
                placeholder="Computer Science"
                className="pl-10"
              />
            </div>
          </FormField>
        </FormRow>

        <FormRow>
          <FormField
            label="Institution"
            required={true}
            error={errors.institution?.message}
            helpText="University, college, or school name"
          >
            <div className="relative">
              <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                {...register('institution')}
                placeholder="University of California, Berkeley"
                className="pl-10"
              />
            </div>
          </FormField>

          <FormField
            label="Location"
            required={true}
            error={errors.location?.message}
            helpText="City, State/Province, Country"
          >
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                {...register('location')}
                placeholder="Berkeley, CA"
                className="pl-10"
              />
            </div>
          </FormField>
        </FormRow>

        {/* Dates */}
        <FormRow>
          <FormField
            label="Start Date"
            required={true}
            error={errors.startDate?.message}
          >
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="date"
                {...register('startDate', {
                  setValueAs: parseDate
                })}
                defaultValue={formatDate(education.startDate)}
                className="pl-10"
              />
            </div>
          </FormField>

          <FormField
            label="End Date"
            error={errors.endDate?.message}
            helpText="Leave empty if currently enrolled"
          >
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="date"
                {...register('endDate', {
                  setValueAs: parseDate
                })}
                defaultValue={formatDate(education.endDate)}
                className="pl-10"
              />
            </div>
          </FormField>
        </FormRow>

        {/* GPA */}
        <FormField
          label="GPA (Optional)"
          error={errors.gpa?.message}
          helpText="Grade Point Average (0.0 - 4.0 scale)"
        >
          <div className="relative">
            <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="number"
              step="0.01"
              min="0"
              max="4.0"
              {...register('gpa', {
                setValueAs: (value) => value === '' ? undefined : parseFloat(value)
              })}
              placeholder="3.8"
              className="pl-10"
            />
          </div>
        </FormField>

        {/* Achievements */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-700">
              Achievements & Honors
              <span className="text-gray-500 ml-1">(Optional)</span>
            </label>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => appendAchievement('')}
              className="flex items-center gap-1"
            >
              <Plus className="h-3 w-3" />
              Add Achievement
            </Button>
          </div>

          {/* Achievement Input */}
          <div className="flex gap-2">
            <div className="flex-1">
              <Input
                value={achievementInput}
                onChange={(e) => setAchievementInput(e.target.value)}
                placeholder="e.g., Magna Cum Laude, Dean's List, Phi Beta Kappa"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddAchievement();
                  }
                }}
              />
            </div>
            <Button
              type="button"
              variant="outline"
              onClick={handleAddAchievement}
              disabled={!achievementInput.trim()}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          {/* Achievement Tags */}
          {watchedValues.achievements && watchedValues.achievements.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {watchedValues.achievements.map((achievement, achievementIndex) => (
                <Badge
                  key={achievementIndex}
                  variant="secondary"
                  className="flex items-center gap-1"
                >
                  <Award className="h-3 w-3" />
                  {achievement}
                  <button
                    type="button"
                    onClick={() => handleRemoveAchievement(achievementIndex)}
                    className="ml-1 text-gray-500 hover:text-gray-700"
                  >
                    ×
                  </button>
                </Badge>
              ))}
            </div>
          )}
        </div>
      </div>
    </FormEntry>
  );
}

export default function EducationForm({
  data,
  onChange,
  onValidationChange
}: EducationFormProps) {
  const [educations, setEducations] = useState<Education[]>(data || []);

  const {
    formState: { isValid, errors }
  } = useForm({
    resolver: zodResolver(educationFormSchema),
    defaultValues: { education: educations },
    mode: 'onChange'
  });

  // Update parent when educations change
  useEffect(() => {
    onChange(educations);
  }, [educations, onChange]);

  // Update validation state
  useEffect(() => {
    const errorMessages: Record<string, string> = {};
    Object.entries(errors).forEach(([key, error]) => {
      if (error?.message) {
        errorMessages[key] = error.message;
      }
    });
    onValidationChange?.(isValid, errorMessages);
  }, [isValid, errors, onValidationChange]);

  const handleAddEducation = () => {
    const newEducation: Education = {
      ...defaultEducation,
      id: `edu-${Date.now()}`,
      achievements: []
    };
    setEducations([...educations, newEducation]);
  };

  const handleUpdateEducation = (index: number, updatedEducation: Education) => {
    const newEducations = [...educations];
    newEducations[index] = updatedEducation;
    setEducations(newEducations);
  };

  const handleRemoveEducation = (index: number) => {
    const newEducations = educations.filter((_, i) => i !== index);
    setEducations(newEducations);
  };

  const totalErrors = Object.keys(errors).length;

  return (
    <FormSection
      title="Education"
      description="Your academic background and qualifications"
      icon={<GraduationCap className="h-4 w-4" />}
      hasErrors={totalErrors > 0}
      errorCount={totalErrors}
      onAdd={handleAddEducation}
      canAdd={true}
      addButtonText="Add Education"
      badge={`${educations.length} degree${educations.length !== 1 ? 's' : ''}`}
      helpText="List your education in reverse chronological order (most recent first)"
    >
      <div className="space-y-6">
        {educations.length === 0 ? (
          <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
            <GraduationCap className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600 mb-4">No education added yet</p>
            <Button onClick={handleAddEducation} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Your First Degree
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {educations.map((education, index) => (
              <EducationEntry
                key={education.id || index}
                education={education}
                index={index}
                total={educations.length}
                onUpdate={handleUpdateEducation}
                onRemove={handleRemoveEducation}
                canRemove={educations.length > 1}
              />
            ))}
          </div>
        )}

        {/* Form Status */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-2">
            {educations.length > 0 && isValid ? (
              <div className="flex items-center gap-2 text-green-600">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Education section complete</span>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-gray-500">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span className="text-sm">
                  {educations.length === 0 ? 'Add your education background' : 'Complete all required fields'}
                </span>
              </div>
            )}
          </div>

          <div className="text-sm text-gray-500">
            {educations.length} of 3+ recommended degrees
          </div>
        </div>
      </div>
    </FormSection>
  );
}
