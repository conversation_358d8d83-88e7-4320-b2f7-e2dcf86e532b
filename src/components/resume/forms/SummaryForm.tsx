'use client';

import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FileText, Lightbulb, Target, Wand2 } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import FormSection, { FormField } from './FormSection';
import { summarySchema, SummaryFormData } from '@/lib/validations/resume-schemas';

interface SummaryFormProps {
  data: string;
  onChange: (data: string) => void;
  onValidationChange?: (isValid: boolean, errors: Record<string, string>) => void;
  userProfile?: {
    jobTitle?: string;
    experience?: string;
    industry?: string;
  };
}

const SUMMARY_TEMPLATES = [
  {
    id: 'experienced',
    title: 'Experienced Professional',
    template: 'Experienced {jobTitle} with {experience}+ years of expertise in {industry}. Proven track record of delivering high-quality solutions and leading cross-functional teams to achieve business objectives.',
  },
  {
    id: 'entry-level',
    title: 'Entry Level',
    template: 'Motivated {jobTitle} with strong foundation in {industry}. Recent graduate with hands-on experience through internships and projects. Eager to contribute to innovative teams and grow professionally.',
  },
  {
    id: 'career-change',
    title: 'Career Change',
    template: 'Results-driven professional transitioning to {jobTitle} role. Bringing transferable skills from previous experience and passion for {industry}. Committed to leveraging diverse background to drive innovation.',
  },
  {
    id: 'leadership',
    title: 'Leadership Focus',
    template: 'Strategic {jobTitle} with proven leadership experience in {industry}. Expert in building high-performing teams, driving operational excellence, and delivering measurable business results.',
  }
];

const WRITING_TIPS = [
  'Start with your years of experience or key qualification',
  'Highlight your most relevant skills and achievements',
  'Mention specific industries or technologies you work with',
  'Keep it concise - aim for 2-4 sentences',
  'Use action words and quantify achievements when possible',
  'Tailor it to the specific role you\'re targeting'
];

export default function SummaryForm({
  data,
  onChange,
  onValidationChange,
  userProfile
}: SummaryFormProps) {
  const [characterCount, setCharacterCount] = useState(0);
  const [showTips, setShowTips] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isValid },
    setValue,
    trigger
  } = useForm<SummaryFormData>({
    resolver: zodResolver(summarySchema),
    defaultValues: { summary: data || '' },
    mode: 'onChange'
  });

  const watchedSummary = watch('summary');

  // Update character count
  useEffect(() => {
    setCharacterCount(watchedSummary?.length || 0);
  }, [watchedSummary]);

  // Update parent component when form data changes
  useEffect(() => {
    const subscription = watch((value) => {
      if (value.summary !== undefined) {
        onChange(value.summary || '');
      }
    });
    return () => subscription.unsubscribe();
  }, [watch, onChange]);

  // Update validation state
  useEffect(() => {
    const errorMessages: Record<string, string> = {};
    if (errors.summary?.message) {
      errorMessages.summary = errors.summary.message;
    }
    onValidationChange?.(isValid, errorMessages);
  }, [isValid, errors, onValidationChange]);

  // Update form when external data changes
  useEffect(() => {
    if (data !== watchedSummary) {
      setValue('summary', data || '');
      trigger();
    }
  }, [data, setValue, trigger, watchedSummary]);

  const handleTemplateSelect = (template: typeof SUMMARY_TEMPLATES[0]) => {
    let filledTemplate = template.template;
    
    // Replace placeholders with user data
    if (userProfile?.jobTitle) {
      filledTemplate = filledTemplate.replace(/{jobTitle}/g, userProfile.jobTitle);
    }
    if (userProfile?.experience) {
      filledTemplate = filledTemplate.replace(/{experience}/g, userProfile.experience);
    }
    if (userProfile?.industry) {
      filledTemplate = filledTemplate.replace(/{industry}/g, userProfile.industry);
    }
    
    // Remove any remaining placeholders
    filledTemplate = filledTemplate.replace(/{[^}]+}/g, '[field]');
    
    setValue('summary', filledTemplate);
    setSelectedTemplate(template.id);
    trigger();
  };

  const handleClear = () => {
    setValue('summary', '');
    setSelectedTemplate(null);
    trigger();
  };

  const getCharacterCountColor = () => {
    if (characterCount < 50) return 'text-red-500';
    if (characterCount > 450) return 'text-yellow-500';
    if (characterCount > 500) return 'text-red-500';
    return 'text-green-500';
  };

  const getCharacterCountBadge = () => {
    if (characterCount < 50) return 'destructive';
    if (characterCount > 450) return 'outline';
    return 'secondary';
  };

  const errorCount = errors.summary ? 1 : 0;

  return (
    <FormSection
      title="Professional Summary"
      description="A brief overview of your professional background and key qualifications"
      icon={<FileText className="h-4 w-4" />}
      hasErrors={errorCount > 0}
      errorCount={errorCount}
      helpText="Your professional summary is often the first thing recruiters read. Make it compelling and relevant to your target role."
    >
      <div className="space-y-6">
        {/* Template Suggestions */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900">Quick Start Templates</h4>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setShowTips(!showTips)}
              className="text-blue-600 hover:text-blue-700"
            >
              <Lightbulb className="h-4 w-4 mr-1" />
              {showTips ? 'Hide Tips' : 'Writing Tips'}
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {SUMMARY_TEMPLATES.map((template) => (
              <Button
                key={template.id}
                type="button"
                variant={selectedTemplate === template.id ? "default" : "outline"}
                size="sm"
                onClick={() => handleTemplateSelect(template)}
                className="justify-start text-left h-auto p-3"
              >
                <div>
                  <div className="font-medium text-sm">{template.title}</div>
                  <div className="text-xs text-gray-500 mt-1 line-clamp-2">
                    {template.template.substring(0, 80)}...
                  </div>
                </div>
              </Button>
            ))}
          </div>
        </div>

        {/* Writing Tips */}
        {showTips && (
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h5 className="text-sm font-medium text-blue-900 mb-2 flex items-center gap-2">
              <Target className="h-4 w-4" />
              Writing Tips for a Strong Summary
            </h5>
            <ul className="text-sm text-blue-800 space-y-1">
              {WRITING_TIPS.map((tip, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-blue-500 mt-1">•</span>
                  {tip}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Summary Input */}
        <FormField
          label="Professional Summary"
          error={errors.summary?.message}
          helpText="Write 2-4 sentences that highlight your experience, skills, and career goals"
        >
          <div className="space-y-2">
            <Textarea
              {...register('summary')}
              placeholder="Write a compelling summary that highlights your professional experience, key skills, and career objectives. Focus on what makes you unique and valuable to potential employers..."
              className="min-h-[120px] resize-none"
              maxLength={500}
            />
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Badge variant={getCharacterCountBadge()} className="text-xs">
                  {characterCount}/500 characters
                </Badge>
                
                {characterCount >= 50 && characterCount <= 500 && (
                  <Badge variant="secondary" className="text-xs text-green-600">
                    Good length
                  </Badge>
                )}
              </div>
              
              <div className="flex items-center gap-2">
                {watchedSummary && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handleClear}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    Clear
                  </Button>
                )}
                
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setShowTips(!showTips)}
                  className="text-blue-600 hover:text-blue-700"
                >
                  <Wand2 className="h-4 w-4 mr-1" />
                  Improve
                </Button>
              </div>
            </div>
          </div>
        </FormField>

        {/* Character Count Guidelines */}
        <div className="text-xs text-gray-500 space-y-1">
          <div className="flex items-center justify-between">
            <span>Recommended length:</span>
            <span className={getCharacterCountColor()}>
              {characterCount < 50 && 'Too short - add more detail'}
              {characterCount >= 50 && characterCount <= 300 && 'Perfect length'}
              {characterCount > 300 && characterCount <= 450 && 'Good length'}
              {characterCount > 450 && characterCount <= 500 && 'Getting long - consider trimming'}
              {characterCount > 500 && 'Too long - please shorten'}
            </span>
          </div>
        </div>

        {/* Form Status */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-2">
            {isValid && characterCount >= 50 ? (
              <div className="flex items-center gap-2 text-green-600">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Summary looks great</span>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-gray-500">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span className="text-sm">
                  {characterCount === 0 ? 'Add your professional summary' : 
                   characterCount < 50 ? 'Add more detail to your summary' :
                   'Summary needs attention'}
                </span>
              </div>
            )}
          </div>

          <div className="text-sm text-gray-500">
            {characterCount > 0 ? 'Summary added' : 'Optional section'}
          </div>
        </div>
      </div>
    </FormSection>
  );
}
