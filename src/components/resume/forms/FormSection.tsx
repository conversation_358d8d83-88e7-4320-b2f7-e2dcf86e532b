'use client';

import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Plus, Trash2, GripVertical, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

interface FormSectionProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  isCollapsible?: boolean;
  defaultCollapsed?: boolean;
  isRequired?: boolean;
  hasErrors?: boolean;
  errorCount?: number;
  className?: string;
  onAdd?: () => void;
  onRemove?: () => void;
  canAdd?: boolean;
  canRemove?: boolean;
  addButtonText?: string;
  removeButtonText?: string;
  isDraggable?: boolean;
  helpText?: string;
  badge?: string;
  badgeVariant?: 'default' | 'secondary' | 'destructive' | 'outline';
}

export default function FormSection({
  title,
  description,
  icon,
  children,
  isCollapsible = false,
  defaultCollapsed = false,
  isRequired = false,
  hasErrors = false,
  errorCount = 0,
  className = '',
  onAdd,
  onRemove,
  canAdd = false,
  canRemove = false,
  addButtonText = 'Add',
  removeButtonText = 'Remove',
  isDraggable = false,
  helpText,
  badge,
  badgeVariant = 'secondary'
}: FormSectionProps) {
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);

  const toggleCollapsed = () => {
    if (isCollapsible) {
      setIsCollapsed(!isCollapsed);
    }
  };

  return (
    <Card className={cn(
      'transition-all duration-200',
      hasErrors && 'border-red-300 bg-red-50/50',
      className
    )}>
      <CardHeader className={cn(
        'pb-3',
        isCollapsible && 'cursor-pointer hover:bg-gray-50',
        hasErrors && 'border-b border-red-200'
      )} onClick={toggleCollapsed}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {isDraggable && (
              <GripVertical className="h-4 w-4 text-gray-400 cursor-grab" />
            )}
            
            {icon && (
              <div className={cn(
                'flex items-center justify-center w-8 h-8 rounded-lg',
                hasErrors ? 'bg-red-100 text-red-600' : 'bg-blue-100 text-blue-600'
              )}>
                {icon}
              </div>
            )}
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <h3 className={cn(
                  'text-lg font-semibold',
                  hasErrors ? 'text-red-900' : 'text-gray-900'
                )}>
                  {title}
                  {isRequired && (
                    <span className="text-red-500 ml-1">*</span>
                  )}
                </h3>
                
                {badge && (
                  <Badge variant={badgeVariant} className="text-xs">
                    {badge}
                  </Badge>
                )}
                
                {hasErrors && errorCount > 0 && (
                  <Badge variant="destructive" className="text-xs">
                    {errorCount} error{errorCount > 1 ? 's' : ''}
                  </Badge>
                )}
                
                {helpText && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">{helpText}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
              
              {description && (
                <p className={cn(
                  'text-sm mt-1',
                  hasErrors ? 'text-red-600' : 'text-gray-600'
                )}>
                  {description}
                </p>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {/* Action Buttons */}
            {canAdd && onAdd && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onAdd();
                }}
                className="flex items-center gap-1"
              >
                <Plus className="h-3 w-3" />
                {addButtonText}
              </Button>
            )}
            
            {canRemove && onRemove && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onRemove();
                }}
                className="flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="h-3 w-3" />
                {removeButtonText}
              </Button>
            )}
            
            {/* Collapse Toggle */}
            {isCollapsible && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="p-1"
              >
                {isCollapsed ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronUp className="h-4 w-4" />
                )}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      {!isCollapsed && (
        <CardContent className="pt-0">
          {children}
        </CardContent>
      )}
    </Card>
  );
}

// Specialized form section variants
interface FormEntryProps extends Omit<FormSectionProps, 'onAdd' | 'onRemove' | 'canAdd' | 'canRemove'> {
  onRemove?: () => void;
  canRemove?: boolean;
  index?: number;
  total?: number;
}

export function FormEntry({
  title,
  onRemove,
  canRemove = true,
  index,
  total,
  ...props
}: FormEntryProps) {
  const entryTitle = index !== undefined && total !== undefined 
    ? `${title} ${index + 1} of ${total}`
    : title;

  return (
    <FormSection
      {...props}
      title={entryTitle}
      onRemove={onRemove}
      canRemove={canRemove}
      removeButtonText="Remove"
      className="border-l-4 border-l-blue-200"
    />
  );
}

interface FormListSectionProps extends Omit<FormSectionProps, 'children'> {
  children: React.ReactNode;
  onAdd: () => void;
  itemCount: number;
  maxItems?: number;
  minItems?: number;
}

export function FormListSection({
  onAdd,
  itemCount,
  maxItems,
  minItems = 0,
  ...props
}: FormListSectionProps) {
  const canAddMore = !maxItems || itemCount < maxItems;
  const hasMinimum = itemCount >= minItems;

  return (
    <FormSection
      {...props}
      onAdd={canAddMore ? onAdd : undefined}
      canAdd={canAddMore}
      badge={`${itemCount} item${itemCount !== 1 ? 's' : ''}`}
      badgeVariant={hasMinimum ? 'secondary' : 'destructive'}
      helpText={
        maxItems 
          ? `Add up to ${maxItems} items. Currently have ${itemCount}.`
          : `Currently have ${itemCount} items.`
      }
    />
  );
}

// Form field wrapper for consistent styling
interface FormFieldProps {
  label: string;
  error?: string;
  required?: boolean;
  helpText?: string;
  children: React.ReactNode;
  className?: string;
}

export function FormField({
  label,
  error,
  required = false,
  helpText,
  children,
  className = ''
}: FormFieldProps) {
  return (
    <div className={cn('space-y-2', className)}>
      <label className={cn(
        'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
        error ? 'text-red-600' : 'text-gray-700'
      )}>
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
        {helpText && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="inline h-3 w-3 ml-1 text-gray-400 cursor-help" />
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs">{helpText}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </label>
      
      {children}
      
      {error && (
        <p className="text-sm text-red-600 flex items-center gap-1">
          <Info className="h-3 w-3" />
          {error}
        </p>
      )}
    </div>
  );
}

// Form row for side-by-side fields
interface FormRowProps {
  children: React.ReactNode;
  className?: string;
}

export function FormRow({ children, className = '' }: FormRowProps) {
  return (
    <div className={cn('grid grid-cols-1 md:grid-cols-2 gap-4', className)}>
      {children}
    </div>
  );
}

// Form grid for multiple columns
interface FormGridProps {
  children: React.ReactNode;
  columns?: number;
  className?: string;
}

export function FormGrid({ children, columns = 3, className = '' }: FormGridProps) {
  return (
    <div className={cn(
      'grid gap-4',
      columns === 2 && 'grid-cols-1 md:grid-cols-2',
      columns === 3 && 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      columns === 4 && 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
      className
    )}>
      {children}
    </div>
  );
}
