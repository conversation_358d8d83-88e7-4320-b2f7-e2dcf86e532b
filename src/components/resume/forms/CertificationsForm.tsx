'use client';

import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  Award, 
  Plus, 
  Trash2, 
  Calendar, 
  Building, 
  Hash,
  ExternalLink,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import FormSection, { FormField, FormRow, FormEntry } from './FormSection';
import { 
  certificationsFormSchema, 
  CertificationFormData,
  defaultCertification 
} from '@/lib/validations/resume-schemas';
import { Certification } from '@/types';

interface CertificationsFormProps {
  data: Certification[];
  onChange: (data: Certification[]) => void;
  onValidationChange?: (isValid: boolean, errors: Record<string, string>) => void;
}

interface CertificationEntryProps {
  certification: Certification;
  index: number;
  total: number;
  onUpdate: (index: number, data: Certification) => void;
  onRemove: (index: number) => void;
  canRemove: boolean;
}

function CertificationEntry({
  certification,
  index,
  total,
  onUpdate,
  onRemove,
  canRemove
}: CertificationEntryProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [neverExpires, setNeverExpires] = useState(!certification.expiryDate);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    setValue,
    trigger
  } = useForm<CertificationFormData>({
    resolver: zodResolver(certificationsFormSchema.shape.certifications.element),
    defaultValues: certification,
    mode: 'onChange'
  });

  const watchedValues = watch();

  // Update parent when form changes
  useEffect(() => {
    const subscription = watch((value) => {
      if (value) {
        onUpdate(index, value as Certification);
      }
    });
    return () => subscription.unsubscribe();
  }, [watch, onUpdate, index]);

  // Handle never expires toggle
  useEffect(() => {
    if (neverExpires) {
      setValue('expiryDate', undefined);
    }
  }, [neverExpires, setValue]);

  const formatDate = (date: Date | undefined) => {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  };

  const parseDate = (dateString: string) => {
    return dateString ? new Date(dateString) : undefined;
  };

  const isExpired = () => {
    if (!watchedValues.expiryDate) return false;
    return new Date(watchedValues.expiryDate) < new Date();
  };

  const getExpiryStatus = () => {
    if (!watchedValues.expiryDate) return null;
    
    const expiryDate = new Date(watchedValues.expiryDate);
    const now = new Date();
    const monthsUntilExpiry = (expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24 * 30);
    
    if (monthsUntilExpiry < 0) return 'expired';
    if (monthsUntilExpiry < 3) return 'expiring-soon';
    return 'valid';
  };

  const getStatusBadge = () => {
    const status = getExpiryStatus();
    switch (status) {
      case 'expired':
        return <Badge variant="destructive" className="text-xs">Expired</Badge>;
      case 'expiring-soon':
        return <Badge variant="outline" className="text-xs text-yellow-600">Expiring Soon</Badge>;
      case 'valid':
        return <Badge variant="secondary" className="text-xs text-green-600">Valid</Badge>;
      default:
        return <Badge variant="secondary" className="text-xs">No Expiry</Badge>;
    }
  };

  return (
    <FormEntry
      title={`${watchedValues.name || 'Certification'} - ${watchedValues.issuer || 'Issuer'}`}
      index={index}
      total={total}
      onRemove={canRemove ? () => onRemove(index) : undefined}
      canRemove={canRemove}
      isCollapsible={true}
      defaultCollapsed={!isExpanded}
      hasErrors={Object.keys(errors).length > 0}
      errorCount={Object.keys(errors).length}
      badge={getStatusBadge()}
    >
      <div className="space-y-4">
        {/* Basic Information */}
        <FormRow>
          <FormField
            label="Certification Name"
            required={true}
            error={errors.name?.message}
            helpText="Full name of the certification"
          >
            <div className="relative">
              <Award className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                {...register('name')}
                placeholder="AWS Certified Solutions Architect"
                className="pl-10"
              />
            </div>
          </FormField>

          <FormField
            label="Issuing Organization"
            required={true}
            error={errors.issuer?.message}
            helpText="Organization that issued the certification"
          >
            <div className="relative">
              <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                {...register('issuer')}
                placeholder="Amazon Web Services"
                className="pl-10"
              />
            </div>
          </FormField>
        </FormRow>

        {/* Dates */}
        <FormRow>
          <FormField
            label="Issue Date"
            required={true}
            error={errors.issueDate?.message}
            helpText="When you received the certification"
          >
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="date"
                {...register('issueDate', {
                  setValueAs: parseDate
                })}
                defaultValue={formatDate(certification.issueDate)}
                className="pl-10"
              />
            </div>
          </FormField>

          <FormField
            label="Expiry Date"
            error={errors.expiryDate?.message}
            helpText="When the certification expires (if applicable)"
          >
            <div className="space-y-2">
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="date"
                  {...register('expiryDate', {
                    setValueAs: parseDate
                  })}
                  defaultValue={formatDate(certification.expiryDate)}
                  disabled={neverExpires}
                  className="pl-10"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id={`never-expires-${index}`}
                  checked={neverExpires}
                  onCheckedChange={setNeverExpires}
                />
                <label
                  htmlFor={`never-expires-${index}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  This certification never expires
                </label>
              </div>
            </div>
          </FormField>
        </FormRow>

        {/* Additional Information */}
        <FormRow>
          <FormField
            label="Credential ID"
            error={errors.credentialId?.message}
            helpText="Unique identifier for verification (optional)"
          >
            <div className="relative">
              <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                {...register('credentialId')}
                placeholder="ABC123456789"
                className="pl-10"
              />
            </div>
          </FormField>

          <FormField
            label="Verification URL"
            error={errors.url?.message}
            helpText="Link to verify the certification (optional)"
          >
            <div className="relative">
              <ExternalLink className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                {...register('url')}
                type="url"
                placeholder="https://verify.example.com/cert/123"
                className="pl-10"
              />
            </div>
          </FormField>
        </FormRow>

        {/* Status Information */}
        {watchedValues.expiryDate && (
          <div className="p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              {isExpired() ? (
                <AlertCircle className="h-4 w-4 text-red-500" />
              ) : (
                <CheckCircle className="h-4 w-4 text-green-500" />
              )}
              <span className="text-sm font-medium">
                {isExpired() ? 'Certification Expired' : 'Certification Valid'}
              </span>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              {isExpired() 
                ? `Expired on ${new Date(watchedValues.expiryDate).toLocaleDateString()}`
                : `Expires on ${new Date(watchedValues.expiryDate).toLocaleDateString()}`
              }
            </p>
          </div>
        )}
      </div>
    </FormEntry>
  );
}

export default function CertificationsForm({
  data,
  onChange,
  onValidationChange
}: CertificationsFormProps) {
  const [certifications, setCertifications] = useState<Certification[]>(data || []);

  const {
    formState: { isValid, errors }
  } = useForm({
    resolver: zodResolver(certificationsFormSchema),
    defaultValues: { certifications },
    mode: 'onChange'
  });

  // Update parent when certifications change
  useEffect(() => {
    onChange(certifications);
  }, [certifications, onChange]);

  // Update validation state
  useEffect(() => {
    const errorMessages: Record<string, string> = {};
    Object.entries(errors).forEach(([key, error]) => {
      if (error?.message) {
        errorMessages[key] = error.message;
      }
    });
    onValidationChange?.(isValid, errorMessages);
  }, [isValid, errors, onValidationChange]);

  const handleAddCertification = () => {
    const newCertification: Certification = {
      ...defaultCertification,
      id: `cert-${Date.now()}`
    };
    setCertifications([...certifications, newCertification]);
  };

  const handleUpdateCertification = (index: number, updatedCertification: Certification) => {
    const newCertifications = [...certifications];
    newCertifications[index] = updatedCertification;
    setCertifications(newCertifications);
  };

  const handleRemoveCertification = (index: number) => {
    const newCertifications = certifications.filter((_, i) => i !== index);
    setCertifications(newCertifications);
  };

  const totalErrors = Object.keys(errors).length;
  const validCertifications = certifications.filter(cert => 
    !cert.expiryDate || new Date(cert.expiryDate) > new Date()
  ).length;

  return (
    <FormSection
      title="Certifications & Licenses"
      description="Professional certifications and licenses you've earned"
      icon={<Award className="h-4 w-4" />}
      hasErrors={totalErrors > 0}
      errorCount={totalErrors}
      onAdd={handleAddCertification}
      canAdd={true}
      addButtonText="Add Certification"
      badge={`${certifications.length} certification${certifications.length !== 1 ? 's' : ''}`}
      helpText="Include relevant professional certifications that demonstrate your expertise"
    >
      <div className="space-y-6">
        {certifications.length === 0 ? (
          <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
            <Award className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600 mb-4">No certifications added yet</p>
            <Button onClick={handleAddCertification} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Your First Certification
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {certifications.map((certification, index) => (
              <CertificationEntry
                key={certification.id || index}
                certification={certification}
                index={index}
                total={certifications.length}
                onUpdate={handleUpdateCertification}
                onRemove={handleRemoveCertification}
                canRemove={true}
              />
            ))}
          </div>
        )}

        {/* Form Status */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-2">
            {certifications.length > 0 && isValid ? (
              <div className="flex items-center gap-2 text-green-600">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">
                  Certifications section complete
                  {validCertifications < certifications.length && (
                    <span className="text-yellow-600 ml-1">
                      ({certifications.length - validCertifications} expired)
                    </span>
                  )}
                </span>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-gray-500">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span className="text-sm">
                  {certifications.length === 0 ? 'Add your certifications (optional)' : 'Complete all required fields'}
                </span>
              </div>
            )}
          </div>

          <div className="text-sm text-gray-500">
            {certifications.length} certification{certifications.length !== 1 ? 's' : ''} added
          </div>
        </div>
      </div>
    </FormSection>
  );
}
