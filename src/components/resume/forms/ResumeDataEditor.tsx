'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Save, AlertCircle, CheckCircle, Clock, FileText, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import PersonalInfoForm from './PersonalInfoForm';
import SummaryForm from './SummaryForm';
import ExperienceForm from './ExperienceForm';
import EducationForm from './EducationForm';
import SkillsForm from './SkillsForm';
import CertificationsForm from './CertificationsForm';
import LanguagesForm from './LanguagesForm';
import PDFExportButton from '../PDFExportButton';
import { ResumeData, PersonalInfo, Experience, Education, Certification, Language } from '@/types';
import { validateResumeData } from '@/lib/validations/resume-schemas';

interface ResumeDataEditorProps {
  data: ResumeData;
  onChange: (data: ResumeData) => void;
  onSave?: (data: ResumeData) => Promise<void>;
  autoSave?: boolean;
  autoSaveDelay?: number;
  className?: string;
  templateId?: string;
  templateName?: string;
  showPDFExport?: boolean;
}

interface SectionValidation {
  isValid: boolean;
  errors: Record<string, string>;
  isComplete: boolean;
}

interface ValidationState {
  personalInfo: SectionValidation;
  summary: SectionValidation;
  experience: SectionValidation;
  education: SectionValidation;
  skills: SectionValidation;
  certifications: SectionValidation;
  languages: SectionValidation;
  overall: {
    isValid: boolean;
    completionPercentage: number;
    requiredSectionsComplete: number;
    totalRequiredSections: number;
  };
}

const REQUIRED_SECTIONS = ['personalInfo', 'experience'];
const OPTIONAL_SECTIONS = ['summary', 'education', 'skills', 'certifications', 'languages', 'projects'];

export default function ResumeDataEditor({
  data,
  onChange,
  onSave,
  autoSave = true,
  autoSaveDelay = 2000,
  className = '',
  templateId,
  templateName,
  showPDFExport = true
}: ResumeDataEditorProps) {
  const [resumeData, setResumeData] = useState<ResumeData>(data);
  const [validation, setValidation] = useState<ValidationState>({
    personalInfo: { isValid: false, errors: {}, isComplete: false },
    summary: { isValid: true, errors: {}, isComplete: false },
    experience: { isValid: false, errors: {}, isComplete: false },
    education: { isValid: true, errors: {}, isComplete: false },
    skills: { isValid: true, errors: {}, isComplete: false },
    certifications: { isValid: true, errors: {}, isComplete: false },
    languages: { isValid: true, errors: {}, isComplete: false },
    overall: { isValid: false, completionPercentage: 0, requiredSectionsComplete: 0, totalRequiredSections: 2 }
  });
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [activeTab, setActiveTab] = useState('personal');

  // Auto-save functionality
  useEffect(() => {
    if (!autoSave || !hasUnsavedChanges) return;

    const timeoutId = setTimeout(async () => {
      if (onSave) {
        setIsSaving(true);
        try {
          await onSave(resumeData);
          setLastSaved(new Date());
          setHasUnsavedChanges(false);
        } catch (error) {
          console.error('Auto-save failed:', error);
        } finally {
          setIsSaving(false);
        }
      }
    }, autoSaveDelay);

    return () => clearTimeout(timeoutId);
  }, [resumeData, autoSave, autoSaveDelay, onSave, hasUnsavedChanges]);

  // Update parent component when data changes
  useEffect(() => {
    onChange(resumeData);
    setHasUnsavedChanges(true);
  }, [resumeData, onChange]);

  // Calculate overall validation state
  useEffect(() => {
    const requiredSectionsComplete = REQUIRED_SECTIONS.filter(section => 
      validation[section as keyof ValidationState]?.isValid
    ).length;

    const allSectionsComplete = [...REQUIRED_SECTIONS, ...OPTIONAL_SECTIONS].filter(section => 
      validation[section as keyof ValidationState]?.isComplete
    ).length;

    const completionPercentage = Math.round(
      (allSectionsComplete / (REQUIRED_SECTIONS.length + OPTIONAL_SECTIONS.length)) * 100
    );

    const isValid = requiredSectionsComplete === REQUIRED_SECTIONS.length;

    setValidation(prev => ({
      ...prev,
      overall: {
        isValid,
        completionPercentage,
        requiredSectionsComplete,
        totalRequiredSections: REQUIRED_SECTIONS.length
      }
    }));
  }, [validation.personalInfo, validation.summary, validation.experience, validation.education, validation.skills, validation.certifications, validation.languages]);

  const handlePersonalInfoChange = useCallback((personalInfo: PersonalInfo) => {
    setResumeData(prev => ({ ...prev, personalInfo }));
  }, []);

  const handleSummaryChange = useCallback((summary: string) => {
    setResumeData(prev => ({ ...prev, summary }));
  }, []);

  const handleExperienceChange = useCallback((experience: Experience[]) => {
    setResumeData(prev => ({ ...prev, experience }));
  }, []);

  const handleEducationChange = useCallback((education: Education[]) => {
    setResumeData(prev => ({ ...prev, education }));
  }, []);

  const handleSkillsChange = useCallback((skills: string[]) => {
    setResumeData(prev => ({ ...prev, skills }));
  }, []);

  const handleCertificationsChange = useCallback((certifications: Certification[]) => {
    setResumeData(prev => ({ ...prev, certifications }));
  }, []);

  const handleLanguagesChange = useCallback((languages: Language[]) => {
    setResumeData(prev => ({ ...prev, languages }));
  }, []);

  const handlePersonalInfoValidation = useCallback((isValid: boolean, errors: Record<string, string>) => {
    setValidation(prev => ({
      ...prev,
      personalInfo: {
        isValid,
        errors,
        isComplete: isValid && Object.keys(resumeData.personalInfo || {}).length > 0
      }
    }));
  }, [resumeData.personalInfo]);

  const handleSummaryValidation = useCallback((isValid: boolean, errors: Record<string, string>) => {
    setValidation(prev => ({
      ...prev,
      summary: {
        isValid,
        errors,
        isComplete: isValid && (resumeData.summary?.length || 0) > 0
      }
    }));
  }, [resumeData.summary]);

  const handleExperienceValidation = useCallback((isValid: boolean, errors: Record<string, string>) => {
    setValidation(prev => ({
      ...prev,
      experience: {
        isValid,
        errors,
        isComplete: isValid && (resumeData.experience?.length || 0) > 0
      }
    }));
  }, [resumeData.experience]);

  const handleEducationValidation = useCallback((isValid: boolean, errors: Record<string, string>) => {
    setValidation(prev => ({
      ...prev,
      education: {
        isValid,
        errors,
        isComplete: isValid && (resumeData.education?.length || 0) > 0
      }
    }));
  }, [resumeData.education]);

  const handleSkillsValidation = useCallback((isValid: boolean, errors: Record<string, string>) => {
    setValidation(prev => ({
      ...prev,
      skills: {
        isValid,
        errors,
        isComplete: isValid && (resumeData.skills?.length || 0) > 0
      }
    }));
  }, [resumeData.skills]);

  const handleCertificationsValidation = useCallback((isValid: boolean, errors: Record<string, string>) => {
    setValidation(prev => ({
      ...prev,
      certifications: {
        isValid,
        errors,
        isComplete: isValid
      }
    }));
  }, []);

  const handleLanguagesValidation = useCallback((isValid: boolean, errors: Record<string, string>) => {
    setValidation(prev => ({
      ...prev,
      languages: {
        isValid,
        errors,
        isComplete: isValid
      }
    }));
  }, []);

  const handleManualSave = async () => {
    if (!onSave) return;

    setIsSaving(true);
    try {
      await onSave(resumeData);
      setLastSaved(new Date());
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Manual save failed:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const getTabStatus = (tabId: string) => {
    const sectionValidation = validation[tabId as keyof ValidationState] as SectionValidation;
    if (!sectionValidation) return 'incomplete';
    
    if (sectionValidation.isValid && sectionValidation.isComplete) return 'complete';
    if (Object.keys(sectionValidation.errors).length > 0) return 'error';
    return 'incomplete';
  };

  const getTabBadge = (tabId: string) => {
    const status = getTabStatus(tabId);
    switch (status) {
      case 'complete': return <CheckCircle className="h-3 w-3 text-green-500" />;
      case 'error': return <AlertCircle className="h-3 w-3 text-red-500" />;
      default: return <Clock className="h-3 w-3 text-gray-400" />;
    }
  };

  return (
    <div className={`resume-data-editor ${className}`}>
      {/* Header */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Resume Content Editor
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                Fill in your information to create your professional resume
              </p>
            </div>
            
            <div className="flex items-center gap-3">
              {/* Save Status */}
              <div className="text-sm text-gray-500">
                {isSaving ? (
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                    Saving...
                  </div>
                ) : lastSaved ? (
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-3 w-3 text-green-500" />
                    Saved {lastSaved.toLocaleTimeString()}
                  </div>
                ) : hasUnsavedChanges ? (
                  <div className="flex items-center gap-2">
                    <Clock className="h-3 w-3 text-yellow-500" />
                    Unsaved changes
                  </div>
                ) : null}
              </div>

              {/* PDF Export Button */}
              {showPDFExport && templateId && (
                <PDFExportButton
                  resumeData={resumeData}
                  templateId={templateId}
                  templateName={templateName}
                  variant="outline"
                  size="sm"
                />
              )}

              {/* Manual Save Button */}
              {onSave && (
                <Button
                  onClick={handleManualSave}
                  disabled={isSaving}
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  {isSaving ? 'Saving...' : 'Save'}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Resume Completion</span>
              <span className="text-sm text-gray-600">
                {validation.overall.completionPercentage}%
              </span>
            </div>
            <Progress value={validation.overall.completionPercentage} className="h-2" />
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>
                {validation.overall.requiredSectionsComplete} of {validation.overall.totalRequiredSections} required sections complete
              </span>
              <span>
                {validation.overall.isValid ? 'Ready to export' : 'Complete required sections'}
              </span>
            </div>
          </div>

          {/* Validation Alert */}
          {!validation.overall.isValid && (
            <Alert className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Please complete the required sections (Personal Information and Work Experience) to create your resume.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Form Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="personal" className="flex items-center gap-1 text-xs">
            {getTabBadge('personalInfo')}
            Personal
            <Badge variant="destructive" className="text-xs ml-1">Required</Badge>
          </TabsTrigger>
          <TabsTrigger value="summary" className="flex items-center gap-1 text-xs">
            {getTabBadge('summary')}
            Summary
          </TabsTrigger>
          <TabsTrigger value="experience" className="flex items-center gap-1 text-xs">
            {getTabBadge('experience')}
            Experience
            <Badge variant="destructive" className="text-xs ml-1">Required</Badge>
          </TabsTrigger>
          <TabsTrigger value="education" className="flex items-center gap-1 text-xs">
            {getTabBadge('education')}
            Education
          </TabsTrigger>
          <TabsTrigger value="skills" className="flex items-center gap-1 text-xs">
            {getTabBadge('skills')}
            Skills
          </TabsTrigger>
          <TabsTrigger value="certifications" className="flex items-center gap-1 text-xs">
            {getTabBadge('certifications')}
            Certs
          </TabsTrigger>
          <TabsTrigger value="languages" className="flex items-center gap-1 text-xs">
            {getTabBadge('languages')}
            Languages
          </TabsTrigger>
        </TabsList>

        <TabsContent value="personal">
          <PersonalInfoForm
            data={resumeData.personalInfo}
            onChange={handlePersonalInfoChange}
            onValidationChange={handlePersonalInfoValidation}
          />
        </TabsContent>

        <TabsContent value="summary">
          <SummaryForm
            data={resumeData.summary || ''}
            onChange={handleSummaryChange}
            onValidationChange={handleSummaryValidation}
            userProfile={{
              jobTitle: resumeData.personalInfo?.jobTitle,
              experience: resumeData.experience?.length ? `${resumeData.experience.length}` : undefined,
              industry: 'Technology' // TODO: Derive from experience or user input
            }}
          />
        </TabsContent>

        <TabsContent value="experience">
          <ExperienceForm
            data={resumeData.experience || []}
            onChange={handleExperienceChange}
            onValidationChange={handleExperienceValidation}
          />
        </TabsContent>

        <TabsContent value="education">
          <EducationForm
            data={resumeData.education || []}
            onChange={handleEducationChange}
            onValidationChange={handleEducationValidation}
          />
        </TabsContent>

        <TabsContent value="skills">
          <SkillsForm
            data={resumeData.skills || []}
            onChange={handleSkillsChange}
            onValidationChange={handleSkillsValidation}
          />
        </TabsContent>

        <TabsContent value="certifications">
          <CertificationsForm
            data={resumeData.certifications || []}
            onChange={handleCertificationsChange}
            onValidationChange={handleCertificationsValidation}
          />
        </TabsContent>

        <TabsContent value="languages">
          <LanguagesForm
            data={resumeData.languages || []}
            onChange={handleLanguagesChange}
            onValidationChange={handleLanguagesValidation}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
