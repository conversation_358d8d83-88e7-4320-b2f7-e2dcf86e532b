'use client';

import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  Globe, 
  Plus, 
  Trash2, 
  Search,
  Star,
  StarHalf,
  Check
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import FormSection, { FormField, FormRow, FormEntry } from './FormSection';
import { 
  languagesFormSchema, 
  LanguageFormData,
  defaultLanguage 
} from '@/lib/validations/resume-schemas';
import { Language } from '@/types';

interface LanguagesFormProps {
  data: Language[];
  onChange: (data: Language[]) => void;
  onValidationChange?: (isValid: boolean, errors: Record<string, string>) => void;
}

interface LanguageEntryProps {
  language: Language;
  index: number;
  total: number;
  onUpdate: (index: number, data: Language) => void;
  onRemove: (index: number) => void;
  canRemove: boolean;
}

// Common languages list for suggestions
const COMMON_LANGUAGES = [
  'English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese', 'Russian',
  'Chinese (Mandarin)', 'Chinese (Cantonese)', 'Japanese', 'Korean', 'Arabic',
  'Hindi', 'Dutch', 'Swedish', 'Norwegian', 'Danish', 'Finnish', 'Polish',
  'Czech', 'Hungarian', 'Romanian', 'Bulgarian', 'Greek', 'Turkish', 'Hebrew',
  'Thai', 'Vietnamese', 'Indonesian', 'Malay', 'Tagalog', 'Swahili', 'Urdu'
];

const PROFICIENCY_LEVELS = [
  { 
    value: 'Basic', 
    label: 'Basic', 
    description: 'Can understand and use familiar everyday expressions',
    stars: 1
  },
  { 
    value: 'Conversational', 
    label: 'Conversational', 
    description: 'Can communicate in simple and routine tasks',
    stars: 2
  },
  { 
    value: 'Fluent', 
    label: 'Fluent', 
    description: 'Can express ideas fluently and spontaneously',
    stars: 3
  },
  { 
    value: 'Native', 
    label: 'Native', 
    description: 'Native or bilingual proficiency',
    stars: 4
  }
];

function ProficiencyStars({ level }: { level: string }) {
  const proficiency = PROFICIENCY_LEVELS.find(p => p.value === level);
  const stars = proficiency?.stars || 0;
  
  return (
    <div className="flex items-center gap-1">
      {[1, 2, 3, 4].map((star) => (
        <Star
          key={star}
          className={`h-3 w-3 ${
            star <= stars 
              ? 'text-yellow-400 fill-current' 
              : 'text-gray-300'
          }`}
        />
      ))}
    </div>
  );
}

function LanguageEntry({
  language,
  index,
  total,
  onUpdate,
  onRemove,
  canRemove
}: LanguageEntryProps) {
  const [isExpanded, setIsExpanded] = useState(true);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    setValue,
    trigger
  } = useForm<LanguageFormData>({
    resolver: zodResolver(languagesFormSchema.shape.languages.element),
    defaultValues: language,
    mode: 'onChange'
  });

  const watchedValues = watch();

  // Update parent when form changes
  useEffect(() => {
    const subscription = watch((value) => {
      if (value) {
        onUpdate(index, value as Language);
      }
    });
    return () => subscription.unsubscribe();
  }, [watch, onUpdate, index]);

  const handleProficiencyChange = (proficiency: string) => {
    setValue('proficiency', proficiency as Language['proficiency']);
    trigger('proficiency');
  };

  const proficiencyLevel = PROFICIENCY_LEVELS.find(p => p.value === watchedValues.proficiency);

  return (
    <FormEntry
      title={`${watchedValues.name || 'Language'} - ${watchedValues.proficiency || 'Proficiency'}`}
      index={index}
      total={total}
      onRemove={canRemove ? () => onRemove(index) : undefined}
      canRemove={canRemove}
      isCollapsible={true}
      defaultCollapsed={!isExpanded}
      hasErrors={Object.keys(errors).length > 0}
      errorCount={Object.keys(errors).length}
      badge={<ProficiencyStars level={watchedValues.proficiency || 'Basic'} />}
    >
      <div className="space-y-4">
        <FormRow>
          <FormField
            label="Language"
            required={true}
            error={errors.name?.message}
            helpText="Name of the language"
          >
            <div className="relative">
              <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                {...register('name')}
                placeholder="Spanish"
                className="pl-10"
              />
            </div>
          </FormField>

          <FormField
            label="Proficiency Level"
            required={true}
            error={errors.proficiency?.message}
            helpText="Your skill level in this language"
          >
            <Select
              value={watchedValues.proficiency}
              onValueChange={handleProficiencyChange}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select proficiency level" />
              </SelectTrigger>
              <SelectContent>
                {PROFICIENCY_LEVELS.map((level) => (
                  <SelectItem key={level.value} value={level.value}>
                    <div className="flex items-center gap-2">
                      <ProficiencyStars level={level.value} />
                      <div>
                        <div className="font-medium">{level.label}</div>
                        <div className="text-xs text-gray-500">{level.description}</div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormField>
        </FormRow>

        {/* Proficiency Description */}
        {proficiencyLevel && (
          <div className="p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <ProficiencyStars level={proficiencyLevel.value} />
              <span className="text-sm font-medium text-blue-900">
                {proficiencyLevel.label} Level
              </span>
            </div>
            <p className="text-sm text-blue-800">
              {proficiencyLevel.description}
            </p>
          </div>
        )}
      </div>
    </FormEntry>
  );
}

export default function LanguagesForm({
  data,
  onChange,
  onValidationChange
}: LanguagesFormProps) {
  const [languages, setLanguages] = useState<Language[]>(data || []);
  const [searchQuery, setSearchQuery] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);

  const {
    formState: { isValid, errors }
  } = useForm({
    resolver: zodResolver(languagesFormSchema),
    defaultValues: { languages },
    mode: 'onChange'
  });

  // Update parent when languages change
  useEffect(() => {
    onChange(languages);
  }, [languages, onChange]);

  // Update validation state
  useEffect(() => {
    const errorMessages: Record<string, string> = {};
    Object.entries(errors).forEach(([key, error]) => {
      if (error?.message) {
        errorMessages[key] = error.message;
      }
    });
    onValidationChange?.(isValid, errorMessages);
  }, [isValid, errors, onValidationChange]);

  // Update suggestions based on search
  useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = COMMON_LANGUAGES
        .filter(lang => 
          lang.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !languages.some(l => l.name.toLowerCase() === lang.toLowerCase())
        )
        .slice(0, 8);
      setSuggestions(filtered);
    } else {
      setSuggestions([]);
    }
  }, [searchQuery, languages]);

  const handleAddLanguage = (languageName?: string) => {
    const newLanguage: Language = {
      ...defaultLanguage,
      id: `lang-${Date.now()}`,
      name: languageName || '',
      proficiency: 'Conversational'
    };
    setLanguages([...languages, newLanguage]);
    setSearchQuery('');
  };

  const handleUpdateLanguage = (index: number, updatedLanguage: Language) => {
    const newLanguages = [...languages];
    newLanguages[index] = updatedLanguage;
    setLanguages(newLanguages);
  };

  const handleRemoveLanguage = (index: number) => {
    const newLanguages = languages.filter((_, i) => i !== index);
    setLanguages(newLanguages);
  };

  const totalErrors = Object.keys(errors).length;

  return (
    <FormSection
      title="Languages"
      description="Languages you speak and your proficiency levels"
      icon={<Globe className="h-4 w-4" />}
      hasErrors={totalErrors > 0}
      errorCount={totalErrors}
      onAdd={() => handleAddLanguage()}
      canAdd={true}
      addButtonText="Add Language"
      badge={`${languages.length} language${languages.length !== 1 ? 's' : ''}`}
      helpText="Include languages that are relevant to your target role or industry"
    >
      <div className="space-y-6">
        {/* Quick Add Languages */}
        {languages.length === 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Quick Add Common Languages</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search for a language..."
                    className="pl-10"
                  />
                </div>
                
                {suggestions.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {suggestions.map((lang, index) => (
                      <Button
                        key={index}
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => handleAddLanguage(lang)}
                        className="justify-start text-left h-auto p-2"
                      >
                        <Plus className="h-3 w-3 mr-1 flex-shrink-0" />
                        <span className="truncate">{lang}</span>
                      </Button>
                    ))}
                  </div>
                )}
                
                {searchQuery && suggestions.length === 0 && (
                  <div className="text-center py-4">
                    <p className="text-sm text-gray-500 mb-2">
                      No matches found for "{searchQuery}"
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => handleAddLanguage(searchQuery)}
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Add "{searchQuery}"
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Language Entries */}
        {languages.length === 0 ? (
          <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
            <Globe className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600 mb-4">No languages added yet</p>
            <Button onClick={() => handleAddLanguage()} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Your First Language
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {languages.map((language, index) => (
              <LanguageEntry
                key={language.id || index}
                language={language}
                index={index}
                total={languages.length}
                onUpdate={handleUpdateLanguage}
                onRemove={handleRemoveLanguage}
                canRemove={true}
              />
            ))}
          </div>
        )}

        {/* Proficiency Guide */}
        {languages.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Proficiency Level Guide</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {PROFICIENCY_LEVELS.map((level) => (
                  <div key={level.value} className="flex items-center gap-3">
                    <ProficiencyStars level={level.value} />
                    <div className="flex-1">
                      <div className="font-medium text-sm">{level.label}</div>
                      <div className="text-xs text-gray-500">{level.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Form Status */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-2">
            {languages.length > 0 && isValid ? (
              <div className="flex items-center gap-2 text-green-600">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Languages section complete</span>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-gray-500">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span className="text-sm">
                  {languages.length === 0 ? 'Add your languages (optional)' : 'Complete all required fields'}
                </span>
              </div>
            )}
          </div>

          <div className="text-sm text-gray-500">
            {languages.length} language{languages.length !== 1 ? 's' : ''} added
          </div>
        </div>
      </div>
    </FormSection>
  );
}
