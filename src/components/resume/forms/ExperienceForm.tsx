'use client';

import React, { useEffect, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  Briefcase, 
  Plus, 
  Trash2, 
  Calendar, 
  MapPin, 
  Building, 
  User, 
  Tag,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import FormSection, { FormField, FormRow, FormEntry } from './FormSection';
import { 
  experienceFormSchema, 
  ExperienceFormData,
  defaultExperience 
} from '@/lib/validations/resume-schemas';
import { Experience } from '@/types';

interface ExperienceFormProps {
  data: Experience[];
  onChange: (data: Experience[]) => void;
  onValidationChange?: (isValid: boolean, errors: Record<string, string>) => void;
}

interface ExperienceEntryProps {
  experience: Experience;
  index: number;
  total: number;
  onUpdate: (index: number, data: Experience) => void;
  onRemove: (index: number) => void;
  canRemove: boolean;
}

function ExperienceEntry({
  experience,
  index,
  total,
  onUpdate,
  onRemove,
  canRemove
}: ExperienceEntryProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [skillInput, setSkillInput] = useState('');

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    setValue,
    trigger,
    control
  } = useForm<ExperienceFormData>({
    resolver: zodResolver(experienceFormSchema.shape.experience.element),
    defaultValues: experience,
    mode: 'onChange'
  });

  const { fields: descriptionFields, append: appendDescription, remove: removeDescription } = useFieldArray({
    control,
    name: 'description'
  });

  const watchedValues = watch();
  const watchedCurrent = watch('current');

  // Update parent when form changes
  useEffect(() => {
    const subscription = watch((value) => {
      if (value) {
        onUpdate(index, value as Experience);
      }
    });
    return () => subscription.unsubscribe();
  }, [watch, onUpdate, index]);

  // Handle current position toggle
  useEffect(() => {
    if (watchedCurrent) {
      setValue('endDate', undefined);
    }
  }, [watchedCurrent, setValue]);

  const handleAddSkill = () => {
    if (skillInput.trim()) {
      const currentSkills = watchedValues.skills || [];
      const newSkills = [...currentSkills, skillInput.trim()];
      setValue('skills', newSkills);
      setSkillInput('');
      trigger('skills');
    }
  };

  const handleRemoveSkill = (skillIndex: number) => {
    const currentSkills = watchedValues.skills || [];
    const newSkills = currentSkills.filter((_, i) => i !== skillIndex);
    setValue('skills', newSkills);
    trigger('skills');
  };

  const handleAddDescription = () => {
    appendDescription('');
  };

  const handleRemoveDescription = (descIndex: number) => {
    removeDescription(descIndex);
  };

  const formatDate = (date: Date | undefined) => {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  };

  const parseDate = (dateString: string) => {
    return dateString ? new Date(dateString) : undefined;
  };

  return (
    <FormEntry
      title={`${watchedValues.position || 'Position'} at ${watchedValues.company || 'Company'}`}
      index={index}
      total={total}
      onRemove={canRemove ? () => onRemove(index) : undefined}
      canRemove={canRemove}
      isCollapsible={true}
      defaultCollapsed={!isExpanded}
      hasErrors={Object.keys(errors).length > 0}
      errorCount={Object.keys(errors).length}
    >
      <div className="space-y-4">
        {/* Basic Information */}
        <FormRow>
          <FormField
            label="Job Title"
            required={true}
            error={errors.position?.message}
          >
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                {...register('position')}
                placeholder="Software Engineer"
                className="pl-10"
              />
            </div>
          </FormField>

          <FormField
            label="Company"
            required={true}
            error={errors.company?.message}
          >
            <div className="relative">
              <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                {...register('company')}
                placeholder="Tech Corp Inc."
                className="pl-10"
              />
            </div>
          </FormField>
        </FormRow>

        <FormField
          label="Location"
          required={true}
          error={errors.location?.message}
        >
          <div className="relative">
            <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              {...register('location')}
              placeholder="San Francisco, CA"
              className="pl-10"
            />
          </div>
        </FormField>

        {/* Dates */}
        <div className="space-y-4">
          <FormRow>
            <FormField
              label="Start Date"
              required={true}
              error={errors.startDate?.message}
            >
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="date"
                  {...register('startDate', {
                    setValueAs: parseDate
                  })}
                  defaultValue={formatDate(experience.startDate)}
                  className="pl-10"
                />
              </div>
            </FormField>

            <FormField
              label="End Date"
              error={errors.endDate?.message}
            >
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="date"
                  {...register('endDate', {
                    setValueAs: parseDate
                  })}
                  defaultValue={formatDate(experience.endDate)}
                  disabled={watchedCurrent}
                  className="pl-10"
                />
              </div>
            </FormField>
          </FormRow>

          <div className="flex items-center space-x-2">
            <Checkbox
              id={`current-${index}`}
              {...register('current')}
              checked={watchedCurrent}
            />
            <label
              htmlFor={`current-${index}`}
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              I currently work here
            </label>
          </div>
        </div>

        {/* Job Description */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-700">
              Job Description
              <span className="text-red-500 ml-1">*</span>
            </label>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleAddDescription}
              className="flex items-center gap-1"
            >
              <Plus className="h-3 w-3" />
              Add Point
            </Button>
          </div>

          <div className="space-y-2">
            {descriptionFields.map((field, descIndex) => (
              <div key={field.id} className="flex gap-2">
                <div className="flex-1">
                  <Textarea
                    {...register(`description.${descIndex}` as const)}
                    placeholder="• Describe your key responsibilities and achievements..."
                    className="min-h-[60px] resize-none"
                  />
                </div>
                {descriptionFields.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleRemoveDescription(descIndex)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                )}
              </div>
            ))}
          </div>

          {errors.description && (
            <p className="text-sm text-red-600">
              Please add at least one job responsibility or achievement
            </p>
          )}
        </div>

        {/* Skills */}
        <div className="space-y-3">
          <label className="text-sm font-medium text-gray-700">
            Skills Used
          </label>

          <div className="flex gap-2">
            <div className="flex-1">
              <Input
                value={skillInput}
                onChange={(e) => setSkillInput(e.target.value)}
                placeholder="Add a skill (e.g., React, Python, Leadership)"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddSkill();
                  }
                }}
              />
            </div>
            <Button
              type="button"
              variant="outline"
              onClick={handleAddSkill}
              disabled={!skillInput.trim()}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          {watchedValues.skills && watchedValues.skills.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {watchedValues.skills.map((skill, skillIndex) => (
                <Badge
                  key={skillIndex}
                  variant="secondary"
                  className="flex items-center gap-1"
                >
                  <Tag className="h-3 w-3" />
                  {skill}
                  <button
                    type="button"
                    onClick={() => handleRemoveSkill(skillIndex)}
                    className="ml-1 text-gray-500 hover:text-gray-700"
                  >
                    ×
                  </button>
                </Badge>
              ))}
            </div>
          )}
        </div>
      </div>
    </FormEntry>
  );
}

export default function ExperienceForm({
  data,
  onChange,
  onValidationChange
}: ExperienceFormProps) {
  const [experiences, setExperiences] = useState<Experience[]>(data || []);

  const {
    formState: { isValid, errors }
  } = useForm({
    resolver: zodResolver(experienceFormSchema),
    defaultValues: { experience: experiences },
    mode: 'onChange'
  });

  // Update parent when experiences change
  useEffect(() => {
    onChange(experiences);
  }, [experiences, onChange]);

  // Update validation state
  useEffect(() => {
    const errorMessages: Record<string, string> = {};
    Object.entries(errors).forEach(([key, error]) => {
      if (error?.message) {
        errorMessages[key] = error.message;
      }
    });
    onValidationChange?.(isValid && experiences.length > 0, errorMessages);
  }, [isValid, errors, experiences.length, onValidationChange]);

  const handleAddExperience = () => {
    const newExperience: Experience = {
      ...defaultExperience,
      id: `exp-${Date.now()}`,
      description: ['']
    };
    setExperiences([...experiences, newExperience]);
  };

  const handleUpdateExperience = (index: number, updatedExperience: Experience) => {
    const newExperiences = [...experiences];
    newExperiences[index] = updatedExperience;
    setExperiences(newExperiences);
  };

  const handleRemoveExperience = (index: number) => {
    const newExperiences = experiences.filter((_, i) => i !== index);
    setExperiences(newExperiences);
  };

  const totalErrors = Object.keys(errors).length;

  return (
    <FormSection
      title="Work Experience"
      description="Your professional work history and achievements"
      icon={<Briefcase className="h-4 w-4" />}
      isRequired={true}
      hasErrors={totalErrors > 0}
      errorCount={totalErrors}
      onAdd={handleAddExperience}
      canAdd={true}
      addButtonText="Add Experience"
      badge={`${experiences.length} position${experiences.length !== 1 ? 's' : ''}`}
      helpText="List your work experience in reverse chronological order (most recent first)"
    >
      <div className="space-y-6">
        {experiences.length === 0 ? (
          <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
            <Briefcase className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600 mb-4">No work experience added yet</p>
            <Button onClick={handleAddExperience} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Your First Position
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {experiences.map((experience, index) => (
              <ExperienceEntry
                key={experience.id || index}
                experience={experience}
                index={index}
                total={experiences.length}
                onUpdate={handleUpdateExperience}
                onRemove={handleRemoveExperience}
                canRemove={experiences.length > 1}
              />
            ))}
          </div>
        )}

        {/* Form Status */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-2">
            {experiences.length > 0 && isValid ? (
              <div className="flex items-center gap-2 text-green-600">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Experience section complete</span>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-gray-500">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span className="text-sm">
                  {experiences.length === 0 ? 'Add your work experience' : 'Complete all required fields'}
                </span>
              </div>
            )}
          </div>

          <div className="text-sm text-gray-500">
            {experiences.length} of 5+ recommended positions
          </div>
        </div>
      </div>
    </FormSection>
  );
}
