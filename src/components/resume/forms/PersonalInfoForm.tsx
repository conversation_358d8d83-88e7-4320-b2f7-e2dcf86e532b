'use client';

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { User, Mail, Phone, MapPin, Globe, Linkedin, Github } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import FormSection, { FormField, FormRow, FormGrid } from './FormSection';
import { 
  personalInfoSchema, 
  PersonalInfoFormData, 
  defaultPersonalInfo 
} from '@/lib/validations/resume-schemas';
import { PersonalInfo } from '@/types';

interface PersonalInfoFormProps {
  data: PersonalInfo;
  onChange: (data: PersonalInfo) => void;
  onValidationChange?: (isValid: boolean, errors: Record<string, string>) => void;
}

export default function PersonalInfoForm({
  data,
  onChange,
  onValidationChange
}: PersonalInfoFormProps) {
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isValid },
    setValue,
    trigger
  } = useForm<PersonalInfoFormData>({
    resolver: zodResolver(personalInfoSchema),
    defaultValues: data || defaultPersonalInfo,
    mode: 'onChange'
  });

  // Watch all form values for real-time updates
  const watchedValues = watch();

  // Update parent component when form data changes
  useEffect(() => {
    const subscription = watch((value) => {
      if (value) {
        onChange(value as PersonalInfo);
      }
    });
    return () => subscription.unsubscribe();
  }, [watch, onChange]);

  // Update validation state
  useEffect(() => {
    const errorMessages: Record<string, string> = {};
    Object.entries(errors).forEach(([key, error]) => {
      if (error?.message) {
        errorMessages[key] = error.message;
      }
    });
    onValidationChange?.(isValid, errorMessages);
  }, [isValid, errors, onValidationChange]);

  // Update form when external data changes
  useEffect(() => {
    if (data) {
      Object.entries(data).forEach(([key, value]) => {
        setValue(key as keyof PersonalInfoFormData, value || '');
      });
      trigger();
    }
  }, [data, setValue, trigger]);

  const handleClearField = (fieldName: keyof PersonalInfoFormData) => {
    setValue(fieldName, '');
    trigger(fieldName);
  };

  const handleAutoFill = () => {
    // Auto-fill with sample data for testing
    const sampleData: PersonalInfoFormData = {
      fullName: 'Alex Johnson',
      jobTitle: 'Senior Software Engineer',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'San Francisco, CA',
      website: 'https://alexjohnson.dev',
      linkedinUrl: 'https://linkedin.com/in/alexjohnson',
      githubUrl: 'https://github.com/alexjohnson'
    };

    Object.entries(sampleData).forEach(([key, value]) => {
      setValue(key as keyof PersonalInfoFormData, value);
    });
    trigger();
  };

  const errorCount = Object.keys(errors).length;

  return (
    <FormSection
      title="Personal Information"
      description="Your contact details and professional identity"
      icon={<User className="h-4 w-4" />}
      isRequired={true}
      hasErrors={errorCount > 0}
      errorCount={errorCount}
      helpText="This information will appear at the top of your resume and should be accurate and professional."
    >
      <form className="space-y-6">
        {/* Development Helper */}
        {process.env.NODE_ENV === 'development' && (
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between">
              <p className="text-sm text-blue-700">Development Mode</p>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAutoFill}
                className="text-blue-600 border-blue-300 hover:bg-blue-100"
              >
                Auto-fill Sample Data
              </Button>
            </div>
          </div>
        )}

        {/* Basic Information */}
        <div className="space-y-4">
          <FormRow>
            <FormField
              label="Full Name"
              required={true}
              error={errors.fullName?.message}
              helpText="Your full legal name as you want it to appear on your resume"
            >
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  {...register('fullName')}
                  placeholder="John Doe"
                  className="pl-10"
                />
                {watchedValues.fullName && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => handleClearField('fullName')}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
                  >
                    ×
                  </Button>
                )}
              </div>
            </FormField>

            <FormField
              label="Professional Title"
              required={true}
              error={errors.jobTitle?.message}
              helpText="Your current job title or desired position"
            >
              <Input
                {...register('jobTitle')}
                placeholder="Software Engineer"
              />
            </FormField>
          </FormRow>

          <FormRow>
            <FormField
              label="Email Address"
              required={true}
              error={errors.email?.message}
              helpText="Professional email address for contact"
            >
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  {...register('email')}
                  type="email"
                  placeholder="<EMAIL>"
                  className="pl-10"
                />
              </div>
            </FormField>

            <FormField
              label="Phone Number"
              required={true}
              error={errors.phone?.message}
              helpText="Phone number with country code"
            >
              <div className="relative">
                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  {...register('phone')}
                  type="tel"
                  placeholder="+****************"
                  className="pl-10"
                />
              </div>
            </FormField>
          </FormRow>

          <FormField
            label="Location"
            required={true}
            error={errors.location?.message}
            helpText="City, State/Province, Country"
          >
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                {...register('location')}
                placeholder="San Francisco, CA, USA"
                className="pl-10"
              />
            </div>
          </FormField>
        </div>

        {/* Online Presence */}
        <div className="space-y-4">
          <h4 className="text-md font-medium text-gray-900 border-b pb-2">
            Online Presence
            <span className="text-sm font-normal text-gray-500 ml-2">(Optional)</span>
          </h4>

          <FormField
            label="Website/Portfolio"
            error={errors.website?.message}
            helpText="Your personal website or portfolio URL"
          >
            <div className="relative">
              <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                {...register('website')}
                type="url"
                placeholder="https://johndoe.dev"
                className="pl-10"
              />
            </div>
          </FormField>

          <FormRow>
            <FormField
              label="LinkedIn Profile"
              error={errors.linkedinUrl?.message}
              helpText="Your LinkedIn profile URL"
            >
              <div className="relative">
                <Linkedin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  {...register('linkedinUrl')}
                  type="url"
                  placeholder="https://linkedin.com/in/johndoe"
                  className="pl-10"
                />
              </div>
            </FormField>

            <FormField
              label="GitHub Profile"
              error={errors.githubUrl?.message}
              helpText="Your GitHub profile URL"
            >
              <div className="relative">
                <Github className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  {...register('githubUrl')}
                  type="url"
                  placeholder="https://github.com/johndoe"
                  className="pl-10"
                />
              </div>
            </FormField>
          </FormRow>
        </div>

        {/* Form Status */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-2">
            {isValid ? (
              <div className="flex items-center gap-2 text-green-600">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">All fields valid</span>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-red-600">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span className="text-sm">
                  {errorCount} error{errorCount !== 1 ? 's' : ''} to fix
                </span>
              </div>
            )}
          </div>

          <div className="text-sm text-gray-500">
            {Object.values(watchedValues).filter(Boolean).length} of {Object.keys(watchedValues).length} fields completed
          </div>
        </div>
      </form>
    </FormSection>
  );
}
