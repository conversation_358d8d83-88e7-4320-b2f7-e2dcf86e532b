'use client';

import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  Zap, 
  Plus, 
  X, 
  Search,
  Code,
  Users,
  Briefcase,
  Lightbulb,
  Star,
  Trash2
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import FormSection, { FormField } from './FormSection';
import { 
  skillsSchema, 
  SkillsFormData 
} from '@/lib/validations/resume-schemas';

interface SkillsFormProps {
  data: string[];
  onChange: (data: string[]) => void;
  onValidationChange?: (isValid: boolean, errors: Record<string, string>) => void;
}

// Predefined skill suggestions by category
const SKILL_SUGGESTIONS = {
  technical: [
    'JavaScript', 'TypeScript', 'React', 'Vue.js', 'Angular', 'Node.js', 'Python', 'Java',
    'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', 'Swift', 'Kotlin', 'HTML', 'CSS', 'SASS',
    'SQL', 'MongoDB', 'PostgreSQL', 'MySQL', 'Redis', 'AWS', 'Azure', 'GCP', 'Docker',
    'Kubernetes', 'Git', 'Jenkins', 'CI/CD', 'REST APIs', 'GraphQL', 'Microservices',
    'Machine Learning', 'AI', 'Data Science', 'TensorFlow', 'PyTorch', 'Pandas', 'NumPy'
  ],
  soft: [
    'Leadership', 'Communication', 'Problem Solving', 'Team Collaboration', 'Project Management',
    'Critical Thinking', 'Adaptability', 'Time Management', 'Creativity', 'Analytical Thinking',
    'Attention to Detail', 'Customer Service', 'Negotiation', 'Public Speaking', 'Mentoring',
    'Strategic Planning', 'Decision Making', 'Conflict Resolution', 'Emotional Intelligence',
    'Cross-functional Collaboration', 'Agile Methodology', 'Scrum', 'Change Management'
  ],
  tools: [
    'Figma', 'Adobe Creative Suite', 'Sketch', 'InVision', 'Photoshop', 'Illustrator',
    'After Effects', 'Premiere Pro', 'Slack', 'Jira', 'Confluence', 'Trello', 'Asana',
    'Notion', 'Microsoft Office', 'Google Workspace', 'Salesforce', 'HubSpot', 'Tableau',
    'Power BI', 'Excel', 'PowerPoint', 'Word', 'Outlook', 'Zoom', 'Teams', 'Webex'
  ],
  languages: [
    'English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese', 'Russian', 'Chinese',
    'Japanese', 'Korean', 'Arabic', 'Hindi', 'Dutch', 'Swedish', 'Norwegian', 'Danish'
  ]
};

const SKILL_CATEGORIES = [
  { id: 'all', name: 'All Skills', icon: Star, skills: [] },
  { id: 'technical', name: 'Technical', icon: Code, skills: SKILL_SUGGESTIONS.technical },
  { id: 'soft', name: 'Soft Skills', icon: Users, skills: SKILL_SUGGESTIONS.soft },
  { id: 'tools', name: 'Tools & Software', icon: Briefcase, skills: SKILL_SUGGESTIONS.tools },
  { id: 'languages', name: 'Languages', icon: Lightbulb, skills: SKILL_SUGGESTIONS.languages }
];

export default function SkillsForm({
  data,
  onChange,
  onValidationChange
}: SkillsFormProps) {
  const [skills, setSkills] = useState<string[]>(data || []);
  const [skillInput, setSkillInput] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [suggestions, setSuggestions] = useState<string[]>([]);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isValid },
    setValue,
    trigger
  } = useForm<SkillsFormData>({
    resolver: zodResolver(skillsSchema),
    defaultValues: { skills },
    mode: 'onChange'
  });

  // Update form when skills change
  useEffect(() => {
    setValue('skills', skills);
    trigger('skills');
    onChange(skills);
  }, [skills, setValue, trigger, onChange]);

  // Update validation state
  useEffect(() => {
    const errorMessages: Record<string, string> = {};
    if (errors.skills?.message) {
      errorMessages.skills = errors.skills.message;
    }
    onValidationChange?.(isValid, errorMessages);
  }, [isValid, errors, onValidationChange]);

  // Update suggestions based on search and category
  useEffect(() => {
    let allSuggestions: string[] = [];
    
    if (activeCategory === 'all') {
      allSuggestions = Object.values(SKILL_SUGGESTIONS).flat();
    } else {
      const category = SKILL_CATEGORIES.find(cat => cat.id === activeCategory);
      allSuggestions = category?.skills || [];
    }

    // Filter by search query and exclude already added skills
    const filtered = allSuggestions
      .filter(skill => 
        skill.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !skills.includes(skill)
      )
      .slice(0, 20); // Limit suggestions

    setSuggestions(filtered);
  }, [searchQuery, activeCategory, skills]);

  const handleAddSkill = (skill: string) => {
    const trimmedSkill = skill.trim();
    if (trimmedSkill && !skills.includes(trimmedSkill)) {
      setSkills([...skills, trimmedSkill]);
      setSkillInput('');
      setSearchQuery('');
    }
  };

  const handleRemoveSkill = (skillToRemove: string) => {
    setSkills(skills.filter(skill => skill !== skillToRemove));
  };

  const handleClearAllSkills = () => {
    setSkills([]);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (skillInput.trim()) {
        handleAddSkill(skillInput);
      }
    }
  };

  const getSkillsByCategory = (categoryId: string) => {
    if (categoryId === 'all') return skills;
    
    const category = SKILL_CATEGORIES.find(cat => cat.id === categoryId);
    if (!category) return [];
    
    return skills.filter(skill => 
      category.skills.some(catSkill => 
        catSkill.toLowerCase() === skill.toLowerCase()
      )
    );
  };

  const errorCount = errors.skills ? 1 : 0;

  return (
    <FormSection
      title="Skills & Expertise"
      description="Showcase your technical and soft skills"
      icon={<Zap className="h-4 w-4" />}
      hasErrors={errorCount > 0}
      errorCount={errorCount}
      badge={`${skills.length} skill${skills.length !== 1 ? 's' : ''}`}
      helpText="Add skills relevant to your target role. Include both technical and soft skills."
    >
      <div className="space-y-6">
        {/* Skill Input */}
        <div className="space-y-3">
          <FormField
            label="Add Skills"
            error={errors.skills?.message}
            helpText="Type a skill and press Enter, or select from suggestions below"
          >
            <div className="flex gap-2">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  value={skillInput}
                  onChange={(e) => {
                    setSkillInput(e.target.value);
                    setSearchQuery(e.target.value);
                  }}
                  onKeyPress={handleKeyPress}
                  placeholder="e.g., JavaScript, Leadership, Project Management"
                  className="pl-10"
                />
              </div>
              <Button
                type="button"
                onClick={() => handleAddSkill(skillInput)}
                disabled={!skillInput.trim() || skills.includes(skillInput.trim())}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </FormField>

          {/* Quick Actions */}
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {skills.length < 3 && (
                <span className="text-red-600">Add at least 3 skills</span>
              )}
              {skills.length >= 3 && skills.length <= 20 && (
                <span className="text-green-600">Good number of skills</span>
              )}
              {skills.length > 20 && (
                <span className="text-yellow-600">Consider reducing to most relevant skills</span>
              )}
            </div>
            
            {skills.length > 0 && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleClearAllSkills}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-3 w-3 mr-1" />
                Clear All
              </Button>
            )}
          </div>
        </div>

        {/* Current Skills */}
        {skills.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Your Skills ({skills.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {skills.map((skill, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="flex items-center gap-1 text-sm"
                  >
                    {skill}
                    <button
                      type="button"
                      onClick={() => handleRemoveSkill(skill)}
                      className="ml-1 text-gray-500 hover:text-gray-700"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Skill Suggestions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Skill Suggestions</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={activeCategory} onValueChange={setActiveCategory}>
              <TabsList className="grid w-full grid-cols-5">
                {SKILL_CATEGORIES.map((category) => {
                  const Icon = category.icon;
                  const categorySkillCount = getSkillsByCategory(category.id).length;
                  
                  return (
                    <TabsTrigger
                      key={category.id}
                      value={category.id}
                      className="flex items-center gap-1 text-xs"
                    >
                      <Icon className="h-3 w-3" />
                      {category.name}
                      {categorySkillCount > 0 && (
                        <Badge variant="secondary" className="text-xs ml-1">
                          {categorySkillCount}
                        </Badge>
                      )}
                    </TabsTrigger>
                  );
                })}
              </TabsList>

              {SKILL_CATEGORIES.map((category) => (
                <TabsContent key={category.id} value={category.id} className="mt-4">
                  {suggestions.length > 0 ? (
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                      {suggestions.map((skill, index) => (
                        <Button
                          key={index}
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => handleAddSkill(skill)}
                          className="justify-start text-left h-auto p-2"
                        >
                          <Plus className="h-3 w-3 mr-1 flex-shrink-0" />
                          <span className="truncate">{skill}</span>
                        </Button>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <div className="text-sm">
                        {searchQuery ? (
                          <>No skills found matching "{searchQuery}"</>
                        ) : (
                          <>All {category.name.toLowerCase()} skills have been added</>
                        )}
                      </div>
                    </div>
                  )}
                </TabsContent>
              ))}
            </Tabs>
          </CardContent>
        </Card>

        {/* Form Status */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-2">
            {skills.length >= 3 && isValid ? (
              <div className="flex items-center gap-2 text-green-600">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Skills section complete</span>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-gray-500">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span className="text-sm">
                  {skills.length === 0 ? 'Add your skills' : 
                   skills.length < 3 ? `Add ${3 - skills.length} more skill${3 - skills.length > 1 ? 's' : ''}` :
                   'Skills section ready'}
                </span>
              </div>
            )}
          </div>

          <div className="text-sm text-gray-500">
            {skills.length} of 8-15 recommended skills
          </div>
        </div>
      </div>
    </FormSection>
  );
}
