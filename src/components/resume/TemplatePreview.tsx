'use client';

import React, { useState, useRef, useEffect } from 'react';
import { ZoomIn, ZoomOut, Maximize2, Download, X, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { 
  getTemplateComponent, 
  templateRegistry,
  ResumeData 
} from '@/lib/templates/template-registry';

interface TemplatePreviewProps {
  templateId: string;
  resumeData: ResumeData;
  isFullscreen?: boolean;
  onClose?: () => void;
  onSelect?: () => void;
  className?: string;
}

export default function TemplatePreview({
  templateId,
  resumeData,
  isFullscreen = false,
  onClose,
  onSelect,
  className = ''
}: TemplatePreviewProps) {
  const [zoom, setZoom] = useState(100);
  const [isLoading, setIsLoading] = useState(true);
  const previewRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const template = templateRegistry.getTemplate(templateId);
  const TemplateComponent = getTemplateComponent(templateId);

  useEffect(() => {
    // Simulate loading time for template rendering
    const timer = setTimeout(() => setIsLoading(false), 500);
    return () => clearTimeout(timer);
  }, [templateId]);

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 200));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 25));
  };

  const handleZoomReset = () => {
    setZoom(100);
  };

  const handleZoomChange = (value: number[]) => {
    setZoom(value[0]);
  };

  const handleDownload = async () => {
    if (!previewRef.current) return;

    try {
      // TODO: Implement PDF generation
      // This would typically use a library like jsPDF or Puppeteer
      console.log('Download functionality to be implemented');
    } catch (error) {
      console.error('Error downloading resume:', error);
    }
  };

  const handleFullscreen = () => {
    // TODO: Implement fullscreen mode
    console.log('Fullscreen mode to be implemented');
  };

  if (!TemplateComponent || !template) {
    return (
      <div className={`flex items-center justify-center h-96 ${className}`}>
        <div className="text-center">
          <div className="text-gray-500 mb-2">Template not found</div>
          <p className="text-sm text-gray-400">
            The selected template could not be loaded
          </p>
        </div>
      </div>
    );
  }

  const metadata = template.metadata;

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-white">
        <div className="flex items-center space-x-4">
          <div>
            <h3 className="font-semibold text-lg">{metadata?.name || 'Template Preview'}</h3>
            <p className="text-sm text-gray-600">{metadata?.description}</p>
          </div>
          <div className="flex items-center space-x-2">
            {metadata?.isPremium && (
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                Premium
              </Badge>
            )}
            {metadata?.atsOptimized && (
              <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">
                ATS Optimized
              </Badge>
            )}
            <Badge variant="outline">
              {metadata?.category}
            </Badge>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Zoom controls */}
          <div className="flex items-center space-x-2 px-3 py-1 bg-gray-100 rounded-lg">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleZoomOut}
              disabled={zoom <= 25}
            >
              <ZoomOut className="w-4 h-4" />
            </Button>
            
            <div className="w-24">
              <Slider
                value={[zoom]}
                onValueChange={handleZoomChange}
                min={25}
                max={200}
                step={25}
                className="w-full"
              />
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleZoomIn}
              disabled={zoom >= 200}
            >
              <ZoomIn className="w-4 h-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleZoomReset}
              title="Reset zoom"
            >
              <RotateCcw className="w-4 h-4" />
            </Button>
            
            <span className="text-sm font-medium min-w-[3rem] text-center">
              {zoom}%
            </span>
          </div>

          {/* Action buttons */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleFullscreen}
          >
            <Maximize2 className="w-4 h-4 mr-2" />
            Fullscreen
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
          >
            <Download className="w-4 h-4 mr-2" />
            Download
          </Button>

          {onSelect && (
            <Button onClick={onSelect}>
              Use This Template
            </Button>
          )}

          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Preview content */}
      <div 
        ref={containerRef}
        className="flex-1 overflow-auto bg-gray-100 p-8"
      >
        <div className="flex justify-center">
          <Card className="shadow-lg">
            <div
              ref={previewRef}
              className="relative bg-white"
              style={{
                transform: `scale(${zoom / 100})`,
                transformOrigin: 'top center',
                width: metadata?.dimensions?.width || 595,
                height: metadata?.dimensions?.height || 842,
                transition: 'transform 0.2s ease-in-out'
              }}
            >
              {isLoading ? (
                <div className="absolute inset-0 flex items-center justify-center bg-white">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                    <div className="text-sm text-gray-600">Loading template...</div>
                  </div>
                </div>
              ) : (
                <TemplateComponent data={resumeData} isPreview={true} />
              )}
            </div>
          </Card>
        </div>
      </div>

      {/* Footer with template info */}
      <div className="p-4 border-t bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center space-x-4">
            <span>
              Dimensions: {metadata?.dimensions?.width} × {metadata?.dimensions?.height}px
            </span>
            <span>
              Layout: {metadata?.layout}
            </span>
            <span>
              Sections: {metadata?.sections?.length || 0}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            {metadata?.tags?.map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Fullscreen preview modal component
export function FullscreenTemplatePreview({
  templateId,
  resumeData,
  isOpen,
  onClose,
  onSelect
}: {
  templateId: string;
  resumeData: ResumeData;
  isOpen: boolean;
  onClose: () => void;
  onSelect?: () => void;
}) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-75 flex items-center justify-center">
      <div className="w-full h-full max-w-7xl max-h-full bg-white rounded-lg overflow-hidden">
        <TemplatePreview
          templateId={templateId}
          resumeData={resumeData}
          isFullscreen={true}
          onClose={onClose}
          onSelect={onSelect}
          className="h-full"
        />
      </div>
    </div>
  );
}
