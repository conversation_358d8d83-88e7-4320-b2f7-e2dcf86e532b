'use client';

import React, { useState, useMemo } from 'react';
import { Search, Filter, Grid, List, Star, Crown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  getAllTemplates,
  getTemplatesByCategory,
  searchTemplates,
  TemplateComponent
} from '@/lib/templates/template-registry';
import { TEMPLATE_CATEGORIES, TEMPLATE_CATALOG } from '@/lib/templates/template-catalog';

interface TemplateSelectorProps {
  selectedTemplateId?: string;
  onTemplateSelect: (templateId: string) => void;
  onPreview?: (templateId: string) => void;
  className?: string;
}

export default function TemplateSelector({
  selectedTemplateId,
  onTemplateSelect,
  onPreview,
  className = ''
}: TemplateSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFavorites, setShowFavorites] = useState(false);

  // Get templates based on current filters
  const filteredTemplates = useMemo(() => {
    let templates = TEMPLATE_CATALOG;

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      templates = templates.filter(template =>
        template.name.toLowerCase().includes(query) ||
        template.description.toLowerCase().includes(query) ||
        template.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Category filter
    if (selectedCategory !== 'all') {
      templates = templates.filter(template => template.category === selectedCategory);
    }

    // Filter by favorites if enabled
    if (showFavorites) {
      // TODO: Implement favorites functionality with user preferences
      templates = templates.filter(template => template.isPremium === false);
    }

    return templates;
  }, [searchQuery, selectedCategory, showFavorites]);

  const handleTemplateSelect = (templateId: string) => {
    onTemplateSelect(templateId);
  };

  const handlePreview = (templateId: string) => {
    if (onPreview) {
      onPreview(templateId);
    }
  };

  const TemplateCard = ({ template }: { template: TemplateComponent }) => {
    const isSelected = selectedTemplateId === template.id;
    const metadata = template.metadata;

    return (
      <Card 
        className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
          isSelected ? 'ring-2 ring-blue-500 shadow-lg' : ''
        }`}
        onClick={() => handleTemplateSelect(template.id)}
      >
        <CardContent className="p-4">
          {/* Template Preview */}
          <div className="relative mb-3 aspect-[3/4] bg-gray-100 rounded-lg overflow-hidden">
            {/* Placeholder for template preview image */}
            <div className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
              <div className="text-gray-500 text-sm font-medium">
                {metadata?.name || 'Template Preview'}
              </div>
            </div>
            
            {/* Premium badge */}
            {metadata?.isPremium && (
              <div className="absolute top-2 right-2">
                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                  <Crown className="w-3 h-3 mr-1" />
                  Premium
                </Badge>
              </div>
            )}

            {/* ATS Optimized badge */}
            {metadata?.atsOptimized && (
              <div className="absolute top-2 left-2">
                <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">
                  ATS
                </Badge>
              </div>
            )}

            {/* Preview button */}
            <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center opacity-0 hover:opacity-100">
              <Button
                variant="secondary"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  handlePreview(template.id);
                }}
              >
                Preview
              </Button>
            </div>
          </div>

          {/* Template Info */}
          <div className="space-y-2">
            <div className="flex items-start justify-between">
              <h3 className="font-semibold text-sm line-clamp-2">
                {metadata?.name || 'Untitled Template'}
              </h3>
              <Button
                variant="ghost"
                size="sm"
                className="p-1 h-auto"
                onClick={(e) => {
                  e.stopPropagation();
                  // TODO: Implement favorites functionality
                }}
              >
                <Star className="w-4 h-4" />
              </Button>
            </div>
            
            <p className="text-xs text-gray-600 line-clamp-2">
              {metadata?.description || 'No description available'}
            </p>

            {/* Template tags */}
            <div className="flex flex-wrap gap-1">
              <Badge variant="outline" className="text-xs">
                {metadata?.category || 'General'}
              </Badge>
              <Badge variant="outline" className="text-xs">
                {metadata?.layout || 'Standard'}
              </Badge>
              {metadata?.hasPhoto && (
                <Badge variant="outline" className="text-xs">
                  Photo
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const TemplateListItem = ({ template }: { template: TemplateComponent }) => {
    const isSelected = selectedTemplateId === template.id;
    const metadata = template.metadata;

    return (
      <Card 
        className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
          isSelected ? 'ring-2 ring-blue-500 shadow-md' : ''
        }`}
        onClick={() => handleTemplateSelect(template.id)}
      >
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            {/* Template thumbnail */}
            <div className="w-16 h-20 bg-gray-100 rounded flex-shrink-0 flex items-center justify-center">
              <div className="text-gray-500 text-xs">Preview</div>
            </div>

            {/* Template info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between mb-2">
                <h3 className="font-semibold text-sm truncate">
                  {metadata?.name || 'Untitled Template'}
                </h3>
                <div className="flex items-center space-x-2 ml-2">
                  {metadata?.isPremium && (
                    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                      <Crown className="w-3 h-3 mr-1" />
                      Premium
                    </Badge>
                  )}
                  {metadata?.atsOptimized && (
                    <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">
                      ATS
                    </Badge>
                  )}
                </div>
              </div>
              
              <p className="text-xs text-gray-600 line-clamp-2 mb-2">
                {metadata?.description || 'No description available'}
              </p>

              <div className="flex items-center justify-between">
                <div className="flex flex-wrap gap-1">
                  <Badge variant="outline" className="text-xs">
                    {metadata?.category || 'General'}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {metadata?.layout || 'Standard'}
                  </Badge>
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    handlePreview(template.id);
                  }}
                >
                  Preview
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Choose a Template</h2>
          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Search and filters */}
        <div className="flex items-center space-x-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button
            variant={showFavorites ? 'default' : 'outline'}
            size="sm"
            onClick={() => setShowFavorites(!showFavorites)}
          >
            <Star className="w-4 h-4 mr-2" />
            Favorites
          </Button>
        </div>
      </div>

      {/* Category tabs */}
      <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
        <TabsList className="grid w-full grid-cols-4 lg:grid-cols-7">
          {TEMPLATE_CATEGORIES.map((category) => (
            <TabsTrigger key={category.id} value={category.id} className="text-xs">
              {category.name}
              {category.count > 0 && (
                <Badge variant="secondary" className="ml-1 text-xs">
                  {category.count}
                </Badge>
              )}
            </TabsTrigger>
          ))}
        </TabsList>

        {TEMPLATE_CATEGORIES.map((category) => (
          <TabsContent key={category.id} value={category.id} className="mt-6">
            {filteredTemplates.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-500 mb-2">No templates found</div>
                <p className="text-sm text-gray-400">
                  Try adjusting your search or filter criteria
                </p>
              </div>
            ) : (
              <div className={
                viewMode === 'grid' 
                  ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                  : 'space-y-4'
              }>
                {filteredTemplates.map((template) => (
                  viewMode === 'grid' ? (
                    <TemplateCard key={template.id} template={template} />
                  ) : (
                    <TemplateListItem key={template.id} template={template} />
                  )
                ))}
              </div>
            )}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
