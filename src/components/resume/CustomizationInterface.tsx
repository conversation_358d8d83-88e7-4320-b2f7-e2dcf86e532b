'use client';

import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Off, <PERSON><PERSON>, Download, Save } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import CustomizationPanel from '@/components/customization/CustomizationPanel';
import TemplateRenderer from './TemplateRenderer';
import { ResumeContent } from '@/types';
import { TemplateCustomization } from '@/types/customization';
import { useTemplateCustomization } from '@/lib/hooks/useTemplateCustomization';

interface CustomizationInterfaceProps {
  templateId: string;
  resumeData: ResumeContent;
  onTemplateChange?: (templateId: string) => void;
  onCustomizationSave?: (customization: TemplateCustomization) => Promise<void>;
  onExportWithCustomization?: (customization: TemplateCustomization) => void;
  className?: string;
}

export default function CustomizationInterface({
  templateId,
  resumeData,
  onTemplateChange,
  onCustomizationSave,
  onExportWithCustomization,
  className = ''
}: CustomizationInterfaceProps) {
  const [showCustomization, setShowCustomization] = useState(true);
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  const {
    customization,
    isDirty,
    isLoading,
    validation,
    saveCustomization
  } = useTemplateCustomization({
    templateId,
    autoSave: false
  });

  const handleCustomizationSave = useCallback(async (customizationData: TemplateCustomization) => {
    try {
      await saveCustomization();
      if (onCustomizationSave) {
        await onCustomizationSave(customizationData);
      }
    } catch (error) {
      console.error('Failed to save customization:', error);
    }
  }, [saveCustomization, onCustomizationSave]);

  const handleExport = useCallback(() => {
    if (onExportWithCustomization) {
      onExportWithCustomization(customization);
    }
  }, [customization, onExportWithCustomization]);

  const togglePreviewMode = () => {
    setIsPreviewMode(!isPreviewMode);
  };

  const toggleCustomizationPanel = () => {
    setShowCustomization(!showCustomization);
  };

  return (
    <div className={`flex h-full ${className}`}>
      {/* Customization Panel */}
      <div className={`transition-all duration-300 ${showCustomization ? 'w-80' : 'w-12'}`}>
        <CustomizationPanel
          templateId={templateId}
          onSave={handleCustomizationSave}
          isCollapsed={!showCustomization}
          onToggleCollapse={toggleCustomizationPanel}
          className="h-full border-r"
        />
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="border-b bg-white p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Palette className="h-5 w-5 text-blue-600" />
                <h2 className="text-lg font-semibold">Template Customization</h2>
              </div>
              
              {isDirty && (
                <Badge variant="secondary" className="text-xs">
                  Unsaved Changes
                </Badge>
              )}
              
              {!validation.isValid && (
                <Badge variant="destructive" className="text-xs">
                  {validation.errors.length} Error(s)
                </Badge>
              )}
              
              {validation.atsCompatible && (
                <Badge variant="default" className="text-xs bg-green-600">
                  ATS Compatible
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-2">
              {/* Preview Mode Toggle */}
              <Button
                variant="outline"
                size="sm"
                onClick={togglePreviewMode}
                className="gap-2"
              >
                {isPreviewMode ? (
                  <>
                    <EyeOff className="h-4 w-4" />
                    Exit Preview
                  </>
                ) : (
                  <>
                    <Eye className="h-4 w-4" />
                    Preview
                  </>
                )}
              </Button>

              <Separator orientation="vertical" className="h-6" />

              {/* Save Button */}
              <Button
                onClick={() => handleCustomizationSave(customization)}
                disabled={!isDirty || isLoading}
                size="sm"
                className="gap-2"
              >
                <Save className="h-4 w-4" />
                {isLoading ? 'Saving...' : 'Save'}
              </Button>

              {/* Export Button */}
              <Button
                onClick={handleExport}
                variant="outline"
                size="sm"
                className="gap-2"
              >
                <Download className="h-4 w-4" />
                Export PDF
              </Button>
            </div>
          </div>

          {/* Validation Messages */}
          {validation.warnings.length > 0 && (
            <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="text-sm text-yellow-800">
                <strong>Suggestions:</strong>
                <ul className="mt-1 list-disc list-inside space-y-1">
                  {validation.warnings.slice(0, 3).map((warning, index) => (
                    <li key={index}>{warning.suggestion}</li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {validation.errors.length > 0 && (
            <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="text-sm text-red-800">
                <strong>Issues Found:</strong>
                <ul className="mt-1 list-disc list-inside space-y-1">
                  {validation.errors.slice(0, 3).map((error, index) => (
                    <li key={index}>{error.message}</li>
                  ))}
                </ul>
              </div>
            </div>
          )}
        </div>

        {/* Template Preview */}
        <div className="flex-1 overflow-auto bg-gray-50">
          <div className={`p-6 ${isPreviewMode ? 'bg-gray-100' : ''}`}>
            <div className="max-w-4xl mx-auto">
              {isPreviewMode ? (
                // Full preview mode - hide UI chrome
                <div className="bg-white shadow-2xl">
                  <TemplateRenderer
                    templateId={templateId}
                    resumeData={resumeData}
                    customization={customization}
                    enableCustomization={true}
                    className="print:shadow-none"
                  />
                </div>
              ) : (
                // Normal editing mode
                <Card className="shadow-2xl">
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">Live Preview</CardTitle>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <span>Template: {templateId}</span>
                        {customization && (
                          <Badge variant="outline" className="text-xs">
                            Customized
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <TemplateRenderer
                      templateId={templateId}
                      resumeData={resumeData}
                      customization={customization}
                      enableCustomization={true}
                      showMetadata={false}
                    />
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t bg-white p-4">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center gap-4">
              <span>Accessibility Score: {validation.accessibilityScore}%</span>
              <span>ATS Compatible: {validation.atsCompatible ? 'Yes' : 'No'}</span>
              {customization && (
                <span>Last Updated: {customization.updatedAt.toLocaleTimeString()}</span>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleCustomizationPanel}
                className="text-xs"
              >
                <Settings className="h-3 w-3 mr-1" />
                {showCustomization ? 'Hide' : 'Show'} Customization
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Standalone customization modal for quick access
export function CustomizationModal({
  isOpen,
  onClose,
  templateId,
  resumeData,
  onSave
}: {
  isOpen: boolean;
  onClose: () => void;
  templateId: string;
  resumeData: ResumeContent;
  onSave?: (customization: TemplateCustomization) => void;
}) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex h-[80vh]">
          <CustomizationInterface
            templateId={templateId}
            resumeData={resumeData}
            onCustomizationSave={onSave}
            className="w-full"
          />
        </div>
        
        <div className="border-t p-4 flex justify-end">
          <Button onClick={onClose} variant="outline">
            Close
          </Button>
        </div>
      </div>
    </div>
  );
}
