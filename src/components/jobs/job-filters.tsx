'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { X, Plus } from 'lucide-react';

interface JobFiltersProps {
  filters: {
    type: string;
    remote: boolean;
    salaryMin?: number;
    salaryMax?: number;
    skills: string[];
    source: string;
  };
  onFiltersChange: (filters: any) => void;
  onClose: () => void;
}

const JOB_TYPES = [
  { value: 'FULL_TIME', label: 'Full Time' },
  { value: 'PART_TIME', label: 'Part Time' },
  { value: 'CONTRACT', label: 'Contract' },
  { value: 'FREELANCE', label: 'Freelance' },
  { value: 'INTERNSHIP', label: 'Internship' },
];

const JOB_SOURCES = [
  'LinkedIn',
  'Indeed',
  'Glassdoor',
  'AngelList',
  'Stack Overflow',
  'GitHub Jobs',
  'Company Website',
];

const POPULAR_SKILLS = [
  'JavaScript',
  'Python',
  'React',
  'Node.js',
  'TypeScript',
  'AWS',
  'Docker',
  'Kubernetes',
  'SQL',
  'MongoDB',
  'GraphQL',
  'Vue.js',
  'Angular',
  'Java',
  'C#',
  'Go',
  'Rust',
  'Machine Learning',
  'Data Science',
  'DevOps',
];

export default function JobFilters({ filters, onFiltersChange, onClose }: JobFiltersProps) {
  const [newSkill, setNewSkill] = useState('');

  const handleFilterChange = (key: string, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const addSkill = (skill: string) => {
    if (skill && !filters.skills.includes(skill)) {
      handleFilterChange('skills', [...filters.skills, skill]);
    }
    setNewSkill('');
  };

  const removeSkill = (skillToRemove: string) => {
    handleFilterChange('skills', filters.skills.filter(skill => skill !== skillToRemove));
  };

  const clearAllFilters = () => {
    onFiltersChange({
      type: '',
      remote: false,
      salaryMin: undefined,
      salaryMax: undefined,
      skills: [],
      source: '',
    });
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-lg">Filters</CardTitle>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={clearAllFilters}>
            Clear All
          </Button>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Job Type */}
        <div>
          <h4 className="font-medium mb-3">Job Type</h4>
          <div className="flex flex-wrap gap-2">
            {JOB_TYPES.map((type) => (
              <Button
                key={type.value}
                variant={filters.type === type.value ? "default" : "outline"}
                size="sm"
                onClick={() => handleFilterChange('type', filters.type === type.value ? '' : type.value)}
              >
                {type.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Remote Work */}
        <div>
          <h4 className="font-medium mb-3">Work Arrangement</h4>
          <Button
            variant={filters.remote ? "default" : "outline"}
            size="sm"
            onClick={() => handleFilterChange('remote', !filters.remote)}
          >
            Remote Only
          </Button>
        </div>

        {/* Salary Range */}
        <div>
          <h4 className="font-medium mb-3">Salary Range (USD/year)</h4>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="text-sm text-gray-600 mb-1 block">Minimum</label>
              <Input
                type="number"
                placeholder="50,000"
                value={filters.salaryMin || ''}
                onChange={(e) => handleFilterChange('salaryMin', e.target.value ? parseInt(e.target.value) : undefined)}
              />
            </div>
            <div>
              <label className="text-sm text-gray-600 mb-1 block">Maximum</label>
              <Input
                type="number"
                placeholder="150,000"
                value={filters.salaryMax || ''}
                onChange={(e) => handleFilterChange('salaryMax', e.target.value ? parseInt(e.target.value) : undefined)}
              />
            </div>
          </div>
        </div>

        {/* Skills */}
        <div>
          <h4 className="font-medium mb-3">Skills</h4>
          
          {/* Selected Skills */}
          {filters.skills.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-3">
              {filters.skills.map((skill) => (
                <Badge key={skill} variant="default" className="flex items-center gap-1">
                  {skill}
                  <button
                    onClick={() => removeSkill(skill)}
                    className="ml-1 hover:bg-white/20 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
          )}

          {/* Add Custom Skill */}
          <div className="flex gap-2 mb-3">
            <Input
              placeholder="Add a skill..."
              value={newSkill}
              onChange={(e) => setNewSkill(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addSkill(newSkill);
                }
              }}
            />
            <Button
              size="sm"
              onClick={() => addSkill(newSkill)}
              disabled={!newSkill || filters.skills.includes(newSkill)}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          {/* Popular Skills */}
          <div>
            <p className="text-sm text-gray-600 mb-2">Popular skills:</p>
            <div className="flex flex-wrap gap-2">
              {POPULAR_SKILLS.filter(skill => !filters.skills.includes(skill)).slice(0, 10).map((skill) => (
                <Button
                  key={skill}
                  variant="outline"
                  size="sm"
                  onClick={() => addSkill(skill)}
                  className="text-xs"
                >
                  {skill}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Job Source */}
        <div>
          <h4 className="font-medium mb-3">Job Source</h4>
          <div className="flex flex-wrap gap-2">
            {JOB_SOURCES.map((source) => (
              <Button
                key={source}
                variant={filters.source === source ? "default" : "outline"}
                size="sm"
                onClick={() => handleFilterChange('source', filters.source === source ? '' : source)}
              >
                {source}
              </Button>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
