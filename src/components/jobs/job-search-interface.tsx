'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  MapPin, 
  Building, 
  Clock, 
  DollarSign,
  Filter,
  Bookmark,
  BookmarkCheck,
  ExternalLink,
  Briefcase,
  Users,
  Calendar
} from 'lucide-react';
import JobCard from './job-card';
import JobFilters from './job-filters';
// import { useDebounce } from '@/hooks/use-debounce';

// Simple debounce hook
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

interface Job {
  id: string;
  title: string;
  company: string;
  location: string;
  type: string;
  remote: boolean;
  salaryMin?: number;
  salaryMax?: number;
  salaryCurrency?: string;
  salaryPeriod?: string;
  description: string;
  requirements: string[];
  skills: string[];
  postedDate: string;
  applicationDeadline?: string;
  source: string;
  url: string;
  isApplied: boolean;
  isSaved: boolean;
  applicationStatus?: string;
  appliedDate?: string;
  totalApplications: number;
}

interface JobSearchResponse {
  jobs: Job[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  filters: any;
}

export default function JobSearchInterface() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '');
  const [location, setLocation] = useState(searchParams.get('location') || '');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    type: searchParams.get('type') || '',
    remote: searchParams.get('remote') === 'true',
    salaryMin: searchParams.get('salaryMin') ? parseInt(searchParams.get('salaryMin')!) : undefined,
    salaryMax: searchParams.get('salaryMax') ? parseInt(searchParams.get('salaryMax')!) : undefined,
    skills: searchParams.get('skills')?.split(',').filter(Boolean) || [],
    source: searchParams.get('source') || '',
  });

  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const debouncedLocation = useDebounce(location, 500);

  const fetchJobs = useCallback(async (page = 1) => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      
      if (debouncedSearchQuery) params.set('q', debouncedSearchQuery);
      if (debouncedLocation) params.set('location', debouncedLocation);
      if (filters.type) params.set('type', filters.type);
      if (filters.remote) params.set('remote', 'true');
      if (filters.salaryMin) params.set('salaryMin', filters.salaryMin.toString());
      if (filters.salaryMax) params.set('salaryMax', filters.salaryMax.toString());
      if (filters.skills.length > 0) params.set('skills', filters.skills.join(','));
      if (filters.source) params.set('source', filters.source);
      params.set('page', page.toString());
      params.set('limit', '20');

      const response = await fetch(`/api/jobs?${params.toString()}`);
      if (!response.ok) throw new Error('Failed to fetch jobs');
      
      const data: JobSearchResponse = await response.json();
      setJobs(data.jobs);
      setPagination(data.pagination);

      // Update URL with current search parameters
      const newUrl = `/jobs?${params.toString()}`;
      router.replace(newUrl, { scroll: false });
    } catch (error) {
      console.error('Error fetching jobs:', error);
    } finally {
      setLoading(false);
    }
  }, [debouncedSearchQuery, debouncedLocation, filters, router]);

  useEffect(() => {
    fetchJobs();
  }, [fetchJobs]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchJobs(1);
  };

  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters);
  };

  const handlePageChange = (page: number) => {
    fetchJobs(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const formatSalary = (min?: number, max?: number, currency = 'USD', period = 'YEARLY') => {
    if (!min && !max) return null;
    
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });

    const periodText = period === 'HOURLY' ? '/hr' : period === 'MONTHLY' ? '/mo' : '/yr';
    
    if (min && max) {
      return `${formatter.format(min)} - ${formatter.format(max)}${periodText}`;
    } else if (min) {
      return `${formatter.format(min)}+${periodText}`;
    } else if (max) {
      return `Up to ${formatter.format(max)}${periodText}`;
    }
    return null;
  };

  return (
    <div className="space-y-6">
      {/* Search Bar */}
      <Card>
        <CardContent className="p-6">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Job title, keywords, or company"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Location"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex gap-2">
                <Button type="submit" className="flex-1">
                  <Search className="h-4 w-4 mr-2" />
                  Search
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowFilters(!showFilters)}
                >
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Filters */}
      {showFilters && (
        <JobFilters
          filters={filters}
          onFiltersChange={handleFilterChange}
          onClose={() => setShowFilters(false)}
        />
      )}

      {/* Results Summary */}
      {pagination && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600">
            Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
            {Math.min(pagination.page * pagination.limit, pagination.totalCount)} of{' '}
            {pagination.totalCount} jobs
          </p>
          
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Sort by:</span>
            <select className="text-sm border rounded px-2 py-1">
              <option value="postedDate">Most Recent</option>
              <option value="relevance">Most Relevant</option>
              <option value="salaryMax">Highest Salary</option>
            </select>
          </div>
        </div>
      )}

      {/* Job Results */}
      <div className="space-y-4">
        {loading ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="space-y-3">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    <div className="h-3 bg-gray-200 rounded w-full"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : jobs.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No jobs found</h3>
              <p className="text-gray-600 mb-4">
                Try adjusting your search criteria or filters to find more opportunities.
              </p>
              <Button onClick={() => {
                setSearchQuery('');
                setLocation('');
                setFilters({
                  type: '',
                  remote: false,
                  salaryMin: undefined,
                  salaryMax: undefined,
                  skills: [],
                  source: '',
                });
              }}>
                Clear all filters
              </Button>
            </CardContent>
          </Card>
        ) : (
          jobs.map((job) => (
            <JobCard key={job.id} job={job} onUpdate={() => fetchJobs(pagination.page)} />
          ))
        )}
      </div>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2">
          <Button
            variant="outline"
            onClick={() => handlePageChange(pagination.page - 1)}
            disabled={!pagination.hasPreviousPage}
          >
            Previous
          </Button>
          
          {[...Array(Math.min(5, pagination.totalPages))].map((_, i) => {
            const page = Math.max(1, pagination.page - 2) + i;
            if (page > pagination.totalPages) return null;
            
            return (
              <Button
                key={page}
                variant={page === pagination.page ? "default" : "outline"}
                onClick={() => handlePageChange(page)}
              >
                {page}
              </Button>
            );
          })}
          
          <Button
            variant="outline"
            onClick={() => handlePageChange(pagination.page + 1)}
            disabled={!pagination.hasNextPage}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}
