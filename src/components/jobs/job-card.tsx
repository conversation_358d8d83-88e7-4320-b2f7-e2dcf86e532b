'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  MapPin, 
  Building, 
  Clock, 
  DollarSign,
  Bookmark,
  BookmarkCheck,
  ExternalLink,
  Users,
  Calendar,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import Link from 'next/link';

interface Job {
  id: string;
  title: string;
  company: string;
  location: string;
  type: string;
  remote: boolean;
  salaryMin?: number;
  salaryMax?: number;
  salaryCurrency?: string;
  salaryPeriod?: string;
  description: string;
  requirements: string[];
  skills: string[];
  postedDate: string;
  applicationDeadline?: string;
  source: string;
  url: string;
  isApplied: boolean;
  isSaved: boolean;
  applicationStatus?: string;
  appliedDate?: string;
  totalApplications: number;
}

interface JobCardProps {
  job: Job;
  onUpdate: () => void;
}

export default function JobCard({ job, onUpdate }: JobCardProps) {
  const [saving, setSaving] = useState(false);
  const [applying, setApplying] = useState(false);

  const formatSalary = (min?: number, max?: number, currency = 'USD', period = 'YEARLY') => {
    if (!min && !max) return null;
    
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });

    const periodText = period === 'HOURLY' ? '/hr' : period === 'MONTHLY' ? '/mo' : '/yr';
    
    if (min && max) {
      return `${formatter.format(min)} - ${formatter.format(max)}${periodText}`;
    } else if (min) {
      return `${formatter.format(min)}+${periodText}`;
    } else if (max) {
      return `Up to ${formatter.format(max)}${periodText}`;
    }
    return null;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return `${Math.ceil(diffDays / 30)} months ago`;
  };

  const handleSaveJob = async () => {
    setSaving(true);
    try {
      const method = job.isSaved ? 'DELETE' : 'POST';
      const response = await fetch(`/api/jobs/${job.id}/save`, { method });
      
      if (response.ok) {
        onUpdate();
      }
    } catch (error) {
      console.error('Error saving job:', error);
    } finally {
      setSaving(false);
    }
  };

  const getApplicationStatusBadge = () => {
    if (!job.isApplied) return null;
    
    const statusConfig = {
      APPLIED: { label: 'Applied', color: 'bg-blue-100 text-blue-800' },
      UNDER_REVIEW: { label: 'Under Review', color: 'bg-yellow-100 text-yellow-800' },
      INTERVIEW_SCHEDULED: { label: 'Interview Scheduled', color: 'bg-purple-100 text-purple-800' },
      INTERVIEWED: { label: 'Interviewed', color: 'bg-indigo-100 text-indigo-800' },
      OFFER_RECEIVED: { label: 'Offer Received', color: 'bg-green-100 text-green-800' },
      REJECTED: { label: 'Rejected', color: 'bg-red-100 text-red-800' },
      WITHDRAWN: { label: 'Withdrawn', color: 'bg-gray-100 text-gray-800' },
    };

    const config = statusConfig[job.applicationStatus as keyof typeof statusConfig];
    if (!config) return null;

    return (
      <Badge className={`${config.color} border-0`}>
        <CheckCircle className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const salary = formatSalary(job.salaryMin, job.salaryMax, job.salaryCurrency, job.salaryPeriod);

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-start justify-between mb-2">
              <h3 className="text-lg font-semibold text-gray-900 hover:text-blue-600 cursor-pointer">
                <Link href={`/jobs/${job.id}`}>
                  {job.title}
                </Link>
              </h3>
              <div className="flex items-center gap-2 ml-4">
                {getApplicationStatusBadge()}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSaveJob}
                  disabled={saving}
                  className="text-gray-500 hover:text-gray-700"
                >
                  {job.isSaved ? (
                    <BookmarkCheck className="h-4 w-4" />
                  ) : (
                    <Bookmark className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
            
            <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
              <div className="flex items-center gap-1">
                <Building className="h-4 w-4" />
                <span>{job.company}</span>
              </div>
              <div className="flex items-center gap-1">
                <MapPin className="h-4 w-4" />
                <span>{job.location}</span>
                {job.remote && <Badge variant="secondary" className="ml-1">Remote</Badge>}
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>{formatDate(job.postedDate)}</span>
              </div>
            </div>

            {salary && (
              <div className="flex items-center gap-1 text-sm text-green-600 mb-3">
                <DollarSign className="h-4 w-4" />
                <span className="font-medium">{salary}</span>
              </div>
            )}

            <p className="text-gray-700 text-sm mb-4 line-clamp-3">
              {job.description.length > 200 
                ? `${job.description.substring(0, 200)}...` 
                : job.description
              }
            </p>

            {job.skills.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-4">
                {job.skills.slice(0, 6).map((skill, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {skill}
                  </Badge>
                ))}
                {job.skills.length > 6 && (
                  <Badge variant="outline" className="text-xs">
                    +{job.skills.length - 6} more
                  </Badge>
                )}
              </div>
            )}

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-xs text-gray-500">
                <div className="flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  <span>{job.totalApplications} applicants</span>
                </div>
                <Badge variant="outline" className="text-xs">
                  {job.type.replace('_', ' ')}
                </Badge>
                <span>via {job.source}</span>
              </div>

              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" asChild>
                  <a href={job.url} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="h-4 w-4 mr-1" />
                    View Job
                  </a>
                </Button>
                
                {!job.isApplied ? (
                  <Button size="sm" asChild>
                    <Link href={`/jobs/${job.id}/apply`}>
                      Apply Now
                    </Link>
                  </Button>
                ) : (
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/applications/${job.id}`}>
                      View Application
                    </Link>
                  </Button>
                )}
              </div>
            </div>

            {job.applicationDeadline && (
              <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="flex items-center gap-1 text-sm text-yellow-800">
                  <AlertCircle className="h-4 w-4" />
                  <span>
                    Application deadline: {new Date(job.applicationDeadline).toLocaleDateString()}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
