/**
 * Analytics Dashboard Component
 * Comprehensive analytics and insights dashboard for users
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Target,
  Users,
  FileText,
  Calendar,
  Award,
  AlertCircle,
  CheckCircle,
  Clock,
  Eye,
  Download,
  Share2,
} from 'lucide-react';
import { DashboardInsights, ResumePerformanceMetrics } from '@/lib/analytics/dashboard-analytics';

interface AnalyticsDashboardProps {
  userId: string;
}

export default function AnalyticsDashboard({ userId }: AnalyticsDashboardProps) {
  const [insights, setInsights] = useState<DashboardInsights | null>(null);
  const [selectedResume, setSelectedResume] = useState<string | null>(null);
  const [resumeMetrics, setResumeMetrics] = useState<ResumePerformanceMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDashboardInsights();
  }, [userId]);

  const loadDashboardInsights = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/analytics/dashboard?type=insights');
      
      if (!response.ok) {
        throw new Error('Failed to load dashboard insights');
      }
      
      const data = await response.json();
      setInsights(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load insights');
    } finally {
      setLoading(false);
    }
  };

  const loadResumeMetrics = async (resumeId: string) => {
    try {
      const response = await fetch(`/api/analytics/dashboard?type=resume-performance&resumeId=${resumeId}`);
      
      if (!response.ok) {
        throw new Error('Failed to load resume metrics');
      }
      
      const data = await response.json();
      setResumeMetrics(data);
      setSelectedResume(resumeId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load resume metrics');
    }
  };

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 20) return 'text-green-600';
    if (rate >= 10) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'declining':
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <BarChart3 className="h-4 w-4 text-gray-600" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-red-600 mx-auto mb-2" />
          <p className="text-red-600">{error}</p>
          <Button onClick={loadDashboardInsights} className="mt-2">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  if (!insights) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Resumes</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{insights.totalResumes}</div>
            <p className="text-xs text-muted-foreground">
              {insights.activeResumes} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Applications</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{insights.totalApplications}</div>
            <p className={`text-xs ${getSuccessRateColor(insights.overallSuccessRate)}`}>
              {insights.overallSuccessRate.toFixed(1)}% success rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Interviews</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{insights.totalInterviews}</div>
            <p className="text-xs text-muted-foreground">
              {insights.totalApplications > 0 
                ? ((insights.totalInterviews / insights.totalApplications) * 100).toFixed(1)
                : 0}% interview rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Job Offers</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{insights.totalOffers}</div>
            <p className="text-xs text-muted-foreground">
              {insights.totalApplications > 0 
                ? ((insights.totalOffers / insights.totalApplications) * 100).toFixed(1)
                : 0}% offer rate
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="goals">Goals</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Best Performing Resume */}
          {insights.bestPerformingResume.resumeId && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5 text-yellow-600" />
                  Best Performing Resume
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">{insights.bestPerformingResume.resumeName}</h3>
                    <p className="text-sm text-muted-foreground">
                      {insights.bestPerformingResume.successRate.toFixed(1)}% success rate
                    </p>
                  </div>
                  <Button 
                    onClick={() => loadResumeMetrics(insights.bestPerformingResume.resumeId)}
                    variant="outline"
                  >
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Industry Benchmarks */}
          <Card>
            <CardHeader>
              <CardTitle>Industry Benchmarks</CardTitle>
              <CardDescription>
                How you compare to industry averages
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Success Rate</span>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">
                    {insights.overallSuccessRate.toFixed(1)}%
                  </span>
                  <span className="text-xs text-muted-foreground">
                    vs {insights.industryBenchmarks.averageSuccessRate}% avg
                  </span>
                </div>
              </div>
              <Progress 
                value={Math.min(insights.overallSuccessRate, 100)} 
                className="h-2" 
              />
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Interview Rate</span>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">
                    {insights.totalApplications > 0 
                      ? ((insights.totalInterviews / insights.totalApplications) * 100).toFixed(1)
                      : 0}%
                  </span>
                  <span className="text-xs text-muted-foreground">
                    vs {insights.industryBenchmarks.averageInterviewRate}% avg
                  </span>
                </div>
              </div>
              <Progress 
                value={Math.min(
                  insights.totalApplications > 0 
                    ? (insights.totalInterviews / insights.totalApplications) * 100
                    : 0, 
                  100
                )} 
                className="h-2" 
              />
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              {insights.recentActivity.length > 0 ? (
                <div className="space-y-3">
                  {insights.recentActivity.slice(0, 5).map((activity, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                      <div className="flex-1">
                        <p className="text-sm">{activity.description}</p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(activity.timestamp).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No recent activity</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          {resumeMetrics && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>{resumeMetrics.resumeName}</span>
                  {getTrendIcon(resumeMetrics.performanceTrend)}
                </CardTitle>
                <CardDescription>
                  Template: {resumeMetrics.templateName}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold">{resumeMetrics.totalApplications}</div>
                    <div className="text-xs text-muted-foreground">Applications</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{resumeMetrics.interviewCallbacks}</div>
                    <div className="text-xs text-muted-foreground">Interviews</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{resumeMetrics.jobOffers}</div>
                    <div className="text-xs text-muted-foreground">Offers</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{resumeMetrics.atsCompatibilityScore}</div>
                    <div className="text-xs text-muted-foreground">ATS Score</div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-2">
                    <Eye className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{resumeMetrics.viewCount} views</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Download className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{resumeMetrics.downloadCount} downloads</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Share2 className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{resumeMetrics.shareCount} shares</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="goals" className="space-y-4">
          <div className="grid gap-4">
            {insights.goals.map((goal) => (
              <Card key={goal.id}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="capitalize">{goal.type.replace('_', ' ')}</span>
                    <Badge variant={goal.progress >= 100 ? 'default' : 'secondary'}>
                      {goal.progress >= 100 ? 'Completed' : 'In Progress'}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span>{goal.current} / {goal.target}</span>
                    </div>
                    <Progress value={Math.min(goal.progress, 100)} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>{goal.progress.toFixed(1)}% complete</span>
                      {goal.deadline && (
                        <span>Due: {new Date(goal.deadline).toLocaleDateString()}</span>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <div className="grid gap-4">
            {insights.recommendations.map((recommendation, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{recommendation.title}</span>
                    <Badge className={getPriorityColor(recommendation.priority)}>
                      {recommendation.priority}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-3">
                    {recommendation.description}
                  </p>
                  {recommendation.actionUrl && (
                    <Button size="sm" asChild>
                      <a href={recommendation.actionUrl}>Take Action</a>
                    </Button>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
