import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import React from 'react';
import PersonalInfoForm from '@/components/resume/forms/PersonalInfoForm';
import SummaryForm from '@/components/resume/forms/SummaryForm';
import ExperienceForm from '@/components/resume/forms/ExperienceForm';
import EducationForm from '@/components/resume/forms/EducationForm';
import SkillsForm from '@/components/resume/forms/SkillsForm';
import CertificationsForm from '@/components/resume/forms/CertificationsForm';
import LanguagesForm from '@/components/resume/forms/LanguagesForm';
import ResumeDataEditor from '@/components/resume/forms/ResumeDataEditor';
import { PersonalInfo, Experience, Education, Certification, Language, ResumeContent } from '@/types';

// Mock data for testing
const mockPersonalInfo: PersonalInfo = {
  fullName: '<PERSON>',
  email: '<EMAIL>',
  phone: '+****************',
  location: 'San Francisco, CA',
  website: 'https://johndoe.dev',
  linkedinUrl: 'https://linkedin.com/in/johndoe',
  githubUrl: 'https://github.com/johndoe'
};

const mockExperience: Experience[] = [
  {
    id: '1',
    company: 'Tech Corp',
    position: 'Software Engineer',
    location: 'San Francisco, CA',
    startDate: new Date('2020-01-01'),
    endDate: undefined,
    current: true,
    description: ['Developed web applications', 'Led team of 3 developers'],
    skills: ['React', 'Node.js', 'TypeScript']
  }
];

const mockEducation: Education[] = [
  {
    id: '1',
    institution: 'University of California, Berkeley',
    degree: 'Bachelor of Science',
    field: 'Computer Science',
    location: 'Berkeley, CA',
    startDate: new Date('2016-08-01'),
    endDate: new Date('2020-05-01'),
    gpa: 3.8,
    achievements: ['Magna Cum Laude', 'Dean\'s List']
  }
];

const mockCertifications: Certification[] = [
  {
    id: '1',
    name: 'AWS Certified Solutions Architect',
    issuer: 'Amazon Web Services',
    issueDate: new Date('2021-06-01'),
    expiryDate: new Date('2024-06-01'),
    credentialId: 'AWS-CSA-123456',
    url: 'https://verify.aws.com/cert/123456'
  }
];

const mockLanguages: Language[] = [
  {
    id: '1',
    name: 'English',
    proficiency: 'Native'
  },
  {
    id: '2',
    name: 'Spanish',
    proficiency: 'Fluent'
  }
];

const mockResumeData: ResumeContent = {
  personalInfo: mockPersonalInfo,
  summary: 'Experienced software engineer with expertise in full-stack development.',
  experience: mockExperience,
  education: mockEducation,
  skills: ['JavaScript', 'React', 'Node.js'],
  certifications: mockCertifications,
  languages: mockLanguages
};

describe('Content Editing Forms', () => {
  describe('PersonalInfoForm', () => {
    const mockOnChange = jest.fn();
    const mockOnValidationChange = jest.fn();

    beforeEach(() => {
      mockOnChange.mockClear();
      mockOnValidationChange.mockClear();
    });

    it('should render personal information form', () => {
      render(
        <PersonalInfoForm
          data={mockPersonalInfo}
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      expect(screen.getByDisplayValue('John Doe')).toBeInTheDocument();
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByDisplayValue('+****************')).toBeInTheDocument();
    });

    it('should validate required fields', async () => {
      render(
        <PersonalInfoForm
          data={{ ...mockPersonalInfo, fullName: '' }}
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      await waitFor(() => {
        expect(mockOnValidationChange).toHaveBeenCalledWith(
          false,
          expect.objectContaining({
            fullName: expect.stringContaining('Full name must be at least 2 characters')
          })
        );
      });
    });

    it('should call onChange when form data changes', async () => {
      const user = userEvent.setup();
      
      render(
        <PersonalInfoForm
          data={mockPersonalInfo}
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      const nameInput = screen.getByDisplayValue('John Doe');
      await user.clear(nameInput);
      await user.type(nameInput, 'Jane Smith');

      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith(
          expect.objectContaining({
            fullName: 'Jane Smith'
          })
        );
      });
    });

    it('should validate email format', async () => {
      const user = userEvent.setup();
      
      render(
        <PersonalInfoForm
          data={mockPersonalInfo}
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      const emailInput = screen.getByDisplayValue('<EMAIL>');
      await user.clear(emailInput);
      await user.type(emailInput, 'invalid-email');

      await waitFor(() => {
        expect(mockOnValidationChange).toHaveBeenCalledWith(
          false,
          expect.objectContaining({
            email: expect.stringContaining('Please enter a valid email address')
          })
        );
      });
    });
  });

  describe('SummaryForm', () => {
    const mockOnChange = jest.fn();
    const mockOnValidationChange = jest.fn();

    beforeEach(() => {
      mockOnChange.mockClear();
      mockOnValidationChange.mockClear();
    });

    it('should render summary form with existing data', () => {
      render(
        <SummaryForm
          data="Test summary content"
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      expect(screen.getByDisplayValue('Test summary content')).toBeInTheDocument();
    });

    it('should show character count', () => {
      render(
        <SummaryForm
          data="Test summary"
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      expect(screen.getByText(/12\/500 characters/)).toBeInTheDocument();
    });

    it('should validate minimum character length', async () => {
      render(
        <SummaryForm
          data="Short"
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      await waitFor(() => {
        expect(mockOnValidationChange).toHaveBeenCalledWith(
          false,
          expect.objectContaining({
            summary: expect.stringContaining('Summary should be at least 50 characters')
          })
        );
      });
    });

    it('should call onChange when summary changes', async () => {
      const user = userEvent.setup();
      
      render(
        <SummaryForm
          data=""
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      const textarea = screen.getByRole('textbox');
      await user.type(textarea, 'New summary content that is long enough to meet the minimum requirements');

      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith(
          'New summary content that is long enough to meet the minimum requirements'
        );
      });
    });
  });

  describe('ExperienceForm', () => {
    const mockOnChange = jest.fn();
    const mockOnValidationChange = jest.fn();

    beforeEach(() => {
      mockOnChange.mockClear();
      mockOnValidationChange.mockClear();
    });

    it('should render experience form with existing data', () => {
      render(
        <ExperienceForm
          data={mockExperience}
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      expect(screen.getByDisplayValue('Software Engineer')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Tech Corp')).toBeInTheDocument();
    });

    it('should allow adding new experience', async () => {
      const user = userEvent.setup();
      
      render(
        <ExperienceForm
          data={[]}
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      const addButton = screen.getByText('Add Your First Position');
      await user.click(addButton);

      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith(
          expect.arrayContaining([
            expect.objectContaining({
              id: expect.any(String),
              company: '',
              position: '',
              description: ['']
            })
          ])
        );
      });
    });

    it('should validate required fields', async () => {
      render(
        <ExperienceForm
          data={[{ ...mockExperience[0], company: '', position: '' }]}
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      await waitFor(() => {
        expect(mockOnValidationChange).toHaveBeenCalledWith(
          false,
          expect.any(Object)
        );
      });
    });
  });

  describe('ResumeDataEditor', () => {
    const mockOnChange = jest.fn();
    const mockOnSave = jest.fn();

    beforeEach(() => {
      mockOnChange.mockClear();
      mockOnSave.mockClear();
    });

    it('should render resume data editor with tabs', () => {
      render(
        <ResumeDataEditor
          data={mockResumeData}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      expect(screen.getByText('Resume Content Editor')).toBeInTheDocument();
      expect(screen.getByText('Personal')).toBeInTheDocument();
      expect(screen.getByText('Summary')).toBeInTheDocument();
      expect(screen.getByText('Experience')).toBeInTheDocument();
    });

    it('should show completion progress', () => {
      render(
        <ResumeDataEditor
          data={mockResumeData}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      expect(screen.getByText('Resume Completion')).toBeInTheDocument();
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('should switch between tabs', async () => {
      const user = userEvent.setup();
      
      render(
        <ResumeDataEditor
          data={mockResumeData}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      const summaryTab = screen.getByText('Summary');
      await user.click(summaryTab);

      expect(screen.getByText('Professional Summary')).toBeInTheDocument();
    });

    it('should call onChange when data changes', async () => {
      render(
        <ResumeDataEditor
          data={mockResumeData}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      // The component should call onChange when any form data changes
      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalled();
      });
    });

    it('should show validation errors for required sections', () => {
      const incompleteData: ResumeContent = {
        ...mockResumeData,
        personalInfo: { ...mockPersonalInfo, fullName: '' },
        experience: []
      };

      render(
        <ResumeDataEditor
          data={incompleteData}
          onChange={mockOnChange}
          onSave={mockOnSave}
        />
      );

      expect(screen.getByText(/Complete required sections/)).toBeInTheDocument();
    });
  });

  describe('Form Integration', () => {
    it('should maintain data consistency across forms', async () => {
      const user = userEvent.setup();
      let currentData = mockResumeData;
      
      const handleChange = (newData: ResumeContent) => {
        currentData = newData;
      };

      const { rerender } = render(
        <ResumeDataEditor
          data={currentData}
          onChange={handleChange}
          onSave={jest.fn()}
        />
      );

      // Switch to summary tab and update summary
      const summaryTab = screen.getByText('Summary');
      await user.click(summaryTab);

      const textarea = screen.getByRole('textbox');
      await user.clear(textarea);
      await user.type(textarea, 'Updated summary with enough characters to meet minimum requirements for validation');

      // Re-render with updated data
      rerender(
        <ResumeDataEditor
          data={currentData}
          onChange={handleChange}
          onSave={jest.fn()}
        />
      );

      // Verify the summary was updated
      expect(textarea).toHaveValue('Updated summary with enough characters to meet minimum requirements for validation');
    });
  });

  describe('EducationForm', () => {
    const mockOnChange = jest.fn();
    const mockOnValidationChange = jest.fn();

    beforeEach(() => {
      mockOnChange.mockClear();
      mockOnValidationChange.mockClear();
    });

    it('should render education form with existing data', () => {
      render(
        <EducationForm
          data={mockEducation}
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      expect(screen.getByDisplayValue('Bachelor of Science')).toBeInTheDocument();
      expect(screen.getByDisplayValue('University of California, Berkeley')).toBeInTheDocument();
    });

    it('should allow adding new education', async () => {
      const user = userEvent.setup();

      render(
        <EducationForm
          data={[]}
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      const addButton = screen.getByText('Add Your First Degree');
      await user.click(addButton);

      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith(
          expect.arrayContaining([
            expect.objectContaining({
              id: expect.any(String),
              institution: '',
              degree: ''
            })
          ])
        );
      });
    });

    it('should validate required fields', async () => {
      render(
        <EducationForm
          data={[{ ...mockEducation[0], institution: '', degree: '' }]}
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      await waitFor(() => {
        expect(mockOnValidationChange).toHaveBeenCalledWith(
          false,
          expect.any(Object)
        );
      });
    });
  });

  describe('SkillsForm', () => {
    const mockOnChange = jest.fn();
    const mockOnValidationChange = jest.fn();

    beforeEach(() => {
      mockOnChange.mockClear();
      mockOnValidationChange.mockClear();
    });

    it('should render skills form with existing data', () => {
      render(
        <SkillsForm
          data={['JavaScript', 'React']}
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      expect(screen.getByText('JavaScript')).toBeInTheDocument();
      expect(screen.getByText('React')).toBeInTheDocument();
    });

    it('should allow adding new skills', async () => {
      const user = userEvent.setup();

      render(
        <SkillsForm
          data={[]}
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      const input = screen.getByPlaceholderText(/e.g., JavaScript/);
      await user.type(input, 'TypeScript');
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith(['TypeScript']);
      });
    });

    it('should validate minimum skills requirement', async () => {
      render(
        <SkillsForm
          data={['JavaScript']}
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      await waitFor(() => {
        expect(mockOnValidationChange).toHaveBeenCalledWith(
          false,
          expect.any(Object)
        );
      });
    });
  });

  describe('CertificationsForm', () => {
    const mockOnChange = jest.fn();
    const mockOnValidationChange = jest.fn();

    beforeEach(() => {
      mockOnChange.mockClear();
      mockOnValidationChange.mockClear();
    });

    it('should render certifications form with existing data', () => {
      render(
        <CertificationsForm
          data={mockCertifications}
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      expect(screen.getByDisplayValue('AWS Certified Solutions Architect')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Amazon Web Services')).toBeInTheDocument();
    });

    it('should allow adding new certification', async () => {
      const user = userEvent.setup();

      render(
        <CertificationsForm
          data={[]}
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      const addButton = screen.getByText('Add Your First Certification');
      await user.click(addButton);

      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith(
          expect.arrayContaining([
            expect.objectContaining({
              id: expect.any(String),
              name: '',
              issuer: ''
            })
          ])
        );
      });
    });
  });

  describe('LanguagesForm', () => {
    const mockOnChange = jest.fn();
    const mockOnValidationChange = jest.fn();

    beforeEach(() => {
      mockOnChange.mockClear();
      mockOnValidationChange.mockClear();
    });

    it('should render languages form with existing data', () => {
      render(
        <LanguagesForm
          data={mockLanguages}
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      expect(screen.getByDisplayValue('English')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Spanish')).toBeInTheDocument();
    });

    it('should allow adding new language', async () => {
      const user = userEvent.setup();

      render(
        <LanguagesForm
          data={[]}
          onChange={mockOnChange}
          onValidationChange={mockOnValidationChange}
        />
      );

      const addButton = screen.getByText('Add Your First Language');
      await user.click(addButton);

      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith(
          expect.arrayContaining([
            expect.objectContaining({
              id: expect.any(String),
              name: '',
              proficiency: 'Conversational'
            })
          ])
        );
      });
    });
  });
});
