/**
 * Analytics System Tests
 * Comprehensive tests for analytics, performance monitoring, and A/B testing
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import { analyticsService, AnalyticsEventType } from '@/lib/analytics/analytics-service';
import { performanceMonitor, PerformanceMetricType } from '@/lib/analytics/performance-monitor';
import { abTestingEngine, ExperimentStatus, ExperimentType } from '@/lib/analytics/ab-testing';
import { onboardingSystem, OnboardingFlowType } from '@/lib/onboarding/onboarding-system';
import { dashboardAnalytics } from '@/lib/analytics/dashboard-analytics';
import AnalyticsDashboard from '@/components/analytics/AnalyticsDashboard';
import OnboardingFlow from '@/components/onboarding/OnboardingFlow';

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  prisma: {
    analyticsEvent: {
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      groupBy: jest.fn(),
    },
    experiment: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
    },
    userExperimentAssignment: {
      create: jest.fn(),
      findUnique: jest.fn(),
    },
    experimentConversion: {
      create: jest.fn(),
      findMany: jest.fn(),
    },
    userOnboardingProgress: {
      create: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
    },
    onboardingFlow: {
      create: jest.fn(),
      findFirst: jest.fn(),
      findUnique: jest.fn(),
    },
    resume: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
    },
    userGoal: {
      create: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
    },
  },
}));

jest.mock('next-auth/react', () => ({
  useSession: () => ({
    data: {
      user: {
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'Test User',
      },
    },
  }),
}));

// Mock fetch for API calls
global.fetch = jest.fn();

describe('Analytics Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Event Tracking', () => {
    it('should track analytics events successfully', async () => {
      const mockCreate = jest.fn().mockResolvedValue({});
      require('@/lib/prisma').prisma.analyticsEvent.create = mockCreate;

      await analyticsService.trackEvent({
        userId: 'test-user-id',
        eventType: AnalyticsEventType.RESUME_CREATED,
        properties: {
          templateId: 'template-1',
          resumeName: 'Test Resume',
        },
      });

      expect(mockCreate).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: 'test-user-id',
          eventType: AnalyticsEventType.RESUME_CREATED,
          properties: expect.objectContaining({
            templateId: 'template-1',
            resumeName: 'Test Resume',
          }),
        }),
      });
    });

    it('should handle tracking errors gracefully', async () => {
      const mockCreate = jest.fn().mockRejectedValue(new Error('Database error'));
      require('@/lib/prisma').prisma.analyticsEvent.create = mockCreate;

      // Should not throw error
      await expect(analyticsService.trackEvent({
        userId: 'test-user-id',
        eventType: AnalyticsEventType.RESUME_CREATED,
        properties: {},
      })).resolves.not.toThrow();
    });

    it('should track user behavior with context', async () => {
      const mockCreate = jest.fn().mockResolvedValue({});
      require('@/lib/prisma').prisma.analyticsEvent.create = mockCreate;

      await analyticsService.trackUserBehavior('test-user-id', 'template_selected', {
        templateId: 'template-1',
        category: 'professional',
      });

      expect(mockCreate).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: 'test-user-id',
          eventType: AnalyticsEventType.FEATURE_USED,
          properties: expect.objectContaining({
            action: 'template_selected',
            templateId: 'template-1',
            category: 'professional',
          }),
        }),
      });
    });
  });

  describe('Metrics Calculation', () => {
    it('should calculate user metrics correctly', async () => {
      const mockGroupBy = jest.fn().mockResolvedValue([
        { userId: 'user-1' },
        { userId: 'user-2' },
        { userId: 'user-3' },
      ]);
      const mockCount = jest.fn().mockResolvedValue(5);

      require('@/lib/prisma').prisma.analyticsEvent.groupBy = mockGroupBy;
      require('@/lib/prisma').prisma.analyticsEvent.count = mockCount;

      const timeframe = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31'),
      };

      const metrics = await analyticsService.getUserMetrics(timeframe);

      expect(metrics).toEqual(expect.objectContaining({
        totalUsers: 3,
        activeUsers: 3,
        newUsers: 5,
      }));
    });
  });
});

describe('Performance Monitor', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock performance.now()
    global.performance = {
      now: jest.fn().mockReturnValue(1000),
    } as any;
  });

  describe('Metric Recording', () => {
    it('should record performance metrics', async () => {
      const mockTrackEvent = jest.spyOn(analyticsService, 'trackEvent').mockResolvedValue();

      await performanceMonitor.recordMetric({
        type: PerformanceMetricType.PAGE_LOAD_TIME,
        value: 1500,
        timestamp: new Date(),
        context: {
          userId: 'test-user-id',
          page: '/dashboard',
        },
      });

      expect(mockTrackEvent).toHaveBeenCalledWith({
        userId: 'test-user-id',
        sessionId: undefined,
        eventType: AnalyticsEventType.API_CALL,
        properties: expect.objectContaining({
          metricType: PerformanceMetricType.PAGE_LOAD_TIME,
          value: 1500,
          page: '/dashboard',
        }),
        timestamp: expect.any(Date),
        userAgent: undefined,
        page: '/dashboard',
      });
    });

    it('should measure page load time', () => {
      global.performance.now = jest.fn()
        .mockReturnValueOnce(1000) // Start time
        .mockReturnValueOnce(2500); // End time

      const endMeasurement = performanceMonitor.measurePageLoad('/dashboard', 'test-user-id');
      endMeasurement();

      // Should have recorded a metric with 1500ms load time
      expect(global.performance.now).toHaveBeenCalledTimes(2);
    });

    it('should measure API call performance', () => {
      global.performance.now = jest.fn()
        .mockReturnValueOnce(1000) // Start time
        .mockReturnValueOnce(1200); // End time

      const endMeasurement = performanceMonitor.measureApiCall('/api/resumes', 'test-user-id');
      endMeasurement();

      // Should have recorded a metric with 200ms response time
      expect(global.performance.now).toHaveBeenCalledTimes(2);
    });
  });

  describe('Performance Alerts', () => {
    it('should generate alerts for slow performance', async () => {
      const mockTrackEvent = jest.spyOn(analyticsService, 'trackEvent').mockResolvedValue();

      // Record a slow page load (above threshold)
      await performanceMonitor.recordMetric({
        type: PerformanceMetricType.PAGE_LOAD_TIME,
        value: 5000, // 5 seconds (above 3 second threshold)
        timestamp: new Date(),
        context: {
          userId: 'test-user-id',
          page: '/dashboard',
        },
      });

      const alerts = performanceMonitor.getActiveAlerts();
      expect(alerts.length).toBeGreaterThan(0);
      expect(alerts[0]).toEqual(expect.objectContaining({
        type: PerformanceMetricType.PAGE_LOAD_TIME,
        threshold: 3000,
        actualValue: 5000,
        severity: 'medium',
      }));
    });
  });
});

describe('A/B Testing Engine', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Experiment Management', () => {
    it('should create experiments successfully', async () => {
      const mockCreate = jest.fn().mockResolvedValue({});
      require('@/lib/prisma').prisma.experiment.create = mockCreate;

      const experiment = await abTestingEngine.createExperiment({
        name: 'Template Performance Test',
        description: 'Test which template performs better',
        type: ExperimentType.TEMPLATE_COMPARISON,
        status: ExperimentStatus.DRAFT,
        variants: [
          {
            id: 'control',
            name: 'Control Template',
            description: 'Current template',
            weight: 50,
            config: { templateId: 'template-1' },
            isControl: true,
          },
          {
            id: 'variant',
            name: 'New Template',
            description: 'New optimized template',
            weight: 50,
            config: { templateId: 'template-2' },
            isControl: false,
          },
        ],
        targetingRules: {
          trafficPercentage: 100,
        },
        successMetrics: {
          primary: 'conversion_rate',
          secondary: ['engagement_time'],
        },
        startDate: new Date(),
        minimumSampleSize: 100,
        confidenceLevel: 95,
        createdBy: 'test-user-id',
      });

      expect(mockCreate).toHaveBeenCalledWith({
        data: expect.objectContaining({
          name: 'Template Performance Test',
          type: ExperimentType.TEMPLATE_COMPARISON,
          status: ExperimentStatus.DRAFT,
        }),
      });
    });

    it('should assign users to variants consistently', async () => {
      const mockFindUnique = jest.fn()
        .mockResolvedValueOnce(null) // No existing assignment
        .mockResolvedValueOnce({ // Mock experiment
          config: {
            id: 'test-experiment',
            variants: [
              { id: 'control', weight: 50, isControl: true },
              { id: 'variant', weight: 50, isControl: false },
            ],
            status: ExperimentStatus.RUNNING,
            targetingRules: { trafficPercentage: 100 },
          },
        });

      const mockCreate = jest.fn().mockResolvedValue({});
      const mockTrackEvent = jest.spyOn(analyticsService, 'trackEvent').mockResolvedValue();

      require('@/lib/prisma').prisma.userExperimentAssignment.findUnique = mockFindUnique;
      require('@/lib/prisma').prisma.experiment.findUnique = mockFindUnique;
      require('@/lib/prisma').prisma.userExperimentAssignment.create = mockCreate;

      const variant = await abTestingEngine.getUserVariant('test-user-id', 'test-experiment');

      expect(variant).toBeDefined();
      expect(['control', 'variant']).toContain(variant?.id);
      expect(mockCreate).toHaveBeenCalled();
      expect(mockTrackEvent).toHaveBeenCalledWith({
        userId: 'test-user-id',
        eventType: AnalyticsEventType.FEATURE_USED,
        properties: expect.objectContaining({
          action: 'experiment_assigned',
          experimentId: 'test-experiment',
        }),
      });
    });
  });

  describe('Conversion Tracking', () => {
    it('should track conversions for experiment participants', async () => {
      const mockFindUnique = jest.fn().mockResolvedValue({
        variantId: 'control',
      });
      const mockCreate = jest.fn().mockResolvedValue({});
      const mockTrackEvent = jest.spyOn(analyticsService, 'trackEvent').mockResolvedValue();

      require('@/lib/prisma').prisma.userExperimentAssignment.findUnique = mockFindUnique;
      require('@/lib/prisma').prisma.experimentConversion.create = mockCreate;

      await abTestingEngine.trackConversion('test-user-id', 'test-experiment', 'conversion', 1);

      expect(mockCreate).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: 'test-user-id',
          experimentId: 'test-experiment',
          variantId: 'control',
          metric: 'conversion',
          value: 1,
        }),
      });

      expect(mockTrackEvent).toHaveBeenCalledWith({
        userId: 'test-user-id',
        eventType: AnalyticsEventType.FEATURE_USED,
        properties: expect.objectContaining({
          action: 'experiment_conversion',
          experimentId: 'test-experiment',
          variantId: 'control',
          metric: 'conversion',
          value: 1,
        }),
      });
    });
  });
});

describe('Onboarding System', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Flow Initialization', () => {
    it('should initialize onboarding for new users', async () => {
      const mockFindUnique = jest.fn().mockResolvedValue(null); // No existing progress
      const mockFindFirst = jest.fn().mockResolvedValue({
        config: {
          id: 'first-time-flow',
          steps: [{ id: 'welcome', title: 'Welcome' }],
        },
      });
      const mockCreate = jest.fn().mockResolvedValue({});
      const mockTrackEvent = jest.spyOn(analyticsService, 'trackEvent').mockResolvedValue();

      require('@/lib/prisma').prisma.userOnboardingProgress.findUnique = mockFindUnique;
      require('@/lib/prisma').prisma.onboardingFlow.findFirst = mockFindFirst;
      require('@/lib/prisma').prisma.userOnboardingProgress.create = mockCreate;

      const progress = await onboardingSystem.initializeOnboarding(
        'test-user-id',
        OnboardingFlowType.FIRST_TIME_USER
      );

      expect(progress).toEqual(expect.objectContaining({
        userId: 'test-user-id',
        flowId: 'first-time-flow',
        currentStepId: 'welcome',
        completedSteps: [],
        skippedSteps: [],
      }));

      expect(mockTrackEvent).toHaveBeenCalledWith({
        userId: 'test-user-id',
        eventType: AnalyticsEventType.FEATURE_USED,
        properties: expect.objectContaining({
          action: 'onboarding_started',
          flowType: OnboardingFlowType.FIRST_TIME_USER,
        }),
      });
    });
  });

  describe('Step Completion', () => {
    it('should complete onboarding steps and track progress', async () => {
      const mockFindUnique = jest.fn().mockResolvedValue({
        userId: 'test-user-id',
        flowId: 'test-flow',
        completedSteps: [],
        skippedSteps: [],
        startedAt: new Date(),
      });

      const mockFindUniqueFlow = jest.fn().mockResolvedValue({
        config: {
          id: 'test-flow',
          steps: [
            { id: 'step-1', title: 'Step 1' },
            { id: 'step-2', title: 'Step 2' },
          ],
        },
      });

      const mockUpdate = jest.fn().mockResolvedValue({});
      const mockTrackEvent = jest.spyOn(analyticsService, 'trackEvent').mockResolvedValue();

      require('@/lib/prisma').prisma.userOnboardingProgress.findUnique = mockFindUnique;
      require('@/lib/prisma').prisma.onboardingFlow.findUnique = mockFindUniqueFlow;
      require('@/lib/prisma').prisma.userOnboardingProgress.update = mockUpdate;

      await onboardingSystem.completeStep('test-user-id', 'test-flow', 'step-1');

      expect(mockUpdate).toHaveBeenCalledWith({
        where: {
          userId_flowId: {
            userId: 'test-user-id',
            flowId: 'test-flow',
          },
        },
        data: expect.objectContaining({
          completedSteps: ['step-1'],
          currentStepId: 'step-2',
        }),
      });

      expect(mockTrackEvent).toHaveBeenCalledWith({
        userId: 'test-user-id',
        eventType: AnalyticsEventType.FEATURE_USED,
        properties: expect.objectContaining({
          action: 'onboarding_step_completed',
          flowId: 'test-flow',
          stepId: 'step-1',
          isFlowCompleted: false,
        }),
      });
    });
  });
});

describe('Dashboard Analytics', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Insights Generation', () => {
    it('should generate comprehensive dashboard insights', async () => {
      const mockFindMany = jest.fn().mockResolvedValue([
        {
          id: 'resume-1',
          title: 'Software Engineer Resume',
          isActive: true,
          applications: [
            { status: 'interview' },
            { status: 'rejected' },
            { status: 'offer' },
          ],
        },
        {
          id: 'resume-2',
          title: 'Product Manager Resume',
          isActive: true,
          applications: [
            { status: 'pending' },
            { status: 'interview' },
          ],
        },
      ]);

      require('@/lib/prisma').prisma.resume.findMany = mockFindMany;
      require('@/lib/prisma').prisma.userGoal.findMany = jest.fn().mockResolvedValue([]);

      const insights = await dashboardAnalytics.getDashboardInsights('test-user-id');

      expect(insights).toEqual(expect.objectContaining({
        userId: 'test-user-id',
        totalResumes: 2,
        activeResumes: 2,
        totalApplications: 5,
        totalInterviews: 2,
        totalOffers: 1,
        overallSuccessRate: 60, // 3 successful out of 5 total
        bestPerformingResume: expect.objectContaining({
          resumeId: 'resume-1',
          resumeName: 'Software Engineer Resume',
          successRate: expect.any(Number),
        }),
      }));
    });
  });
});

describe('Analytics Dashboard Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        totalResumes: 3,
        activeResumes: 2,
        totalApplications: 15,
        totalInterviews: 5,
        totalOffers: 2,
        overallSuccessRate: 33.3,
        bestPerformingResume: {
          resumeId: 'resume-1',
          resumeName: 'Best Resume',
          successRate: 50,
        },
        recommendations: [],
        goals: [],
        industryBenchmarks: {
          averageSuccessRate: 25,
          averageInterviewRate: 10,
          averageOfferRate: 5,
        },
        recentActivity: [],
      }),
    });
  });

  it('should render dashboard with analytics data', async () => {
    render(<AnalyticsDashboard userId="test-user-id" />);

    await waitFor(() => {
      expect(screen.getByText('3')).toBeInTheDocument(); // Total resumes
      expect(screen.getByText('15')).toBeInTheDocument(); // Total applications
      expect(screen.getByText('5')).toBeInTheDocument(); // Total interviews
      expect(screen.getByText('2')).toBeInTheDocument(); // Total offers
    });
  });

  it('should handle loading state', () => {
    (global.fetch as jest.Mock).mockImplementation(() => new Promise(() => {})); // Never resolves

    render(<AnalyticsDashboard userId="test-user-id" />);

    expect(screen.getByRole('status')).toBeInTheDocument(); // Loading spinner
  });

  it('should handle error state', async () => {
    (global.fetch as jest.Mock).mockRejectedValue(new Error('API Error'));

    render(<AnalyticsDashboard userId="test-user-id" />);

    await waitFor(() => {
      expect(screen.getByText(/failed to load insights/i)).toBeInTheDocument();
      expect(screen.getByText('Try Again')).toBeInTheDocument();
    });
  });
});

describe('Onboarding Flow Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        progress: {
          userId: 'test-user-id',
          flowId: 'first-time-flow',
          currentStepId: 'welcome',
          completedSteps: [],
          skippedSteps: [],
          startedAt: new Date().toISOString(),
        },
      }),
    });
  });

  it('should render onboarding flow', async () => {
    render(
      <OnboardingFlow
        flowType={OnboardingFlowType.FIRST_TIME_USER}
        userId="test-user-id"
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Welcome to CVLeap')).toBeInTheDocument();
      expect(screen.getByText('Continue')).toBeInTheDocument();
    });
  });

  it('should handle step completion', async () => {
    render(
      <OnboardingFlow
        flowType={OnboardingFlowType.FIRST_TIME_USER}
        userId="test-user-id"
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Continue')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Continue'));

    expect(global.fetch).toHaveBeenCalledWith('/api/onboarding', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'complete-step',
        flowId: 'first-time-flow',
        stepId: 'welcome',
      }),
    });
  });
});
