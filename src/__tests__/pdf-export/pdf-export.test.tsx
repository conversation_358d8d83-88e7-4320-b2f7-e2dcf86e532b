import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import PDFExportButton from '@/components/resume/PDFExportButton';
import PDFExportModal from '@/components/resume/PDFExportModal';
import { usePDFExport, generatePDFFilename, validateResumeForExport } from '@/lib/hooks/usePDFExport';
import { ResumeContent } from '@/types';

// Mock the PDF export hook
jest.mock('@/lib/hooks/usePDFExport', () => ({
  usePDFExport: jest.fn(),
  generatePDFFilename: jest.fn(),
  validateResumeForExport: jest.fn()
}));

// Mock fetch for API calls
global.fetch = jest.fn();

const mockResumeData: ResumeContent = {
  personalInfo: {
    fullName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    location: 'San Francisco, CA'
  },
  summary: 'Experienced software engineer with 5+ years of expertise in full-stack development.',
  experience: [
    {
      id: '1',
      company: 'Tech Corp',
      position: 'Senior Developer',
      location: 'San Francisco, CA',
      startDate: new Date('2020-01-01'),
      current: true,
      description: ['Led development of key features', 'Mentored junior developers'],
      skills: ['React', 'Node.js', 'TypeScript']
    }
  ],
  education: [
    {
      id: '1',
      institution: 'University of California',
      degree: 'Bachelor of Science',
      field: 'Computer Science',
      location: 'Berkeley, CA',
      startDate: new Date('2015-09-01'),
      endDate: new Date('2019-05-01'),
      gpa: 3.8,
      achievements: ['Magna Cum Laude', 'Dean\'s List']
    }
  ],
  skills: ['JavaScript', 'React', 'Node.js', 'Python', 'AWS'],
  certifications: [
    {
      id: '1',
      name: 'AWS Certified Developer',
      issuer: 'Amazon Web Services',
      issueDate: new Date('2021-06-01'),
      expiryDate: new Date('2024-06-01'),
      credentialId: 'AWS-123456',
      verificationUrl: 'https://aws.amazon.com/verification/123456'
    }
  ],
  languages: [
    {
      id: '1',
      name: 'English',
      proficiency: 'Native'
    },
    {
      id: '2',
      name: 'Spanish',
      proficiency: 'Conversational'
    }
  ]
};

describe('PDF Export System', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (generatePDFFilename as jest.Mock).mockReturnValue('john_doe_resume_2024-01-01.pdf');
    (validateResumeForExport as jest.Mock).mockReturnValue({
      isValid: true,
      missingFields: [],
      warnings: []
    });
  });

  describe('PDFExportButton', () => {
    it('renders export button correctly', () => {
      render(
        <PDFExportButton
          resumeData={mockResumeData}
          templateId="modern-template"
          templateName="Modern Template"
        />
      );

      expect(screen.getByText('Export PDF')).toBeInTheDocument();
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('opens modal when clicked', () => {
      render(
        <PDFExportButton
          resumeData={mockResumeData}
          templateId="modern-template"
          templateName="Modern Template"
        />
      );

      fireEvent.click(screen.getByText('Export PDF'));
      expect(screen.getByText('Export Resume as PDF')).toBeInTheDocument();
    });

    it('renders with custom props', () => {
      render(
        <PDFExportButton
          resumeData={mockResumeData}
          templateId="modern-template"
          variant="outline"
          size="sm"
          showIcon={false}
        >
          Custom Export
        </PDFExportButton>
      );

      expect(screen.getByText('Custom Export')).toBeInTheDocument();
    });
  });

  describe('PDFExportModal', () => {
    const mockExportToPDF = jest.fn();
    const mockClearError = jest.fn();

    beforeEach(() => {
      (usePDFExport as jest.Mock).mockReturnValue({
        isExporting: false,
        progress: 0,
        error: null,
        lastExported: null,
        exportToPDF: mockExportToPDF,
        clearError: mockClearError
      });
    });

    it('renders modal when open', () => {
      render(
        <PDFExportModal
          isOpen={true}
          onClose={jest.fn()}
          resumeData={mockResumeData}
          templateId="modern-template"
          templateName="Modern Template"
        />
      );

      expect(screen.getByText('Export Resume as PDF')).toBeInTheDocument();
      expect(screen.getByText('Resume Validation')).toBeInTheDocument();
      expect(screen.getByText('Export Options')).toBeInTheDocument();
    });

    it('does not render when closed', () => {
      render(
        <PDFExportModal
          isOpen={false}
          onClose={jest.fn()}
          resumeData={mockResumeData}
          templateId="modern-template"
        />
      );

      expect(screen.queryByText('Export Resume as PDF')).not.toBeInTheDocument();
    });

    it('shows validation success for complete resume', () => {
      render(
        <PDFExportModal
          isOpen={true}
          onClose={jest.fn()}
          resumeData={mockResumeData}
          templateId="modern-template"
        />
      );

      expect(screen.getByText('Your resume is ready for export! All required sections are complete.')).toBeInTheDocument();
    });

    it('shows validation errors for incomplete resume', () => {
      (validateResumeForExport as jest.Mock).mockReturnValue({
        isValid: false,
        missingFields: ['Full name', 'Email address'],
        warnings: ['Add more skills (recommended: 5-10)']
      });

      render(
        <PDFExportModal
          isOpen={true}
          onClose={jest.fn()}
          resumeData={mockResumeData}
          templateId="modern-template"
        />
      );

      expect(screen.getByText('Missing required information:')).toBeInTheDocument();
      expect(screen.getByText('Full name')).toBeInTheDocument();
      expect(screen.getByText('Email address')).toBeInTheDocument();
    });

    it('calls export function when export button clicked', async () => {
      mockExportToPDF.mockResolvedValue({ success: true, filename: 'test.pdf' });

      render(
        <PDFExportModal
          isOpen={true}
          onClose={jest.fn()}
          resumeData={mockResumeData}
          templateId="modern-template"
        />
      );

      fireEvent.click(screen.getByText('Export PDF'));

      await waitFor(() => {
        expect(mockExportToPDF).toHaveBeenCalledWith(
          mockResumeData,
          'modern-template',
          expect.objectContaining({
            quality: 'high',
            format: 'A4'
          })
        );
      });
    });

    it('shows progress during export', () => {
      (usePDFExport as jest.Mock).mockReturnValue({
        isExporting: true,
        progress: 50,
        error: null,
        lastExported: null,
        exportToPDF: mockExportToPDF,
        clearError: mockClearError
      });

      render(
        <PDFExportModal
          isOpen={true}
          onClose={jest.fn()}
          resumeData={mockResumeData}
          templateId="modern-template"
        />
      );

      expect(screen.getByText('Generating PDF...')).toBeInTheDocument();
      expect(screen.getByText('50%')).toBeInTheDocument();
    });

    it('shows error message when export fails', () => {
      (usePDFExport as jest.Mock).mockReturnValue({
        isExporting: false,
        progress: 0,
        error: 'Failed to generate PDF',
        lastExported: null,
        exportToPDF: mockExportToPDF,
        clearError: mockClearError
      });

      render(
        <PDFExportModal
          isOpen={true}
          onClose={jest.fn()}
          resumeData={mockResumeData}
          templateId="modern-template"
        />
      );

      expect(screen.getByText('Export failed')).toBeInTheDocument();
      expect(screen.getByText('Failed to generate PDF')).toBeInTheDocument();
    });
  });

  describe('PDF Export Utilities', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('generates correct filename', () => {
      (generatePDFFilename as jest.Mock).mockImplementation((personalInfo, templateName) => {
        const date = '2024-01-01';
        const name = personalInfo?.fullName?.replace(/[^a-zA-Z0-9]/g, '_') || 'resume';
        const template = templateName?.replace(/[^a-zA-Z0-9]/g, '_') || '';
        return `${name}${template ? `_${template}` : ''}_${date}.pdf`;
      });

      const filename = generatePDFFilename(
        { fullName: 'John Doe' },
        'Modern Template'
      );

      expect(filename).toBe('John_Doe_Modern_Template_2024-01-01.pdf');
    });

    it('validates complete resume correctly', () => {
      (validateResumeForExport as jest.Mock).mockImplementation((resumeData) => {
        const missingFields = [];
        const warnings = [];

        if (!resumeData.personalInfo?.fullName) missingFields.push('Full name');
        if (!resumeData.personalInfo?.email) missingFields.push('Email address');
        if (!resumeData.experience || resumeData.experience.length === 0) {
          missingFields.push('Work experience');
        }

        return {
          isValid: missingFields.length === 0,
          missingFields,
          warnings
        };
      });

      const validation = validateResumeForExport(mockResumeData);

      expect(validation.isValid).toBe(true);
      expect(validation.missingFields).toHaveLength(0);
    });

    it('validates incomplete resume correctly', () => {
      (validateResumeForExport as jest.Mock).mockImplementation((resumeData) => {
        const missingFields = [];
        if (!resumeData.personalInfo?.fullName) missingFields.push('Full name');
        if (!resumeData.personalInfo?.email) missingFields.push('Email address');
        
        return {
          isValid: missingFields.length === 0,
          missingFields,
          warnings: []
        };
      });

      const incompleteData = {
        ...mockResumeData,
        personalInfo: {}
      };

      const validation = validateResumeForExport(incompleteData);

      expect(validation.isValid).toBe(false);
      expect(validation.missingFields).toContain('Full name');
      expect(validation.missingFields).toContain('Email address');
    });
  });
});
