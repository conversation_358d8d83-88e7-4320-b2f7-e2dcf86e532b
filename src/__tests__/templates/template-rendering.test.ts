import { describe, it, expect, beforeEach } from '@jest/globals';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import React from 'react';
import { TEMPLATE_CATALOG } from '@/lib/templates/template-catalog';
import { templateRegistry, getTemplateComponent } from '@/lib/templates/template-registry';
import TemplateRenderer from '@/components/resume/TemplateRenderer';
import TemplateSwitcher from '@/components/resume/TemplateSwitcher';
import { ResumeData } from '@/types';

// Mock resume data for testing
const mockResumeData: ResumeData = {
  personalInfo: {
    fullName: '<PERSON>',
    jobTitle: 'Software Engineer',
    email: '<EMAIL>',
    phone: '+****************',
    location: 'San Francisco, CA',
    website: 'johndoe.dev',
    linkedinUrl: 'linkedin.com/in/johndoe',
    githubUrl: 'github.com/johndoe'
  },
  summary: 'Experienced software engineer with expertise in full-stack development.',
  experience: [
    {
      id: '1',
      company: 'Tech Corp',
      position: 'Senior Software Engineer',
      location: 'San Francisco, CA',
      startDate: new Date('2020-01-01'),
      endDate: undefined,
      current: true,
      description: ['Led development of web applications', 'Mentored junior developers'],
      skills: ['React', 'Node.js', 'TypeScript']
    }
  ],
  education: [
    {
      id: '1',
      institution: 'University of Technology',
      degree: 'Bachelor of Science',
      field: 'Computer Science',
      location: 'San Francisco, CA',
      startDate: new Date('2016-08-01'),
      endDate: new Date('2020-05-01'),
      gpa: 3.8
    }
  ],
  skills: ['JavaScript', 'React', 'Node.js', 'TypeScript', 'Python'],
  certifications: [
    {
      id: '1',
      name: 'AWS Certified Developer',
      issuer: 'Amazon Web Services',
      issueDate: new Date('2021-06-01'),
      expiryDate: new Date('2024-06-01')
    }
  ],
  languages: [
    {
      id: '1',
      name: 'English',
      proficiency: 'native'
    }
  ]
};

describe('Template Rendering System', () => {
  beforeEach(() => {
    // Reset any mocks or state before each test
    jest.clearAllMocks();
  });

  describe('Template Catalog', () => {
    it('should have 60 templates in the catalog', () => {
      expect(TEMPLATE_CATALOG).toHaveLength(60);
    });

    it('should have templates with required metadata fields', () => {
      TEMPLATE_CATALOG.forEach(template => {
        expect(template).toHaveProperty('id');
        expect(template).toHaveProperty('name');
        expect(template).toHaveProperty('category');
        expect(template).toHaveProperty('description');
        expect(template).toHaveProperty('isPremium');
        expect(template).toHaveProperty('tags');
        expect(template).toHaveProperty('industry');
        expect(template).toHaveProperty('experience');
        expect(template).toHaveProperty('styles');
        expect(template).toHaveProperty('sections');
        expect(template).toHaveProperty('features');
        expect(template).toHaveProperty('createdAt');
        expect(template).toHaveProperty('updatedAt');
      });
    });

    it('should have unique template IDs', () => {
      const ids = TEMPLATE_CATALOG.map(t => t.id);
      const uniqueIds = new Set(ids);
      expect(uniqueIds.size).toBe(ids.length);
    });

    it('should have valid categories', () => {
      const validCategories = ['professional', 'creative', 'modern', 'minimalist', 'executive', 'academic'];
      TEMPLATE_CATALOG.forEach(template => {
        expect(validCategories).toContain(template.category);
      });
    });
  });

  describe('Template Registry', () => {
    it('should register templates correctly', () => {
      const templateIds = templateRegistry.getTemplateIds();
      expect(templateIds.length).toBeGreaterThan(0);
    });

    it('should return template components for registered templates', () => {
      const availableTemplates = ['modern-template', 'clean-template', 'professional-template'];
      
      availableTemplates.forEach(templateId => {
        const component = getTemplateComponent(templateId);
        expect(component).toBeDefined();
      });
    });

    it('should handle template search correctly', () => {
      const searchResults = templateRegistry.searchTemplates('modern');
      expect(searchResults.length).toBeGreaterThan(0);
      
      searchResults.forEach(result => {
        const metadata = result.metadata;
        expect(
          metadata?.name.toLowerCase().includes('modern') ||
          metadata?.description.toLowerCase().includes('modern') ||
          metadata?.tags.some(tag => tag.toLowerCase().includes('modern'))
        ).toBe(true);
      });
    });

    it('should filter templates by category', () => {
      const professionalTemplates = templateRegistry.getTemplatesByCategory('professional');
      expect(professionalTemplates.length).toBeGreaterThan(0);
      
      professionalTemplates.forEach(template => {
        expect(template.metadata?.category).toBe('professional');
      });
    });
  });

  describe('TemplateRenderer Component', () => {
    it('should render without crashing', () => {
      render(
        <TemplateRenderer
          templateId="modern-template"
          resumeData={mockResumeData}
        />
      );
    });

    it('should show error message for invalid template ID', () => {
      render(
        <TemplateRenderer
          templateId="invalid-template-id"
          resumeData={mockResumeData}
        />
      );
      
      expect(screen.getByText(/Template not found/i)).toBeInTheDocument();
    });

    it('should render fallback component when template component is not available', () => {
      render(
        <TemplateRenderer
          templateId="business-consultant"
          resumeData={mockResumeData}
        />
      );
      
      // Should render fallback with user data
      expect(screen.getByText(mockResumeData.personalInfo.fullName)).toBeInTheDocument();
      expect(screen.getByText(mockResumeData.personalInfo.jobTitle)).toBeInTheDocument();
    });

    it('should display template metadata when showMetadata is true', () => {
      render(
        <TemplateRenderer
          templateId="modern-template"
          resumeData={mockResumeData}
          showMetadata={true}
        />
      );
      
      // Should show template name and description
      const template = TEMPLATE_CATALOG.find(t => t.id === 'modern-template');
      if (template) {
        expect(screen.getByText(template.name)).toBeInTheDocument();
      }
    });
  });

  describe('TemplateSwitcher Component', () => {
    const mockOnTemplateChange = jest.fn();
    const mockOnPreview = jest.fn();

    beforeEach(() => {
      mockOnTemplateChange.mockClear();
      mockOnPreview.mockClear();
    });

    it('should render template grid', () => {
      render(
        <TemplateSwitcher
          currentTemplateId="modern-template"
          resumeData={mockResumeData}
          onTemplateChange={mockOnTemplateChange}
          onPreview={mockOnPreview}
        />
      );
      
      expect(screen.getByText(/Choose Your Template/i)).toBeInTheDocument();
      expect(screen.getByText(/60 professionally designed/i)).toBeInTheDocument();
    });

    it('should filter templates by search query', async () => {
      render(
        <TemplateSwitcher
          currentTemplateId="modern-template"
          resumeData={mockResumeData}
          onTemplateChange={mockOnTemplateChange}
          onPreview={mockOnPreview}
        />
      );
      
      const searchInput = screen.getByPlaceholderText(/Search templates/i);
      fireEvent.change(searchInput, { target: { value: 'modern' } });
      
      await waitFor(() => {
        // Should show filtered results
        const resultText = screen.getByText(/Showing \d+ of 60 templates/);
        expect(resultText).toBeInTheDocument();
      });
    });

    it('should call onTemplateChange when template is selected', async () => {
      render(
        <TemplateSwitcher
          currentTemplateId="modern-template"
          resumeData={mockResumeData}
          onTemplateChange={mockOnTemplateChange}
          onPreview={mockOnPreview}
        />
      );
      
      // Find and click a template select button
      const selectButtons = screen.getAllByText(/Select/i);
      if (selectButtons.length > 0) {
        fireEvent.click(selectButtons[0]);
        expect(mockOnTemplateChange).toHaveBeenCalled();
      }
    });

    it('should show premium badge for premium templates', () => {
      render(
        <TemplateSwitcher
          currentTemplateId="modern-template"
          resumeData={mockResumeData}
          onTemplateChange={mockOnTemplateChange}
          onPreview={mockOnPreview}
        />
      );
      
      // Should show premium badges for premium templates
      const premiumBadges = screen.getAllByText(/Premium/i);
      expect(premiumBadges.length).toBeGreaterThan(0);
    });

    it('should clear filters when clear button is clicked', async () => {
      render(
        <TemplateSwitcher
          currentTemplateId="modern-template"
          resumeData={mockResumeData}
          onTemplateChange={mockOnTemplateChange}
          onPreview={mockOnPreview}
        />
      );
      
      // Apply a search filter
      const searchInput = screen.getByPlaceholderText(/Search templates/i);
      fireEvent.change(searchInput, { target: { value: 'nonexistent' } });
      
      await waitFor(() => {
        expect(screen.getByText(/No templates found/i)).toBeInTheDocument();
      });
      
      // Click clear filters
      const clearButton = screen.getByText(/Clear all filters/i);
      fireEvent.click(clearButton);
      
      await waitFor(() => {
        expect(screen.getByText(/Showing 60 of 60 templates/i)).toBeInTheDocument();
      });
    });
  });

  describe('Template Data Integration', () => {
    it('should properly map resume data to template props', () => {
      const template = TEMPLATE_CATALOG[0];
      
      render(
        <TemplateRenderer
          templateId={template.id}
          resumeData={mockResumeData}
        />
      );
      
      // Should render user's personal information
      expect(screen.getByText(mockResumeData.personalInfo.fullName)).toBeInTheDocument();
      expect(screen.getByText(mockResumeData.personalInfo.jobTitle)).toBeInTheDocument();
    });

    it('should handle missing resume data gracefully', () => {
      const incompleteData: Partial<ResumeData> = {
        personalInfo: {
          fullName: 'Test User',
          jobTitle: '',
          email: '',
          phone: '',
          location: ''
        }
      };
      
      render(
        <TemplateRenderer
          templateId="modern-template"
          resumeData={incompleteData as ResumeData}
        />
      );
      
      expect(screen.getByText('Test User')).toBeInTheDocument();
    });
  });

  describe('Performance and Error Handling', () => {
    it('should handle template rendering errors gracefully', () => {
      // Mock console.error to avoid noise in test output
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      render(
        <TemplateRenderer
          templateId="modern-template"
          resumeData={mockResumeData}
          onError={(error) => {
            expect(error).toBeInstanceOf(Error);
          }}
        />
      );
      
      consoleSpy.mockRestore();
    });

    it('should show loading state initially', () => {
      render(
        <TemplateRenderer
          templateId="modern-template"
          resumeData={mockResumeData}
        />
      );
      
      // Should show loading state briefly
      expect(screen.getByText(/Loading template/i)).toBeInTheDocument();
    });
  });
});
