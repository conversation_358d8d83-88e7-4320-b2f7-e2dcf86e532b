// Integration tests for the complete template system
// Tests end-to-end functionality including API, components, and data flow

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { templateRegistry } from '@/lib/templates/template-registry';
import { templateSwitcher } from '@/lib/templates/template-switcher';
import { accessibilityChecker } from '@/lib/templates/accessibility';
import { templateErrorHandler } from '@/lib/templates/error-handling';
import TemplateSelector from '@/components/resume/TemplateSelector';
import TemplatePreview from '@/components/resume/TemplatePreview';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
  }),
}));

// Mock session
jest.mock('next-auth/react', () => ({
  useSession: () => ({
    data: {
      user: {
        name: 'Test User',
        email: '<EMAIL>'
      }
    }
  })
}));

describe('Template System Integration', () => {
  const mockResumeData = {
    personalInfo: {
      name: 'Integration Test User',
      jobTitle: 'Software Engineer',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Test City, TC'
    },
    summary: 'Test summary for integration testing',
    experience: [
      {
        company: 'Test Company',
        jobTitle: 'Test Engineer',
        dates: '2020 - Present',
        description: 'Testing various systems and components'
      }
    ],
    education: [
      {
        degree: 'BS Computer Science',
        institution: 'Test University',
        dates: '2016 - 2020'
      }
    ],
    skills: ['Testing', 'Integration', 'Quality Assurance']
  };

  beforeEach(() => {
    // Clear any previous state
    templateErrorHandler.clearErrors();
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Cleanup after each test
    jest.restoreAllMocks();
  });

  describe('Template Registry Integration', () => {
    it('should load all templates from catalog', () => {
      const allTemplates = templateRegistry.getAllTemplates();
      expect(allTemplates.length).toBeGreaterThan(0);
      
      // Check that catalog templates are loaded
      const catalogTemplateIds = ['professional-sidebar-1', 'modern-photo-cv', 'clean-professional-3'];
      catalogTemplateIds.forEach(templateId => {
        expect(templateRegistry.isTemplateAvailable(templateId)).toBe(true);
      });
    });

    it('should provide template components for rendering', () => {
      const templateIds = templateRegistry.getTemplateIds();
      
      templateIds.forEach(templateId => {
        const component = templateRegistry.getTemplateComponent(templateId);
        expect(component).toBeDefined();
      });
    });

    it('should filter templates correctly', () => {
      const professionalTemplates = templateRegistry.getTemplatesByCategory('professional');
      const modernTemplates = templateRegistry.getTemplatesByCategory('modern');
      
      expect(professionalTemplates.length).toBeGreaterThan(0);
      expect(modernTemplates.length).toBeGreaterThan(0);
      
      // Categories should be different
      expect(professionalTemplates[0].metadata?.category).toBe('professional');
      expect(modernTemplates[0].metadata?.category).toBe('modern');
    });
  });

  describe('Template Switching Integration', () => {
    it('should switch templates while preserving data', () => {
      const fromTemplate = 'professional-sidebar-1';
      const toTemplate = 'modern-photo-cv';
      
      const result = templateSwitcher.switchTemplate(
        mockResumeData,
        fromTemplate,
        toTemplate
      );
      
      expect(result.success).toBe(true);
      expect(result.adaptedData).toBeDefined();
      expect(result.adaptedData?.personalInfo.name).toBe(mockResumeData.personalInfo.name);
    });

    it('should provide warnings for potential data loss', () => {
      const dataWithExtras = {
        ...mockResumeData,
        certifications: ['Test Certification'],
        volunteer: [{
          organization: 'Test Org',
          role: 'Volunteer',
          dates: '2019',
          description: 'Volunteer work'
        }]
      };
      
      const result = templateSwitcher.switchTemplate(
        dataWithExtras,
        'modern-photo-cv',
        'clean-professional-3'
      );
      
      // Should have warnings about unsupported sections
      expect(result.warnings.length).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Accessibility Integration', () => {
    it('should check template accessibility', () => {
      const templateId = 'professional-sidebar-1';
      const report = accessibilityChecker.checkTemplate(templateId, mockResumeData);
      
      expect(report).toBeDefined();
      expect(report.score).toBeGreaterThanOrEqual(0);
      expect(report.score).toBeLessThanOrEqual(100);
      expect(report.wcagLevel).toMatch(/^(A|AA|AAA|FAIL)$/);
      expect(Array.isArray(report.issues)).toBe(true);
      expect(Array.isArray(report.recommendations)).toBe(true);
    });

    it('should provide actionable recommendations', () => {
      const templateId = 'clean-professional-3';
      const report = accessibilityChecker.checkTemplate(templateId, mockResumeData);
      
      report.recommendations.forEach(recommendation => {
        expect(typeof recommendation).toBe('string');
        expect(recommendation.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Error Handling Integration', () => {
    it('should log template errors', () => {
      const error = {
        code: 'TEST_ERROR',
        message: 'Test error message',
        templateId: 'test-template',
        recoverable: true
      };
      
      templateErrorHandler.logError(error);
      
      const recentErrors = templateErrorHandler.getRecentErrors('test-template');
      expect(recentErrors.length).toBeGreaterThan(0);
      expect(recentErrors[0].code).toBe('TEST_ERROR');
    });

    it('should detect recent errors for templates', () => {
      const templateId = 'error-test-template';
      
      // Log an error
      templateErrorHandler.logError({
        code: 'RECENT_ERROR',
        message: 'Recent error',
        templateId,
        recoverable: true
      });
      
      expect(templateErrorHandler.hasRecentErrors(templateId, 5)).toBe(true);
      expect(templateErrorHandler.hasRecentErrors('other-template', 5)).toBe(false);
    });
  });

  describe('Component Integration', () => {
    it('should render TemplateSelector component', () => {
      const mockOnSelect = jest.fn();
      const mockOnPreview = jest.fn();
      
      render(
        <TemplateSelector
          selectedTemplateId=""
          onTemplateSelect={mockOnSelect}
          onPreview={mockOnPreview}
        />
      );
      
      expect(screen.getByText('Choose a Template')).toBeInTheDocument();
    });

    it('should handle template selection', async () => {
      const mockOnSelect = jest.fn();
      
      render(
        <TemplateSelector
          selectedTemplateId=""
          onTemplateSelect={mockOnSelect}
        />
      );
      
      // This would require more detailed component testing
      // depending on the actual implementation
      expect(screen.getByText('Choose a Template')).toBeInTheDocument();
    });

    it('should render TemplatePreview component', () => {
      const templateId = 'professional-sidebar-1';
      
      render(
        <TemplatePreview
          templateId={templateId}
          resumeData={mockResumeData}
        />
      );
      
      // Check that preview renders without errors
      expect(screen.getByText(/Template Preview|Professional Sidebar Resume/i)).toBeInTheDocument();
    });
  });

  describe('Data Flow Integration', () => {
    it('should handle complete template workflow', async () => {
      // 1. Select template
      const selectedTemplate = 'professional-sidebar-1';
      expect(templateRegistry.isTemplateAvailable(selectedTemplate)).toBe(true);
      
      // 2. Validate data
      const isValid = templateRegistry.validateTemplateData(mockResumeData, selectedTemplate);
      expect(isValid).toBe(true);
      
      // 3. Check accessibility
      const accessibilityReport = accessibilityChecker.checkTemplate(selectedTemplate, mockResumeData);
      expect(accessibilityReport.score).toBeGreaterThan(0);
      
      // 4. Switch to different template
      const switchResult = templateSwitcher.switchTemplate(
        mockResumeData,
        selectedTemplate,
        'modern-photo-cv'
      );
      expect(switchResult.success).toBe(true);
      
      // 5. Validate switched data
      const isValidAfterSwitch = templateRegistry.validateTemplateData(
        switchResult.adaptedData!,
        'modern-photo-cv'
      );
      expect(isValidAfterSwitch).toBe(true);
    });

    it('should handle error scenarios gracefully', () => {
      // Test with invalid template ID
      const invalidTemplate = 'non-existent-template';
      expect(templateRegistry.isTemplateAvailable(invalidTemplate)).toBe(false);
      
      // Test switching with invalid template
      const switchResult = templateSwitcher.switchTemplate(
        mockResumeData,
        'professional-sidebar-1',
        invalidTemplate
      );
      expect(switchResult.success).toBe(false);
      expect(switchResult.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Performance Integration', () => {
    it('should handle multiple template operations efficiently', () => {
      const startTime = performance.now();
      
      // Perform multiple operations
      const templates = templateRegistry.getAllTemplates();
      templates.forEach(template => {
        templateRegistry.validateTemplateData(mockResumeData, template.id);
        accessibilityChecker.checkTemplate(template.id, mockResumeData);
      });
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should complete within reasonable time
      expect(duration).toBeLessThan(1000); // 1 second threshold
    });

    it('should cache template operations', () => {
      const templateId = 'professional-sidebar-1';
      
      // First access
      const start1 = performance.now();
      templateRegistry.getTemplate(templateId);
      const end1 = performance.now();
      
      // Second access (should be cached)
      const start2 = performance.now();
      templateRegistry.getTemplate(templateId);
      const end2 = performance.now();
      
      // Second access should be faster (cached)
      expect(end2 - start2).toBeLessThanOrEqual(end1 - start1);
    });
  });

  describe('State Management Integration', () => {
    it('should maintain consistent state across operations', () => {
      const initialTemplateCount = templateRegistry.getAllTemplates().length;
      
      // Perform various operations
      templateRegistry.getTemplatesByCategory('professional');
      templateRegistry.searchTemplates('modern');
      templateSwitcher.switchTemplate(mockResumeData, 'professional-sidebar-1', 'modern-photo-cv');
      
      // Template count should remain consistent
      const finalTemplateCount = templateRegistry.getAllTemplates().length;
      expect(finalTemplateCount).toBe(initialTemplateCount);
    });

    it('should handle concurrent operations safely', async () => {
      const operations = [
        () => templateRegistry.getAllTemplates(),
        () => templateRegistry.getTemplatesByCategory('modern'),
        () => templateRegistry.searchTemplates('professional'),
        () => templateSwitcher.switchTemplate(mockResumeData, 'professional-sidebar-1', 'modern-photo-cv'),
        () => accessibilityChecker.checkTemplate('clean-professional-3', mockResumeData)
      ];
      
      // Run operations concurrently
      const results = await Promise.all(operations.map(op => 
        new Promise(resolve => {
          try {
            const result = op();
            resolve(result);
          } catch (error) {
            resolve(error);
          }
        })
      ));
      
      // All operations should complete without errors
      results.forEach(result => {
        expect(result).toBeDefined();
      });
    });
  });
});
