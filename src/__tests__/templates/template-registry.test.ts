// Tests for template registry functionality
// Ensures template registration, retrieval, and validation work correctly

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { 
  TemplateRegistry, 
  templateRegistry,
  ResumeData,
  validateTemplateData 
} from '@/lib/templates/template-registry';
import { TEMPLATE_CATALOG } from '@/lib/templates/template-catalog';

// Mock React component for testing
const MockTemplateComponent = jest.fn(() => null);

describe('TemplateRegistry', () => {
  let registry: TemplateRegistry;

  beforeEach(() => {
    registry = TemplateRegistry.getInstance();
    jest.clearAllMocks();
  });

  describe('Template Registration', () => {
    it('should register a template successfully', () => {
      const templateId = 'test-template';
      registry.registerTemplate(templateId, MockTemplateComponent);
      
      expect(registry.isTemplateAvailable(templateId)).toBe(true);
      expect(registry.getTemplateComponent(templateId)).toBe(MockTemplateComponent);
    });

    it('should retrieve template metadata', () => {
      const templateId = 'professional-sidebar-1';
      const template = registry.getTemplate(templateId);
      
      expect(template).toBeDefined();
      expect(template?.id).toBe(templateId);
      expect(template?.metadata).toBeDefined();
    });

    it('should return undefined for non-existent template', () => {
      const template = registry.getTemplate('non-existent-template');
      expect(template).toBeUndefined();
    });
  });

  describe('Template Filtering', () => {
    it('should filter templates by category', () => {
      const professionalTemplates = registry.getTemplatesByCategory('professional');
      expect(professionalTemplates.length).toBeGreaterThan(0);
      
      professionalTemplates.forEach(template => {
        expect(template.metadata?.category).toBe('professional');
      });
    });

    it('should return all templates for "all" category', () => {
      const allTemplates = registry.getTemplatesByCategory('all');
      const totalTemplates = registry.getAllTemplates();
      
      expect(allTemplates.length).toBe(totalTemplates.length);
    });

    it('should search templates by query', () => {
      const searchResults = registry.searchTemplates('professional');
      expect(searchResults.length).toBeGreaterThan(0);
      
      searchResults.forEach(template => {
        const metadata = template.metadata;
        const matchesQuery = 
          metadata?.name.toLowerCase().includes('professional') ||
          metadata?.description.toLowerCase().includes('professional') ||
          metadata?.tags.some(tag => tag.toLowerCase().includes('professional'));
        
        expect(matchesQuery).toBe(true);
      });
    });
  });

  describe('Template IDs', () => {
    it('should return list of available template IDs', () => {
      const templateIds = registry.getTemplateIds();
      expect(Array.isArray(templateIds)).toBe(true);
      expect(templateIds.length).toBeGreaterThan(0);
    });

    it('should include catalog templates in available IDs', () => {
      const templateIds = registry.getTemplateIds();
      const catalogIds = TEMPLATE_CATALOG.map(t => t.id);
      
      catalogIds.forEach(catalogId => {
        expect(templateIds).toContain(catalogId);
      });
    });
  });
});

describe('Template Data Validation', () => {
  const validResumeData: ResumeData = {
    personalInfo: {
      name: 'John Doe',
      jobTitle: 'Software Engineer',
      email: '<EMAIL>',
      phone: '(*************'
    },
    experience: [
      {
        company: 'Tech Corp',
        jobTitle: 'Senior Developer',
        dates: '2021 - Present',
        description: 'Developed web applications'
      }
    ],
    education: [
      {
        degree: 'BS Computer Science',
        institution: 'University',
        dates: '2017 - 2021'
      }
    ],
    skills: ['JavaScript', 'React', 'Node.js']
  };

  it('should validate complete resume data', () => {
    const isValid = validateTemplateData(validResumeData, 'professional-sidebar-1');
    expect(isValid).toBe(true);
  });

  it('should reject data missing required fields', () => {
    const incompleteData = {
      ...validResumeData,
      personalInfo: {
        ...validResumeData.personalInfo,
        name: '', // Missing required name
        email: '' // Missing required email
      }
    };

    const isValid = validateTemplateData(incompleteData, 'professional-sidebar-1');
    expect(isValid).toBe(false);
  });

  it('should handle templates requiring photos', () => {
    const templateWithPhoto = 'modern-photo-cv';
    
    // Without photo
    const isValidWithoutPhoto = validateTemplateData(validResumeData, templateWithPhoto);
    expect(isValidWithoutPhoto).toBe(false);
    
    // With photo
    const dataWithPhoto = {
      ...validResumeData,
      personalInfo: {
        ...validResumeData.personalInfo,
        photo: 'https://example.com/photo.jpg'
      }
    };
    
    const isValidWithPhoto = validateTemplateData(dataWithPhoto, templateWithPhoto);
    expect(isValidWithPhoto).toBe(true);
  });

  it('should validate required sections', () => {
    const dataWithoutExperience = {
      ...validResumeData,
      experience: []
    };

    const isValid = validateTemplateData(dataWithoutExperience, 'professional-sidebar-1');
    expect(isValid).toBe(false);
  });
});

describe('Singleton Pattern', () => {
  it('should return the same instance', () => {
    const instance1 = TemplateRegistry.getInstance();
    const instance2 = TemplateRegistry.getInstance();
    
    expect(instance1).toBe(instance2);
  });

  it('should maintain state across getInstance calls', () => {
    const instance1 = TemplateRegistry.getInstance();
    instance1.registerTemplate('test-singleton', MockTemplateComponent);
    
    const instance2 = TemplateRegistry.getInstance();
    expect(instance2.isTemplateAvailable('test-singleton')).toBe(true);
  });
});

describe('Error Handling', () => {
  it('should handle invalid template IDs gracefully', () => {
    expect(() => {
      registry.getTemplate('invalid-template-id');
    }).not.toThrow();
  });

  it('should handle null/undefined template components', () => {
    expect(() => {
      registry.registerTemplate('null-template', null as any);
    }).not.toThrow();
  });

  it('should handle malformed metadata', () => {
    const templateWithBadMetadata = {
      id: 'bad-metadata-template',
      component: MockTemplateComponent,
      metadata: null as any
    };

    expect(() => {
      registry.registerTemplate(
        templateWithBadMetadata.id, 
        templateWithBadMetadata.component, 
        templateWithBadMetadata.metadata
      );
    }).not.toThrow();
  });
});

describe('Template Catalog Integration', () => {
  it('should load all catalog templates', () => {
    const catalogTemplateIds = TEMPLATE_CATALOG.map(t => t.id);
    const registryTemplateIds = registry.getTemplateIds();
    
    catalogTemplateIds.forEach(catalogId => {
      expect(registryTemplateIds).toContain(catalogId);
    });
  });

  it('should preserve catalog metadata', () => {
    TEMPLATE_CATALOG.forEach(catalogTemplate => {
      const registryTemplate = registry.getTemplate(catalogTemplate.id);
      
      expect(registryTemplate?.metadata?.name).toBe(catalogTemplate.name);
      expect(registryTemplate?.metadata?.category).toBe(catalogTemplate.category);
      expect(registryTemplate?.metadata?.description).toBe(catalogTemplate.description);
    });
  });

  it('should handle missing catalog entries gracefully', () => {
    const nonCatalogTemplate = 'custom-template-not-in-catalog';
    registry.registerTemplate(nonCatalogTemplate, MockTemplateComponent);
    
    const template = registry.getTemplate(nonCatalogTemplate);
    expect(template).toBeDefined();
    expect(template?.metadata).toBeDefined();
  });
});

describe('Performance', () => {
  it('should handle large numbers of templates efficiently', () => {
    const startTime = performance.now();
    
    // Register 100 test templates
    for (let i = 0; i < 100; i++) {
      registry.registerTemplate(`perf-test-${i}`, MockTemplateComponent);
    }
    
    // Perform operations
    registry.getAllTemplates();
    registry.getTemplatesByCategory('professional');
    registry.searchTemplates('test');
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Should complete within reasonable time (adjust threshold as needed)
    expect(duration).toBeLessThan(100); // 100ms threshold
  });

  it('should cache template lookups', () => {
    const templateId = 'cache-test-template';
    registry.registerTemplate(templateId, MockTemplateComponent);
    
    const startTime = performance.now();
    
    // Perform multiple lookups
    for (let i = 0; i < 1000; i++) {
      registry.getTemplate(templateId);
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Cached lookups should be very fast
    expect(duration).toBeLessThan(50); // 50ms for 1000 lookups
  });
});
