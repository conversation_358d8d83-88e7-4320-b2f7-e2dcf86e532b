// Tests for template switching functionality
// Ensures data preservation and proper adaptation when switching templates

import { describe, it, expect, beforeEach } from '@jest/globals';
import { 
  TemplateSwitcher,
  templateSwitcher,
  switchTemplate,
  canSwitchTemplate 
} from '@/lib/templates/template-switcher';
import { ResumeData } from '@/lib/templates/template-registry';

describe('TemplateSwitcher', () => {
  let switcher: TemplateSwitcher;
  
  const sampleResumeData: ResumeData = {
    personalInfo: {
      name: '<PERSON>',
      jobTitle: 'Product Manager',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Seattle, WA',
      linkedin: 'linkedin.com/in/janesmith',
      photo: 'https://example.com/photo.jpg'
    },
    summary: 'Experienced product manager with 5+ years in tech startups.',
    profile: 'Strategic product leader focused on user experience and growth.',
    experience: [
      {
        company: 'TechStart Inc.',
        jobTitle: 'Senior Product Manager',
        dates: '2020 - Present',
        description: 'Led product strategy for B2B SaaS platform.',
        location: 'Seattle, WA'
      }
    ],
    education: [
      {
        degree: 'MBA',
        institution: 'Business School',
        school: 'Business School',
        dates: '2018 - 2020',
        year: '2020'
      }
    ],
    skills: ['Product Strategy', 'User Research', 'Data Analysis', 'Agile'],
    projects: [
      {
        name: 'Mobile App Launch',
        description: 'Led the launch of mobile application with 100K+ downloads'
      }
    ],
    certifications: ['PMP', 'Scrum Master'],
    awards: [
      {
        title: 'Product Innovation Award',
        date: '2022',
        description: 'Recognized for innovative product features'
      }
    ]
  };

  beforeEach(() => {
    switcher = TemplateSwitcher.getInstance();
  });

  describe('Template Switching', () => {
    it('should switch between compatible templates successfully', () => {
      const result = switcher.switchTemplate(
        sampleResumeData,
        'professional-sidebar-1',
        'modern-photo-cv'
      );

      expect(result.success).toBe(true);
      expect(result.adaptedData).toBeDefined();
      expect(result.errors.length).toBe(0);
    });

    it('should preserve core personal information', () => {
      const result = switcher.switchTemplate(
        sampleResumeData,
        'professional-sidebar-1',
        'clean-professional-3'
      );

      expect(result.adaptedData?.personalInfo.name).toBe(sampleResumeData.personalInfo.name);
      expect(result.adaptedData?.personalInfo.email).toBe(sampleResumeData.personalInfo.email);
      expect(result.adaptedData?.personalInfo.phone).toBe(sampleResumeData.personalInfo.phone);
    });

    it('should adapt field mappings correctly', () => {
      const result = switcher.switchTemplate(
        sampleResumeData,
        'professional-sidebar-1',
        'modern-photo-cv'
      );

      // Should map summary to profile or vice versa
      expect(
        result.adaptedData?.summary || result.adaptedData?.profile
      ).toBeDefined();
    });

    it('should handle missing templates gracefully', () => {
      const result = switcher.switchTemplate(
        sampleResumeData,
        'non-existent-template',
        'professional-sidebar-1'
      );

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0]).toContain('Template not found');
    });
  });

  describe('Data Loss Detection', () => {
    it('should warn about unsupported sections', () => {
      const dataWithCertifications = {
        ...sampleResumeData,
        certifications: ['AWS Certified', 'Google Cloud']
      };

      const result = switcher.switchTemplate(
        dataWithCertifications,
        'professional-sidebar-1',
        'clean-professional-3'
      );

      // Check if warnings mention certifications if not supported
      const hasWarning = result.warnings.some(warning => 
        warning.toLowerCase().includes('certification')
      );
      
      // This depends on template configuration
      expect(result.warnings.length).toBeGreaterThanOrEqual(0);
    });

    it('should warn about photo requirements', () => {
      const dataWithoutPhoto = {
        ...sampleResumeData,
        personalInfo: {
          ...sampleResumeData.personalInfo,
          photo: undefined
        }
      };

      const result = switcher.switchTemplate(
        dataWithoutPhoto,
        'clean-professional-3',
        'modern-photo-cv'
      );

      const hasPhotoWarning = result.warnings.some(warning => 
        warning.toLowerCase().includes('photo')
      );
      
      expect(hasPhotoWarning).toBe(true);
    });

    it('should detect section compatibility issues', () => {
      const dataWithVolunteer = {
        ...sampleResumeData,
        volunteer: [
          {
            organization: 'Local Charity',
            role: 'Volunteer Coordinator',
            dates: '2019 - 2020',
            description: 'Organized community events'
          }
        ]
      };

      const result = switcher.switchTemplate(
        dataWithVolunteer,
        'modern-photo-cv',
        'clean-professional-3'
      );

      // Should warn if volunteer section is not supported in target template
      expect(result.warnings.length).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Field Mapping', () => {
    it('should map summary to profile correctly', () => {
      const dataWithSummary = {
        ...sampleResumeData,
        summary: 'Professional summary text',
        profile: undefined
      };

      const result = switcher.switchTemplate(
        dataWithSummary,
        'clean-professional-3',
        'professional-sidebar-1'
      );

      expect(result.adaptedData?.profile).toBe(dataWithSummary.summary);
    });

    it('should map education fields correctly', () => {
      const result = switcher.switchTemplate(
        sampleResumeData,
        'professional-sidebar-1',
        'modern-photo-cv'
      );

      // Check that education mapping works
      expect(result.adaptedData?.education).toBeDefined();
      expect(result.adaptedData?.education.length).toBeGreaterThan(0);
    });

    it('should preserve unmapped fields', () => {
      const result = switcher.switchTemplate(
        sampleResumeData,
        'professional-sidebar-1',
        'modern-photo-cv'
      );

      // Core fields should always be preserved
      expect(result.adaptedData?.skills).toEqual(sampleResumeData.skills);
      expect(result.adaptedData?.experience).toEqual(sampleResumeData.experience);
    });
  });

  describe('Validation', () => {
    it('should validate required fields in target template', () => {
      const incompleteData = {
        ...sampleResumeData,
        personalInfo: {
          ...sampleResumeData.personalInfo,
          name: '',
          email: ''
        }
      };

      const result = switcher.switchTemplate(
        incompleteData,
        'professional-sidebar-1',
        'modern-photo-cv'
      );

      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.success).toBe(false);
    });

    it('should provide helpful error messages', () => {
      const invalidData = {
        personalInfo: { name: '', jobTitle: '', email: '', phone: '' },
        experience: [],
        education: [],
        skills: []
      } as ResumeData;

      const result = switcher.switchTemplate(
        invalidData,
        'professional-sidebar-1',
        'modern-photo-cv'
      );

      expect(result.errors.length).toBeGreaterThan(0);
      result.errors.forEach(error => {
        expect(typeof error).toBe('string');
        expect(error.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Helper Functions', () => {
    it('should provide switchTemplate helper function', () => {
      const result = switchTemplate(
        sampleResumeData,
        'professional-sidebar-1',
        'modern-photo-cv'
      );

      expect(result).toBeDefined();
      expect(typeof result.success).toBe('boolean');
    });

    it('should provide canSwitchTemplate helper function', () => {
      const canSwitch = canSwitchTemplate(
        sampleResumeData,
        'professional-sidebar-1',
        'modern-photo-cv'
      );

      expect(typeof canSwitch).toBe('boolean');
    });

    it('should return false for incompatible switches', () => {
      const incompleteData = {
        personalInfo: { name: '', jobTitle: '', email: '', phone: '' },
        experience: [],
        education: [],
        skills: []
      } as ResumeData;

      const canSwitch = canSwitchTemplate(
        incompleteData,
        'professional-sidebar-1',
        'modern-photo-cv'
      );

      expect(canSwitch).toBe(false);
    });
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = TemplateSwitcher.getInstance();
      const instance2 = TemplateSwitcher.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty resume data', () => {
      const emptyData = {
        personalInfo: { name: '', jobTitle: '', email: '', phone: '' },
        experience: [],
        education: [],
        skills: []
      } as ResumeData;

      const result = switcher.switchTemplate(
        emptyData,
        'professional-sidebar-1',
        'modern-photo-cv'
      );

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should handle switching to the same template', () => {
      const result = switcher.switchTemplate(
        sampleResumeData,
        'professional-sidebar-1',
        'professional-sidebar-1'
      );

      expect(result.success).toBe(true);
      expect(result.adaptedData).toEqual(sampleResumeData);
    });

    it('should handle null/undefined data gracefully', () => {
      expect(() => {
        switcher.switchTemplate(
          null as any,
          'professional-sidebar-1',
          'modern-photo-cv'
        );
      }).not.toThrow();
    });

    it('should handle very large resume data', () => {
      const largeData = {
        ...sampleResumeData,
        experience: Array(50).fill(sampleResumeData.experience[0]),
        skills: Array(100).fill('Skill'),
        projects: Array(20).fill(sampleResumeData.projects![0])
      };

      const result = switcher.switchTemplate(
        largeData,
        'professional-sidebar-1',
        'modern-photo-cv'
      );

      expect(result).toBeDefined();
      expect(result.adaptedData).toBeDefined();
    });
  });
});
