import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { AIContentService } from '@/lib/ai/content-service';
import { useAIContent } from '@/lib/hooks/useAIContent';
import AIContentPanel from '@/components/ai/AIContentPanel';
import SmartContentGenerator from '@/components/ai/SmartContentGenerator';
import { ResumeContent } from '@/types';

// Mock the AI service
jest.mock('@/lib/ai/content-service');
const mockAIContentService = AIContentService as jest.MockedClass<typeof AIContentService>;

// Mock the hook
jest.mock('@/lib/hooks/useAIContent');
const mockUseAIContent = useAIContent as jest.MockedFunction<typeof useAIContent>;

// Sample resume data for testing
const sampleResumeContent: ResumeContent = {
  personalInfo: {
    fullName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567890',
    location: 'San Francisco, CA',
    website: 'https://johndoe.dev',
    linkedinUrl: 'https://linkedin.com/in/johndoe',
    githubUrl: 'https://github.com/johndoe'
  },
  summary: 'Experienced software engineer with 5+ years of expertise.',
  experience: [
    {
      id: '1',
      company: 'Tech Corp',
      position: 'Senior Developer',
      location: 'San Francisco, CA',
      startDate: new Date('2020-01-01'),
      endDate: new Date('2023-01-01'),
      current: false,
      description: ['Led development of web applications', 'Mentored junior developers'],
      skills: ['React', 'Node.js', 'TypeScript']
    }
  ],
  education: [],
  skills: ['JavaScript', 'React', 'Node.js', 'TypeScript'],
  certifications: [],
  languages: []
};

describe('AI Content System', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('AIContentService', () => {
    let service: AIContentService;

    beforeEach(() => {
      service = AIContentService.getInstance();
    });

    it('should analyze content successfully', async () => {
      const mockAnalysis = {
        overallScore: 85,
        atsScore: 90,
        keywordDensity: 0.75,
        readabilityScore: 88,
        impactScore: 82,
        industryAlignment: 85,
        strengths: ['Strong technical skills', 'Clear experience progression'],
        weaknesses: ['Missing quantified achievements'],
        missingKeywords: ['cloud computing', 'agile'],
        suggestions: [
          {
            id: '1',
            type: 'improvement' as const,
            section: 'summary' as const,
            original: 'Current summary',
            suggested: 'Improved summary with keywords',
            reason: 'Better keyword optimization',
            confidence: 0.9,
            impact: 'high' as const,
            category: 'keywords' as const,
            keywords: ['leadership', 'innovation']
          }
        ],
        sectionScores: {
          summary: 80,
          experience: 90,
          skills: 85,
          education: 75,
          certifications: 70,
          projects: 80
        }
      };

      const mockResponse = {
        success: true,
        data: mockAnalysis,
        metadata: {
          processingTime: 1500,
          tokensUsed: 1000,
          cost: 0.02,
          model: 'gpt-4',
          confidence: 0.85
        }
      };

      jest.spyOn(service, 'analyzeContent').mockResolvedValue(mockResponse);

      const result = await service.analyzeContent(sampleResumeContent);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockAnalysis);
      expect(result.data?.overallScore).toBe(85);
      expect(result.data?.suggestions).toHaveLength(1);
    });

    it('should generate content successfully', async () => {
      const mockGeneration = {
        generatedContent: 'Experienced software engineer with proven track record...',
        alternatives: [
          'Senior software developer with 5+ years...',
          'Results-driven engineer specializing in...',
          'Full-stack developer with expertise in...'
        ],
        keywords: ['software engineer', 'full-stack', 'leadership'],
        reasoning: 'Generated content focuses on achievements and keywords',
        confidence: 0.88,
        suggestions: ['Consider adding specific metrics', 'Include industry keywords']
      };

      const mockResponse = {
        success: true,
        data: mockGeneration,
        metadata: {
          processingTime: 2000,
          tokensUsed: 800,
          cost: 0.015,
          model: 'gpt-4',
          confidence: 0.88
        }
      };

      jest.spyOn(service, 'generateContent').mockResolvedValue(mockResponse);

      const request = {
        section: 'summary' as const,
        context: {
          role: 'Software Engineer',
          industry: 'Technology',
          experience: sampleResumeContent.experience,
          skills: sampleResumeContent.skills,
          achievements: ['Led team of 5 developers']
        },
        options: {
          length: 'medium' as const,
          style: 'paragraph' as const,
          focus: 'achievements' as const,
          tone: 'professional' as const
        }
      };

      const result = await service.generateContent(request);

      expect(result.success).toBe(true);
      expect(result.data?.generatedContent).toBeTruthy();
      expect(result.data?.alternatives).toHaveLength(3);
      expect(result.data?.confidence).toBeGreaterThan(0.8);
    });

    it('should handle errors gracefully', async () => {
      const mockErrorResponse = {
        success: false,
        error: 'AI service unavailable'
      };

      jest.spyOn(service, 'analyzeContent').mockResolvedValue(mockErrorResponse);

      const result = await service.analyzeContent(sampleResumeContent);

      expect(result.success).toBe(false);
      expect(result.error).toBe('AI service unavailable');
    });
  });

  describe('useAIContent Hook', () => {
    it('should provide correct initial state', () => {
      const mockHookReturn = {
        analysis: null,
        suggestions: [],
        realTimeSuggestions: [],
        atsOptimization: null,
        isAnalyzing: false,
        isGenerating: false,
        isOptimizing: false,
        error: null,
        metrics: {
          totalSuggestions: 0,
          acceptedSuggestions: 0,
          rejectedSuggestions: 0,
          averageConfidence: 0,
          improvementScore: 0,
          timeSpent: 0,
          sectionsOptimized: [],
          keywordsAdded: 0,
          atsScoreImprovement: 0
        },
        analyzeContent: jest.fn(),
        generateContent: jest.fn(),
        optimizeContent: jest.fn(),
        analyzeATS: jest.fn(),
        applySuggestion: jest.fn(),
        rejectSuggestion: jest.fn(),
        dismissSuggestion: jest.fn(),
        clearSuggestions: jest.fn(),
        refreshAnalysis: jest.fn(),
        getSuggestionsBySection: jest.fn(),
        getSuggestionsByType: jest.fn(),
        getHighImpactSuggestions: jest.fn(),
        calculateImprovementScore: jest.fn()
      };

      mockUseAIContent.mockReturnValue(mockHookReturn);

      const { result } = renderHook(() => useAIContent({
        resumeContent: sampleResumeContent,
        enableRealTime: true,
        autoAnalyze: true
      }));

      expect(result.current.analysis).toBeNull();
      expect(result.current.suggestions).toEqual([]);
      expect(result.current.isAnalyzing).toBe(false);
    });
  });

  describe('AIContentPanel Component', () => {
    beforeEach(() => {
      const mockHookReturn = {
        analysis: {
          overallScore: 85,
          atsScore: 90,
          keywordDensity: 0.75,
          readabilityScore: 88,
          impactScore: 82,
          industryAlignment: 85,
          strengths: ['Strong technical skills'],
          weaknesses: ['Missing quantified achievements'],
          missingKeywords: ['cloud computing'],
          suggestions: [
            {
              id: '1',
              type: 'improvement' as const,
              section: 'summary' as const,
              original: 'Current summary',
              suggested: 'Improved summary',
              reason: 'Better impact',
              confidence: 0.9,
              impact: 'high' as const,
              category: 'keywords' as const,
              keywords: ['leadership']
            }
          ],
          sectionScores: {
            summary: 80,
            experience: 90,
            skills: 85,
            education: 75,
            certifications: 70,
            projects: 80
          }
        },
        suggestions: [
          {
            id: '1',
            type: 'improvement' as const,
            section: 'summary' as const,
            original: 'Current summary',
            suggested: 'Improved summary',
            reason: 'Better impact',
            confidence: 0.9,
            impact: 'high' as const,
            category: 'keywords' as const,
            keywords: ['leadership']
          }
        ],
        realTimeSuggestions: [],
        atsOptimization: null,
        isAnalyzing: false,
        isGenerating: false,
        isOptimizing: false,
        error: null,
        metrics: {
          totalSuggestions: 1,
          acceptedSuggestions: 0,
          rejectedSuggestions: 0,
          averageConfidence: 0.9,
          improvementScore: 0,
          timeSpent: 0,
          sectionsOptimized: [],
          keywordsAdded: 0,
          atsScoreImprovement: 0
        },
        analyzeContent: jest.fn(),
        generateContent: jest.fn(),
        optimizeContent: jest.fn(),
        analyzeATS: jest.fn(),
        applySuggestion: jest.fn(),
        rejectSuggestion: jest.fn(),
        dismissSuggestion: jest.fn(),
        clearSuggestions: jest.fn(),
        refreshAnalysis: jest.fn(),
        getSuggestionsBySection: jest.fn().mockReturnValue([]),
        getSuggestionsByType: jest.fn(),
        getHighImpactSuggestions: jest.fn().mockReturnValue([
          {
            id: '1',
            type: 'improvement' as const,
            section: 'summary' as const,
            original: 'Current summary',
            suggested: 'Improved summary',
            reason: 'Better impact',
            confidence: 0.9,
            impact: 'high' as const,
            category: 'keywords' as const,
            keywords: ['leadership']
          }
        ]),
        calculateImprovementScore: jest.fn().mockReturnValue(92)
      };

      mockUseAIContent.mockReturnValue(mockHookReturn);
    });

    it('should render AI content panel with analysis data', () => {
      render(
        <AIContentPanel
          resumeContent={sampleResumeContent}
          onContentUpdate={jest.fn()}
          onSuggestionApply={jest.fn()}
        />
      );

      expect(screen.getByText('AI Content Assistant')).toBeInTheDocument();
      expect(screen.getByText('85')).toBeInTheDocument(); // Overall score
      expect(screen.getByText('90')).toBeInTheDocument(); // ATS score
      expect(screen.getByText('1')).toBeInTheDocument(); // Suggestions count
    });

    it('should handle suggestion application', async () => {
      const mockOnSuggestionApply = jest.fn();
      
      render(
        <AIContentPanel
          resumeContent={sampleResumeContent}
          onContentUpdate={jest.fn()}
          onSuggestionApply={mockOnSuggestionApply}
        />
      );

      // Click on suggestions tab
      fireEvent.click(screen.getByText('Suggestions'));

      // Find and click apply button
      const applyButton = screen.getByText('Apply');
      fireEvent.click(applyButton);

      await waitFor(() => {
        expect(mockOnSuggestionApply).toHaveBeenCalled();
      });
    });

    it('should display error messages', () => {
      const mockHookReturnWithError = {
        analysis: null,
        suggestions: [],
        realTimeSuggestions: [],
        atsOptimization: null,
        isAnalyzing: false,
        isGenerating: false,
        isOptimizing: false,
        error: 'AI service is currently unavailable',
        metrics: {
          totalSuggestions: 0,
          acceptedSuggestions: 0,
          rejectedSuggestions: 0,
          averageConfidence: 0,
          improvementScore: 0,
          timeSpent: 0,
          sectionsOptimized: [],
          keywordsAdded: 0,
          atsScoreImprovement: 0
        },
        analyzeContent: jest.fn(),
        generateContent: jest.fn(),
        optimizeContent: jest.fn(),
        analyzeATS: jest.fn(),
        applySuggestion: jest.fn(),
        rejectSuggestion: jest.fn(),
        dismissSuggestion: jest.fn(),
        clearSuggestions: jest.fn(),
        refreshAnalysis: jest.fn(),
        getSuggestionsBySection: jest.fn(),
        getSuggestionsByType: jest.fn(),
        getHighImpactSuggestions: jest.fn(),
        calculateImprovementScore: jest.fn()
      };

      mockUseAIContent.mockReturnValue(mockHookReturnWithError);

      render(
        <AIContentPanel
          resumeContent={sampleResumeContent}
          onContentUpdate={jest.fn()}
          onSuggestionApply={jest.fn()}
        />
      );

      expect(screen.getByText('AI service is currently unavailable')).toBeInTheDocument();
    });
  });

  describe('SmartContentGenerator Component', () => {
    beforeEach(() => {
      const mockHookReturn = {
        analysis: null,
        suggestions: [],
        realTimeSuggestions: [],
        atsOptimization: null,
        isAnalyzing: false,
        isGenerating: false,
        isOptimizing: false,
        error: null,
        metrics: {
          totalSuggestions: 0,
          acceptedSuggestions: 0,
          rejectedSuggestions: 0,
          averageConfidence: 0,
          improvementScore: 0,
          timeSpent: 0,
          sectionsOptimized: [],
          keywordsAdded: 0,
          atsScoreImprovement: 0
        },
        analyzeContent: jest.fn(),
        generateContent: jest.fn().mockResolvedValue({
          generatedContent: 'Generated summary content',
          alternatives: ['Alt 1', 'Alt 2', 'Alt 3'],
          keywords: ['keyword1', 'keyword2'],
          reasoning: 'Content generated based on experience',
          confidence: 0.85,
          suggestions: ['Add metrics', 'Include achievements']
        }),
        optimizeContent: jest.fn(),
        analyzeATS: jest.fn(),
        applySuggestion: jest.fn(),
        rejectSuggestion: jest.fn(),
        dismissSuggestion: jest.fn(),
        clearSuggestions: jest.fn(),
        refreshAnalysis: jest.fn(),
        getSuggestionsBySection: jest.fn(),
        getSuggestionsByType: jest.fn(),
        getHighImpactSuggestions: jest.fn(),
        calculateImprovementScore: jest.fn()
      };

      mockUseAIContent.mockReturnValue(mockHookReturn);
    });

    it('should render content generator interface', () => {
      render(
        <SmartContentGenerator
          resumeContent={sampleResumeContent}
          section="summary"
          onContentGenerated={jest.fn()}
          onContentApply={jest.fn()}
        />
      );

      expect(screen.getByText('Smart Content Generator')).toBeInTheDocument();
      expect(screen.getByText('Generate Content')).toBeInTheDocument();
    });

    it('should handle content generation', async () => {
      const mockOnContentGenerated = jest.fn();
      
      render(
        <SmartContentGenerator
          resumeContent={sampleResumeContent}
          section="summary"
          onContentGenerated={mockOnContentGenerated}
          onContentApply={jest.fn()}
        />
      );

      const generateButton = screen.getByText('Generate Content');
      fireEvent.click(generateButton);

      await waitFor(() => {
        expect(mockOnContentGenerated).toHaveBeenCalledWith('Generated summary content');
      });
    });
  });
});

// Helper function for rendering hooks
function renderHook<T>(callback: () => T) {
  let result: { current: T };
  
  function TestComponent() {
    result = { current: callback() };
    return null;
  }
  
  render(<TestComponent />);
  return { result };
}
