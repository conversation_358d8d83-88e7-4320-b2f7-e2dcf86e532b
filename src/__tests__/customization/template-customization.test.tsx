import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { CustomizationEngine } from '@/lib/customization/customization-engine';
import CustomizationPanel from '@/components/customization/CustomizationPanel';
import CustomizableTemplate from '@/components/customization/CustomizableTemplate';
import { useTemplateCustomization } from '@/lib/hooks/useTemplateCustomization';
import { 
  DEFAULT_COLOR_SCHEME, 
  DEFAULT_TYPOGRAPHY, 
  DEFAULT_LAYOUT, 
  DEFAULT_SPACING,
  TemplateCustomization 
} from '@/types/customization';

// Mock the hook
jest.mock('@/lib/hooks/useTemplateCustomization');
const mockUseTemplateCustomization = useTemplateCustomization as jest.MockedFunction<typeof useTemplateCustomization>;

describe('CustomizationEngine', () => {
  let engine: CustomizationEngine;

  beforeEach(() => {
    engine = new CustomizationEngine();
  });

  test('should initialize with default values', () => {
    const customization = engine.getCustomization();
    
    expect(customization.colorScheme).toEqual(DEFAULT_COLOR_SCHEME);
    expect(customization.typography).toEqual(DEFAULT_TYPOGRAPHY);
    expect(customization.layout).toEqual(DEFAULT_LAYOUT);
    expect(customization.spacing).toEqual(DEFAULT_SPACING);
  });

  test('should generate CSS custom properties', () => {
    const cssProperties = engine.getCSSProperties();
    
    expect(cssProperties['--template-color-primary']).toBe(DEFAULT_COLOR_SCHEME.primary);
    expect(cssProperties['--template-color-secondary']).toBe(DEFAULT_COLOR_SCHEME.secondary);
    expect(cssProperties['--template-font-heading']).toBe(DEFAULT_TYPOGRAPHY.headingFont);
    expect(cssProperties['--template-font-body']).toBe(DEFAULT_TYPOGRAPHY.bodyFont);
  });

  test('should update color scheme', () => {
    const newColors = { primary: '#ff0000', secondary: '#00ff00' };
    engine.updateColorScheme(newColors);
    
    const customization = engine.getCustomization();
    expect(customization.colorScheme.primary).toBe('#ff0000');
    expect(customization.colorScheme.secondary).toBe('#00ff00');
    expect(customization.colorScheme.accent).toBe(DEFAULT_COLOR_SCHEME.accent); // Should remain unchanged
  });

  test('should update typography', () => {
    const newTypography = { headingFont: 'Arial', bodyFont: 'Helvetica' };
    engine.updateTypography(newTypography);
    
    const customization = engine.getCustomization();
    expect(customization.typography.headingFont).toBe('Arial');
    expect(customization.typography.bodyFont).toBe('Helvetica');
  });

  test('should validate customization', () => {
    // Test with good contrast
    engine.updateColorScheme({ text: '#000000', background: '#ffffff' });
    let validation = engine.validateCustomization();
    expect(validation.isValid).toBe(true);
    expect(validation.atsCompatible).toBe(true);
    expect(validation.accessibilityScore).toBeGreaterThan(90);

    // Test with poor contrast
    engine.updateColorScheme({ text: '#cccccc', background: '#ffffff' });
    validation = engine.validateCustomization();
    expect(validation.isValid).toBe(false);
    expect(validation.errors.length).toBeGreaterThan(0);
    expect(validation.accessibilityScore).toBeLessThan(100);
  });

  test('should reset to default', () => {
    // Make some changes
    engine.updateColorScheme({ primary: '#ff0000' });
    engine.updateTypography({ headingFont: 'Arial' });
    
    // Reset
    engine.resetToDefault();
    
    const customization = engine.getCustomization();
    expect(customization.colorScheme.primary).toBe(DEFAULT_COLOR_SCHEME.primary);
    expect(customization.typography.headingFont).toBe(DEFAULT_TYPOGRAPHY.headingFont);
  });

  test('should generate CSS string', () => {
    const cssString = engine.getCSSString();
    
    expect(cssString).toContain('--template-color-primary:');
    expect(cssString).toContain('--template-font-heading:');
    expect(cssString).toContain(';');
  });
});

describe('CustomizableTemplate', () => {
  const mockCustomization: TemplateCustomization = {
    id: 'test-customization',
    templateId: 'test-template',
    name: 'Test Customization',
    colorScheme: {
      ...DEFAULT_COLOR_SCHEME,
      primary: '#ff0000'
    },
    typography: DEFAULT_TYPOGRAPHY,
    layout: DEFAULT_LAYOUT,
    spacing: DEFAULT_SPACING,
    isDefault: false,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  test('should render children without customization', () => {
    render(
      <CustomizableTemplate enableCustomization={false}>
        <div data-testid="child">Test Content</div>
      </CustomizableTemplate>
    );

    expect(screen.getByTestId('child')).toBeInTheDocument();
  });

  test('should apply customization styles', () => {
    render(
      <CustomizableTemplate customization={mockCustomization}>
        <div data-testid="child">Test Content</div>
      </CustomizableTemplate>
    );

    const container = screen.getByTestId('child').parentElement;
    expect(container).toHaveAttribute('data-customizable', 'true');
    expect(container).toHaveClass('template-customizable');
  });

  test('should inject custom CSS', () => {
    render(
      <CustomizableTemplate customization={mockCustomization}>
        <div data-testid="child">Test Content</div>
      </CustomizableTemplate>
    );

    // Check if style tag is present
    const styleTag = document.querySelector('style');
    expect(styleTag).toBeInTheDocument();
    expect(styleTag?.textContent).toContain('--template-color-primary: #ff0000');
  });
});

describe('CustomizationPanel', () => {
  const mockHookReturn = {
    customization: {
      id: 'test',
      templateId: 'test-template',
      name: 'Test',
      colorScheme: DEFAULT_COLOR_SCHEME,
      typography: DEFAULT_TYPOGRAPHY,
      layout: DEFAULT_LAYOUT,
      spacing: DEFAULT_SPACING,
      isDefault: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    engine: new CustomizationEngine(),
    isDirty: false,
    isLoading: false,
    validation: {
      isValid: true,
      errors: [],
      warnings: [],
      atsCompatible: true,
      accessibilityScore: 100
    },
    updateColorScheme: jest.fn(),
    updateTypography: jest.fn(),
    updateLayout: jest.fn(),
    updateSpacing: jest.fn(),
    applyPreset: jest.fn(),
    resetToDefault: jest.fn(),
    saveCustomization: jest.fn(),
    loadCustomization: jest.fn(),
    getCSSProperties: jest.fn(),
    getCSSString: jest.fn(),
    exportCustomization: jest.fn(),
    importCustomization: jest.fn()
  };

  beforeEach(() => {
    mockUseTemplateCustomization.mockReturnValue(mockHookReturn);
  });

  test('should render customization panel', () => {
    render(
      <CustomizationPanel
        templateId="test-template"
        isCollapsed={false}
      />
    );

    expect(screen.getByText('Customize')).toBeInTheDocument();
    expect(screen.getByText('Quick Presets')).toBeInTheDocument();
  });

  test('should render collapsed state', () => {
    render(
      <CustomizationPanel
        templateId="test-template"
        isCollapsed={true}
        onToggleCollapse={jest.fn()}
      />
    );

    expect(screen.queryByText('Customize')).not.toBeInTheDocument();
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  test('should show validation errors', () => {
    const mockWithErrors = {
      ...mockHookReturn,
      validation: {
        isValid: false,
        errors: [{ field: 'colorScheme.text', message: 'Poor contrast', severity: 'error' as const }],
        warnings: [],
        atsCompatible: false,
        accessibilityScore: 60
      }
    };
    mockUseTemplateCustomization.mockReturnValue(mockWithErrors);

    render(
      <CustomizationPanel
        templateId="test-template"
        isCollapsed={false}
      />
    );

    expect(screen.getByText('1 error(s) found')).toBeInTheDocument();
  });

  test('should handle color scheme updates', async () => {
    render(
      <CustomizationPanel
        templateId="test-template"
        isCollapsed={false}
      />
    );

    // Click on colors tab (should be active by default)
    const colorTab = screen.getByRole('tab', { name: /palette/i });
    fireEvent.click(colorTab);

    // The color picker should be present
    expect(screen.getByText('Primary Color')).toBeInTheDocument();
  });

  test('should handle save action', async () => {
    const mockSave = jest.fn();
    render(
      <CustomizationPanel
        templateId="test-template"
        isCollapsed={false}
        onSave={mockSave}
      />
    );

    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockHookReturn.saveCustomization).toHaveBeenCalled();
    });
  });

  test('should handle preset application', async () => {
    render(
      <CustomizationPanel
        templateId="test-template"
        isCollapsed={false}
      />
    );

    // Find and click a preset button
    const presetButton = screen.getByText('Modern Professional');
    fireEvent.click(presetButton);

    expect(mockHookReturn.applyPreset).toHaveBeenCalled();
  });

  test('should handle reset to default', async () => {
    render(
      <CustomizationPanel
        templateId="test-template"
        isCollapsed={false}
      />
    );

    // Find reset button (should be the rotate icon)
    const resetButton = screen.getByRole('button', { name: '' }); // Icon button
    fireEvent.click(resetButton);

    expect(mockHookReturn.resetToDefault).toHaveBeenCalled();
  });
});

describe('Integration Tests', () => {
  test('should integrate customization with template rendering', () => {
    const mockCustomization: TemplateCustomization = {
      id: 'test',
      templateId: 'test-template',
      name: 'Test',
      colorScheme: {
        ...DEFAULT_COLOR_SCHEME,
        primary: '#ff0000'
      },
      typography: DEFAULT_TYPOGRAPHY,
      layout: DEFAULT_LAYOUT,
      spacing: DEFAULT_SPACING,
      isDefault: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    render(
      <CustomizableTemplate customization={mockCustomization}>
        <div className="template-primary" data-testid="styled-element">
          Test Content
        </div>
      </CustomizableTemplate>
    );

    const styledElement = screen.getByTestId('styled-element');
    expect(styledElement).toBeInTheDocument();
    
    // Check if the container has the customizable class
    const container = styledElement.parentElement;
    expect(container).toHaveClass('template-customizable');
  });
});
