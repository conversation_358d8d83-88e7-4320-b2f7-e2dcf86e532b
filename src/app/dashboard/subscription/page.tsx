'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Crown, 
  Building, 
  Zap, 
  Check, 
  X, 
  Calendar,
  CreditCard,
  ArrowUpCircle,
  Settings
} from 'lucide-react';

interface SubscriptionStatus {
  plan: 'FREE' | 'PRO' | 'ENTERPRISE';
  status: string;
  features: string[];
  limits: {
    resumes: number;
    templates: number;
    aiGenerations: number;
    exports: number;
  };
  currentPeriodEnd?: string;
  cancelAtPeriodEnd: boolean;
  isActive: boolean;
  canUpgrade: boolean;
}

interface UsageStats {
  resumesUsed: number;
  aiGenerationsUsed: number;
  exportsUsed: number;
}

export default function SubscriptionPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [subscription, setSubscription] = useState<SubscriptionStatus | null>(null);
  const [usage, setUsage] = useState<UsageStats>({ resumesUsed: 2, aiGenerationsUsed: 3, exportsUsed: 5 });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (session?.user) {
      fetchSubscriptionData();
    }
  }, [session]);

  const fetchSubscriptionData = async () => {
    try {
      const response = await fetch('/api/subscription/status');
      if (response.ok) {
        const data = await response.json();
        setSubscription(data);
      }
    } catch (error) {
      console.error('Failed to fetch subscription data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpgrade = async (plan: 'PRO' | 'ENTERPRISE') => {
    try {
      const response = await fetch('/api/subscription/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ plan }),
      });

      const data = await response.json();
      if (data.url) {
        window.location.href = data.url;
      }
    } catch (error) {
      console.error('Failed to create checkout session:', error);
    }
  };

  const handleManageBilling = async () => {
    try {
      const response = await fetch('/api/subscription/portal', {
        method: 'POST',
      });

      const data = await response.json();
      if (data.url) {
        window.location.href = data.url;
      }
    } catch (error) {
      console.error('Failed to create billing portal session:', error);
    }
  };

  const getPlanIcon = (plan: string) => {
    switch (plan) {
      case 'PRO':
        return <Crown className="h-6 w-6 text-blue-600" />;
      case 'ENTERPRISE':
        return <Building className="h-6 w-6 text-purple-600" />;
      default:
        return <Zap className="h-6 w-6 text-gray-600" />;
    }
  };

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'PRO':
        return 'border-blue-200 bg-blue-50';
      case 'ENTERPRISE':
        return 'border-purple-200 bg-purple-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const getUsagePercentage = (used: number, limit: number) => {
    if (limit === -1) return 0; // Unlimited
    return Math.min((used / limit) * 100, 100);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading subscription details...</p>
        </div>
      </div>
    );
  }

  if (!subscription) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600">Failed to load subscription data</p>
          <Button onClick={() => window.location.reload()} className="mt-4">
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Subscription & Billing</h1>
          <p className="text-gray-600">Manage your CVLeap subscription and view usage statistics</p>
        </div>

        {/* Current Plan */}
        <Card className={`mb-8 ${getPlanColor(subscription.plan)}`}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {getPlanIcon(subscription.plan)}
                <div>
                  <CardTitle className="text-2xl">{subscription.plan} Plan</CardTitle>
                  <CardDescription>
                    {subscription.plan === 'FREE' && 'Perfect for getting started'}
                    {subscription.plan === 'PRO' && 'For serious job seekers'}
                    {subscription.plan === 'ENTERPRISE' && 'For teams and organizations'}
                  </CardDescription>
                </div>
              </div>
              <Badge variant={subscription.isActive ? 'default' : 'destructive'}>
                {subscription.status}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-3">Plan Features</h4>
                <ul className="space-y-2">
                  {subscription.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-green-600" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-3">Plan Actions</h4>
                <div className="space-y-2">
                  {subscription.canUpgrade && (
                    <Button 
                      onClick={() => router.push('/pricing')} 
                      className="w-full"
                      variant="default"
                    >
                      <ArrowUpCircle className="h-4 w-4 mr-2" />
                      Upgrade Plan
                    </Button>
                  )}
                  {subscription.plan !== 'FREE' && (
                    <Button 
                      onClick={handleManageBilling} 
                      variant="outline" 
                      className="w-full"
                    >
                      <CreditCard className="h-4 w-4 mr-2" />
                      Manage Billing
                    </Button>
                  )}
                </div>
              </div>
            </div>
            
            {subscription.currentPeriodEnd && (
              <div className="mt-6 p-4 bg-white rounded-lg border">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Calendar className="h-4 w-4" />
                  <span>
                    {subscription.cancelAtPeriodEnd 
                      ? `Plan expires on ${new Date(subscription.currentPeriodEnd).toLocaleDateString()}`
                      : `Next billing date: ${new Date(subscription.currentPeriodEnd).toLocaleDateString()}`
                    }
                  </span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Usage Statistics */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Usage Statistics</CardTitle>
            <CardDescription>Track your current usage against plan limits</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Resumes */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Resumes Created</span>
                  <span className="text-sm text-gray-600">
                    {usage.resumesUsed} / {subscription.limits.resumes === -1 ? '∞' : subscription.limits.resumes}
                  </span>
                </div>
                <Progress 
                  value={getUsagePercentage(usage.resumesUsed, subscription.limits.resumes)} 
                  className="h-2"
                />
              </div>

              {/* AI Generations */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">AI Generations</span>
                  <span className="text-sm text-gray-600">
                    {usage.aiGenerationsUsed} / {subscription.limits.aiGenerations === -1 ? '∞' : subscription.limits.aiGenerations}
                  </span>
                </div>
                <Progress 
                  value={getUsagePercentage(usage.aiGenerationsUsed, subscription.limits.aiGenerations)} 
                  className="h-2"
                />
              </div>

              {/* Exports */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Resume Exports</span>
                  <span className="text-sm text-gray-600">
                    {usage.exportsUsed} / {subscription.limits.exports === -1 ? '∞' : subscription.limits.exports}
                  </span>
                </div>
                <Progress 
                  value={getUsagePercentage(usage.exportsUsed, subscription.limits.exports)} 
                  className="h-2"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Upgrade Options */}
        {subscription.canUpgrade && (
          <Card>
            <CardHeader>
              <CardTitle>Upgrade Your Plan</CardTitle>
              <CardDescription>Unlock more features and higher limits</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4">
                {subscription.plan === 'FREE' && (
                  <Button 
                    onClick={() => handleUpgrade('PRO')} 
                    className="h-auto p-4 flex flex-col items-start"
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <Crown className="h-5 w-5" />
                      <span className="font-semibold">Upgrade to Pro</span>
                    </div>
                    <span className="text-sm opacity-90">$9.99/month</span>
                  </Button>
                )}
                {subscription.plan !== 'ENTERPRISE' && (
                  <Button 
                    onClick={() => handleUpgrade('ENTERPRISE')} 
                    variant="outline"
                    className="h-auto p-4 flex flex-col items-start"
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <Building className="h-5 w-5" />
                      <span className="font-semibold">Upgrade to Enterprise</span>
                    </div>
                    <span className="text-sm opacity-70">$29.99/month</span>
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
