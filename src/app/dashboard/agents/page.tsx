'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle, AlertCircle, Bot, TrendingUp, FileText } from 'lucide-react';

interface AgentHealth {
  timestamp: string;
  status: 'healthy' | 'degraded' | 'error';
  services: {
    ai: {
      config: { valid: boolean; errors: string[] };
      providers: { openai: boolean; anthropic: boolean; overall: boolean };
    };
    agents: {
      registered: number;
      health: Record<string, boolean>;
      list: Array<{
        type: string;
        name: string;
        description: string;
        version: string;
      }>;
    };
  };
  warnings?: string[];
}

interface AgentResponse {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: {
    tokensUsed: number;
    cost: number;
    processingTime: number;
    provider: string;
  };
}

export default function AgentsPage() {
  const { data: session } = useSession();
  const [agentHealth, setAgentHealth] = useState<AgentHealth | null>(null);
  const [loading, setLoading] = useState(true);
  const [testLoading, setTestLoading] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<Record<string, AgentResponse>>({});

  // Resume generation form
  const [resumeForm, setResumeForm] = useState({
    jobTitle: 'Software Engineer',
    jobDescription: '',
    companyName: '',
    experienceLevel: 'mid',
    resumeType: 'chronological',
  });

  // Job market analysis form
  const [jobMarketForm, setJobMarketForm] = useState({
    jobTitle: 'Software Engineer',
    location: 'San Francisco, CA',
    industry: 'Technology',
    analysisType: 'comprehensive',
  });

  // ATS scoring form
  const [atsForm, setAtsForm] = useState({
    resumeContent: '',
    jobDescription: '',
  });

  useEffect(() => {
    fetchAgentHealth();
  }, []);

  const fetchAgentHealth = async () => {
    try {
      const response = await fetch('/api/agents/health');
      const data = await response.json();
      setAgentHealth(data);
    } catch (error) {
      console.error('Failed to fetch agent health:', error);
    } finally {
      setLoading(false);
    }
  };

  const testAgent = async (endpoint: string, data: any, testKey: string) => {
    setTestLoading(testKey);
    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      const result = await response.json();
      setTestResults(prev => ({ ...prev, [testKey]: result }));
    } catch (error) {
      setTestResults(prev => ({ 
        ...prev, 
        [testKey]: { 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        } 
      }));
    } finally {
      setTestLoading(null);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'degraded': return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'error': return <XCircle className="h-5 w-5 text-red-500" />;
      default: return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'bg-green-100 text-green-800';
      case 'degraded': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!session) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Please sign in to access the AI agents dashboard.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">AI Agents Dashboard</h1>
          <p className="text-muted-foreground">
            Test and monitor CVLeap's AI agent infrastructure
          </p>
        </div>
        <Button onClick={fetchAgentHealth} variant="outline">
          Refresh Status
        </Button>
      </div>

      {/* Agent Health Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {agentHealth && getStatusIcon(agentHealth.status)}
            System Health
            <Badge className={getStatusColor(agentHealth?.status || 'error')}>
              {agentHealth?.status || 'Unknown'}
            </Badge>
          </CardTitle>
          <CardDescription>
            Overall status of AI services and agents
          </CardDescription>
        </CardHeader>
        <CardContent>
          {agentHealth && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">AI Providers</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span>OpenAI</span>
                    <Badge variant={agentHealth.services.ai.providers.openai ? 'default' : 'destructive'}>
                      {agentHealth.services.ai.providers.openai ? 'Connected' : 'Disconnected'}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Anthropic</span>
                    <Badge variant={agentHealth.services.ai.providers.anthropic ? 'default' : 'destructive'}>
                      {agentHealth.services.ai.providers.anthropic ? 'Connected' : 'Disconnected'}
                    </Badge>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Registered Agents</h4>
                <div className="space-y-2">
                  {agentHealth.services.agents.list.map((agent) => (
                    <div key={agent.type} className="flex items-center justify-between">
                      <span className="flex items-center gap-2">
                        {agent.type === 'RESUME' && <FileText className="h-4 w-4" />}
                        {agent.type === 'RESEARCH' && <TrendingUp className="h-4 w-4" />}
                        {agent.type === 'PROFILE' && <Bot className="h-4 w-4" />}
                        {agent.name}
                      </span>
                      <Badge variant={agentHealth.services.agents.health[agent.type] ? 'default' : 'destructive'}>
                        {agentHealth.services.agents.health[agent.type] ? 'Healthy' : 'Unhealthy'}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {agentHealth?.warnings && agentHealth.warnings.length > 0 && (
            <Alert className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Warnings:</strong>
                <ul className="list-disc list-inside mt-1">
                  {agentHealth.warnings.map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Agent Testing */}
      <Card>
        <CardHeader>
          <CardTitle>Agent Testing</CardTitle>
          <CardDescription>
            Test individual AI agents with sample data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="resume" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="resume">Resume Generation</TabsTrigger>
              <TabsTrigger value="research">Job Market Research</TabsTrigger>
              <TabsTrigger value="ats">ATS Scoring</TabsTrigger>
            </TabsList>

            <TabsContent value="resume" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="jobTitle">Job Title</Label>
                  <Input
                    id="jobTitle"
                    value={resumeForm.jobTitle}
                    onChange={(e) => setResumeForm(prev => ({ ...prev, jobTitle: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="companyName">Company Name</Label>
                  <Input
                    id="companyName"
                    value={resumeForm.companyName}
                    onChange={(e) => setResumeForm(prev => ({ ...prev, companyName: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="experienceLevel">Experience Level</Label>
                  <Select
                    value={resumeForm.experienceLevel}
                    onValueChange={(value) => setResumeForm(prev => ({ ...prev, experienceLevel: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="entry">Entry Level</SelectItem>
                      <SelectItem value="mid">Mid Level</SelectItem>
                      <SelectItem value="senior">Senior Level</SelectItem>
                      <SelectItem value="executive">Executive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="resumeType">Resume Type</Label>
                  <Select
                    value={resumeForm.resumeType}
                    onValueChange={(value) => setResumeForm(prev => ({ ...prev, resumeType: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="chronological">Chronological</SelectItem>
                      <SelectItem value="functional">Functional</SelectItem>
                      <SelectItem value="combination">Combination</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <Label htmlFor="jobDescription">Job Description (Optional)</Label>
                <Textarea
                  id="jobDescription"
                  value={resumeForm.jobDescription}
                  onChange={(e) => setResumeForm(prev => ({ ...prev, jobDescription: e.target.value }))}
                  placeholder="Paste the job description here for better optimization..."
                  rows={4}
                />
              </div>
              <Button
                onClick={() => testAgent('/api/agents/resume/generate', resumeForm, 'resume')}
                disabled={testLoading === 'resume'}
                className="w-full"
              >
                {testLoading === 'resume' && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Generate Resume
              </Button>
              {testResults.resume && (
                <Alert className={testResults.resume.success ? 'border-green-200' : 'border-red-200'}>
                  <AlertDescription>
                    <pre className="whitespace-pre-wrap text-sm">
                      {JSON.stringify(testResults.resume, null, 2)}
                    </pre>
                  </AlertDescription>
                </Alert>
              )}
            </TabsContent>

            <TabsContent value="research" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="researchJobTitle">Job Title</Label>
                  <Input
                    id="researchJobTitle"
                    value={jobMarketForm.jobTitle}
                    onChange={(e) => setJobMarketForm(prev => ({ ...prev, jobTitle: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    value={jobMarketForm.location}
                    onChange={(e) => setJobMarketForm(prev => ({ ...prev, location: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="industry">Industry</Label>
                  <Input
                    id="industry"
                    value={jobMarketForm.industry}
                    onChange={(e) => setJobMarketForm(prev => ({ ...prev, industry: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="analysisType">Analysis Type</Label>
                  <Select
                    value={jobMarketForm.analysisType}
                    onValueChange={(value) => setJobMarketForm(prev => ({ ...prev, analysisType: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="salary">Salary Analysis</SelectItem>
                      <SelectItem value="skills">Skills Analysis</SelectItem>
                      <SelectItem value="trends">Market Trends</SelectItem>
                      <SelectItem value="comprehensive">Comprehensive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <Button
                onClick={() => testAgent('/api/agents/research/job-market', jobMarketForm, 'research')}
                disabled={testLoading === 'research'}
                className="w-full"
              >
                {testLoading === 'research' && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Analyze Job Market
              </Button>
              {testResults.research && (
                <Alert className={testResults.research.success ? 'border-green-200' : 'border-red-200'}>
                  <AlertDescription>
                    <pre className="whitespace-pre-wrap text-sm">
                      {JSON.stringify(testResults.research, null, 2)}
                    </pre>
                  </AlertDescription>
                </Alert>
              )}
            </TabsContent>

            <TabsContent value="ats" className="space-y-4">
              <div>
                <Label htmlFor="resumeContent">Resume Content</Label>
                <Textarea
                  id="resumeContent"
                  value={atsForm.resumeContent}
                  onChange={(e) => setAtsForm(prev => ({ ...prev, resumeContent: e.target.value }))}
                  placeholder="Paste your resume content here..."
                  rows={6}
                />
              </div>
              <div>
                <Label htmlFor="atsJobDescription">Job Description</Label>
                <Textarea
                  id="atsJobDescription"
                  value={atsForm.jobDescription}
                  onChange={(e) => setAtsForm(prev => ({ ...prev, jobDescription: e.target.value }))}
                  placeholder="Paste the job description here..."
                  rows={4}
                />
              </div>
              <Button
                onClick={() => testAgent('/api/agents/resume/ats-score', atsForm, 'ats')}
                disabled={testLoading === 'ats'}
                className="w-full"
              >
                {testLoading === 'ats' && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Score ATS Compatibility
              </Button>
              {testResults.ats && (
                <Alert className={testResults.ats.success ? 'border-green-200' : 'border-red-200'}>
                  <AlertDescription>
                    <pre className="whitespace-pre-wrap text-sm">
                      {JSON.stringify(testResults.ats, null, 2)}
                    </pre>
                  </AlertDescription>
                </Alert>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
