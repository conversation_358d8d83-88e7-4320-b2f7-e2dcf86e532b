import { Metadata } from 'next';
import { getServerSession } from 'next-auth/next';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth/config';
import { getCurrentUserWithProfile } from '@/lib/auth/utils';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  User, 
  FileText, 
  Briefcase, 
  TrendingUp, 
  Plus,
  Settings,
  LogOut
} from 'lucide-react';
import { signOut } from 'next-auth/react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Dashboard | CVLeap',
  description: 'Your CVLeap dashboard - manage resumes, track applications, and accelerate your career.',
};

export default async function DashboardPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/signin');
  }

  const user = await getCurrentUserWithProfile();

  if (!user) {
    redirect('/auth/signin');
  }

  const stats = {
    resumes: user.resumes?.length || 0,
    applications: user._count?.resumes || 0, // This would be job applications in real implementation
    responseRate: user.analytics?.responseRate || 0,
    interviewRate: user.analytics?.interviewRate || 0,
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                CVLeap
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600 dark:text-gray-300">
                Welcome back, {user.name || user.email}
              </span>
              <Button variant="outline" size="sm" asChild>
                <Link href="/auth/signout">
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign Out
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Welcome to your Dashboard
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Manage your resumes, track job applications, and accelerate your career with AI-powered tools.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Resumes</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.resumes}</div>
              <p className="text-xs text-muted-foreground">
                +0 from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Applications</CardTitle>
              <Briefcase className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.applications}</div>
              <p className="text-xs text-muted-foreground">
                +0 from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Response Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.responseRate.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">
                +0% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Interview Rate</CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.interviewRate.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">
                +0% from last month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Get started with building your perfect resume
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button className="w-full justify-start" asChild>
                <Link href="/resumes/new">
                  <Plus className="h-4 w-4 mr-2" />
                  Create New Resume
                </Link>
              </Button>
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href="/jobs">
                  <Briefcase className="h-4 w-4 mr-2" />
                  Browse Jobs
                </Link>
              </Button>
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href="/profile">
                  <Settings className="h-4 w-4 mr-2" />
                  Update Profile
                </Link>
              </Button>
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href="/dashboard/agents">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  AI Agents Dashboard
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Your latest resumes and applications
              </CardDescription>
            </CardHeader>
            <CardContent>
              {user.resumes && user.resumes.length > 0 ? (
                <div className="space-y-4">
                  {user.resumes.slice(0, 3).map((resume) => (
                    <div key={resume.id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{resume.title}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          Updated {new Date(resume.updatedAt).toLocaleDateString()}
                        </p>
                      </div>
                      <Badge variant="secondary">
                        ATS: {resume.atsScore || 0}%
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    No resumes yet. Create your first resume to get started!
                  </p>
                  <Button asChild>
                    <Link href="/resumes/new">
                      <Plus className="h-4 w-4 mr-2" />
                      Create Resume
                    </Link>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* User Profile Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Profile Summary</CardTitle>
            <CardDescription>
              Complete your profile to get better AI recommendations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-2">Basic Information</h4>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Name:</span> {user.profile?.firstName} {user.profile?.lastName}</p>
                  <p><span className="font-medium">Email:</span> {user.email}</p>
                  <p><span className="font-medium">Phone:</span> {user.profile?.phone || 'Not provided'}</p>
                  <p><span className="font-medium">Location:</span> {user.profile?.location || 'Not provided'}</p>
                </div>
              </div>
              <div>
                <h4 className="font-medium mb-2">Professional Summary</h4>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Title:</span> {user.profile?.title || 'Not provided'}</p>
                  <p><span className="font-medium">Experience:</span> {user.profile?.yearsOfExperience || 0} years</p>
                  <p><span className="font-medium">Skills:</span> {user.profile?.skills?.length || 0} added</p>
                  <p><span className="font-medium">Education:</span> {user.profile?.educations?.length || 0} entries</p>
                </div>
              </div>
            </div>
            <div className="mt-6">
              <Button variant="outline" asChild>
                <Link href="/profile">
                  <Settings className="h-4 w-4 mr-2" />
                  Complete Profile
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
