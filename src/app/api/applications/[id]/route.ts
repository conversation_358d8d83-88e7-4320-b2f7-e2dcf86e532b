import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db';
import { z } from 'zod';

interface RouteParams {
  params: { id: string };
}

const updateApplicationSchema = z.object({
  status: z.enum(['SAVED', 'APPLIED', 'UNDER_REVIEW', 'INTERVIEW_SCHEDULED', 'INTERVIEWED', 'OFFER_RECEIVED', 'REJECTED', 'WITHDRAWN']).optional(),
  notes: z.string().optional(),
  followUpDate: z.string().transform((str) => new Date(str)).optional(),
  interviewDates: z.array(z.string().transform((str) => new Date(str))).optional(),
  feedback: z.string().optional(),
});

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const application = await prisma.jobApplication.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
      include: {
        job: {
          select: {
            id: true,
            title: true,
            company: true,
            location: true,
            type: true,
            remote: true,
            salaryMin: true,
            salaryMax: true,
            salaryCurrency: true,
            salaryPeriod: true,
            description: true,
            requirements: true,
            skills: true,
            url: true,
            source: true,
            postedDate: true,
            applicationDeadline: true,
          },
        },
        resume: {
          select: {
            id: true,
            title: true,
            atsScore: true,
          },
        },
        coverLetter: {
          select: {
            id: true,
            title: true,
          },
        },
      },
    });

    if (!application) {
      return NextResponse.json({ error: 'Application not found' }, { status: 404 });
    }

    return NextResponse.json(application);
  } catch (error) {
    console.error('Error fetching application:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const updateData = updateApplicationSchema.parse(body);

    // Check if application exists and belongs to user
    const existingApplication = await prisma.jobApplication.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    });

    if (!existingApplication) {
      return NextResponse.json({ error: 'Application not found' }, { status: 404 });
    }

    // Update application
    const updatedApplication = await prisma.jobApplication.update({
      where: { id: params.id },
      data: updateData,
      include: {
        job: {
          select: {
            id: true,
            title: true,
            company: true,
            location: true,
            type: true,
            remote: true,
            salaryMin: true,
            salaryMax: true,
            salaryCurrency: true,
            salaryPeriod: true,
            url: true,
            source: true,
            postedDate: true,
          },
        },
        resume: {
          select: {
            id: true,
            title: true,
          },
        },
        coverLetter: {
          select: {
            id: true,
            title: true,
          },
        },
      },
    });

    // Update analytics if status changed
    if (updateData.status && updateData.status !== existingApplication.status) {
      const analytics = await prisma.userAnalytics.findUnique({
        where: { userId: session.user.id },
      });

      if (analytics) {
        let updateAnalytics: any = {};

        // Calculate response rate if moving to UNDER_REVIEW or beyond
        if (updateData.status === 'UNDER_REVIEW' && existingApplication.status === 'APPLIED') {
          const totalApplications = analytics.totalApplications;
          const responses = await prisma.jobApplication.count({
            where: {
              userId: session.user.id,
              status: { in: ['UNDER_REVIEW', 'INTERVIEW_SCHEDULED', 'INTERVIEWED', 'OFFER_RECEIVED'] },
            },
          });
          updateAnalytics.responseRate = totalApplications > 0 ? responses / totalApplications : 0;
        }

        // Calculate interview rate if moving to INTERVIEW_SCHEDULED or beyond
        if (updateData.status === 'INTERVIEW_SCHEDULED' && !['INTERVIEW_SCHEDULED', 'INTERVIEWED', 'OFFER_RECEIVED'].includes(existingApplication.status)) {
          const totalApplications = analytics.totalApplications;
          const interviews = await prisma.jobApplication.count({
            where: {
              userId: session.user.id,
              status: { in: ['INTERVIEW_SCHEDULED', 'INTERVIEWED', 'OFFER_RECEIVED'] },
            },
          });
          updateAnalytics.interviewRate = totalApplications > 0 ? interviews / totalApplications : 0;
        }

        // Calculate offer rate if moving to OFFER_RECEIVED
        if (updateData.status === 'OFFER_RECEIVED' && existingApplication.status !== 'OFFER_RECEIVED') {
          const totalApplications = analytics.totalApplications;
          const offers = await prisma.jobApplication.count({
            where: {
              userId: session.user.id,
              status: 'OFFER_RECEIVED',
            },
          });
          updateAnalytics.offerRate = totalApplications > 0 ? offers / totalApplications : 0;
        }

        if (Object.keys(updateAnalytics).length > 0) {
          await prisma.userAnalytics.update({
            where: { userId: session.user.id },
            data: updateAnalytics,
          });
        }
      }
    }

    return NextResponse.json(updatedApplication);
  } catch (error) {
    console.error('Error updating application:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid application data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if application exists and belongs to user
    const application = await prisma.jobApplication.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    });

    if (!application) {
      return NextResponse.json({ error: 'Application not found' }, { status: 404 });
    }

    // Delete application
    await prisma.jobApplication.delete({
      where: { id: params.id },
    });

    // Update user analytics
    await prisma.userAnalytics.update({
      where: { userId: session.user.id },
      data: {
        totalApplications: { decrement: 1 },
      },
    });

    return NextResponse.json({ message: 'Application deleted successfully' });
  } catch (error) {
    console.error('Error deleting application:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
