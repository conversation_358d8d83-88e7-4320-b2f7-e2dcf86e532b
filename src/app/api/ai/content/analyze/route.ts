import { NextRequest, NextResponse } from 'next/server';
import { aiContentService } from '@/lib/ai/content-service';
import { ResumeContent } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { resumeContent, jobDescription } = body;

    // Validate required fields
    if (!resumeContent) {
      return NextResponse.json(
        { success: false, error: 'Resume content is required' },
        { status: 400 }
      );
    }

    // Validate resume content structure
    if (!resumeContent.personalInfo || !resumeContent.personalInfo.fullName) {
      return NextResponse.json(
        { success: false, error: 'Invalid resume content structure' },
        { status: 400 }
      );
    }

    // Analyze content using AI service
    const result = await aiContentService.analyzeContent(
      resumeContent as ResumeContent,
      jobDescription
    );

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      metadata: result.metadata
    });

  } catch (error) {
    console.error('Content analysis error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error during content analysis' 
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { 
      success: false, 
      error: 'Method not allowed. Use POST to analyze content.' 
    },
    { status: 405 }
  );
}
