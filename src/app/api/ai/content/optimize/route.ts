import { NextRequest, NextResponse } from 'next/server';
import { aiContentService } from '@/lib/ai/content-service';
import { ContentOptimizationRequest } from '@/types/ai-content';
import { ResumeContent } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      resumeContent, 
      jobDescription, 
      targetRole, 
      targetIndustry, 
      optimizationType, 
      userPreferences 
    } = body;

    // Validate required fields
    if (!resumeContent || !optimizationType) {
      return NextResponse.json(
        { success: false, error: 'Resume content and optimization type are required' },
        { status: 400 }
      );
    }

    // Validate optimization type
    const validOptimizationTypes = ['ats', 'keywords', 'impact', 'clarity', 'industry', 'comprehensive'];
    if (!validOptimizationTypes.includes(optimizationType)) {
      return NextResponse.json(
        { success: false, error: 'Invalid optimization type' },
        { status: 400 }
      );
    }

    // Validate resume content structure
    if (!resumeContent.personalInfo || !resumeContent.personalInfo.fullName) {
      return NextResponse.json(
        { success: false, error: 'Invalid resume content structure' },
        { status: 400 }
      );
    }

    // Validate user preferences if provided
    if (userPreferences) {
      const validTones = ['professional', 'casual', 'technical', 'creative'];
      const validLengths = ['concise', 'detailed', 'balanced'];
      const validFocus = ['achievements', 'skills', 'experience', 'leadership'];

      if (userPreferences.tone && !validTones.includes(userPreferences.tone)) {
        return NextResponse.json(
          { success: false, error: 'Invalid tone preference' },
          { status: 400 }
        );
      }

      if (userPreferences.length && !validLengths.includes(userPreferences.length)) {
        return NextResponse.json(
          { success: false, error: 'Invalid length preference' },
          { status: 400 }
        );
      }

      if (userPreferences.focus && !validFocus.includes(userPreferences.focus)) {
        return NextResponse.json(
          { success: false, error: 'Invalid focus preference' },
          { status: 400 }
        );
      }
    }

    // Create optimization request
    const optimizationRequest: ContentOptimizationRequest = {
      resumeContent: resumeContent as ResumeContent,
      jobDescription,
      targetRole,
      targetIndustry,
      optimizationType,
      userPreferences
    };

    // Optimize content using AI service
    const result = await aiContentService.optimizeContent(optimizationRequest);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      metadata: result.metadata
    });

  } catch (error) {
    console.error('Content optimization error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error during content optimization' 
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { 
      success: false, 
      error: 'Method not allowed. Use POST to optimize content.' 
    },
    { status: 405 }
  );
}
