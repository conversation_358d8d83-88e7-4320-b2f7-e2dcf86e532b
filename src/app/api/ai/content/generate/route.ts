import { NextRequest, NextResponse } from 'next/server';
import { aiContentService } from '@/lib/ai/content-service';
import { SmartContentGeneration } from '@/types/ai-content';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { section, context, options } = body;

    // Validate required fields
    if (!section || !context || !options) {
      return NextResponse.json(
        { success: false, error: 'Section, context, and options are required' },
        { status: 400 }
      );
    }

    // Validate section type
    const validSections = ['summary', 'experience', 'skills', 'projects'];
    if (!validSections.includes(section)) {
      return NextResponse.json(
        { success: false, error: 'Invalid section type' },
        { status: 400 }
      );
    }

    // Validate options
    const validLengths = ['short', 'medium', 'long'];
    const validStyles = ['bullet', 'paragraph', 'hybrid'];
    const validFocus = ['achievements', 'responsibilities', 'skills', 'impact'];
    const validTones = ['professional', 'dynamic', 'technical', 'leadership'];

    if (!validLengths.includes(options.length) ||
        !validStyles.includes(options.style) ||
        !validFocus.includes(options.focus) ||
        !validTones.includes(options.tone)) {
      return NextResponse.json(
        { success: false, error: 'Invalid options provided' },
        { status: 400 }
      );
    }

    // Create generation request
    const generationRequest: SmartContentGeneration = {
      section,
      context,
      options
    };

    // Generate content using AI service
    const result = await aiContentService.generateContent(generationRequest);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      metadata: result.metadata
    });

  } catch (error) {
    console.error('Content generation error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error during content generation' 
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { 
      success: false, 
      error: 'Method not allowed. Use POST to generate content.' 
    },
    { status: 405 }
  );
}
