/**
 * Onboarding API
 * Endpoint for managing user onboarding flows and progress
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { onboardingSystem, OnboardingFlowType } from '@/lib/onboarding/onboarding-system';
import { performanceMonitor } from '@/lib/analytics/performance-monitor';

export async function GET(request: NextRequest) {
  const endMeasurement = performanceMonitor.measureApiCall('/api/onboarding');

  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const flowType = searchParams.get('flowType') as OnboardingFlowType;

    switch (action) {
      case 'get-progress':
        if (!flowType) {
          return NextResponse.json(
            { error: 'Flow type is required' },
            { status: 400 }
          );
        }
        
        const progress = await onboardingSystem.getUserProgress(session.user.id, flowType);
        return NextResponse.json({ progress });

      case 'get-analytics':
        const flowId = searchParams.get('flowId');
        if (!flowId) {
          return NextResponse.json(
            { error: 'Flow ID is required' },
            { status: 400 }
          );
        }
        
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        const timeframe = startDate && endDate ? {
          start: new Date(startDate),
          end: new Date(endDate),
        } : undefined;
        
        const analytics = await onboardingSystem.getOnboardingAnalytics(flowId, timeframe);
        return NextResponse.json({ analytics });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Failed to process onboarding request:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  } finally {
    endMeasurement();
  }
}

export async function POST(request: NextRequest) {
  const endMeasurement = performanceMonitor.measureApiCall('/api/onboarding');

  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, ...data } = body;

    switch (action) {
      case 'initialize':
        const { flowType } = data;
        if (!flowType) {
          return NextResponse.json(
            { error: 'Flow type is required' },
            { status: 400 }
          );
        }
        
        const progress = await onboardingSystem.initializeOnboarding(session.user.id, flowType);
        return NextResponse.json({ progress });

      case 'complete-step':
        const { flowId, stepId } = data;
        if (!flowId || !stepId) {
          return NextResponse.json(
            { error: 'Flow ID and step ID are required' },
            { status: 400 }
          );
        }
        
        await onboardingSystem.completeStep(session.user.id, flowId, stepId);
        return NextResponse.json({ success: true });

      case 'skip-step':
        const { flowId: skipFlowId, stepId: skipStepId } = data;
        if (!skipFlowId || !skipStepId) {
          return NextResponse.json(
            { error: 'Flow ID and step ID are required' },
            { status: 400 }
          );
        }
        
        await onboardingSystem.skipStep(session.user.id, skipFlowId, skipStepId);
        return NextResponse.json({ success: true });

      case 'create-flow':
        // Admin only - would need additional authorization
        const flow = await onboardingSystem.createOnboardingFlow(data);
        return NextResponse.json({ flow });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Failed to process onboarding request:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  } finally {
    endMeasurement();
  }
}
