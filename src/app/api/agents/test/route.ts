import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/config';
import { agentTester } from '@/lib/ai/agent-tester';
import { aiConfigManager } from '@/lib/ai/config-manager';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Allow testing in development mode without authentication
    if (!aiConfigManager.isDevelopmentMode() && !session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const testType = searchParams.get('type') || 'all';
    const agentType = searchParams.get('agent');
    const operation = searchParams.get('operation');

    const userId = session?.user?.id || 'test-user';

    switch (testType) {
      case 'health':
        const healthStatus = await agentTester.testAIServiceHealth();
        return NextResponse.json({
          timestamp: new Date().toISOString(),
          type: 'health',
          ...healthStatus,
        });

      case 'agent':
        if (!agentType || !operation) {
          return NextResponse.json(
            { error: 'Agent type and operation are required for agent tests' },
            { status: 400 }
          );
        }

        // Get test input from request body or use default
        const testInput = {
          jobTitle: 'Software Engineer',
          jobDescription: 'Looking for a skilled software engineer...',
        };

        const agentResult = await agentTester.testAgent(
          agentType,
          operation,
          testInput,
          userId
        );

        return NextResponse.json({
          timestamp: new Date().toISOString(),
          type: 'agent',
          result: agentResult,
        });

      case 'all':
      default:
        const testResults = await agentTester.runAllTests(userId);
        return NextResponse.json({
          timestamp: new Date().toISOString(),
          type: 'comprehensive',
          ...testResults,
        });
    }
  } catch (error) {
    console.error('Agent test error:', error);
    return NextResponse.json(
      {
        error: 'Agent test failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Allow testing in development mode without authentication
    if (!aiConfigManager.isDevelopmentMode() && !session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { agentType, operation, input } = body;

    if (!agentType || !operation || !input) {
      return NextResponse.json(
        { error: 'Agent type, operation, and input are required' },
        { status: 400 }
      );
    }

    const userId = session?.user?.id || 'test-user';
    const result = await agentTester.testAgent(agentType, operation, input, userId);

    return NextResponse.json({
      timestamp: new Date().toISOString(),
      type: 'custom',
      result,
    });
  } catch (error) {
    console.error('Custom agent test error:', error);
    return NextResponse.json(
      {
        error: 'Custom agent test failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
