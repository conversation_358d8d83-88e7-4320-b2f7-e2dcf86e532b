import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/config';
import { agentRegistry } from '@/lib/ai/agent-registry';
import { ResumeAgent } from '@/agents/resume/resume-agent';
import { checkRateLimit } from '@/lib/auth/utils';
import { z } from 'zod';

// Register the resume agent if not already registered
if (!agentRegistry.isRegistered('RESUME')) {
  agentRegistry.register(new ResumeAgent());
}

// Request validation schema
const ResumeGenerationRequestSchema = z.object({
  jobTitle: z.string().min(1, 'Job title is required'),
  jobDescription: z.string().optional(),
  companyName: z.string().optional(),
  targetIndustry: z.string().optional(),
  experienceLevel: z.enum(['entry', 'mid', 'senior', 'executive']).default('mid'),
  resumeType: z.enum(['chronological', 'functional', 'combination']).default('chronological'),
  includeSkills: z.array(z.string()).optional(),
  customInstructions: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    
    const rateLimitPassed = checkRateLimit(
      `resume-generation:${session.user.id}`,
      5, // 5 requests
      3600000 // per hour (in ms)
    );

    if (!rateLimitPassed) {
      return NextResponse.json(
        {
          error: 'Rate limit exceeded. Please try again later.',
        },
        { status: 429 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = ResumeGenerationRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const input = validationResult.data;

    // Execute resume generation agent
    const agentResponse = await agentRegistry.execute('RESUME', {
      input,
      context: {
        userId: session.user.id,
        sessionId: request.headers.get('x-session-id') || undefined,
        metadata: {
          userAgent: request.headers.get('user-agent') || '',
          ip: clientIP,
          timestamp: new Date().toISOString(),
        },
      },
      options: {
        temperature: 0.7,
        maxTokens: 3000,
      },
    });

    if (!agentResponse.success) {
      return NextResponse.json(
        { 
          error: 'Resume generation failed',
          details: agentResponse.error 
        },
        { status: 500 }
      );
    }

    // Return successful response
    return NextResponse.json({
      success: true,
      data: agentResponse.data,
      metadata: {
        tokensUsed: agentResponse.metadata?.tokensUsed || 0,
        cost: agentResponse.metadata?.cost || 0,
        processingTime: agentResponse.metadata?.processingTime || 0,
        provider: agentResponse.metadata?.provider || 'unknown',
      },
    });

  } catch (error) {
    console.error('Resume generation API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get agent information and metrics
    const agent = agentRegistry.getAgent('RESUME');
    if (!agent) {
      return NextResponse.json(
        { error: 'Resume agent not available' },
        { status: 503 }
      );
    }

    const agentInfo = agent.getInfo();
    const metrics = await agent.getMetrics(session.user.id);
    const recentInteractions = await agent.getRecentInteractions(session.user.id, 5);

    return NextResponse.json({
      agent: agentInfo,
      metrics,
      recentInteractions,
    });

  } catch (error) {
    console.error('Resume agent info API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
