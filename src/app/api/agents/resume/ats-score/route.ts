import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/config';
import { agentRegistry } from '@/lib/ai/agent-registry';
import { ResumeAgent } from '@/agents/resume/resume-agent';
import { checkRateLimit } from '@/lib/auth/utils';
import { z } from 'zod';

// Register the resume agent if not already registered
if (!agentRegistry.isRegistered('RESUME')) {
  agentRegistry.register(new ResumeAgent());
}

// Request validation schema
const ATSScoringRequestSchema = z.object({
  resumeContent: z.string().min(1, 'Resume content is required'),
  jobDescription: z.string().min(1, 'Job description is required'),
});

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    
    const rateLimitPassed = checkRateLimit(
      `ats-scoring:${session.user.id}`,
      10, // 10 requests
      3600000 // per hour (in ms)
    );

    if (!rateLimitPassed) {
      return NextResponse.json(
        {
          error: 'Rate limit exceeded. Please try again later.',
        },
        { status: 429 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = ATSScoringRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const input = validationResult.data;

    // Execute ATS scoring agent
    const agentResponse = await agentRegistry.execute('RESUME', {
      input,
      context: {
        userId: session.user.id,
        sessionId: request.headers.get('x-session-id') || undefined,
        metadata: {
          userAgent: request.headers.get('user-agent') || '',
          ip: clientIP,
          timestamp: new Date().toISOString(),
          operation: 'ats-scoring',
        },
      },
      options: {
        temperature: 0.3,
        maxTokens: 1500,
      },
    });

    if (!agentResponse.success) {
      return NextResponse.json(
        { 
          error: 'ATS scoring failed',
          details: agentResponse.error 
        },
        { status: 500 }
      );
    }

    // Return successful response
    return NextResponse.json({
      success: true,
      data: agentResponse.data,
      metadata: {
        tokensUsed: agentResponse.metadata?.tokensUsed || 0,
        cost: agentResponse.metadata?.cost || 0,
        processingTime: agentResponse.metadata?.processingTime || 0,
        provider: agentResponse.metadata?.provider || 'unknown',
      },
    });

  } catch (error) {
    console.error('ATS scoring API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
