import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/config';
import { agentRegistry } from '@/lib/ai/agent-registry';
import { aiConfigManager } from '@/lib/ai/config-manager';
import { aiService } from '@/lib/ai/service';
import { ResumeAgent } from '@/agents/resume/resume-agent';
import { ResearchAgent } from '@/agents/research/research-agent';
import { LinkedInAgent } from '@/agents/linkedin/linkedin-agent';

// Initialize agents
if (!agentRegistry.isRegistered('RESUME')) {
  agentRegistry.register(new ResumeAgent());
}

if (!agentRegistry.isRegistered('RESEARCH')) {
  agentRegistry.register(new ResearchAgent());
}

if (!agentRegistry.isRegistered('LINKEDIN_OPTIMIZER')) {
  agentRegistry.register(new LinkedInAgent());
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication for detailed metrics
    const session = await getServerSession(authOptions);
    const isAuthenticated = !!session?.user?.id;

    // Basic health check (no auth required)
    const aiHealthStatus = await aiService.getHealthStatus();
    const agentHealthCheck = await agentRegistry.healthCheck();
    const registeredAgents = agentRegistry.getAllAgents();

    // Determine overall status
    let overallStatus = 'healthy';
    if (aiHealthStatus.status === 'unhealthy') {
      overallStatus = 'unhealthy';
    } else if (aiHealthStatus.status === 'degraded' || !aiHealthStatus.providers.overall) {
      overallStatus = 'degraded';
    }

    const healthData: any = {
      timestamp: new Date().toISOString(),
      status: overallStatus,
      services: {
        ai: aiHealthStatus,
        agents: {
          registered: registeredAgents.length,
          health: agentHealthCheck,
          list: registeredAgents,
        },
      },
      warnings: [],
    };

    // Add warnings based on status
    if (aiHealthStatus.status !== 'healthy') {
      healthData.warnings.push('AI configuration issues detected');
    }
    if (!aiHealthStatus.providers.overall && !aiHealthStatus.developmentMode) {
      healthData.warnings.push('No AI providers available');
    }
    if (aiHealthStatus.developmentMode) {
      healthData.warnings.push('Running in development mode with mock responses');
    }

    // Add detailed metrics if authenticated
    if (isAuthenticated) {
      try {
        const allMetrics = await agentRegistry.getAllMetrics(session.user.id);
        healthData.metrics = allMetrics;
      } catch (error) {
        console.warn('Failed to get agent metrics:', error);
        healthData.metrics = { error: 'Failed to retrieve metrics' };
      }
    }

    // Status code based on overall health
    const statusCode = overallStatus === 'healthy' ? 200 : 503;
    return NextResponse.json(healthData, { status: statusCode });

  } catch (error) {
    console.error('Agent health check error:', error);
    return NextResponse.json(
      {
        timestamp: new Date().toISOString(),
        status: 'error',
        error: 'Health check failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body for specific health check operations
    const body = await request.json();
    const { operation, agentType } = body;

    switch (operation) {
      case 'test-agent':
        if (!agentType) {
          return NextResponse.json(
            { error: 'Agent type required for test operation' },
            { status: 400 }
          );
        }

        const agent = agentRegistry.getAgent(agentType);
        if (!agent) {
          return NextResponse.json(
            { error: `Agent ${agentType} not found` },
            { status: 404 }
          );
        }

        // Perform a simple test operation
        const testInput = agentType === 'RESUME' 
          ? { resumeContent: 'Test resume content', jobDescription: 'Test job description' }
          : { jobTitle: 'Software Engineer', analysisType: 'comprehensive' };

        const testResponse = await agentRegistry.execute(agentType, {
          input: testInput,
          context: {
            userId: session.user.id,
            metadata: { test: true },
          },
          options: {
            maxTokens: 100,
            temperature: 0.1,
          },
        });

        return NextResponse.json({
          agentType,
          testResult: {
            success: testResponse.success,
            error: testResponse.error,
            processingTime: testResponse.metadata?.processingTime || 0,
          },
        });

      case 'get-metrics':
        const metrics = await agentRegistry.getAllMetrics(session.user.id);
        return NextResponse.json({ metrics });

      case 'reset-cache':
        // This would reset any caching if implemented
        return NextResponse.json({ 
          message: 'Cache reset requested',
          timestamp: new Date().toISOString(),
        });

      default:
        return NextResponse.json(
          { error: 'Unknown operation' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Agent health operation error:', error);
    return NextResponse.json(
      { error: 'Operation failed' },
      { status: 500 }
    );
  }
}
