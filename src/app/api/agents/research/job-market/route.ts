import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/config';
import { agentRegistry } from '@/lib/ai/agent-registry';
import { ResearchAgent } from '@/agents/research/research-agent';
import { checkRateLimit } from '@/lib/auth/utils';
import { z } from 'zod';

// Register the research agent if not already registered
if (!agentRegistry.isRegistered('RESEARCH')) {
  agentRegistry.register(new ResearchAgent());
}

// Request validation schema
const JobMarketAnalysisRequestSchema = z.object({
  jobTitle: z.string().min(1, 'Job title is required'),
  location: z.string().optional(),
  industry: z.string().optional(),
  experienceLevel: z.enum(['entry', 'mid', 'senior', 'executive']).optional(),
  analysisType: z.enum(['salary', 'skills', 'trends', 'comprehensive']).default('comprehensive'),
});

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    
    const rateLimitPassed = checkRateLimit(
      `job-market-analysis:${session.user.id}`,
      5, // 5 requests
      3600000 // per hour (in ms)
    );

    if (!rateLimitPassed) {
      return NextResponse.json(
        {
          error: 'Rate limit exceeded. Please try again later.',
        },
        { status: 429 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = JobMarketAnalysisRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const input = validationResult.data;

    // Execute job market analysis agent
    const agentResponse = await agentRegistry.execute('RESEARCH', {
      input,
      context: {
        userId: session.user.id,
        sessionId: request.headers.get('x-session-id') || undefined,
        metadata: {
          userAgent: request.headers.get('user-agent') || '',
          ip: clientIP,
          timestamp: new Date().toISOString(),
          operation: 'job-market-analysis',
        },
      },
      options: {
        temperature: 0.3,
        maxTokens: 2500,
      },
    });

    if (!agentResponse.success) {
      return NextResponse.json(
        { 
          error: 'Job market analysis failed',
          details: agentResponse.error 
        },
        { status: 500 }
      );
    }

    // Return successful response
    return NextResponse.json({
      success: true,
      data: agentResponse.data,
      metadata: {
        tokensUsed: agentResponse.metadata?.tokensUsed || 0,
        cost: agentResponse.metadata?.cost || 0,
        processingTime: agentResponse.metadata?.processingTime || 0,
        provider: agentResponse.metadata?.provider || 'unknown',
      },
    });

  } catch (error) {
    console.error('Job market analysis API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get agent information and metrics
    const agent = agentRegistry.getAgent('RESEARCH');
    if (!agent) {
      return NextResponse.json(
        { error: 'Research agent not available' },
        { status: 503 }
      );
    }

    const agentInfo = agent.getInfo();
    const metrics = await agent.getMetrics(session.user.id);
    const recentInteractions = await agent.getRecentInteractions(session.user.id, 5);

    return NextResponse.json({
      agent: agentInfo,
      metrics,
      recentInteractions,
    });

  } catch (error) {
    console.error('Research agent info API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
