import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/config';
import { agentRegistry } from '@/lib/ai/agent-registry';
import { ApplicationAgent } from '@/agents/application/application-agent';
import { checkRateLimit } from '@/lib/auth/utils';
import { z } from 'zod';

// Register the application agent if not already registered
if (!agentRegistry.isRegistered('APPLICATION')) {
  agentRegistry.register(new ApplicationAgent());
}

// Request validation schema
const JobApplicationRequestSchema = z.object({
  jobId: z.string().min(1, 'Job ID is required'),
  resumeId: z.string().min(1, 'Resume ID is required'),
  generateCoverLetter: z.boolean().default(true),
  customMessage: z.string().optional(),
  applicationNotes: z.string().optional(),
  followUpDate: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Get client IP for rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check rate limiting
    const rateLimitResult = await checkRateLimit(
      `application-submit:${session.user.id}`,
      10, // 10 requests
      3600 // per hour
    );

    if (!rateLimitResult.success) {
      return NextResponse.json(
        { 
          error: 'Rate limit exceeded',
          retryAfter: rateLimitResult.retryAfter 
        },
        { status: 429 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = JobApplicationRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const input = validationResult.data;

    // Execute job application agent
    const agentResponse = await agentRegistry.execute('APPLICATION', {
      input,
      context: {
        userId: session.user.id,
        sessionId: request.headers.get('x-session-id') || undefined,
        metadata: {
          userAgent: request.headers.get('user-agent') || '',
          ip: clientIP,
          timestamp: new Date().toISOString(),
          operation: 'job-application',
        },
      },
      options: {
        temperature: 0.7,
        maxTokens: 2000,
      },
    });

    if (!agentResponse.success) {
      return NextResponse.json(
        { 
          error: 'Job application failed',
          details: agentResponse.error 
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: agentResponse.data,
      metadata: agentResponse.metadata,
    });

  } catch (error) {
    console.error('Job application API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve application status
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const applicationId = searchParams.get('applicationId');

    if (!applicationId) {
      return NextResponse.json(
        { error: 'Application ID is required' },
        { status: 400 }
      );
    }

    // Fetch application from database
    const { prisma } = await import('@/lib/db');
    const application = await prisma.jobApplication.findFirst({
      where: {
        id: applicationId,
        userId: session.user.id,
      },
      include: {
        job: true,
        resume: true,
        coverLetter: true,
      },
    });

    if (!application) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: application.id,
        status: application.status,
        appliedDate: application.appliedDate,
        lastUpdated: application.lastUpdated,
        notes: application.notes,
        followUpDate: application.followUpDate,
        job: {
          id: application.job.id,
          title: application.job.title,
          company: application.job.company,
          location: application.job.location,
        },
        resume: {
          id: application.resume.id,
          title: application.resume.title,
        },
        coverLetter: application.coverLetter ? {
          id: application.coverLetter.id,
          title: application.coverLetter.title,
        } : null,
      },
    });

  } catch (error) {
    console.error('Get application API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
