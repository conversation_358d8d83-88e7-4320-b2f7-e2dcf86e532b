import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/config';
import { agentRegistry } from '@/lib/ai/agent-registry';
import { ApplicationAgent } from '@/agents/application/application-agent';
import { checkRateLimit } from '@/lib/auth/utils';
import { z } from 'zod';

// Register the application agent if not already registered
if (!agentRegistry.isRegistered('APPLICATION')) {
  agentRegistry.register(new ApplicationAgent());
}

// Request validation schema
const CoverLetterRequestSchema = z.object({
  jobTitle: z.string().min(1, 'Job title is required'),
  companyName: z.string().min(1, 'Company name is required'),
  jobDescription: z.string().min(1, 'Job description is required'),
  resumeContent: z.string().min(1, 'Resume content is required'),
  tone: z.enum(['professional', 'enthusiastic', 'creative', 'technical']).default('professional'),
  length: z.enum(['short', 'medium', 'long']).default('medium'),
  customInstructions: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Get client IP for rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check rate limiting
    const rateLimitResult = await checkRateLimit(
      `cover-letter:${session.user.id}`,
      20, // 20 requests
      3600 // per hour
    );

    if (!rateLimitResult.success) {
      return NextResponse.json(
        { 
          error: 'Rate limit exceeded',
          retryAfter: rateLimitResult.retryAfter 
        },
        { status: 429 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = CoverLetterRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const input = validationResult.data;

    // Execute cover letter generation agent
    const agentResponse = await agentRegistry.execute('APPLICATION', {
      input,
      context: {
        userId: session.user.id,
        sessionId: request.headers.get('x-session-id') || undefined,
        metadata: {
          userAgent: request.headers.get('user-agent') || '',
          ip: clientIP,
          timestamp: new Date().toISOString(),
          operation: 'cover-letter-generation',
        },
      },
      options: {
        temperature: 0.7,
        maxTokens: 2000,
      },
    });

    if (!agentResponse.success) {
      return NextResponse.json(
        { 
          error: 'Cover letter generation failed',
          details: agentResponse.error 
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: agentResponse.data,
      metadata: agentResponse.metadata,
    });

  } catch (error) {
    console.error('Cover letter generation API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve saved cover letters
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Fetch cover letters from database
    const { prisma } = await import('@/lib/db');
    
    const whereClause: any = {
      userId: session.user.id,
    };

    if (jobId) {
      whereClause.jobId = jobId;
    }

    const [coverLetters, total] = await Promise.all([
      prisma.coverLetter.findMany({
        where: whereClause,
        include: {
          job: {
            select: {
              id: true,
              title: true,
              company: true,
              location: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: offset,
        take: limit,
      }),
      prisma.coverLetter.count({
        where: whereClause,
      }),
    ]);

    return NextResponse.json({
      success: true,
      data: {
        coverLetters: coverLetters.map(cl => ({
          id: cl.id,
          title: cl.title,
          content: cl.content,
          createdAt: cl.createdAt,
          updatedAt: cl.updatedAt,
          job: cl.job,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    });

  } catch (error) {
    console.error('Get cover letters API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
