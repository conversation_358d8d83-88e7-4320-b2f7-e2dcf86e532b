import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/config';
import { agentRegistry } from '@/lib/ai/agent-registry';
import { ApplicationAgent } from '@/agents/application/application-agent';
import { checkRateLimit } from '@/lib/auth/utils';
import { z } from 'zod';

// Register the application agent if not already registered
if (!agentRegistry.isRegistered('APPLICATION')) {
  agentRegistry.register(new ApplicationAgent());
}

// Request validation schema
const ApplicationOptimizationRequestSchema = z.object({
  jobDescription: z.string().min(1, 'Job description is required'),
  resumeContent: z.string().min(1, 'Resume content is required'),
  applicationHistory: z.array(z.object({
    jobTitle: z.string(),
    company: z.string(),
    status: z.string(),
    appliedDate: z.string(),
  })).optional(),
  targetRole: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Get client IP for rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check rate limiting
    const rateLimitResult = await checkRateLimit(
      `application-optimize:${session.user.id}`,
      15, // 15 requests
      3600 // per hour
    );

    if (!rateLimitResult.success) {
      return NextResponse.json(
        { 
          error: 'Rate limit exceeded',
          retryAfter: rateLimitResult.retryAfter 
        },
        { status: 429 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = ApplicationOptimizationRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const input = validationResult.data;

    // If no application history provided, fetch from database
    if (!input.applicationHistory) {
      const { prisma } = await import('@/lib/db');
      const recentApplications = await prisma.jobApplication.findMany({
        where: {
          userId: session.user.id,
        },
        include: {
          job: {
            select: {
              title: true,
              company: true,
            },
          },
        },
        orderBy: {
          appliedDate: 'desc',
        },
        take: 10,
      });

      input.applicationHistory = recentApplications.map(app => ({
        jobTitle: app.job.title,
        company: app.job.company,
        status: app.status,
        appliedDate: app.appliedDate.toISOString(),
      }));
    }

    // Execute application optimization agent
    const agentResponse = await agentRegistry.execute('APPLICATION', {
      input,
      context: {
        userId: session.user.id,
        sessionId: request.headers.get('x-session-id') || undefined,
        metadata: {
          userAgent: request.headers.get('user-agent') || '',
          ip: clientIP,
          timestamp: new Date().toISOString(),
          operation: 'application-optimization',
        },
      },
      options: {
        temperature: 0.6,
        maxTokens: 2500,
      },
    });

    if (!agentResponse.success) {
      return NextResponse.json(
        { 
          error: 'Application optimization failed',
          details: agentResponse.error 
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: agentResponse.data,
      metadata: agentResponse.metadata,
    });

  } catch (error) {
    console.error('Application optimization API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve optimization history
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Fetch application analytics from database
    const { prisma } = await import('@/lib/db');
    
    const [applications, userAnalytics] = await Promise.all([
      prisma.jobApplication.findMany({
        where: {
          userId: session.user.id,
        },
        include: {
          job: {
            select: {
              id: true,
              title: true,
              company: true,
              location: true,
              skills: true,
              atsKeywords: true,
            },
          },
          resume: {
            select: {
              id: true,
              title: true,
            },
          },
        },
        orderBy: {
          appliedDate: 'desc',
        },
        skip: offset,
        take: limit,
      }),
      prisma.userAnalytics.findUnique({
        where: {
          userId: session.user.id,
        },
      }),
    ]);

    // Calculate success metrics
    const totalApplications = applications.length;
    const statusCounts = applications.reduce((acc, app) => {
      acc[app.status] = (acc[app.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const responseRate = userAnalytics?.responseRate || 0;
    const interviewRate = userAnalytics?.interviewRate || 0;
    const offerRate = userAnalytics?.offerRate || 0;

    return NextResponse.json({
      success: true,
      data: {
        applications: applications.map(app => ({
          id: app.id,
          status: app.status,
          appliedDate: app.appliedDate,
          lastUpdated: app.lastUpdated,
          notes: app.notes,
          job: app.job,
          resume: app.resume,
        })),
        analytics: {
          totalApplications: userAnalytics?.totalApplications || totalApplications,
          responseRate,
          interviewRate,
          offerRate,
          statusBreakdown: statusCounts,
        },
        pagination: {
          page,
          limit,
          total: totalApplications,
          totalPages: Math.ceil(totalApplications / limit),
        },
      },
    });

  } catch (error) {
    console.error('Get optimization history API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
