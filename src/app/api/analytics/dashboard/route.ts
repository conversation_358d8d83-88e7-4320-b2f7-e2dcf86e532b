/**
 * Dashboard Analytics API
 * Endpoint for retrieving comprehensive dashboard insights and metrics
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { dashboardAnalytics } from '@/lib/analytics/dashboard-analytics';
import { performanceMonitor } from '@/lib/analytics/performance-monitor';

export async function GET(request: NextRequest) {
  const endMeasurement = performanceMonitor.measureApiCall('/api/analytics/dashboard');

  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'insights';

    switch (type) {
      case 'insights':
        const insights = await dashboardAnalytics.getDashboardInsights(session.user.id);
        return NextResponse.json(insights);

      case 'resume-performance':
        const resumeId = searchParams.get('resumeId');
        if (!resumeId) {
          return NextResponse.json(
            { error: 'Resume ID is required for performance metrics' },
            { status: 400 }
          );
        }
        
        const performance = await dashboardAnalytics.getResumePerformanceMetrics(resumeId);
        return NextResponse.json(performance);

      case 'compare':
        const resumeIds = searchParams.get('resumeIds');
        if (!resumeIds) {
          return NextResponse.json(
            { error: 'Resume IDs are required for comparison' },
            { status: 400 }
          );
        }
        
        const comparison = await dashboardAnalytics.compareResumes(
          session.user.id,
          resumeIds.split(',')
        );
        return NextResponse.json(comparison);

      default:
        return NextResponse.json(
          { error: 'Invalid analytics type' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Failed to get dashboard analytics:', error);
    return NextResponse.json(
      { error: 'Failed to get analytics data' },
      { status: 500 }
    );
  } finally {
    endMeasurement();
  }
}

export async function POST(request: NextRequest) {
  const endMeasurement = performanceMonitor.measureApiCall('/api/analytics/dashboard');

  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, ...data } = body;

    switch (action) {
      case 'set-goal':
        const goal = await dashboardAnalytics.setUserGoal({
          userId: session.user.id,
          ...data,
        });
        return NextResponse.json(goal);

      case 'update-goal-progress':
        await dashboardAnalytics.updateGoalProgress(data.goalId, data.current);
        return NextResponse.json({ success: true });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Failed to process dashboard analytics request:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  } finally {
    endMeasurement();
  }
}
