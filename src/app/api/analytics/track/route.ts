/**
 * Analytics Event Tracking API
 * Endpoint for tracking user behavior and analytics events
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { analyticsService, AnalyticsEventType, AnalyticsEventProperties } from '@/lib/analytics/analytics-service';
import { performanceMonitor } from '@/lib/analytics/performance-monitor';

interface TrackEventRequest {
  eventType: AnalyticsEventType;
  properties?: AnalyticsEventProperties;
  sessionId?: string;
  page?: string;
  referrer?: string;
}

export async function POST(request: NextRequest) {
  const endMeasurement = performanceMonitor.measureApiCall('/api/analytics/track');

  try {
    const session = await getServerSession(authOptions);
    const body = await request.json() as TrackEventRequest;

    // Validate request
    if (!body.eventType) {
      return NextResponse.json(
        { error: 'Event type is required' },
        { status: 400 }
      );
    }

    // Get user agent and IP for tracking
    const userAgent = request.headers.get('user-agent') || undefined;
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     undefined;

    // Track the event
    await analyticsService.trackEvent({
      userId: session?.user?.id,
      sessionId: body.sessionId,
      eventType: body.eventType,
      properties: body.properties || {},
      timestamp: new Date(),
      userAgent,
      ipAddress,
      referrer: body.referrer,
      page: body.page,
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to track analytics event:', error);
    return NextResponse.json(
      { error: 'Failed to track event' },
      { status: 500 }
    );
  } finally {
    endMeasurement();
  }
}

export async function GET(request: NextRequest) {
  const endMeasurement = performanceMonitor.measureApiCall('/api/analytics/track');

  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: 'Start date and end date are required' },
        { status: 400 }
      );
    }

    const timeframe = {
      start: new Date(startDate),
      end: new Date(endDate),
    };

    // Get user analytics metrics
    const metrics = await analyticsService.getUserMetrics(timeframe);

    return NextResponse.json(metrics);
  } catch (error) {
    console.error('Failed to get analytics metrics:', error);
    return NextResponse.json(
      { error: 'Failed to get metrics' },
      { status: 500 }
    );
  } finally {
    endMeasurement();
  }
}
