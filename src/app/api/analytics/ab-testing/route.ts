/**
 * A/B Testing API
 * Endpoint for managing experiments and tracking conversions
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { abTestingEngine, ExperimentStatus, ExperimentType } from '@/lib/analytics/ab-testing';
import { performanceMonitor } from '@/lib/analytics/performance-monitor';

export async function GET(request: NextRequest) {
  const endMeasurement = performanceMonitor.measureApiCall('/api/analytics/ab-testing');

  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const experimentId = searchParams.get('experimentId');

    switch (action) {
      case 'get-variant':
        if (!experimentId) {
          return NextResponse.json(
            { error: 'Experiment ID is required' },
            { status: 400 }
          );
        }
        
        const variant = await abTestingEngine.getUserVariant(session.user.id, experimentId);
        return NextResponse.json({ variant });

      case 'get-experiments':
        const status = searchParams.get('status') as ExperimentStatus | undefined;
        const experiments = await abTestingEngine.getExperiments(status);
        return NextResponse.json({ experiments });

      case 'get-results':
        if (!experimentId) {
          return NextResponse.json(
            { error: 'Experiment ID is required' },
            { status: 400 }
          );
        }
        
        const results = await abTestingEngine.getExperimentResults(experimentId);
        return NextResponse.json({ results });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Failed to process A/B testing request:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  } finally {
    endMeasurement();
  }
}

export async function POST(request: NextRequest) {
  const endMeasurement = performanceMonitor.measureApiCall('/api/analytics/ab-testing');

  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, ...data } = body;

    switch (action) {
      case 'create-experiment':
        const experiment = await abTestingEngine.createExperiment({
          ...data,
          createdBy: session.user.id,
        });
        return NextResponse.json({ experiment });

      case 'track-conversion':
        const { experimentId, metric, value } = data;
        if (!experimentId || !metric) {
          return NextResponse.json(
            { error: 'Experiment ID and metric are required' },
            { status: 400 }
          );
        }
        
        await abTestingEngine.trackConversion(
          session.user.id,
          experimentId,
          metric,
          value
        );
        return NextResponse.json({ success: true });

      case 'update-status':
        const { experimentId: expId, status } = data;
        if (!expId || !status) {
          return NextResponse.json(
            { error: 'Experiment ID and status are required' },
            { status: 400 }
          );
        }
        
        await abTestingEngine.updateExperimentStatus(expId, status);
        return NextResponse.json({ success: true });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Failed to process A/B testing request:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  } finally {
    endMeasurement();
  }
}
