import { NextResponse } from 'next/server';
import { runHealthCheck, getSystemStats } from '@/lib/db/migrations';

export async function GET() {
  try {
    const health = await runHealthCheck();
    const stats = await getSystemStats();

    const status = health.database && health.tables ? 'healthy' : 'unhealthy';

    return NextResponse.json({
      status,
      timestamp: new Date().toISOString(),
      database: health,
      stats,
      version: process.env.npm_package_version || '0.1.0',
    });
  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json(
      {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
        database: {
          database: false,
          tables: false,
          seed: false,
        },
        stats: null,
      },
      { status: 500 }
    );
  }
}
