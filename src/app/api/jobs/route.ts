import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db';
import { z } from 'zod';

// Job search query schema
const jobSearchSchema = z.object({
  q: z.string().optional(), // search query
  location: z.string().optional(),
  type: z.enum(['FULL_TIME', 'PART_TIME', 'CONTRACT', 'FREELANCE', 'INTERNSHIP']).optional(),
  remote: z.boolean().optional(),
  salaryMin: z.number().optional(),
  salaryMax: z.number().optional(),
  skills: z.array(z.string()).optional(),
  source: z.string().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(50).default(20),
  sortBy: z.enum(['postedDate', 'salaryMax', 'relevance']).default('postedDate'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Job creation schema
const createJobSchema = z.object({
  title: z.string().min(1),
  company: z.string().min(1),
  location: z.string().min(1),
  type: z.enum(['FULL_TIME', 'PART_TIME', 'CONTRACT', 'FREELANCE', 'INTERNSHIP']),
  remote: z.boolean().default(false),
  salaryMin: z.number().optional(),
  salaryMax: z.number().optional(),
  salaryCurrency: z.string().default('USD'),
  salaryPeriod: z.enum(['HOURLY', 'MONTHLY', 'YEARLY']).optional(),
  description: z.string().min(1),
  requirements: z.array(z.string()),
  skills: z.array(z.string()),
  postedDate: z.string().transform((str) => new Date(str)),
  applicationDeadline: z.string().transform((str) => new Date(str)).optional(),
  source: z.string().min(1),
  url: z.string().url(),
  atsKeywords: z.array(z.string()).default([]),
});

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    // Parse and validate query parameters
    const {
      q,
      location,
      type,
      remote,
      salaryMin,
      salaryMax,
      skills,
      source,
      page,
      limit,
      sortBy,
      sortOrder,
    } = jobSearchSchema.parse({
      ...queryParams,
      remote: queryParams.remote === 'true',
      salaryMin: queryParams.salaryMin ? parseInt(queryParams.salaryMin) : undefined,
      salaryMax: queryParams.salaryMax ? parseInt(queryParams.salaryMax) : undefined,
      skills: queryParams.skills ? queryParams.skills.split(',') : undefined,
      page: queryParams.page ? parseInt(queryParams.page) : 1,
      limit: queryParams.limit ? parseInt(queryParams.limit) : 20,
    });

    const skip = (page - 1) * limit;

    // Build where clause for filtering
    const where: any = {};

    if (q) {
      where.OR = [
        { title: { contains: q, mode: 'insensitive' } },
        { company: { contains: q, mode: 'insensitive' } },
        { description: { contains: q, mode: 'insensitive' } },
        { skills: { hasSome: [q] } },
      ];
    }

    if (location) {
      where.location = { contains: location, mode: 'insensitive' };
    }

    if (type) {
      where.type = type;
    }

    if (remote !== undefined) {
      where.remote = remote;
    }

    if (salaryMin || salaryMax) {
      where.AND = where.AND || [];
      if (salaryMin) {
        where.AND.push({ salaryMax: { gte: salaryMin } });
      }
      if (salaryMax) {
        where.AND.push({ salaryMin: { lte: salaryMax } });
      }
    }

    if (skills && skills.length > 0) {
      where.skills = { hasSome: skills };
    }

    if (source) {
      where.source = source;
    }

    // Build orderBy clause
    let orderBy: any = {};
    if (sortBy === 'relevance' && q) {
      // For relevance, we'll use a simple approach based on title match
      orderBy = { title: sortOrder };
    } else {
      orderBy[sortBy] = sortOrder;
    }

    // Execute query
    const [jobs, totalCount] = await Promise.all([
      prisma.job.findMany({
        where,
        orderBy,
        skip,
        take: limit,
        include: {
          applications: {
            where: { userId: session.user.id },
            select: { id: true, status: true, appliedDate: true },
          },
          savedJobs: {
            where: { userId: session.user.id },
            select: { id: true, createdAt: true },
          },
          _count: {
            select: { applications: true },
          },
        },
      }),
      prisma.job.count({ where }),
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return NextResponse.json({
      jobs: jobs.map(job => ({
        ...job,
        isApplied: job.applications.length > 0,
        isSaved: job.savedJobs.length > 0,
        applicationStatus: job.applications[0]?.status || null,
        appliedDate: job.applications[0]?.appliedDate || null,
        totalApplications: job._count.applications,
      })),
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      },
      filters: {
        q,
        location,
        type,
        remote,
        salaryMin,
        salaryMax,
        skills,
        source,
        sortBy,
        sortOrder,
      },
    });
  } catch (error) {
    console.error('Error fetching jobs:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // For now, only allow admin users to create jobs
    // In a real app, this would be integrated with job board APIs
    if (session.user.email !== '<EMAIL>') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createJobSchema.parse(body);

    const job = await prisma.job.create({
      data: validatedData,
      include: {
        _count: {
          select: { applications: true },
        },
      },
    });

    return NextResponse.json(job, { status: 201 });
  } catch (error) {
    console.error('Error creating job:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid job data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
