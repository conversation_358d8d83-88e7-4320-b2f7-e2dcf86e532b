import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db';
import { z } from 'zod';
import { checkRateLimit } from '@/lib/auth/utils';

interface RouteParams {
  params: { id: string };
}

const applyJobSchema = z.object({
  resumeId: z.string().min(1),
  coverLetterId: z.string().optional(),
  notes: z.string().optional(),
});

export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Rate limiting - max 10 applications per hour
    const rateLimitResult = await checkRateLimit(
      `job_apply_${session.user.id}`,
      10,
      3600
    );

    if (!rateLimitResult.success) {
      return NextResponse.json(
        { 
          error: 'Rate limit exceeded',
          message: 'You can only apply to 10 jobs per hour. Please try again later.',
          retryAfter: rateLimitResult.retryAfter 
        },
        { status: 429 }
      );
    }

    const body = await request.json();
    const { resumeId, coverLetterId, notes } = applyJobSchema.parse(body);

    // Check if job exists
    const job = await prisma.job.findUnique({
      where: { id: params.id },
    });

    if (!job) {
      return NextResponse.json({ error: 'Job not found' }, { status: 404 });
    }

    // Check if user owns the resume
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: session.user.id,
      },
    });

    if (!resume) {
      return NextResponse.json({ error: 'Resume not found' }, { status: 404 });
    }

    // Check if cover letter exists and belongs to user (if provided)
    if (coverLetterId) {
      const coverLetter = await prisma.coverLetter.findFirst({
        where: {
          id: coverLetterId,
          userId: session.user.id,
        },
      });

      if (!coverLetter) {
        return NextResponse.json({ error: 'Cover letter not found' }, { status: 404 });
      }
    }

    // Check if user has already applied to this job
    const existingApplication = await prisma.jobApplication.findUnique({
      where: {
        userId_jobId: {
          userId: session.user.id,
          jobId: params.id,
        },
      },
    });

    if (existingApplication) {
      return NextResponse.json(
        { error: 'You have already applied to this job' },
        { status: 409 }
      );
    }

    // Create job application
    const application = await prisma.jobApplication.create({
      data: {
        userId: session.user.id,
        jobId: params.id,
        resumeId,
        coverLetterId,
        status: 'APPLIED',
        notes,
      },
      include: {
        job: {
          select: {
            id: true,
            title: true,
            company: true,
          },
        },
        resume: {
          select: {
            id: true,
            title: true,
          },
        },
        coverLetter: {
          select: {
            id: true,
            title: true,
          },
        },
      },
    });

    // Update user analytics
    await prisma.userAnalytics.upsert({
      where: { userId: session.user.id },
      update: {
        totalApplications: { increment: 1 },
      },
      create: {
        userId: session.user.id,
        totalApplications: 1,
      },
    });

    // TODO: Integrate with actual job board APIs to submit application
    // For now, we just track it in our database

    return NextResponse.json({
      message: 'Application submitted successfully',
      application,
    }, { status: 201 });
  } catch (error) {
    console.error('Error applying to job:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid application data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
