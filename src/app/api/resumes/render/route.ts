import { NextRequest, NextResponse } from 'next/server';
import { getTemplateComponent } from '@/lib/templates/template-registry';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const templateId = searchParams.get('templateId');

  if (!templateId) {
    return NextResponse.json(
      { error: 'Template ID is required' },
      { status: 400 }
    );
  }

  // Generate HTML page that will render the template
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Resume PDF Render</title>
  <style>
    /* Reset and base styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.5;
      color: #000000;
      background: #ffffff;
      width: 8.5in;
      min-height: 11in;
      margin: 0 auto;
      padding: 0;
    }
    
    /* Print-specific styles */
    @media print {
      body {
        margin: 0;
        padding: 0;
        background: white !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
      }
      
      * {
        box-shadow: none !important;
        text-shadow: none !important;
      }
      
      .no-print {
        display: none !important;
      }
      
      .page-break {
        page-break-before: always;
      }
      
      .avoid-break {
        page-break-inside: avoid;
      }
    }
    
    /* Template container */
    .template-container {
      width: 100%;
      min-height: 100vh;
      background: white;
      position: relative;
    }
    
    /* Loading state */
    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100vh;
      font-size: 18px;
      color: #666;
    }
    
    /* Error state */
    .error {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100vh;
      font-size: 18px;
      color: #dc2626;
      text-align: center;
      padding: 20px;
    }
    
    /* Common resume styles */
    .resume-template {
      width: 100%;
      background: white;
      color: black;
      font-size: 14px;
      line-height: 1.4;
    }
    
    .resume-template h1 {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 8px;
    }
    
    .resume-template h2 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 6px;
    }
    
    .resume-template h3 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
    }
    
    .resume-template p {
      margin-bottom: 4px;
    }
    
    .resume-template ul {
      margin-left: 20px;
      margin-bottom: 8px;
    }
    
    .resume-template li {
      margin-bottom: 2px;
    }
    
    /* Ensure text is selectable for ATS */
    .resume-template * {
      user-select: text;
      -webkit-user-select: text;
      -moz-user-select: text;
      -ms-user-select: text;
    }
  </style>
</head>
<body>
  <div class="template-container" data-resume-template>
    <div class="loading" id="loading">
      Loading resume template...
    </div>
    <div class="error" id="error" style="display: none;">
      Failed to load resume template. Please try again.
    </div>
    <div id="template-content" style="display: none;">
      <!-- Template will be rendered here -->
    </div>
  </div>

  <script>
    // Template rendering logic
    const templateId = '${templateId}';
    let resumeData = null;
    
    // Listen for resume data from Puppeteer
    window.addEventListener('resumeDataReady', (event) => {
      resumeData = event.detail;
      renderTemplate();
    });
    
    // Check if data is already available (for direct access)
    if (window.resumeData) {
      resumeData = window.resumeData;
      renderTemplate();
    }
    
    function renderTemplate() {
      const loading = document.getElementById('loading');
      const error = document.getElementById('error');
      const content = document.getElementById('template-content');
      
      try {
        if (!resumeData) {
          throw new Error('No resume data provided');
        }
        
        // Hide loading
        loading.style.display = 'none';
        
        // Render template based on templateId
        const templateHtml = generateTemplateHtml(templateId, resumeData);
        content.innerHTML = templateHtml;
        content.style.display = 'block';
        
        // Notify that rendering is complete
        window.dispatchEvent(new CustomEvent('templateRendered'));
        
      } catch (err) {
        console.error('Template rendering error:', err);
        loading.style.display = 'none';
        error.style.display = 'flex';
        error.textContent = 'Error: ' + (err.message || 'Unknown error');
      }
    }
    
    function generateTemplateHtml(templateId, data) {
      // This is a simplified template renderer
      // In a real implementation, you'd have proper template components
      
      const personalInfo = data.personalInfo || {};
      const summary = data.summary || '';
      const experience = data.experience || [];
      const education = data.education || [];
      const skills = data.skills || [];
      const certifications = data.certifications || [];
      const languages = data.languages || [];
      
      return \`
        <div class="resume-template">
          <!-- Header -->
          <header style="text-align: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 2px solid #333;">
            <h1>\${personalInfo.fullName || 'Your Name'}</h1>
            <div style="margin-top: 8px; font-size: 14px; color: #666;">
              \${personalInfo.email ? \`<span>\${personalInfo.email}</span>\` : ''}
              \${personalInfo.phone ? \`<span style="margin-left: 15px;">\${personalInfo.phone}</span>\` : ''}
              \${personalInfo.location ? \`<span style="margin-left: 15px;">\${personalInfo.location}</span>\` : ''}
            </div>
            \${personalInfo.website || personalInfo.linkedinUrl ? \`
              <div style="margin-top: 4px; font-size: 14px;">
                \${personalInfo.website ? \`<span>\${personalInfo.website}</span>\` : ''}
                \${personalInfo.linkedinUrl ? \`<span style="margin-left: 15px;">\${personalInfo.linkedinUrl}</span>\` : ''}
              </div>
            \` : ''}
          </header>
          
          <!-- Summary -->
          \${summary ? \`
            <section style="margin-bottom: 20px;">
              <h2 style="color: #333; border-bottom: 1px solid #ddd; padding-bottom: 4px;">Professional Summary</h2>
              <p style="margin-top: 8px; text-align: justify;">\${summary}</p>
            </section>
          \` : ''}
          
          <!-- Experience -->
          \${experience.length > 0 ? \`
            <section style="margin-bottom: 20px;">
              <h2 style="color: #333; border-bottom: 1px solid #ddd; padding-bottom: 4px;">Professional Experience</h2>
              \${experience.map(exp => \`
                <div style="margin-top: 15px;" class="avoid-break">
                  <div style="display: flex; justify-content: space-between; align-items: baseline;">
                    <h3>\${exp.position} - \${exp.company}</h3>
                    <span style="font-size: 12px; color: #666;">
                      \${exp.startDate ? new Date(exp.startDate).toLocaleDateString() : ''} - 
                      \${exp.current ? 'Present' : (exp.endDate ? new Date(exp.endDate).toLocaleDateString() : '')}
                    </span>
                  </div>
                  \${exp.location ? \`<div style="font-size: 12px; color: #666; margin-bottom: 4px;">\${exp.location}</div>\` : ''}
                  \${exp.description && exp.description.length > 0 ? \`
                    <ul style="margin-top: 6px;">
                      \${exp.description.map(desc => \`<li>\${desc}</li>\`).join('')}
                    </ul>
                  \` : ''}
                  \${exp.skills && exp.skills.length > 0 ? \`
                    <div style="margin-top: 6px; font-size: 12px;">
                      <strong>Skills:</strong> \${exp.skills.join(', ')}
                    </div>
                  \` : ''}
                </div>
              \`).join('')}
            </section>
          \` : ''}
          
          <!-- Education -->
          \${education.length > 0 ? \`
            <section style="margin-bottom: 20px;">
              <h2 style="color: #333; border-bottom: 1px solid #ddd; padding-bottom: 4px;">Education</h2>
              \${education.map(edu => \`
                <div style="margin-top: 15px;" class="avoid-break">
                  <div style="display: flex; justify-content: space-between; align-items: baseline;">
                    <h3>\${edu.degree} in \${edu.field}</h3>
                    <span style="font-size: 12px; color: #666;">
                      \${edu.startDate ? new Date(edu.startDate).getFullYear() : ''} - 
                      \${edu.endDate ? new Date(edu.endDate).getFullYear() : 'Present'}
                    </span>
                  </div>
                  <div style="font-weight: 500;">\${edu.institution}</div>
                  \${edu.location ? \`<div style="font-size: 12px; color: #666;">\${edu.location}</div>\` : ''}
                  \${edu.gpa ? \`<div style="font-size: 12px; margin-top: 2px;">GPA: \${edu.gpa}</div>\` : ''}
                  \${edu.achievements && edu.achievements.length > 0 ? \`
                    <div style="margin-top: 4px; font-size: 12px;">
                      <strong>Achievements:</strong> \${edu.achievements.join(', ')}
                    </div>
                  \` : ''}
                </div>
              \`).join('')}
            </section>
          \` : ''}
          
          <!-- Skills -->
          \${skills.length > 0 ? \`
            <section style="margin-bottom: 20px;">
              <h2 style="color: #333; border-bottom: 1px solid #ddd; padding-bottom: 4px;">Skills</h2>
              <div style="margin-top: 8px;">
                \${skills.join(' • ')}
              </div>
            </section>
          \` : ''}
          
          <!-- Certifications -->
          \${certifications.length > 0 ? \`
            <section style="margin-bottom: 20px;">
              <h2 style="color: #333; border-bottom: 1px solid #ddd; padding-bottom: 4px;">Certifications</h2>
              \${certifications.map(cert => \`
                <div style="margin-top: 8px;" class="avoid-break">
                  <div style="font-weight: 500;">\${cert.name}</div>
                  <div style="font-size: 12px; color: #666;">
                    \${cert.issuer} • 
                    \${cert.issueDate ? new Date(cert.issueDate).toLocaleDateString() : ''}
                    \${cert.expiryDate ? \` - \${new Date(cert.expiryDate).toLocaleDateString()}\` : ''}
                  </div>
                  \${cert.credentialId ? \`<div style="font-size: 11px; color: #888;">ID: \${cert.credentialId}</div>\` : ''}
                </div>
              \`).join('')}
            </section>
          \` : ''}
          
          <!-- Languages -->
          \${languages.length > 0 ? \`
            <section style="margin-bottom: 20px;">
              <h2 style="color: #333; border-bottom: 1px solid #ddd; padding-bottom: 4px;">Languages</h2>
              <div style="margin-top: 8px;">
                \${languages.map(lang => \`\${lang.name} (\${lang.proficiency})\`).join(' • ')}
              </div>
            </section>
          \` : ''}
        </div>
      \`;
    }
    
    // Fallback: try to render after a short delay if no data received
    setTimeout(() => {
      if (!resumeData && window.resumeData) {
        resumeData = window.resumeData;
        renderTemplate();
      }
    }, 1000);
  </script>
</body>
</html>
  `;

  return new NextResponse(html, {
    headers: {
      'Content-Type': 'text/html',
    },
  });
}
