import { NextRequest, NextResponse } from 'next/server';
import puppeteer from 'puppeteer';
import { ResumeContent } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { resumeData, templateId, options = {} } = body;

    // Validate required fields
    if (!resumeData || !templateId) {
      return NextResponse.json(
        { error: 'Resume data and template ID are required' },
        { status: 400 }
      );
    }

    // Default PDF options
    const pdfOptions = {
      format: 'A4' as const,
      quality: options.quality || 'high',
      margin: options.margin || { top: '0.5in', right: '0.5in', bottom: '0.5in', left: '0.5in' },
      filename: options.filename || 'resume.pdf',
      ...options
    };

    // Launch Puppeteer browser
    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    });

    try {
      const page = await browser.newPage();

      // Set viewport for consistent rendering
      await page.setViewport({
        width: 1200,
        height: 1600,
        deviceScaleFactor: 2
      });

      // Construct the URL for the PDF render route
      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const renderUrl = `${baseUrl}/api/resumes/render?templateId=${templateId}`;

      // Navigate to the render page with resume data
      await page.goto(renderUrl, { 
        waitUntil: 'networkidle0',
        timeout: 30000 
      });

      // Inject resume data into the page
      await page.evaluate((data) => {
        (window as any).resumeData = data;
        // Trigger a custom event to notify the template that data is ready
        window.dispatchEvent(new CustomEvent('resumeDataReady', { detail: data }));
      }, resumeData);

      // Wait for the template to render
      await page.waitForSelector('[data-resume-template]', { timeout: 10000 });

      // Add print-specific styles
      await page.addStyleTag({
        content: `
          @media print {
            body { 
              margin: 0; 
              padding: 0; 
              background: white !important;
              -webkit-print-color-adjust: exact;
              color-adjust: exact;
            }
            * { 
              box-shadow: none !important; 
              text-shadow: none !important;
            }
            .no-print { 
              display: none !important; 
            }
            .page-break { 
              page-break-before: always; 
            }
            .avoid-break { 
              page-break-inside: avoid; 
            }
          }
          
          /* Ensure text is selectable and ATS-friendly */
          * {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          }
          
          /* High contrast for better readability */
          body {
            color: #000000;
            background: #ffffff;
          }
        `
      });

      // Generate PDF with high quality settings
      const pdf = await page.pdf({
        format: pdfOptions.format,
        margin: pdfOptions.margin,
        printBackground: true,
        preferCSSPageSize: true,
        displayHeaderFooter: false,
        scale: pdfOptions.quality === 'high' ? 1.0 : 0.8,
        timeout: 30000
      });

      // Set response headers for PDF download
      const headers = new Headers();
      headers.set('Content-Type', 'application/pdf');
      headers.set('Content-Disposition', `attachment; filename="${pdfOptions.filename}"`);
      headers.set('Content-Length', pdf.length.toString());
      headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');

      return new NextResponse(pdf, {
        status: 200,
        headers
      });

    } finally {
      await browser.close();
    }

  } catch (error) {
    console.error('PDF generation error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to generate PDF',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
