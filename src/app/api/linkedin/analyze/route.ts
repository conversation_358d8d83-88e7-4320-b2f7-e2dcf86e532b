import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db';
import { aiService } from '@/lib/ai/service';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { profileData, targetRole, targetIndustry } = await request.json();

    // Get user's LinkedIn profile
    const linkedinProfile = await prisma.linkedInProfile.findUnique({
      where: { userId: session.user.id },
    });

    if (!linkedinProfile) {
      return NextResponse.json(
        { error: 'LinkedIn profile not found. Please create a profile first.' },
        { status: 404 }
      );
    }

    // Analyze profile using AI
    const analysisPrompt = `
Analyze this LinkedIn profile and provide optimization recommendations:

Profile Data:
- Headline: ${linkedinProfile.headline || 'Not provided'}
- Industry: ${linkedinProfile.industry || 'Not provided'}
- Location: ${linkedinProfile.location || 'Not provided'}
- Current Position: ${linkedinProfile.currentPosition || 'Not provided'}
- Current Company: ${linkedinProfile.currentCompany || 'Not provided'}
- Summary: ${linkedinProfile.summary || 'Not provided'}
- About Section: ${linkedinProfile.aboutSection || 'Not provided'}
- Profile Strength: ${linkedinProfile.profileStrength || 'Not provided'}
- Connections: ${linkedinProfile.connectionsCount || 'Not provided'}
- Open to Work: ${linkedinProfile.isOpenToWork ? 'Yes' : 'No'}

Target Role: ${targetRole || 'Not specified'}
Target Industry: ${targetIndustry || 'Not specified'}

Please provide:
1. Missing fields that should be completed
2. Specific optimization tips for better recruiter visibility
3. Keyword suggestions for the target role/industry
4. 3-5 headline suggestions
5. 2-3 summary suggestions
6. Overall profile score (0-100)
7. Visibility score for recruiters (0-100)
8. Engagement score potential (0-100)

Format the response as JSON with the following structure:
{
  "missingFields": ["field1", "field2"],
  "optimizationTips": ["tip1", "tip2"],
  "keywordSuggestions": ["keyword1", "keyword2"],
  "headlineSuggestions": ["headline1", "headline2"],
  "summarySuggestions": ["summary1", "summary2"],
  "profileScore": 85,
  "visibilityScore": 78,
  "engagementScore": 82,
  "overallRecommendation": "Brief overall recommendation"
}
`;

    const analysisResult = await aiService.generateContent(analysisPrompt, {
      maxTokens: 2000,
      temperature: 0.7,
    });

    let analysis;
    try {
      analysis = JSON.parse(analysisResult);
    } catch (parseError) {
      // Fallback analysis if AI response isn't valid JSON
      analysis = generateFallbackAnalysis(linkedinProfile, targetRole, targetIndustry);
    }

    // Update LinkedIn profile with analysis results
    const updatedProfile = await prisma.linkedInProfile.update({
      where: { userId: session.user.id },
      data: {
        missingFields: analysis.missingFields || [],
        optimizationTips: analysis.optimizationTips || [],
        keywordSuggestions: analysis.keywordSuggestions || [],
        headlineSuggestions: analysis.headlineSuggestions || [],
        summarySuggestions: analysis.summarySuggestions || [],
        profileScore: analysis.profileScore || linkedinProfile.profileScore,
        visibilityScore: analysis.visibilityScore || 0,
        engagementScore: analysis.engagementScore || 0,
        lastAnalyzed: new Date(),
      },
    });

    return NextResponse.json({
      analysis,
      profile: updatedProfile,
    });
  } catch (error) {
    console.error('Error analyzing LinkedIn profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function generateFallbackAnalysis(profile: any, targetRole?: string, targetIndustry?: string) {
  const missingFields = [];
  const optimizationTips = [];
  const keywordSuggestions = [];
  const headlineSuggestions = [];
  const summarySuggestions = [];

  // Check for missing fields
  if (!profile.headline) missingFields.push('headline');
  if (!profile.industry) missingFields.push('industry');
  if (!profile.summary) missingFields.push('summary');
  if (!profile.currentPosition) missingFields.push('currentPosition');
  if (!profile.currentCompany) missingFields.push('currentCompany');
  if (!profile.profileImageUrl) missingFields.push('profileImage');

  // Generate optimization tips
  if (!profile.headline) {
    optimizationTips.push('Add a compelling headline that includes your target role and key skills');
  }
  if (!profile.summary || profile.summary.length < 200) {
    optimizationTips.push('Write a comprehensive summary (200+ words) highlighting your achievements');
  }
  if (!profile.connectionsCount || profile.connectionsCount < 100) {
    optimizationTips.push('Build your network by connecting with industry professionals');
  }
  if (!profile.isOpenToWork && targetRole) {
    optimizationTips.push('Consider enabling "Open to Work" to increase recruiter visibility');
  }

  // Generate keyword suggestions based on target role
  if (targetRole) {
    const roleKeywords = generateRoleKeywords(targetRole);
    keywordSuggestions.push(...roleKeywords);
  }

  // Generate headline suggestions
  if (targetRole && profile.currentPosition) {
    headlineSuggestions.push(
      `${targetRole} | ${profile.currentPosition} at ${profile.currentCompany || 'Leading Company'}`,
      `Experienced ${targetRole} | Driving Innovation in ${targetIndustry || 'Technology'}`,
      `${targetRole} Specialist | Helping Companies Achieve Growth`
    );
  }

  // Generate summary suggestions
  if (targetRole) {
    summarySuggestions.push(
      `Passionate ${targetRole} with proven track record of delivering results...`,
      `Results-driven ${targetRole} specializing in ${targetIndustry || 'technology solutions'}...`
    );
  }

  return {
    missingFields,
    optimizationTips,
    keywordSuggestions,
    headlineSuggestions,
    summarySuggestions,
    profileScore: calculateBasicScore(profile),
    visibilityScore: calculateVisibilityScore(profile),
    engagementScore: calculateEngagementScore(profile),
    overallRecommendation: 'Focus on completing missing fields and optimizing content for your target role.',
  };
}

function generateRoleKeywords(role: string): string[] {
  const keywordMap: Record<string, string[]> = {
    'software engineer': ['JavaScript', 'Python', 'React', 'Node.js', 'AWS', 'Docker'],
    'data scientist': ['Python', 'Machine Learning', 'SQL', 'TensorFlow', 'Statistics'],
    'product manager': ['Product Strategy', 'Agile', 'User Research', 'Analytics', 'Roadmap'],
    'marketing manager': ['Digital Marketing', 'SEO', 'Content Strategy', 'Analytics', 'Brand Management'],
    'sales manager': ['Sales Strategy', 'CRM', 'Lead Generation', 'Account Management', 'Revenue Growth'],
  };

  const normalizedRole = role.toLowerCase();
  for (const [key, keywords] of Object.entries(keywordMap)) {
    if (normalizedRole.includes(key)) {
      return keywords;
    }
  }

  return ['Leadership', 'Strategy', 'Innovation', 'Growth', 'Excellence'];
}

function calculateBasicScore(profile: any): number {
  let score = 0;
  const fields = ['headline', 'industry', 'summary', 'currentPosition', 'currentCompany'];
  
  fields.forEach(field => {
    if (profile[field]) score += 20;
  });

  return Math.min(100, score);
}

function calculateVisibilityScore(profile: any): number {
  let score = 0;
  
  if (profile.headline) score += 25;
  if (profile.summary && profile.summary.length > 200) score += 25;
  if (profile.connectionsCount && profile.connectionsCount > 100) score += 25;
  if (profile.isOpenToWork) score += 25;

  return score;
}

function calculateEngagementScore(profile: any): number {
  let score = 0;
  
  if (profile.profileViews && profile.profileViews > 10) score += 30;
  if (profile.searchAppearances && profile.searchAppearances > 5) score += 30;
  if (profile.connectionsCount && profile.connectionsCount > 200) score += 40;

  return Math.min(100, score);
}
