import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db';
import { aiService } from '@/lib/ai/service';
import { z } from 'zod';

const optimizeRequestSchema = z.object({
  section: z.enum(['headline', 'summary', 'about', 'skills']),
  targetRole: z.string().optional(),
  targetIndustry: z.string().optional(),
  currentContent: z.string().optional(),
  tone: z.enum(['professional', 'creative', 'technical', 'executive']).default('professional'),
  keywords: z.array(z.string()).optional(),
});

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { section, targetRole, targetIndustry, currentContent, tone, keywords } = 
      optimizeRequestSchema.parse(body);

    // Get user's profile data for context
    const [linkedinProfile, userProfile, resumes] = await Promise.all([
      prisma.linkedInProfile.findUnique({
        where: { userId: session.user.id },
      }),
      prisma.userProfile.findUnique({
        where: { userId: session.user.id },
        include: {
          skills: true,
          experiences: true,
          educations: true,
        },
      }),
      prisma.resume.findMany({
        where: { userId: session.user.id },
        orderBy: { updatedAt: 'desc' },
        take: 1,
      }),
    ]);

    if (!linkedinProfile) {
      return NextResponse.json(
        { error: 'LinkedIn profile not found' },
        { status: 404 }
      );
    }

    // Generate optimized content based on section
    let optimizedContent;
    
    switch (section) {
      case 'headline':
        optimizedContent = await generateOptimizedHeadline({
          linkedinProfile,
          userProfile,
          targetRole,
          targetIndustry,
          tone,
          keywords,
        });
        break;
        
      case 'summary':
        optimizedContent = await generateOptimizedSummary({
          linkedinProfile,
          userProfile,
          resumes: resumes[0],
          targetRole,
          targetIndustry,
          currentContent,
          tone,
          keywords,
        });
        break;
        
      case 'about':
        optimizedContent = await generateOptimizedAbout({
          linkedinProfile,
          userProfile,
          targetRole,
          targetIndustry,
          currentContent,
          tone,
          keywords,
        });
        break;
        
      case 'skills':
        optimizedContent = await generateOptimizedSkills({
          userProfile,
          targetRole,
          targetIndustry,
          keywords,
        });
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid section' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      section,
      optimizedContent,
      suggestions: optimizedContent.suggestions || [],
      keywords: optimizedContent.keywords || [],
    });
  } catch (error) {
    console.error('Error optimizing LinkedIn content:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function generateOptimizedHeadline({
  linkedinProfile,
  userProfile,
  targetRole,
  targetIndustry,
  tone,
  keywords,
}: any) {
  const prompt = `
Generate 5 optimized LinkedIn headlines for this professional:

Current Info:
- Current Position: ${linkedinProfile.currentPosition || 'Not specified'}
- Current Company: ${linkedinProfile.currentCompany || 'Not specified'}
- Industry: ${linkedinProfile.industry || targetIndustry || 'Not specified'}
- Target Role: ${targetRole || 'Not specified'}
- Skills: ${userProfile?.skills?.map((s: any) => s.name).join(', ') || 'Not specified'}

Requirements:
- Tone: ${tone}
- Include keywords: ${keywords?.join(', ') || 'None specified'}
- Maximum 220 characters each
- Focus on value proposition and target role
- Include relevant skills and industry terms

Return as JSON array of strings.
`;

  try {
    const response = await aiService.generateContent(prompt, {
      maxTokens: 1000,
      temperature: 0.8,
    });

    const headlines = JSON.parse(response);
    
    return {
      suggestions: Array.isArray(headlines) ? headlines : [response],
      keywords: keywords || [],
    };
  } catch (error) {
    // Fallback headlines
    const fallbackHeadlines = [
      `${targetRole || 'Professional'} | ${linkedinProfile.currentPosition || 'Experienced'} | ${targetIndustry || 'Industry'} Expert`,
      `Driving Innovation in ${targetIndustry || 'Technology'} | ${targetRole || 'Leader'} | Growth-Focused`,
      `${targetRole || 'Professional'} Specialist | Helping Companies Achieve Excellence`,
    ];
    
    return {
      suggestions: fallbackHeadlines,
      keywords: keywords || [],
    };
  }
}

async function generateOptimizedSummary({
  linkedinProfile,
  userProfile,
  resumes,
  targetRole,
  targetIndustry,
  currentContent,
  tone,
  keywords,
}: any) {
  const prompt = `
Write an optimized LinkedIn summary for this professional:

Current Summary: ${currentContent || linkedinProfile.summary || 'None'}
Current Position: ${linkedinProfile.currentPosition || 'Not specified'}
Current Company: ${linkedinProfile.currentCompany || 'Not specified'}
Target Role: ${targetRole || 'Not specified'}
Target Industry: ${targetIndustry || 'Not specified'}
Experience: ${userProfile?.experiences?.map((e: any) => `${e.title} at ${e.company}`).join(', ') || 'Not specified'}
Skills: ${userProfile?.skills?.map((s: any) => s.name).join(', ') || 'Not specified'}

Requirements:
- Tone: ${tone}
- Include keywords: ${keywords?.join(', ') || 'None specified'}
- 3-4 paragraphs, 300-500 words
- Start with a strong value proposition
- Include specific achievements and metrics
- End with a call to action
- Optimize for recruiter searches

Generate 2 different versions.
Return as JSON array of strings.
`;

  try {
    const response = await aiService.generateContent(prompt, {
      maxTokens: 1500,
      temperature: 0.7,
    });

    const summaries = JSON.parse(response);
    
    return {
      suggestions: Array.isArray(summaries) ? summaries : [response],
      keywords: keywords || [],
    };
  } catch (error) {
    // Fallback summary
    const fallbackSummary = `Experienced ${targetRole || 'professional'} with a proven track record of delivering results in ${targetIndustry || 'the industry'}. 

Currently serving as ${linkedinProfile.currentPosition || 'a key contributor'} at ${linkedinProfile.currentCompany || 'a leading organization'}, I specialize in driving innovation and growth through strategic thinking and hands-on execution.

My expertise includes ${userProfile?.skills?.slice(0, 5).map((s: any) => s.name).join(', ') || 'various technical and leadership skills'}, enabling me to tackle complex challenges and deliver measurable outcomes.

I'm passionate about ${targetIndustry || 'technology'} and always looking to connect with like-minded professionals. Let's discuss how we can create value together.`;
    
    return {
      suggestions: [fallbackSummary],
      keywords: keywords || [],
    };
  }
}

async function generateOptimizedAbout({
  linkedinProfile,
  userProfile,
  targetRole,
  targetIndustry,
  currentContent,
  tone,
  keywords,
}: any) {
  // Similar to summary but more personal and story-driven
  const prompt = `
Write an optimized LinkedIn "About" section that tells a compelling professional story:

Current About: ${currentContent || linkedinProfile.aboutSection || 'None'}
Target Role: ${targetRole || 'Not specified'}
Target Industry: ${targetIndustry || 'Not specified'}
Background: ${userProfile?.experiences?.map((e: any) => `${e.title} at ${e.company}`).join(', ') || 'Not specified'}

Requirements:
- Tone: ${tone}
- Include keywords: ${keywords?.join(', ') || 'None specified'}
- Tell a story of professional journey
- Highlight unique value proposition
- Include personality and passion
- 200-300 words
- End with contact invitation

Generate 2 versions with different storytelling approaches.
Return as JSON array of strings.
`;

  try {
    const response = await aiService.generateContent(prompt, {
      maxTokens: 1200,
      temperature: 0.8,
    });

    const aboutSections = JSON.parse(response);
    
    return {
      suggestions: Array.isArray(aboutSections) ? aboutSections : [response],
      keywords: keywords || [],
    };
  } catch (error) {
    const fallbackAbout = `My journey in ${targetIndustry || 'technology'} began with a simple curiosity that has evolved into a passion for creating meaningful impact.

As a ${targetRole || 'professional'}, I've had the privilege of working with diverse teams and tackling challenges that push the boundaries of what's possible. Each project has taught me something new and reinforced my belief in the power of collaboration and innovation.

What drives me is the opportunity to solve complex problems and help organizations achieve their goals. I believe that the best solutions come from understanding both the technical requirements and the human element behind every challenge.

When I'm not working, you'll find me exploring new technologies, mentoring aspiring professionals, or enjoying time with family and friends.

I'm always open to connecting with fellow professionals and exploring new opportunities. Feel free to reach out!`;
    
    return {
      suggestions: [fallbackAbout],
      keywords: keywords || [],
    };
  }
}

async function generateOptimizedSkills({
  userProfile,
  targetRole,
  targetIndustry,
  keywords,
}: any) {
  const currentSkills = userProfile?.skills?.map((s: any) => s.name) || [];
  
  const prompt = `
Suggest optimized skills for LinkedIn profile:

Current Skills: ${currentSkills.join(', ') || 'None'}
Target Role: ${targetRole || 'Not specified'}
Target Industry: ${targetIndustry || 'Not specified'}
Additional Keywords: ${keywords?.join(', ') || 'None'}

Requirements:
- Suggest 15-20 relevant skills
- Mix of technical and soft skills
- Industry-specific skills
- Skills that recruiters search for
- Remove outdated or irrelevant skills

Return as JSON object with:
{
  "recommended": ["skill1", "skill2"],
  "toAdd": ["new_skill1", "new_skill2"],
  "toRemove": ["outdated_skill1"],
  "priority": ["top_skill1", "top_skill2"]
}
`;

  try {
    const response = await aiService.generateContent(prompt, {
      maxTokens: 800,
      temperature: 0.6,
    });

    return JSON.parse(response);
  } catch (error) {
    // Fallback skills optimization
    return {
      recommended: keywords || [],
      toAdd: ['Leadership', 'Strategy', 'Innovation'],
      toRemove: [],
      priority: keywords?.slice(0, 5) || ['Leadership', 'Strategy'],
    };
  }
}
