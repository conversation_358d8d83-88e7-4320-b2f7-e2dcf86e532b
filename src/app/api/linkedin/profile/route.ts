import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db';
import { z } from 'zod';

const linkedinProfileSchema = z.object({
  headline: z.string().optional(),
  industry: z.string().optional(),
  location: z.string().optional(),
  summary: z.string().optional(),
  currentPosition: z.string().optional(),
  currentCompany: z.string().optional(),
  connectionsCount: z.number().optional(),
  profileViews: z.number().optional(),
  searchAppearances: z.number().optional(),
  profileStrength: z.enum(['Beginner', 'Intermediate', 'Advanced', 'All-Star']).optional(),
  profileUrl: z.string().url().optional(),
  profileImageUrl: z.string().url().optional(),
  backgroundImageUrl: z.string().url().optional(),
  isOpenToWork: z.boolean().default(false),
  openToWorkDetails: z.string().optional(),
  featuredSection: z.string().optional(),
  aboutSection: z.string().optional(),
  contactInfo: z.record(z.any()).optional(),
});

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const linkedinProfile = await prisma.linkedInProfile.findUnique({
      where: { userId: session.user.id },
    });

    if (!linkedinProfile) {
      // Create a default profile if none exists
      const newProfile = await prisma.linkedInProfile.create({
        data: {
          userId: session.user.id,
          profileScore: 0,
          completenessScore: 0,
          visibilityScore: 0,
          engagementScore: 0,
        },
      });
      return NextResponse.json(newProfile);
    }

    return NextResponse.json(linkedinProfile);
  } catch (error) {
    console.error('Error fetching LinkedIn profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = linkedinProfileSchema.parse(body);

    // Calculate profile completeness score
    const completenessScore = calculateCompletenessScore(validatedData);
    
    // Calculate profile score based on various factors
    const profileScore = calculateProfileScore(validatedData, completenessScore);

    const linkedinProfile = await prisma.linkedInProfile.upsert({
      where: { userId: session.user.id },
      update: {
        ...validatedData,
        completenessScore,
        profileScore,
        lastOptimized: new Date(),
        updatedAt: new Date(),
      },
      create: {
        userId: session.user.id,
        ...validatedData,
        completenessScore,
        profileScore,
        lastOptimized: new Date(),
      },
    });

    return NextResponse.json(linkedinProfile);
  } catch (error) {
    console.error('Error updating LinkedIn profile:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid profile data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function calculateCompletenessScore(profile: any): number {
  const fields = [
    'headline',
    'industry',
    'location',
    'summary',
    'currentPosition',
    'currentCompany',
    'profileImageUrl',
    'aboutSection',
  ];

  const completedFields = fields.filter(field => profile[field] && profile[field].trim() !== '');
  return Math.round((completedFields.length / fields.length) * 100);
}

function calculateProfileScore(profile: any, completenessScore: number): number {
  let score = 0;

  // Base score from completeness (40% weight)
  score += completenessScore * 0.4;

  // Headline quality (15% weight)
  if (profile.headline) {
    const headlineScore = Math.min(100, profile.headline.length * 2); // Longer headlines score better
    score += headlineScore * 0.15;
  }

  // Summary quality (20% weight)
  if (profile.summary) {
    const summaryScore = Math.min(100, profile.summary.length / 10); // Aim for ~1000 characters
    score += summaryScore * 0.2;
  }

  // Profile strength (10% weight)
  const strengthScores = {
    'Beginner': 25,
    'Intermediate': 50,
    'Advanced': 75,
    'All-Star': 100,
  };
  if (profile.profileStrength) {
    score += strengthScores[profile.profileStrength as keyof typeof strengthScores] * 0.1;
  }

  // Connections count (5% weight)
  if (profile.connectionsCount) {
    const connectionScore = Math.min(100, profile.connectionsCount / 5); // 500+ connections = 100%
    score += connectionScore * 0.05;
  }

  // Open to work status (5% weight)
  if (profile.isOpenToWork) {
    score += 100 * 0.05;
  }

  // Contact info completeness (5% weight)
  if (profile.contactInfo && Object.keys(profile.contactInfo).length > 0) {
    score += 100 * 0.05;
  }

  return Math.round(Math.min(100, score));
}
