// API endpoint for template preview generation
// Generates preview images and renders templates with sample data

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { templateRegistry, ResumeData } from '@/lib/templates/template-registry';

interface RouteParams {
  params: {
    templateId: string;
  };
}

// Sample resume data for preview generation
const SAMPLE_RESUME_DATA: ResumeData = {
  personalInfo: {
    name: '<PERSON>',
    jobTitle: 'Senior Software Engineer',
    email: '<EMAIL>',
    phone: '(*************',
    location: 'San Francisco, CA',
    linkedin: 'linkedin.com/in/alexjohnson',
    github: 'github.com/alexjohnson',
    photo: '/api/templates/sample-photo.jpg'
  },
  summary: 'Experienced software engineer with 8+ years of expertise in full-stack development, cloud architecture, and team leadership. Proven track record of delivering scalable solutions and mentoring development teams.',
  experience: [
    {
      company: 'TechCorp Inc.',
      jobTitle: 'Senior Software Engineer',
      dates: '2021 - Present',
      description: 'Lead development of microservices architecture serving 10M+ users. Mentored junior developers and improved system performance by 40%. Technologies: React, Node.js, AWS, Docker.',
      location: 'San Francisco, CA'
    },
    {
      company: 'StartupXYZ',
      jobTitle: 'Full Stack Developer',
      dates: '2019 - 2021',
      description: 'Built and maintained web applications using modern JavaScript frameworks. Collaborated with design and product teams to deliver user-centric solutions.',
      location: 'Remote'
    },
    {
      company: 'Digital Solutions Ltd.',
      jobTitle: 'Software Developer',
      dates: '2017 - 2019',
      description: 'Developed responsive web applications and RESTful APIs. Participated in code reviews and agile development processes.',
      location: 'New York, NY'
    }
  ],
  education: [
    {
      degree: 'Master of Science in Computer Science',
      institution: 'Stanford University',
      dates: '2015 - 2017'
    },
    {
      degree: 'Bachelor of Science in Software Engineering',
      institution: 'UC Berkeley',
      dates: '2011 - 2015'
    }
  ],
  skills: [
    'JavaScript', 'TypeScript', 'React', 'Node.js', 'Python', 'AWS', 
    'Docker', 'Kubernetes', 'PostgreSQL', 'MongoDB', 'Git', 'Agile'
  ],
  projects: [
    {
      name: 'E-commerce Platform',
      description: 'Built a scalable e-commerce platform using React, Node.js, and AWS. Implemented payment processing, inventory management, and real-time analytics.'
    },
    {
      name: 'Task Management App',
      description: 'Developed a collaborative task management application with real-time updates, file sharing, and team communication features.'
    }
  ],
  certifications: [
    'AWS Certified Solutions Architect',
    'Certified Kubernetes Administrator',
    'Google Cloud Professional Developer'
  ],
  awards: [
    {
      title: 'Employee of the Year',
      date: '2022',
      description: 'Recognized for outstanding performance and leadership in software development'
    },
    {
      title: 'Innovation Award',
      date: '2021',
      description: 'Awarded for developing innovative solutions that improved system efficiency'
    }
  ],
  hobbies: ['Open Source Contributing', 'Photography', 'Hiking', 'Chess'],
  links: {
    linkedin: 'https://linkedin.com/in/alexjohnson',
    github: 'https://github.com/alexjohnson'
  }
};

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { templateId } = params;
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'json'; // json, html, image
    const width = parseInt(searchParams.get('width') || '595');
    const height = parseInt(searchParams.get('height') || '842');

    // Get template from database
    const template = await prisma.resumeTemplate.findUnique({
      where: { id: templateId }
    });

    if (!template) {
      return NextResponse.json(
        { success: false, error: 'Template not found' },
        { status: 404 }
      );
    }

    // Check if template component is available
    const TemplateComponent = templateRegistry.getTemplateComponent(templateId);
    
    if (!TemplateComponent) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Template component not available',
          details: 'Template exists in database but component is not loaded in registry'
        },
        { status: 404 }
      );
    }

    // Handle different response formats
    switch (format) {
      case 'json':
        return NextResponse.json({
          success: true,
          data: {
            template: {
              id: template.id,
              name: template.name,
              category: template.category,
              description: template.description,
              isPremium: template.isPremium,
              atsOptimized: template.atsOptimized,
              styles: template.styles
            },
            sampleData: SAMPLE_RESUME_DATA,
            previewUrl: `/api/templates/${templateId}/preview?format=html`,
            imageUrl: `/api/templates/${templateId}/preview?format=image&width=${width}&height=${height}`
          }
        });

      case 'html':
        // For HTML preview, we would need to render the React component to HTML
        // This would typically require a server-side rendering setup
        return new NextResponse(
          `
          <!DOCTYPE html>
          <html>
            <head>
              <title>Template Preview - ${template.name}</title>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1">
              <script src="https://cdn.tailwindcss.com"></script>
              <style>
                body { margin: 0; padding: 20px; background: #f3f4f6; }
                .preview-container { 
                  max-width: ${width}px; 
                  margin: 0 auto; 
                  background: white; 
                  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                }
              </style>
            </head>
            <body>
              <div class="preview-container">
                <div style="padding: 20px; text-align: center; background: #f9fafb; border-bottom: 1px solid #e5e7eb;">
                  <h1 style="margin: 0; font-size: 24px; color: #374151;">Template Preview</h1>
                  <p style="margin: 5px 0 0 0; color: #6b7280;">${template.name}</p>
                </div>
                <div id="template-preview" style="width: ${width}px; height: ${height}px;">
                  <!-- Template would be rendered here -->
                  <div style="padding: 40px; text-align: center; color: #6b7280;">
                    <p>Template preview rendering requires client-side React setup.</p>
                    <p>Use the JSON format to get template data and render on the client.</p>
                    <p><strong>Template:</strong> ${template.name}</p>
                    <p><strong>Category:</strong> ${template.category}</p>
                    <p><strong>ATS Optimized:</strong> ${template.atsOptimized ? 'Yes' : 'No'}</p>
                  </div>
                </div>
              </div>
            </body>
          </html>
          `,
          {
            headers: {
              'Content-Type': 'text/html',
            },
          }
        );

      case 'image':
        // For image generation, we would need a service like Puppeteer or similar
        // For now, return a placeholder response
        return NextResponse.json({
          success: false,
          error: 'Image generation not implemented',
          details: 'Image preview generation requires additional setup with Puppeteer or similar service',
          alternatives: {
            html: `/api/templates/${templateId}/preview?format=html`,
            json: `/api/templates/${templateId}/preview?format=json`
          }
        }, { status: 501 });

      default:
        return NextResponse.json(
          { 
            success: false, 
            error: 'Invalid format',
            supportedFormats: ['json', 'html', 'image']
          },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error generating template preview:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate template preview',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST endpoint for custom preview with user data
export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { templateId } = params;
    const body = await request.json();
    const { resumeData, format = 'json' } = body;

    // Get template from database
    const template = await prisma.resumeTemplate.findUnique({
      where: { id: templateId }
    });

    if (!template) {
      return NextResponse.json(
        { success: false, error: 'Template not found' },
        { status: 404 }
      );
    }

    // Validate resume data structure
    if (!resumeData || typeof resumeData !== 'object') {
      return NextResponse.json(
        { success: false, error: 'Invalid resume data provided' },
        { status: 400 }
      );
    }

    // Merge provided data with sample data for missing fields
    const mergedData: ResumeData = {
      ...SAMPLE_RESUME_DATA,
      ...resumeData,
      personalInfo: {
        ...SAMPLE_RESUME_DATA.personalInfo,
        ...resumeData.personalInfo
      }
    };

    return NextResponse.json({
      success: true,
      data: {
        template: {
          id: template.id,
          name: template.name,
          category: template.category,
          styles: template.styles
        },
        resumeData: mergedData,
        previewGenerated: true,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error generating custom template preview:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate custom template preview',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
