// API endpoint for individual template operations
// Handles template retrieval, updates, and deletion

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { templateRegistry } from '@/lib/templates/template-registry';

interface RouteParams {
  params: {
    templateId: string;
  };
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { templateId } = params;

    // Get template from database
    const template = await prisma.resumeTemplate.findUnique({
      where: { id: templateId }
    });

    if (!template) {
      return NextResponse.json(
        { success: false, error: 'Template not found' },
        { status: 404 }
      );
    }

    // Check if template component is available
    const registryTemplate = templateRegistry.getTemplate(templateId);
    const isComponentAvailable = templateRegistry.isTemplateAvailable(templateId);

    const enhancedTemplate = {
      ...template,
      isAvailable: isComponentAvailable,
      component: registryTemplate ? 'available' : 'not-loaded',
      metadata: {
        ...template.styles,
        dbId: template.id,
        lastUpdated: template.updatedAt,
        registryInfo: registryTemplate ? {
          componentLoaded: true,
          metadata: registryTemplate.metadata
        } : {
          componentLoaded: false,
          reason: 'Component not registered in template registry'
        }
      }
    };

    return NextResponse.json({
      success: true,
      data: enhancedTemplate
    });

  } catch (error) {
    console.error('Error fetching template:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch template',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    
    // Only allow admin users to update templates
    if (!session?.user?.email || !isAdminUser(session.user.email)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { templateId } = params;
    const body = await request.json();

    // Check if template exists
    const existingTemplate = await prisma.resumeTemplate.findUnique({
      where: { id: templateId }
    });

    if (!existingTemplate) {
      return NextResponse.json(
        { success: false, error: 'Template not found' },
        { status: 404 }
      );
    }

    // Update template
    const updatedTemplate = await prisma.resumeTemplate.update({
      where: { id: templateId },
      data: {
        ...body,
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedTemplate
    });

  } catch (error) {
    console.error('Error updating template:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update template',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    
    // Only allow admin users to delete templates
    if (!session?.user?.email || !isAdminUser(session.user.email)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { templateId } = params;

    // Check if template exists
    const existingTemplate = await prisma.resumeTemplate.findUnique({
      where: { id: templateId },
      include: {
        _count: {
          select: { resumes: true }
        }
      }
    });

    if (!existingTemplate) {
      return NextResponse.json(
        { success: false, error: 'Template not found' },
        { status: 404 }
      );
    }

    // Check if template is being used by any resumes
    if (existingTemplate._count.resumes > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Cannot delete template that is being used by existing resumes',
          details: `Template is used by ${existingTemplate._count.resumes} resume(s)`
        },
        { status: 400 }
      );
    }

    // Delete template
    await prisma.resumeTemplate.delete({
      where: { id: templateId }
    });

    return NextResponse.json({
      success: true,
      message: 'Template deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting template:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete template',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Helper function to check if user is admin
function isAdminUser(email: string): boolean {
  const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
  return adminEmails.includes(email);
}
