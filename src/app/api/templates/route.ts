// API endpoint for resume templates
// Handles template retrieval, filtering, and management

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { templateRegistry } from '@/lib/templates/template-registry';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const premium = searchParams.get('premium');
    const atsOptimized = searchParams.get('atsOptimized');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build filter conditions
    const where: any = {};
    
    if (category && category !== 'all') {
      where.category = category;
    }
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    if (premium !== null) {
      where.isPremium = premium === 'true';
    }
    
    if (atsOptimized !== null) {
      where.atsOptimized = atsOptimized === 'true';
    }

    // Get templates from database
    const [templates, totalCount] = await Promise.all([
      prisma.resumeTemplate.findMany({
        where,
        take: limit,
        skip: offset,
        orderBy: [
          { isPremium: 'asc' }, // Free templates first
          { name: 'asc' }
        ]
      }),
      prisma.resumeTemplate.count({ where })
    ]);

    // Enhance templates with registry information
    const enhancedTemplates = templates.map(template => {
      const registryTemplate = templateRegistry.getTemplate(template.id);
      return {
        ...template,
        isAvailable: templateRegistry.isTemplateAvailable(template.id),
        component: registryTemplate ? 'available' : 'not-loaded',
        metadata: {
          ...template.styles,
          dbId: template.id,
          lastUpdated: template.updatedAt
        }
      };
    });

    return NextResponse.json({
      success: true,
      data: {
        templates: enhancedTemplates,
        pagination: {
          total: totalCount,
          limit,
          offset,
          hasMore: offset + limit < totalCount
        },
        filters: {
          category,
          search,
          premium,
          atsOptimized
        }
      }
    });

  } catch (error) {
    console.error('Error fetching templates:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch templates',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Only allow admin users to create templates
    if (!session?.user?.email || !isAdminUser(session.user.email)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      name,
      category,
      description,
      preview,
      isPremium = false,
      atsOptimized = true,
      styles
    } = body;

    // Validate required fields
    if (!name || !category || !description || !styles) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields: name, category, description, styles' 
        },
        { status: 400 }
      );
    }

    // Create template in database
    const template = await prisma.resumeTemplate.create({
      data: {
        name,
        category,
        description,
        preview: preview || `/api/templates/${name.toLowerCase().replace(/\s+/g, '-')}/preview`,
        isPremium,
        atsOptimized,
        styles
      }
    });

    return NextResponse.json({
      success: true,
      data: template
    });

  } catch (error) {
    console.error('Error creating template:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create template',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Helper function to check if user is admin
function isAdminUser(email: string): boolean {
  const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
  return adminEmails.includes(email);
}

// GET /api/templates/categories - Get template categories with counts
export async function getTemplateCategories() {
  try {
    const categories = await prisma.resumeTemplate.groupBy({
      by: ['category'],
      _count: { category: true },
      orderBy: { category: 'asc' }
    });

    const totalCount = await prisma.resumeTemplate.count();

    const categoriesWithCounts = [
      { id: 'all', name: 'All Templates', count: totalCount },
      ...categories.map(cat => ({
        id: cat.category,
        name: cat.category.charAt(0).toUpperCase() + cat.category.slice(1),
        count: cat._count.category
      }))
    ];

    return NextResponse.json({
      success: true,
      data: categoriesWithCounts
    });

  } catch (error) {
    console.error('Error fetching template categories:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch template categories',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
