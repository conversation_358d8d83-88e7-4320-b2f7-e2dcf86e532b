import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { hashPassword, checkRateLimit, createVerificationToken } from '@/lib/auth/utils';
import { registerSchema } from '@/lib/validations';
import { sendVerificationEmail } from '@/lib/email';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';

    // Rate limiting
    if (!checkRateLimit(`register:${clientIP}`, 3, 15 * 60 * 1000)) {
      return NextResponse.json(
        { error: 'Too many registration attempts. Please try again later.' },
        { status: 429 }
      );
    }

    // Validate input
    const validation = registerSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { email, password, name, acceptTerms } = validation.data;

    if (!acceptTerms) {
      return NextResponse.json(
        { error: 'You must accept the terms and conditions' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      );
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create user
    const user = await prisma.user.create({
      data: {
        email,
        name,
        password: hashedPassword,
      },
    });

    // Create user profile
    await prisma.userProfile.create({
      data: {
        userId: user.id,
      },
    });

    // Create user analytics
    await prisma.userAnalytics.create({
      data: {
        userId: user.id,
        totalApplications: 0,
        responseRate: 0,
        interviewRate: 0,
        offerRate: 0,
        topSkills: [],
        industryBreakdown: {},
        monthlyStats: [],
      },
    });

    // Create email verification token
    const verificationToken = await createVerificationToken(email);

    // Send verification email
    try {
      await sendVerificationEmail(email, verificationToken);
    } catch (emailError) {
      console.error('Failed to send verification email:', emailError);
      // Don't fail registration if email fails
    }

    // Log registration event
    await prisma.agentInteraction.create({
      data: {
        userId: user.id,
        agentType: 'ANALYTICS',
        prompt: `User registration request for ${email}`,
        response: `Successfully created user account for ${email}`,
        context: {
          action: 'REGISTER',
          input: { email, name },
          output: { success: true, userId: user.id },
          metadata: {
            userAgent: request.headers.get('user-agent') || '',
            ip: clientIP,
          },
        },
        success: true,
      },
    });

    return NextResponse.json(
      {
        message: 'User created successfully. Please check your email for verification.',
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
