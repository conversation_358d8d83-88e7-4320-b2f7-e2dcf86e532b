import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { verifyEmailToken } from '@/lib/auth/utils';

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { error: 'Verification token is required' },
        { status: 400 }
      );
    }

    // Verify the token
    const email = await verifyEmailToken(token);

    if (!email) {
      return NextResponse.json(
        { error: 'Invalid or expired verification token' },
        { status: 400 }
      );
    }

    // Update user email verification status
    const user = await prisma.user.update({
      where: { email },
      data: { emailVerified: new Date() },
    });

    // Log verification event
    await prisma.agentInteraction.create({
      data: {
        userId: user.id,
        agentType: 'AUTHENTICATION',
        action: 'EMAIL_VERIFIED',
        input: { email },
        output: { success: true },
        metadata: {
          userAgent: request.headers.get('user-agent') || '',
          ip: request.headers.get('x-forwarded-for') || 'unknown',
        },
      },
    });

    return NextResponse.json({
      message: 'Email verified successfully',
      user: {
        id: user.id,
        email: user.email,
        emailVerified: user.emailVerified,
      },
    });
  } catch (error) {
    console.error('Email verification error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
