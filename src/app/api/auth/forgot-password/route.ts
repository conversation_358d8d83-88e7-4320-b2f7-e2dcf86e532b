import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { createPasswordResetToken, checkRateLimit } from '@/lib/auth/utils';
import { sendPasswordResetEmail } from '@/lib/email';
import { z } from 'zod';

const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';

    // Rate limiting
    if (!checkRateLimit(`forgot-password:${clientIP}`, 3, 15 * 60 * 1000)) {
      return NextResponse.json(
        { error: 'Too many password reset attempts. Please try again later.' },
        { status: 429 }
      );
    }

    // Validate input
    const validation = forgotPasswordSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid email address' },
        { status: 400 }
      );
    }

    const { email } = validation.data;

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email },
    });

    // Always return success to prevent email enumeration
    if (!user) {
      return NextResponse.json({
        message: 'If an account with that email exists, we have sent a password reset link.',
      });
    }

    try {
      // Create password reset token
      const resetToken = await createPasswordResetToken(email);

      // Send password reset email
      await sendPasswordResetEmail(email, resetToken);

      // Log password reset request
      await prisma.agentInteraction.create({
        data: {
          userId: user.id,
          agentType: 'AUTHENTICATION',
          action: 'PASSWORD_RESET_REQUESTED',
          input: { email },
          output: { success: true },
          metadata: {
            userAgent: request.headers.get('user-agent') || '',
            ip: clientIP,
          },
        },
      });
    } catch (error) {
      console.error('Password reset error:', error);
      // Still return success to prevent information disclosure
    }

    return NextResponse.json({
      message: 'If an account with that email exists, we have sent a password reset link.',
    });
  } catch (error) {
    console.error('Forgot password error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
