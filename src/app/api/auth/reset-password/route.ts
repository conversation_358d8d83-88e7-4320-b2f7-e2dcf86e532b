import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { resetUserPassword, checkRateLimit } from '@/lib/auth/utils';
import { z } from 'zod';

const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';

    // Rate limiting
    if (!checkRateLimit(`reset-password:${clientIP}`, 5, 15 * 60 * 1000)) {
      return NextResponse.json(
        { error: 'Too many password reset attempts. Please try again later.' },
        { status: 429 }
      );
    }

    // Validate input
    const validation = resetPasswordSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { token, password } = validation.data;

    // Reset password
    const user = await resetUserPassword(token, password);

    // Log password reset completion
    await prisma.agentInteraction.create({
      data: {
        userId: user.id,
        agentType: 'AUTHENTICATION',
        action: 'PASSWORD_RESET_COMPLETED',
        input: { hasToken: true },
        output: { success: true },
        metadata: {
          userAgent: request.headers.get('user-agent') || '',
          ip: clientIP,
        },
      },
    });

    return NextResponse.json({
      message: 'Password reset successfully. You can now sign in with your new password.',
    });
  } catch (error) {
    console.error('Reset password error:', error);
    
    if (error instanceof Error && error.message === 'Invalid or expired reset token') {
      return NextResponse.json(
        { error: 'Invalid or expired reset token' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
