import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { STRIPE_PLANS } from '@/lib/stripe';

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's subscription
    const subscription = await prisma.subscription.findUnique({
      where: { userId: session.user.id },
    });

    // Default to FREE plan if no subscription
    const plan = subscription?.plan || 'FREE';
    const planConfig = STRIPE_PLANS[plan];

    const subscriptionStatus = {
      plan,
      status: subscription?.status || 'ACTIVE',
      features: planConfig.features,
      limits: planConfig.limits,
      currentPeriodEnd: subscription?.currentPeriodEnd,
      cancelAtPeriodEnd: subscription?.cancelAtPeriodEnd || false,
      isActive: subscription?.status === 'ACTIVE' || plan === 'FREE',
      canUpgrade: plan !== 'ENTERPRISE',
      canDowngrade: plan !== 'FREE',
    };

    return NextResponse.json(subscriptionStatus);
  } catch (error) {
    console.error('Failed to get subscription status:', error);
    return NextResponse.json(
      { error: 'Failed to get subscription status' },
      { status: 500 }
    );
  }
}
