'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, Zap, Crown, Building } from 'lucide-react';
import { STRIPE_PLANS } from '@/lib/stripe';

export default function PricingPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState<string | null>(null);

  const handleSubscribe = async (plan: 'PRO' | 'ENTERPRISE') => {
    if (!session) {
      router.push('/auth/signin');
      return;
    }

    setIsLoading(plan);

    try {
      const response = await fetch('/api/subscription/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ plan }),
      });

      const data = await response.json();

      if (data.url) {
        window.location.href = data.url;
      } else {
        throw new Error('No checkout URL returned');
      }
    } catch (error) {
      console.error('Failed to create checkout session:', error);
      setIsLoading(null);
    }
  };

  const plans = [
    {
      key: 'FREE' as const,
      name: STRIPE_PLANS.FREE.name,
      description: STRIPE_PLANS.FREE.description,
      price: STRIPE_PLANS.FREE.price,
      features: STRIPE_PLANS.FREE.features,
      icon: <Zap className="h-6 w-6" />,
      popular: false,
      buttonText: 'Get Started',
      buttonAction: () => router.push('/auth/signin'),
    },
    {
      key: 'PRO' as const,
      name: STRIPE_PLANS.PRO.name,
      description: STRIPE_PLANS.PRO.description,
      price: STRIPE_PLANS.PRO.price,
      features: STRIPE_PLANS.PRO.features,
      icon: <Crown className="h-6 w-6" />,
      popular: true,
      buttonText: 'Start Pro Trial',
      buttonAction: () => handleSubscribe('PRO'),
    },
    {
      key: 'ENTERPRISE' as const,
      name: STRIPE_PLANS.ENTERPRISE.name,
      description: STRIPE_PLANS.ENTERPRISE.description,
      price: STRIPE_PLANS.ENTERPRISE.price,
      features: STRIPE_PLANS.ENTERPRISE.features,
      icon: <Building className="h-6 w-6" />,
      popular: false,
      buttonText: 'Contact Sales',
      buttonAction: () => router.push('/contact'),
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Choose Your Plan
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Start building professional resumes with our AI-powered platform. 
            Upgrade anytime to unlock premium features.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan) => (
            <Card 
              key={plan.key} 
              className={`relative ${
                plan.popular 
                  ? 'border-blue-500 shadow-lg scale-105' 
                  : 'border-gray-200'
              }`}
            >
              {plan.popular && (
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-blue-500">
                  Most Popular
                </Badge>
              )}
              
              <CardHeader className="text-center pb-4">
                <div className="flex justify-center mb-4">
                  <div className={`p-3 rounded-full ${
                    plan.popular 
                      ? 'bg-blue-100 text-blue-600' 
                      : 'bg-gray-100 text-gray-600'
                  }`}>
                    {plan.icon}
                  </div>
                </div>
                <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
                <CardDescription className="text-gray-600">
                  {plan.description}
                </CardDescription>
                <div className="mt-4">
                  <span className="text-4xl font-bold text-gray-900">
                    ${plan.price}
                  </span>
                  {plan.price > 0 && (
                    <span className="text-gray-600">/month</span>
                  )}
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                <ul className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button
                  className={`w-full ${
                    plan.popular
                      ? 'bg-blue-600 hover:bg-blue-700'
                      : 'bg-gray-900 hover:bg-gray-800'
                  }`}
                  onClick={plan.buttonAction}
                  disabled={isLoading === plan.key}
                >
                  {isLoading === plan.key ? 'Loading...' : plan.buttonText}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-16">
          <p className="text-gray-600 mb-4">
            All plans include our core resume building features
          </p>
          <div className="flex justify-center gap-8 text-sm text-gray-500">
            <span>✓ Secure & Private</span>
            <span>✓ 24/7 Support</span>
            <span>✓ Cancel Anytime</span>
          </div>
        </div>
      </div>
    </div>
  );
}
