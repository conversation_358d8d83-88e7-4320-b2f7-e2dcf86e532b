import { Metadata } from 'next';
import { getServerSession } from 'next-auth/next';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth/config';
import LinkedInProfileOptimizer from '@/components/linkedin/linkedin-profile-optimizer';

export const metadata: Metadata = {
  title: 'LinkedIn Profile Optimization | CVLeap',
  description: 'Optimize your LinkedIn profile with AI-powered suggestions to attract recruiters and opportunities.',
};

export default async function LinkedInOptimizationPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/signin');
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                LinkedIn Profile Optimization
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Enhance your professional presence and attract more opportunities
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <LinkedInProfileOptimizer />
      </main>
    </div>
  );
}
