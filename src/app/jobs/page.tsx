import { Metadata } from 'next';
import { getServerSession } from 'next-auth/next';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth/config';
import JobSearchInterface from '@/components/jobs/job-search-interface';

export const metadata: Metadata = {
  title: 'Job Search | CVLeap',
  description: 'Find your dream job with AI-powered job search and matching.',
};

export default async function JobSearchPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/signin');
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Job Search
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Discover opportunities that match your skills and experience
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <JobSearchInterface />
      </main>
    </div>
  );
}
