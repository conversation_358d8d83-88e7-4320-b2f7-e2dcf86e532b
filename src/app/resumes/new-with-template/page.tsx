'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  ArrowLeft, 
  ArrowRight, 
  FileText, 
  Briefcase, 
  User,
  Palette,
  CheckCircle,
  Eye
} from 'lucide-react';
import TemplateSelector from '@/components/resume/TemplateSelector';
import TemplatePreview, { FullscreenTemplatePreview } from '@/components/resume/TemplatePreview';
import { ResumeData } from '@/lib/templates/template-registry';
import { templateRegistry } from '@/lib/templates/template-registry';

interface ResumeFormData {
  // Template selection
  selectedTemplateId: string;
  
  // Basic Info
  jobTitle: string;
  jobDescription: string;
  companyName: string;
  targetIndustry: string;
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
  
  // Personal Details
  fullName: string;
  email: string;
  phone: string;
  location: string;
  linkedinUrl: string;
  portfolioUrl: string;
  
  // Professional Summary
  summary: string;
  
  // Skills
  skills: string[];
  
  // Custom Instructions
  customInstructions: string;
}

const STEPS = [
  { id: 'template', title: 'Choose Template', icon: Palette },
  { id: 'target', title: 'Target Position', icon: Briefcase },
  { id: 'personal', title: 'Personal Info', icon: User },
  { id: 'preview', title: 'Preview & Create', icon: Eye },
];

export default function NewResumeWithTemplatePage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [previewTemplateId, setPreviewTemplateId] = useState<string | null>(null);
  const [showFullscreenPreview, setShowFullscreenPreview] = useState(false);

  const [formData, setFormData] = useState<ResumeFormData>({
    selectedTemplateId: '',
    jobTitle: '',
    jobDescription: '',
    companyName: '',
    targetIndustry: '',
    experienceLevel: 'mid',
    fullName: session?.user?.name || '',
    email: session?.user?.email || '',
    phone: '',
    location: '',
    linkedinUrl: '',
    portfolioUrl: '',
    summary: '',
    skills: [],
    customInstructions: '',
  });

  const updateFormData = (updates: Partial<ResumeFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const addSkill = (skill: string) => {
    if (skill.trim() && !formData.skills.includes(skill.trim())) {
      updateFormData({ skills: [...formData.skills, skill.trim()] });
    }
  };

  const removeSkill = (skillToRemove: string) => {
    updateFormData({ skills: formData.skills.filter(skill => skill !== skillToRemove) });
  };

  const nextStep = () => {
    if (currentStep < STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0: // Template Selection
        return formData.selectedTemplateId.length > 0;
      case 1: // Target Position
        return formData.jobTitle.trim().length > 0;
      case 2: // Personal Info
        return formData.fullName.trim().length > 0 && formData.email.trim().length > 0;
      case 3: // Preview & Create
        return true;
      default:
        return false;
    }
  };

  const createResume = async () => {
    setIsCreating(true);
    setError(null);

    try {
      // Convert form data to resume data format
      const resumeData: ResumeData = {
        personalInfo: {
          name: formData.fullName,
          jobTitle: formData.jobTitle,
          email: formData.email,
          phone: formData.phone,
          location: formData.location,
          linkedin: formData.linkedinUrl,
        },
        summary: formData.summary,
        experience: [], // Will be populated by AI or user input later
        education: [], // Will be populated by AI or user input later
        skills: formData.skills,
        projects: [],
      };

      const response = await fetch('/api/resumes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          templateId: formData.selectedTemplateId,
          data: resumeData,
          jobTitle: formData.jobTitle,
          jobDescription: formData.jobDescription,
          companyName: formData.companyName,
          targetIndustry: formData.targetIndustry,
          experienceLevel: formData.experienceLevel,
          customInstructions: formData.customInstructions,
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Redirect to the edit page for the new resume
        router.push(`/resumes/${result.data.resumeId}/edit`);
      } else {
        setError(result.error || 'Failed to create resume');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setIsCreating(false);
    }
  };

  const handleTemplateSelect = (templateId: string) => {
    updateFormData({ selectedTemplateId: templateId });
  };

  const handleTemplatePreview = (templateId: string) => {
    setPreviewTemplateId(templateId);
    setShowFullscreenPreview(true);
  };

  const generateSampleResumeData = (): ResumeData => {
    return {
      personalInfo: {
        name: formData.fullName || 'John Doe',
        jobTitle: formData.jobTitle || 'Software Engineer',
        email: formData.email || '<EMAIL>',
        phone: formData.phone || '(*************',
        location: formData.location || 'San Francisco, CA',
        linkedin: formData.linkedinUrl,
      },
      summary: formData.summary || 'Experienced software engineer with a passion for creating innovative solutions and leading development teams.',
      experience: [
        {
          company: 'Tech Corp',
          jobTitle: 'Senior Software Engineer',
          dates: '2021 - Present',
          description: 'Led development of scalable web applications using React and Node.js. Managed a team of 5 developers and improved system performance by 40%.',
        },
        {
          company: 'StartupXYZ',
          jobTitle: 'Software Engineer',
          dates: '2019 - 2021',
          description: 'Developed full-stack applications and implemented CI/CD pipelines. Collaborated with cross-functional teams to deliver high-quality products.',
        }
      ],
      education: [
        {
          degree: 'Bachelor of Science in Computer Science',
          institution: 'University of Technology',
          dates: '2015 - 2019',
        }
      ],
      skills: formData.skills.length > 0 ? formData.skills : ['JavaScript', 'React', 'Node.js', 'Python', 'AWS'],
      projects: [
        {
          name: 'E-commerce Platform',
          description: 'Built a full-stack e-commerce platform with React, Node.js, and MongoDB. Implemented payment processing and inventory management.',
        }
      ],
    };
  };

  const progress = ((currentStep + 1) / STEPS.length) * 100;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" onClick={() => router.back()}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Create Resume with Template
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Step {currentStep + 1} of {STEPS.length}: {STEPS[currentStep].title}
                </p>
              </div>
            </div>
            <div className="text-sm text-gray-500">
              {Math.round(progress)}% Complete
            </div>
          </div>
          
          {/* Progress Bar */}
          <Progress value={progress} className="mb-6" />
          
          {/* Step Indicators */}
          <div className="flex justify-between mb-6">
            {STEPS.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === currentStep;
              const isCompleted = index < currentStep;
              
              return (
                <div key={step.id} className="flex flex-col items-center">
                  <div className={`
                    w-10 h-10 rounded-full flex items-center justify-center border-2 transition-colors
                    ${isActive 
                      ? 'bg-blue-600 border-blue-600 text-white' 
                      : isCompleted 
                        ? 'bg-green-600 border-green-600 text-white'
                        : 'bg-gray-200 border-gray-300 text-gray-500'
                    }
                  `}>
                    {isCompleted ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>
                  <span className={`
                    text-xs mt-2 text-center
                    ${isActive ? 'text-blue-600 font-medium' : 'text-gray-500'}
                  `}>
                    {step.title}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Step 0: Template Selection */}
        {currentStep === 0 && (
          <TemplateSelector
            selectedTemplateId={formData.selectedTemplateId}
            onTemplateSelect={handleTemplateSelect}
            onPreview={handleTemplatePreview}
          />
        )}

        {/* Steps 1-3: Form Content */}
        {currentStep > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Form Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {React.createElement(STEPS[currentStep].icon, { className: "h-5 w-5" })}
                  {STEPS[currentStep].title}
                </CardTitle>
                <CardDescription>
                  {currentStep === 1 && "Tell us about the position you're targeting"}
                  {currentStep === 2 && "Provide your contact information"}
                  {currentStep === 3 && "Review your resume and create it"}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-6">
                {/* Form content will be added here */}
                {/* This would include the same form fields as the original page */}
                <div className="text-center py-8">
                  <p className="text-gray-500">Form content for step {currentStep + 1} will be implemented here</p>
                </div>

                {/* Navigation Buttons */}
                <div className="flex justify-between pt-6 border-t">
                  <Button
                    variant="outline"
                    onClick={prevStep}
                    disabled={currentStep === 0}
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Previous
                  </Button>

                  {currentStep < STEPS.length - 1 ? (
                    <Button
                      onClick={nextStep}
                      disabled={!canProceed()}
                    >
                      Next
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  ) : (
                    <Button
                      onClick={createResume}
                      disabled={isCreating || !canProceed()}
                    >
                      {isCreating ? 'Creating...' : 'Create Resume'}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Preview Section */}
            {formData.selectedTemplateId && (
              <div className="lg:sticky lg:top-8">
                <Card>
                  <CardHeader>
                    <CardTitle>Live Preview</CardTitle>
                    <CardDescription>
                      See how your resume will look with the selected template
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <TemplatePreview
                      templateId={formData.selectedTemplateId}
                      resumeData={generateSampleResumeData()}
                      className="h-96"
                    />
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        )}
      </main>

      {/* Fullscreen Preview Modal */}
      {previewTemplateId && (
        <FullscreenTemplatePreview
          templateId={previewTemplateId}
          resumeData={generateSampleResumeData()}
          isOpen={showFullscreenPreview}
          onClose={() => {
            setShowFullscreenPreview(false);
            setPreviewTemplateId(null);
          }}
          onSelect={() => {
            handleTemplateSelect(previewTemplateId);
            setShowFullscreenPreview(false);
            setPreviewTemplateId(null);
            nextStep();
          }}
        />
      )}
    </div>
  );
}
