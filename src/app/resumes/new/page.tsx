'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  ArrowLeft, 
  ArrowRight, 
  Wand2, 
  FileText, 
  Briefcase, 
  User,
  GraduationCap,
  Award,
  Code,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface ResumeFormData {
  // Basic Info
  jobTitle: string;
  jobDescription: string;
  companyName: string;
  targetIndustry: string;
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
  resumeType: 'chronological' | 'functional' | 'combination';
  
  // Personal Details
  fullName: string;
  email: string;
  phone: string;
  location: string;
  linkedinUrl: string;
  portfolioUrl: string;
  
  // Professional Summary
  summary: string;
  
  // Skills
  skills: string[];
  
  // Custom Instructions
  customInstructions: string;
}

const STEPS = [
  { id: 'target', title: 'Target Position', icon: Briefcase },
  { id: 'personal', title: 'Personal Info', icon: User },
  { id: 'summary', title: 'Summary & Skills', icon: FileText },
  { id: 'generate', title: 'Generate Resume', icon: Wand2 },
];

export default function NewResumePage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationResult, setGenerationResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState<ResumeFormData>({
    jobTitle: '',
    jobDescription: '',
    companyName: '',
    targetIndustry: '',
    experienceLevel: 'mid',
    resumeType: 'chronological',
    fullName: session?.user?.name || '',
    email: session?.user?.email || '',
    phone: '',
    location: '',
    linkedinUrl: '',
    portfolioUrl: '',
    summary: '',
    skills: [],
    customInstructions: '',
  });

  const updateFormData = (updates: Partial<ResumeFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const addSkill = (skill: string) => {
    if (skill.trim() && !formData.skills.includes(skill.trim())) {
      updateFormData({ skills: [...formData.skills, skill.trim()] });
    }
  };

  const removeSkill = (skillToRemove: string) => {
    updateFormData({ skills: formData.skills.filter(skill => skill !== skillToRemove) });
  };

  const nextStep = () => {
    if (currentStep < STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const generateResume = async () => {
    setIsGenerating(true);
    setError(null);

    try {
      const response = await fetch('/api/agents/resume/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jobTitle: formData.jobTitle,
          jobDescription: formData.jobDescription,
          companyName: formData.companyName,
          targetIndustry: formData.targetIndustry,
          experienceLevel: formData.experienceLevel,
          resumeType: formData.resumeType,
          includeSkills: formData.skills,
          customInstructions: formData.customInstructions,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setGenerationResult(result);
        // Redirect to the edit page for the new resume
        router.push(`/resumes/${result.data.resumeId}/edit`);
      } else {
        setError(result.error || 'Failed to generate resume');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0: // Target Position
        return formData.jobTitle.trim().length > 0;
      case 1: // Personal Info
        return formData.fullName.trim().length > 0 && formData.email.trim().length > 0;
      case 2: // Summary & Skills
        return true; // Optional step
      case 3: // Generate
        return true;
      default:
        return false;
    }
  };

  const progress = ((currentStep + 1) / STEPS.length) * 100;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" onClick={() => router.back()}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Create New Resume
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Step {currentStep + 1} of {STEPS.length}: {STEPS[currentStep].title}
                </p>
              </div>
            </div>
            <div className="text-sm text-gray-500">
              {Math.round(progress)}% Complete
            </div>
          </div>
          
          {/* Progress Bar */}
          <Progress value={progress} className="mb-6" />
          
          {/* Step Indicators */}
          <div className="flex justify-between mb-6">
            {STEPS.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === currentStep;
              const isCompleted = index < currentStep;
              
              return (
                <div key={step.id} className="flex flex-col items-center">
                  <div className={`
                    w-10 h-10 rounded-full flex items-center justify-center border-2 transition-colors
                    ${isActive 
                      ? 'bg-blue-600 border-blue-600 text-white' 
                      : isCompleted 
                        ? 'bg-green-600 border-green-600 text-white'
                        : 'bg-gray-200 border-gray-300 text-gray-500'
                    }
                  `}>
                    {isCompleted ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>
                  <span className={`
                    text-xs mt-2 text-center
                    ${isActive ? 'text-blue-600 font-medium' : 'text-gray-500'}
                  `}>
                    {step.title}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {React.createElement(STEPS[currentStep].icon, { className: "h-5 w-5" })}
              {STEPS[currentStep].title}
            </CardTitle>
            <CardDescription>
              {currentStep === 0 && "Tell us about the position you're targeting"}
              {currentStep === 1 && "Provide your contact information"}
              {currentStep === 2 && "Add your professional summary and key skills"}
              {currentStep === 3 && "Generate your AI-powered resume"}
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Step 0: Target Position */}
            {currentStep === 0 && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="jobTitle">Job Title *</Label>
                    <Input
                      id="jobTitle"
                      value={formData.jobTitle}
                      onChange={(e) => updateFormData({ jobTitle: e.target.value })}
                      placeholder="e.g., Senior Software Engineer"
                    />
                  </div>
                  <div>
                    <Label htmlFor="companyName">Target Company (Optional)</Label>
                    <Input
                      id="companyName"
                      value={formData.companyName}
                      onChange={(e) => updateFormData({ companyName: e.target.value })}
                      placeholder="e.g., Google, Microsoft"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="experienceLevel">Experience Level</Label>
                    <Select
                      value={formData.experienceLevel}
                      onValueChange={(value: any) => updateFormData({ experienceLevel: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="entry">Entry Level (0-2 years)</SelectItem>
                        <SelectItem value="mid">Mid Level (3-5 years)</SelectItem>
                        <SelectItem value="senior">Senior Level (6-10 years)</SelectItem>
                        <SelectItem value="executive">Executive (10+ years)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="resumeType">Resume Type</Label>
                    <Select
                      value={formData.resumeType}
                      onValueChange={(value: any) => updateFormData({ resumeType: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="chronological">Chronological</SelectItem>
                        <SelectItem value="functional">Functional</SelectItem>
                        <SelectItem value="combination">Combination</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="targetIndustry">Target Industry (Optional)</Label>
                  <Input
                    id="targetIndustry"
                    value={formData.targetIndustry}
                    onChange={(e) => updateFormData({ targetIndustry: e.target.value })}
                    placeholder="e.g., Technology, Healthcare, Finance"
                  />
                </div>

                <div>
                  <Label htmlFor="jobDescription">Job Description (Optional)</Label>
                  <Textarea
                    id="jobDescription"
                    value={formData.jobDescription}
                    onChange={(e) => updateFormData({ jobDescription: e.target.value })}
                    placeholder="Paste the job description here for better optimization..."
                    rows={4}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Adding a job description helps our AI create a more targeted resume
                  </p>
                </div>
              </div>
            )}

            {/* Step 1: Personal Info */}
            {currentStep === 1 && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="fullName">Full Name *</Label>
                    <Input
                      id="fullName"
                      value={formData.fullName}
                      onChange={(e) => updateFormData({ fullName: e.target.value })}
                      placeholder="John Doe"
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => updateFormData({ email: e.target.value })}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => updateFormData({ phone: e.target.value })}
                      placeholder="+****************"
                    />
                  </div>
                  <div>
                    <Label htmlFor="location">Location</Label>
                    <Input
                      id="location"
                      value={formData.location}
                      onChange={(e) => updateFormData({ location: e.target.value })}
                      placeholder="San Francisco, CA"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="linkedinUrl">LinkedIn Profile</Label>
                    <Input
                      id="linkedinUrl"
                      value={formData.linkedinUrl}
                      onChange={(e) => updateFormData({ linkedinUrl: e.target.value })}
                      placeholder="https://linkedin.com/in/johndoe"
                    />
                  </div>
                  <div>
                    <Label htmlFor="portfolioUrl">Portfolio/Website</Label>
                    <Input
                      id="portfolioUrl"
                      value={formData.portfolioUrl}
                      onChange={(e) => updateFormData({ portfolioUrl: e.target.value })}
                      placeholder="https://johndoe.com"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Summary & Skills */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <div>
                  <Label htmlFor="summary">Professional Summary (Optional)</Label>
                  <Textarea
                    id="summary"
                    value={formData.summary}
                    onChange={(e) => updateFormData({ summary: e.target.value })}
                    placeholder="Brief overview of your professional background and key achievements..."
                    rows={4}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Leave blank to let AI generate a summary based on your profile
                  </p>
                </div>

                <div>
                  <Label>Key Skills</Label>
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <Input
                        placeholder="Add a skill and press Enter"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            addSkill(e.currentTarget.value);
                            e.currentTarget.value = '';
                          }
                        }}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={(e) => {
                          const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                          addSkill(input.value);
                          input.value = '';
                        }}
                      >
                        Add
                      </Button>
                    </div>
                    
                    {formData.skills.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {formData.skills.map((skill, index) => (
                          <Badge key={index} variant="secondary" className="cursor-pointer">
                            {skill}
                            <button
                              onClick={() => removeSkill(skill)}
                              className="ml-2 hover:text-red-500"
                            >
                              ×
                            </button>
                          </Badge>
                        ))}
                      </div>
                    )}
                    
                    <p className="text-xs text-gray-500">
                      Add relevant skills for your target position. AI will suggest additional skills based on the job description.
                    </p>
                  </div>
                </div>

                <div>
                  <Label htmlFor="customInstructions">Custom Instructions (Optional)</Label>
                  <Textarea
                    id="customInstructions"
                    value={formData.customInstructions}
                    onChange={(e) => updateFormData({ customInstructions: e.target.value })}
                    placeholder="Any specific requirements or preferences for your resume..."
                    rows={3}
                  />
                </div>
              </div>
            )}

            {/* Step 3: Generate Resume */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <div className="text-center">
                  <Wand2 className="h-16 w-16 text-blue-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-2">Ready to Generate Your Resume</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    Our AI will create a professional, ATS-optimized resume based on your information.
                  </p>
                </div>

                {error && (
                  <Alert className="border-red-200">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                  <h4 className="font-medium mb-2">Resume Summary:</h4>
                  <div className="text-sm space-y-1">
                    <div><strong>Position:</strong> {formData.jobTitle}</div>
                    {formData.companyName && <div><strong>Target Company:</strong> {formData.companyName}</div>}
                    <div><strong>Experience Level:</strong> {formData.experienceLevel}</div>
                    <div><strong>Resume Type:</strong> {formData.resumeType}</div>
                    {formData.skills.length > 0 && (
                      <div><strong>Skills:</strong> {formData.skills.slice(0, 5).join(', ')}{formData.skills.length > 5 ? '...' : ''}</div>
                    )}
                  </div>
                </div>

                <Button 
                  onClick={generateResume} 
                  disabled={isGenerating}
                  className="w-full"
                  size="lg"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Generating Your Resume...
                    </>
                  ) : (
                    <>
                      <Wand2 className="h-4 w-4 mr-2" />
                      Generate AI Resume
                    </>
                  )}
                </Button>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between pt-6 border-t">
              <Button
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 0}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>

              {currentStep < STEPS.length - 1 && (
                <Button
                  onClick={nextStep}
                  disabled={!canProceed()}
                >
                  Next
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
