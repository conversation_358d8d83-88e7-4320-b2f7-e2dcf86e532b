import { Metadata } from 'next';
import { getServerSession } from 'next-auth/next';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/db';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  Plus, 
  Edit, 
  Copy, 
  Download, 
  Trash2,
  Star,
  Clock,
  TrendingUp
} from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'My Resumes | CVLeap',
  description: 'Manage your AI-powered resumes and track their performance.',
};

async function getUserResumes(userId: string) {
  return await prisma.resume.findMany({
    where: { userId },
    orderBy: { updatedAt: 'desc' },
    include: {
      _count: {
        select: { jobApplications: true }
      }
    }
  });
}

export default async function ResumesPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/signin');
  }

  const resumes = await getUserResumes(session.user.id);

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  };

  const getATSScoreColor = (score: number) => {
    if (score >= 80) return 'bg-green-100 text-green-800';
    if (score >= 60) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                My Resumes
              </h1>
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
                Create, manage, and optimize your AI-powered resumes
              </p>
            </div>
            <Button asChild>
              <Link href="/resumes/new">
                <Plus className="h-4 w-4 mr-2" />
                Create New Resume
              </Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {resumes.length === 0 ? (
          /* Empty State */
          <div className="text-center py-12">
            <FileText className="h-24 w-24 text-gray-400 mx-auto mb-6" />
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
              No resumes yet
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-8 max-w-md mx-auto">
              Create your first AI-powered resume and start landing your dream job. 
              Our intelligent system will help you craft the perfect resume for any position.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="/resumes/new">
                  <Plus className="h-5 w-5 mr-2" />
                  Create Your First Resume
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/templates">
                  <FileText className="h-5 w-5 mr-2" />
                  Browse Templates
                </Link>
              </Button>
            </div>
          </div>
        ) : (
          /* Resume Grid */
          <div className="space-y-6">
            {/* Stats Overview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Resumes</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{resumes.length}</div>
                  <p className="text-xs text-muted-foreground">
                    {resumes.filter(r => r.updatedAt > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)).length} updated this week
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Average ATS Score</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {resumes.length > 0 
                      ? Math.round(resumes.reduce((acc, r) => acc + (r.atsScore || 0), 0) / resumes.length)
                      : 0
                    }%
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Across all resumes
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Applications Sent</CardTitle>
                  <Star className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {resumes.reduce((acc, r) => acc + (r._count?.jobApplications || 0), 0)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Total job applications
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Resume Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {resumes.map((resume) => (
                <Card key={resume.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg line-clamp-1">
                          {resume.title}
                        </CardTitle>
                        <CardDescription className="flex items-center gap-2 mt-1">
                          <Clock className="h-3 w-3" />
                          Updated {formatDate(resume.updatedAt)}
                        </CardDescription>
                      </div>
                      <Badge className={getATSScoreColor(resume.atsScore || 0)}>
                        ATS: {resume.atsScore || 0}%
                      </Badge>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <div className="space-y-4">
                      {/* Resume Preview/Stats */}
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        <div className="flex justify-between">
                          <span>Template:</span>
                          <span className="capitalize">{resume.templateId}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Applications:</span>
                          <span>{resume._count?.jobApplications || 0}</span>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex flex-wrap gap-2">
                        <Button size="sm" asChild className="flex-1">
                          <Link href={`/resumes/${resume.id}/edit`}>
                            <Edit className="h-3 w-3 mr-1" />
                            Edit
                          </Link>
                        </Button>
                        
                        <Button size="sm" variant="outline" asChild>
                          <Link href={`/resumes/${resume.id}/preview`}>
                            <FileText className="h-3 w-3 mr-1" />
                            Preview
                          </Link>
                        </Button>
                        
                        <Button size="sm" variant="outline">
                          <Copy className="h-3 w-3 mr-1" />
                          Clone
                        </Button>
                      </div>

                      <div className="flex gap-2">
                        <Button size="sm" variant="outline" className="flex-1">
                          <Download className="h-3 w-3 mr-1" />
                          Download
                        </Button>
                        
                        <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
