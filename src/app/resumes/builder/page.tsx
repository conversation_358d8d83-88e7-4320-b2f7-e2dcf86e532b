'use client';

import React, { useState, useEffect } from 'react';
import { Save, Download, <PERSON>, Set<PERSON>s, <PERSON>L<PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useRouter } from 'next/navigation';
import TemplateRenderer from '@/components/resume/TemplateRenderer';
import TemplateSwitcher from '@/components/resume/TemplateSwitcher';
import ResumeDataEditor from '@/components/resume/forms/ResumeDataEditor';
import CustomizationInterface from '@/components/resume/CustomizationInterface';
import AIContentPanel from '@/components/ai/AIContentPanel';
import { TEMPLATE_CATALOG } from '@/lib/templates/template-catalog';
import { ResumeContent } from '@/types';
import { TemplateCustomization } from '@/types/customization';

// Sample resume data for demonstration
const sampleResumeData: ResumeContent = {
  personalInfo: {
    fullName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    location: 'San Francisco, CA',
    website: 'https://alexjohnson.dev',
    linkedinUrl: 'https://linkedin.com/in/alexjohnson',
    githubUrl: 'https://github.com/alexjohnson'
  },
  summary: 'Experienced software engineer with 8+ years of expertise in full-stack development, cloud architecture, and team leadership. Proven track record of delivering scalable solutions and mentoring junior developers in fast-paced startup environments.',
  experience: [
    {
      id: '1',
      company: 'TechCorp Inc.',
      position: 'Senior Software Engineer',
      location: 'San Francisco, CA',
      startDate: new Date('2021-03-01'),
      endDate: undefined,
      current: true,
      description: [
        'Led development of microservices architecture serving 10M+ users',
        'Mentored team of 5 junior developers and improved code quality by 40%',
        'Implemented CI/CD pipelines reducing deployment time by 60%'
      ],
      skills: ['React', 'Node.js', 'AWS', 'Docker', 'Kubernetes']
    },
    {
      id: '2',
      company: 'StartupXYZ',
      position: 'Full Stack Developer',
      location: 'San Francisco, CA',
      startDate: new Date('2019-01-01'),
      endDate: new Date('2021-02-28'),
      current: false,
      description: [
        'Built responsive web applications using React and TypeScript',
        'Developed RESTful APIs with Node.js and PostgreSQL',
        'Collaborated with design team to implement pixel-perfect UIs'
      ],
      skills: ['React', 'TypeScript', 'Node.js', 'PostgreSQL']
    }
  ],
  education: [
    {
      id: '1',
      institution: 'University of California, Berkeley',
      degree: 'Bachelor of Science',
      field: 'Computer Science',
      location: 'Berkeley, CA',
      startDate: new Date('2015-08-01'),
      endDate: new Date('2019-05-01'),
      gpa: 3.8,
      achievements: ['Magna Cum Laude', 'Dean\'s List (6 semesters)']
    }
  ],
  skills: [
    'JavaScript', 'TypeScript', 'React', 'Node.js', 'Python', 'AWS', 
    'Docker', 'Kubernetes', 'PostgreSQL', 'MongoDB', 'Git', 'Agile'
  ],
  certifications: [
    {
      id: '1',
      name: 'AWS Certified Solutions Architect',
      issuer: 'Amazon Web Services',
      issueDate: new Date('2022-06-01'),
      expiryDate: new Date('2025-06-01'),
      credentialId: 'AWS-CSA-123456'
    }
  ],
  languages: [
    {
      id: '1',
      name: 'English',
      proficiency: 'Native'
    },
    {
      id: '2',
      name: 'Spanish',
      proficiency: 'Conversational'
    }
  ],
  projects: [
    {
      id: '1',
      name: 'E-commerce Platform',
      description: 'Built a full-stack e-commerce platform with React, Node.js, and Stripe integration',
      technologies: ['React', 'Node.js', 'Stripe', 'MongoDB'],
      url: 'github.com/alexjohnson/ecommerce',
      startDate: new Date('2022-01-01'),
      endDate: new Date('2022-06-01')
    }
  ]
};

export default function ResumeBuilderPage() {
  const router = useRouter();
  const [currentTemplateId, setCurrentTemplateId] = useState('modern-template');
  const [resumeData, setResumeData] = useState<ResumeContent>(sampleResumeData);
  const [activeTab, setActiveTab] = useState('template');
  const [previewMode, setPreviewMode] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [customization, setCustomization] = useState<TemplateCustomization | undefined>();

  const currentTemplate = TEMPLATE_CATALOG.find(t => t.id === currentTemplateId);

  useEffect(() => {
    // Load saved resume data from localStorage or API
    const savedData = localStorage.getItem('resumeBuilderData');
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData);
        setResumeData(parsed.resumeData || sampleResumeData);
        setCurrentTemplateId(parsed.templateId || 'modern-template');
      } catch (error) {
        console.error('Failed to load saved data:', error);
      }
    }
  }, []);

  const handleTemplateChange = (templateId: string) => {
    setCurrentTemplateId(templateId);
    // Auto-save template selection
    saveToLocalStorage({ resumeData, templateId });
  };

  const handlePreview = (templateId: string) => {
    setCurrentTemplateId(templateId);
    setPreviewMode(true);
  };

  const saveToLocalStorage = (data: { resumeData: ResumeContent; templateId: string }) => {
    try {
      localStorage.setItem('resumeBuilderData', JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save data:', error);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Save to localStorage for now (replace with API call)
      saveToLocalStorage({ resumeData, templateId: currentTemplateId });
      
      // TODO: Implement API save
      // await saveResume({ resumeData, templateId: currentTemplateId });
      
      console.log('Resume saved successfully');
    } catch (error) {
      console.error('Failed to save resume:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDownload = () => {
    // TODO: Implement PDF download functionality
    console.log('Download resume as PDF');
  };

  if (previewMode) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Preview Header */}
        <div className="bg-white border-b px-6 py-4">
          <div className="flex items-center justify-between max-w-7xl mx-auto">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => setPreviewMode(false)}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Editor
              </Button>
              <div>
                <h1 className="text-xl font-semibold">Template Preview</h1>
                <p className="text-sm text-gray-600">{currentTemplate?.name}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button onClick={handleSave} disabled={isSaving}>
                <Save className="h-4 w-4 mr-2" />
                {isSaving ? 'Saving...' : 'Save'}
              </Button>
              <Button onClick={handleDownload}>
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </Button>
            </div>
          </div>
        </div>

        {/* Preview Content */}
        <div className="p-6">
          <div className="max-w-4xl mx-auto">
            <TemplateRenderer
              templateId={currentTemplateId}
              resumeData={resumeData}
              showMetadata={true}
              className="shadow-2xl"
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => router.push('/resumes')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Resumes
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Resume Builder</h1>
              <p className="text-sm text-gray-600">
                Create your professional resume with {TEMPLATE_CATALOG.length} templates
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="flex items-center gap-1">
              <Palette className="h-3 w-3" />
              {currentTemplate?.name}
            </Badge>
            <Button
              variant="outline"
              onClick={() => setPreviewMode(true)}
              className="flex items-center gap-2"
            >
              <Eye className="h-4 w-4" />
              Preview
            </Button>
            <Button onClick={handleSave} disabled={isSaving}>
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? 'Saving...' : 'Save'}
            </Button>
            <Button onClick={handleDownload}>
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex h-[calc(100vh-80px)]">
        {/* Left Panel - Editor */}
        <div className="w-1/2 border-r bg-white overflow-y-auto">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
            <TabsList className="grid w-full grid-cols-5 sticky top-0 z-10">
              <TabsTrigger value="template">Template</TabsTrigger>
              <TabsTrigger value="content">Content</TabsTrigger>
              <TabsTrigger value="customize">Customize</TabsTrigger>
              <TabsTrigger value="ai">
                <Brain className="h-4 w-4 mr-1" />
                AI Assistant
              </TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="template" className="p-6 space-y-0">
              <TemplateSwitcher
                currentTemplateId={currentTemplateId}
                resumeData={resumeData}
                onTemplateChange={handleTemplateChange}
                onPreview={handlePreview}
              />
            </TabsContent>

            <TabsContent value="content" className="p-6 space-y-0">
              <ResumeDataEditor
                data={resumeData}
                onChange={setResumeData}
                onSave={async (data) => {
                  // Save to localStorage for now
                  saveToLocalStorage({ resumeData: data, templateId: currentTemplateId });
                }}
                autoSave={true}
                autoSaveDelay={1500}
                templateId={currentTemplateId}
                templateName={TEMPLATE_CATALOG.find(t => t.id === currentTemplateId)?.name}
                showPDFExport={true}
              />
            </TabsContent>

            <TabsContent value="customize" className="p-0 h-full">
              <CustomizationInterface
                templateId={currentTemplateId}
                resumeData={resumeData}
                onTemplateChange={handleTemplateChange}
                onCustomizationSave={async (customizationData) => {
                  setCustomization(customizationData);
                  // Save customization to localStorage
                  localStorage.setItem(`template-customization-${currentTemplateId}`, JSON.stringify(customizationData));
                }}
                onExportWithCustomization={(customizationData) => {
                  // TODO: Implement PDF export with customization
                  console.log('Export with customization:', customizationData);
                }}
                className="h-full"
              />
            </TabsContent>

            <TabsContent value="ai" className="p-0 h-full">
              <AIContentPanel
                resumeContent={resumeData}
                onContentUpdate={(updatedContent) => {
                  setResumeData(prev => ({ ...prev, ...updatedContent }));
                }}
                onSuggestionApply={(suggestion) => {
                  // Handle suggestion application
                  console.log('Applied suggestion:', suggestion);
                }}
                className="h-full p-6"
              />
            </TabsContent>

            <TabsContent value="settings" className="p-6">
              <Card>
                <CardHeader>
                  <CardTitle>Template Settings</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    Template customization options will be implemented here.
                    This will include color schemes, fonts, layout options, etc.
                  </p>
                  {/* TODO: Implement template customization */}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Panel - Live Preview */}
        <div className="w-1/2 bg-gray-100 overflow-y-auto">
          <div className="p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold mb-2">Live Preview</h3>
              <p className="text-sm text-gray-600">
                See your changes in real-time
              </p>
            </div>
            
            <div className="bg-white rounded-lg shadow-lg p-4">
              <TemplateRenderer
                templateId={currentTemplateId}
                resumeData={resumeData}
                customization={customization}
                enableCustomization={true}
                className="transform scale-75 origin-top"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
