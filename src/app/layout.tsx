import type { <PERSON>ada<PERSON> } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";
import { Toaster } from "react-hot-toast";
import { ThemeProvider } from "@/components/ui/theme-provider";
import { SessionProvider } from "@/components/auth/session-provider";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "CVLeap - AI-Powered Resume Builder & Job Search Platform",
  description: "Create ATS-optimized resumes, track job applications, and accelerate your career with AI-powered tools. Build professional resumes in minutes with our advanced resume builder.",
  keywords: "resume builder, CV maker, job search, ATS optimization, career tools, AI resume, job tracker",
  authors: [{ name: "CVLeap Team" }],
  creator: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  publisher: "CVLeap",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXTAUTH_URL || 'http://localhost:3000'),
  openGraph: {
    title: "CVLeap - AI-Powered Resume Builder & Job Search Platform",
    description: "Create ATS-optimized resumes, track job applications, and accelerate your career with AI-powered tools.",
    url: "/",
    siteName: "CVLeap",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "CVLeap - AI-Powered Resume Builder",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "CVLeap - AI-Powered Resume Builder & Job Search Platform",
    description: "Create ATS-optimized resumes, track job applications, and accelerate your career with AI-powered tools.",
    images: ["/og-image.png"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} font-sans antialiased bg-white dark:bg-gray-950 text-gray-900 dark:text-gray-100`}
      >
        <SessionProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <div className="min-h-screen flex flex-col">
              {children}
            </div>
          </ThemeProvider>
        </SessionProvider>
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
            success: {
              duration: 3000,
              iconTheme: {
                primary: '#10b981',
                secondary: '#fff',
              },
            },
            error: {
              duration: 5000,
              iconTheme: {
                primary: '#ef4444',
                secondary: '#fff',
              },
            },
          }}
        />
      </body>
    </html>
  );
}
