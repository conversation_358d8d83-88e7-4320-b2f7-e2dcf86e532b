import { Metadata } from 'next';
import { getServerSession } from 'next-auth/next';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth/config';
import { SignInForm } from '@/components/auth/signin-form';

export const metadata: Metadata = {
  title: 'Sign In | CVLeap',
  description: 'Sign in to your CVLeap account to access your resumes and job applications.',
};

export default async function SignInPage() {
  const session = await getServerSession(authOptions);

  // Redirect if already signed in
  if (session) {
    redirect('/dashboard');
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            CVLeap
          </h1>
          <p className="text-gray-600 mt-2">
            AI-Powered Resume Builder & Job Search Platform
          </p>
        </div>
        <SignInForm />
      </div>
    </div>
  );
}
