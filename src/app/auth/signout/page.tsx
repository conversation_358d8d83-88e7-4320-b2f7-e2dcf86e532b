'use client';

import { useEffect } from 'react';
import { signOut } from 'next-auth/react';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

export default function SignOutPage() {
  useEffect(() => {
    signOut({ callbackUrl: '/' });
  }, []);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="text-center">
        <LoadingSpinner className="mx-auto mb-4" />
        <p className="text-gray-600">Signing you out...</p>
      </div>
    </div>
  );
}
