{"name": "cvleap", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "db:setup": "tsx scripts/setup-db.ts", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "dependencies": {"@ai-sdk/openai": "^2.0.30", "@anthropic-ai/sdk": "^0.62.0", "@headlessui/react": "^2.2.8", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.16.1", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@radix-ui/react-tooltip": "^1.2.8", "@types/nodemailer": "^7.0.1", "@types/puppeteer": "^5.4.7", "@types/uuid": "^10.0.0", "ai": "^5.0.44", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "lucide-react": "^0.544.0", "next": "15.5.3", "next-auth": "^4.24.11", "nodemailer": "^7.0.6", "openai": "^5.20.2", "prisma": "^6.16.1", "puppeteer": "^24.20.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.6.0", "recharts": "^3.2.0", "stripe": "^18.5.0", "tailwind-merge": "^3.3.1", "tiktoken": "^1.0.22", "uuid": "^13.0.0", "zod": "^4.1.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.3", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4", "tsx": "^4.20.5", "typescript": "^5"}, "prisma": {"seed": "tsx prisma/seed.ts"}}