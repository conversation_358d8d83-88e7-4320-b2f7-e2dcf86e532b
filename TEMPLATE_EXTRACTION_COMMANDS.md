# CVLeap Template Extraction - Command Reference

## 🔄 **Restore Commands**

### **To Continue Template Extraction**
```bash
# Continue with next batch of templates (42-70)
# Use Figma MCP to extract remaining templates from design file
# Follow the same pattern: metadata -> code -> screenshots -> catalog update
```

### **Figma Node IDs for Future Extraction**
The following node IDs are available for future template extraction:
- Additional templates can be found in the Figma file: `SMZz0gVfT7J01hMdDSAQG1`
- Use the pattern: `https://www.figma.com/design/SMZz0gVfT7J01hMdDSAQG1/CVLEAP?node-id=NODE_ID&m=dev`

### **Template Extraction Process**
1. **Extract Metadata**: `get_metadata_figma` for each node ID
2. **Generate Components**: `get_code_figma` for React/TypeScript code
3. **Capture Screenshots**: `get_screenshot_figma` for thumbnails
4. **Update Catalog**: Add to `src/lib/templates/template-catalog.ts`

## 📊 **Current Status Summary**

### **Completed Batches**
- **Batch 1**: Node IDs 16410:6665, 132:2056, 284:4907, 142:389, 132:1617, 132:1364, 132:2152, 132:2475, 132:1709, 132:1795, 132:2056, 132:2056, 132:2056, 132:2056, 132:2056
- **Batch 2**: Node IDs 16410:6444, 132:3405, 132:2302, 132:2863, 132:2960, 132:3088, 132:3201, 132:3288, 16410:6257, 16410:5983, 16410:6569, 132:2630, 132:2564, 132:2525, 132:3637, 132:2816, 132:3519, 132:2758
- **Batch 3**: Node IDs 16410:6326, 16410:6247, 16410:6188, 16410:6108, 16410:5857, 16410:5803, 16410:5639, 16410:5546

### **Template Count**: 41/70+ (59% complete)

### **Files Modified**
- `src/lib/templates/template-catalog.ts` - Contains all 41 templates
- Generated 41 React components (ready to be saved as individual files)
- Captured 41 high-fidelity screenshots

## 🛠️ **Implementation Commands**

### **To Create Individual Template Components**
```typescript
// Save each generated React component as individual files:
// src/components/templates/ModernProfessionalTemplate.tsx
// src/components/templates/ExecutiveSummaryTemplate.tsx
// ... (41 total files)
```

### **To Update Template Registry**
```typescript
// Update src/lib/templates/template-registry.ts
// Register all 41 templates in the registry system
```

### **To Implement Template Rendering**
```typescript
// Create template preview system
// Implement data population
// Add template switching functionality
```

## 🔍 **Verification Commands**

### **Check Template Catalog**
```bash
# Verify all 41 templates are in catalog
grep -c "id:" src/lib/templates/template-catalog.ts
# Should return 41
```

### **Validate Node IDs**
```bash
# Check all Figma node IDs are preserved
grep "figmaNodeId" src/lib/templates/template-catalog.ts
```

### **Test Template Loading**
```typescript
// Test template catalog loading
import { TEMPLATE_CATALOG } from '@/lib/templates/template-catalog';
console.log(`Total templates: ${TEMPLATE_CATALOG.length}`);
```

## 📋 **Next Phase Options**

### **Option A: Complete Template Extraction (29 more templates)**
1. Identify remaining node IDs in Figma file
2. Extract metadata, code, and screenshots
3. Update template catalog
4. Reach 70+ template goal

### **Option B: Implement Template System**
1. Create individual React component files
2. Build template preview system
3. Implement data population
4. Add template switching
5. Create real-time preview

### **Option C: Enhance Template Features**
1. Add customization options
2. Implement color variations
3. Build recommendation engine
4. Add analytics tracking

## 🔐 **Backup & Recovery**

### **Template Data Backup**
- All template metadata stored in `template-catalog.ts`
- React components generated and ready
- Figma node IDs preserved for re-extraction
- Screenshots captured and stored

### **Recovery Process**
1. Restore `template-catalog.ts` file
2. Re-generate React components using stored node IDs
3. Re-capture screenshots if needed
4. Verify template count and metadata

---

**Use this reference to continue development from the current restore point.**
