# CVLeap - AI-Powered Resume Builder & Job Search Platform

CVLeap is a comprehensive, AI-powered resume builder and job search platform designed to accelerate your career journey. Built with modern technologies and featuring a multi-agent AI system, CVLeap helps you create ATS-optimized resumes, track job applications, and optimize your professional profile.

## 🚀 Features

### Core Features
- **AI Resume Builder** - Generate professional, ATS-optimized resumes with AI assistance
- **Content Generator** - AI-powered bullet points, summaries, and job descriptions
- **Resume Analyzer** - Real-time ATS compatibility scoring and optimization suggestions
- **Cover Letter Builder** - Personalized cover letters for each application
- **Job Application Tracker** - Kanban-style dashboard to manage your job search
- **LinkedIn Profile Optimizer** - AI-powered LinkedIn profile enhancement
- **Bulk Personalization** - Mass-customize resumes for multiple job applications
- **Job Search & Scraping** - Automated job discovery from multiple sources
- **Networking Hub** - Track connections and networking activities
- **Analytics Dashboard** - Success tracking and career progression insights

### AI Agent System
- **Research Agent** - Market analysis and skill trend prediction
- **Resume Agent** - Intelligent resume optimization and generation
- **Branding Agent** - Professional brand development and consistency
- **Application Tracker Agent** - Automated application management
- **Analytics Agent** - Performance tracking and insights

## 🛠️ Tech Stack

- **Frontend**: Next.js 14, React 19, TypeScript, Tailwind CSS 4
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL
- **AI**: OpenAI GPT-4, Claude (Anthropic)
- **Authentication**: NextAuth.js with OAuth providers
- **UI Components**: Radix UI, Headless UI, Framer Motion
- **Styling**: Tailwind CSS with custom design system
- **Development**: Turbopack, ESLint, Prettier

## 📋 Prerequisites

- Node.js 18+
- PostgreSQL 15+
- npm or yarn package manager

## 🚀 Quick Start

### 1. Clone and Install

```bash
git clone <repository-url>
cd cvleap
npm install
```

### 2. Environment Setup

Copy the environment template:
```bash
cp .env.example .env.local
```

Update `.env.local` with your configuration:
```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/cvleap"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# OpenAI API
OPENAI_API_KEY="your-openai-api-key"

# OAuth Providers (optional)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
```

### 3. Database Setup

#### Option A: Using Docker (Recommended)
```bash
# Start PostgreSQL with Docker
npm run docker:up

# Set up database schema and seed data
npm run db:push
npm run db:seed
```

#### Option B: Using Local PostgreSQL
```bash
# Create database
createdb cvleap

# Set up schema and seed data
npm run db:push
npm run db:seed
```

### 4. Verify Setup

```bash
# Run database health check
npm run db:setup

# Start development server
npm run dev
```

Visit `http://localhost:3000` to see your CVLeap platform!

## 📊 Database Health Check

Check your database status:
```bash
curl http://localhost:3000/api/health
```

Expected response:
```json
{
  "status": "healthy",
  "database": {
    "database": true,
    "tables": true,
    "seed": true
  },
  "stats": {
    "users": 0,
    "resumes": 0,
    "jobs": 2,
    "applications": 0,
    "templates": 6
  }
}
```

## 🗄️ Database Commands

```bash
# Generate Prisma client
npm run db:generate

# Push schema changes to database
npm run db:push

# Create and run migrations
npm run db:migrate

# Seed database with sample data
npm run db:seed

# Open Prisma Studio (database GUI)
npm run db:studio

# Reset database (⚠️ destructive)
npm run db:reset

# Run database setup and health check
npm run db:setup
```

## 🐳 Docker Commands

```bash
# Start PostgreSQL and Redis
npm run docker:up

# Stop containers
npm run docker:down
```

## 🏗️ Project Structure

```
cvleap/
├── src/
│   ├── app/                 # Next.js App Router pages
│   ├── components/          # React components
│   │   ├── ui/             # Base UI components
│   │   ├── forms/          # Form components
│   │   ├── dashboard/      # Dashboard components
│   │   ├── resume/         # Resume builder components
│   │   ├── jobs/           # Job search components
│   │   └── analytics/      # Analytics components
│   ├── lib/                # Utility libraries
│   │   ├── auth/           # Authentication logic
│   │   ├── db/             # Database utilities
│   │   ├── ai/             # AI integration
│   │   └── validations/    # Form validation schemas
│   ├── agents/             # AI agent implementations
│   ├── hooks/              # Custom React hooks
│   ├── store/              # State management
│   ├── types/              # TypeScript type definitions
│   └── templates/          # Resume and email templates
├── prisma/                 # Database schema and migrations
├── public/                 # Static assets
└── scripts/                # Utility scripts
```

## 🎨 Design System

CVLeap features a comprehensive design system with:
- **Dark/Light theme support** with system preference detection
- **Responsive design** optimized for all devices
- **Accessible components** following WCAG guidelines
- **Consistent spacing and typography** using Tailwind CSS
- **Professional color palette** optimized for business use

## 🔐 Security & Compliance

- **GDPR & CCPA compliant** with privacy-by-design architecture
- **End-to-end encryption** for sensitive user data
- **Secure authentication** with OAuth providers
- **Input validation** and sanitization
- **Rate limiting** and DDoS protection
- **Regular security audits** and dependency updates

## 🚀 Development

### Available Scripts

```bash
npm run dev          # Start development server with Turbopack
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript type checking
```

### Code Quality

- **TypeScript** for type safety
- **ESLint** for code linting
- **Prettier** for code formatting
- **Husky** for git hooks
- **Conventional commits** for consistent commit messages

## 📈 Roadmap

### Phase 1: Foundation ✅
- [x] Project setup and core infrastructure
- [x] Database architecture and setup
- [x] UI component library and design system
- [x] Basic authentication system

### Phase 2: Core Features (In Progress)
- [ ] AI-powered resume builder
- [ ] Job application tracking
- [ ] Resume templates and customization
- [ ] ATS optimization engine

### Phase 3: Advanced Features
- [ ] Multi-agent AI system
- [ ] LinkedIn integration
- [ ] Job board integrations
- [ ] Advanced analytics and insights

### Phase 4: Enterprise Features
- [ ] Team collaboration
- [ ] White-label solutions
- [ ] Advanced reporting
- [ ] API for third-party integrations

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs.cvleap.com](https://docs.cvleap.com)
- **Issues**: [GitHub Issues](https://github.com/cvleap/cvleap/issues)
- **Discussions**: [GitHub Discussions](https://github.com/cvleap/cvleap/discussions)
- **Email**: <EMAIL>

---

Built with ❤️ by the CVLeap team
