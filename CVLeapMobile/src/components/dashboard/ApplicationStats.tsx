import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { JobApplication, ApplicationStatus } from '../../types';

interface ApplicationStatsProps {
  applications: JobApplication[];
}

export default function ApplicationStats({ applications }: ApplicationStatsProps) {
  const getStatusCounts = () => {
    const counts = {
      [ApplicationStatus.APPLIED]: 0,
      [ApplicationStatus.REVIEWING]: 0,
      [ApplicationStatus.INTERVIEW]: 0,
      [ApplicationStatus.OFFER]: 0,
      [ApplicationStatus.REJECTED]: 0,
    };

    applications.forEach((app) => {
      counts[app.status]++;
    });

    return counts;
  };

  const statusCounts = getStatusCounts();
  const totalApplications = applications.length;

  const getSuccessRate = () => {
    const successful = statusCounts[ApplicationStatus.INTERVIEW] + statusCounts[ApplicationStatus.OFFER];
    return totalApplications > 0 ? Math.round((successful / totalApplications) * 100) : 0;
  };

  const getResponseRate = () => {
    const responded = totalApplications - statusCounts[ApplicationStatus.APPLIED];
    return totalApplications > 0 ? Math.round((responded / totalApplications) * 100) : 0;
  };

  const stats = [
    {
      label: 'Total Applications',
      value: totalApplications.toString(),
      color: '#3B82F6',
    },
    {
      label: 'Response Rate',
      value: `${getResponseRate()}%`,
      color: '#10B981',
    },
    {
      label: 'Interview Rate',
      value: `${getSuccessRate()}%`,
      color: '#8B5CF6',
    },
    {
      label: 'Active',
      value: (statusCounts[ApplicationStatus.APPLIED] + statusCounts[ApplicationStatus.REVIEWING]).toString(),
      color: '#F59E0B',
    },
  ];

  if (totalApplications === 0) {
    return (
      <View style={styles.container}>
        <Text style={styles.sectionTitle}>Application Stats</Text>
        <View style={styles.emptyState}>
          <Text style={styles.emptyText}>No applications yet</Text>
          <Text style={styles.emptySubtext}>Start applying to jobs to see your stats here</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Application Stats</Text>
      <View style={styles.statsContainer}>
        {stats.map((stat, index) => (
          <View key={index} style={styles.statItem}>
            <Text style={[styles.statValue, { color: stat.color }]}>{stat.value}</Text>
            <Text style={styles.statLabel}>{stat.label}</Text>
          </View>
        ))}
      </View>
      
      {/* Status Breakdown */}
      <View style={styles.statusBreakdown}>
        <Text style={styles.breakdownTitle}>Status Breakdown</Text>
        <View style={styles.statusList}>
          {Object.entries(statusCounts).map(([status, count]) => (
            <View key={status} style={styles.statusItem}>
              <View style={[styles.statusDot, { backgroundColor: getStatusColor(status as ApplicationStatus) }]} />
              <Text style={styles.statusText}>{formatStatus(status as ApplicationStatus)}</Text>
              <Text style={styles.statusCount}>{count}</Text>
            </View>
          ))}
        </View>
      </View>
    </View>
  );
}

function getStatusColor(status: ApplicationStatus): string {
  switch (status) {
    case ApplicationStatus.APPLIED:
      return '#6B7280';
    case ApplicationStatus.REVIEWING:
      return '#3B82F6';
    case ApplicationStatus.INTERVIEW:
      return '#8B5CF6';
    case ApplicationStatus.OFFER:
      return '#10B981';
    case ApplicationStatus.REJECTED:
      return '#EF4444';
    default:
      return '#6B7280';
  }
}

function formatStatus(status: ApplicationStatus): string {
  switch (status) {
    case ApplicationStatus.APPLIED:
      return 'Applied';
    case ApplicationStatus.REVIEWING:
      return 'Under Review';
    case ApplicationStatus.INTERVIEW:
      return 'Interview';
    case ApplicationStatus.OFFER:
      return 'Offer';
    case ApplicationStatus.REJECTED:
      return 'Rejected';
    default:
      return status;
  }
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 16,
  },
  emptyState: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B7280',
    marginBottom: 4,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
  },
  statusBreakdown: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  breakdownTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  statusList: {
    gap: 8,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 12,
  },
  statusText: {
    flex: 1,
    fontSize: 14,
    color: '#374151',
  },
  statusCount: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
});
