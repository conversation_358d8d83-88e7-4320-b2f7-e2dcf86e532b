import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface QuickActionProps {
  title: string;
  icon: string;
  color: string;
  onPress: () => void;
}

function QuickActionButton({ title, icon, color, onPress }: QuickActionProps) {
  return (
    <TouchableOpacity style={styles.actionButton} onPress={onPress}>
      <View style={[styles.actionIcon, { backgroundColor: `${color}20` }]}>
        <Icon name={icon} size={24} color={color} />
      </View>
      <Text style={styles.actionTitle}>{title}</Text>
    </TouchableOpacity>
  );
}

export default function QuickActions() {
  const actions = [
    {
      title: 'Create Resume',
      icon: 'add-circle',
      color: '#3B82F6',
      onPress: () => {
        // Navigate to resume creation
        console.log('Create Resume');
      },
    },
    {
      title: 'Search Jobs',
      icon: 'search',
      color: '#10B981',
      onPress: () => {
        // Navigate to job search
        console.log('Search Jobs');
      },
    },
    {
      title: 'Optimize Profile',
      icon: 'trending-up',
      color: '#8B5CF6',
      onPress: () => {
        // Navigate to LinkedIn optimization
        console.log('Optimize Profile');
      },
    },
    {
      title: 'View Analytics',
      icon: 'analytics',
      color: '#F59E0B',
      onPress: () => {
        // Navigate to analytics
        console.log('View Analytics');
      },
    },
  ];

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Quick Actions</Text>
      <View style={styles.actionsGrid}>
        {actions.map((action, index) => (
          <QuickActionButton
            key={index}
            title={action.title}
            icon={action.icon}
            color={action.color}
            onPress={action.onPress}
          />
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 16,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    width: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    textAlign: 'center',
  },
});
