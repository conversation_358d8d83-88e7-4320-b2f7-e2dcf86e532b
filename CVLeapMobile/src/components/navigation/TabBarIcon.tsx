import React from 'react';
import { View, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface TabBarIconProps {
  name: string;
  size: number;
  color: string;
  focused: boolean;
}

export default function TabBarIcon({ name, size, color, focused }: TabBarIconProps) {
  return (
    <View style={[styles.container, focused && styles.focused]}>
      <Icon name={name} size={size} color={color} />
      {focused && <View style={[styles.indicator, { backgroundColor: color }]} />}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
  },
  focused: {
    // Add any focused state styling here
  },
  indicator: {
    width: 4,
    height: 4,
    borderRadius: 2,
    marginTop: 2,
  },
});
