import AsyncStorage from '@react-native-async-storage/async-storage';

// API Configuration
export const API_CONFIG = {
  BASE_URL: __DEV__ ? 'http://localhost:3003' : 'https://api.cvleap.com',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/api/auth/signin',
    REGISTER: '/api/auth/signup',
    LOGOUT: '/api/auth/signout',
    REFRESH: '/api/auth/refresh',
    FORGOT_PASSWORD: '/api/auth/forgot-password',
    RESET_PASSWORD: '/api/auth/reset-password',
    VERIFY_EMAIL: '/api/auth/verify-email',
  },
  
  // User Profile
  PROFILE: {
    GET: '/api/profile',
    UPDATE: '/api/profile',
    UPLOAD_AVATAR: '/api/profile/avatar',
  },
  
  // Resumes
  RESUMES: {
    LIST: '/api/resumes',
    CREATE: '/api/resumes',
    GET: (id: string) => `/api/resumes/${id}`,
    UPDATE: (id: string) => `/api/resumes/${id}`,
    DELETE: (id: string) => `/api/resumes/${id}`,
    DUPLICATE: (id: string) => `/api/resumes/${id}/duplicate`,
    EXPORT: (id: string) => `/api/resumes/${id}/export`,
    ATS_SCORE: (id: string) => `/api/resumes/${id}/ats-score`,
  },
  
  // Jobs
  JOBS: {
    SEARCH: '/api/jobs',
    GET: (id: string) => `/api/jobs/${id}`,
    SAVE: (id: string) => `/api/jobs/${id}/save`,
    UNSAVE: (id: string) => `/api/jobs/${id}/save`,
    APPLY: (id: string) => `/api/jobs/${id}/apply`,
    SAVED: '/api/jobs/saved',
  },
  
  // Applications
  APPLICATIONS: {
    LIST: '/api/applications',
    CREATE: '/api/applications',
    GET: (id: string) => `/api/applications/${id}`,
    UPDATE: (id: string) => `/api/applications/${id}`,
    DELETE: (id: string) => `/api/applications/${id}`,
    STATS: '/api/applications/stats',
  },
  
  // LinkedIn
  LINKEDIN: {
    PROFILE: '/api/linkedin/profile',
    ANALYZE: '/api/linkedin/analyze',
    OPTIMIZE: '/api/linkedin/optimize',
  },
  
  // AI Agents
  AGENTS: {
    HEALTH: '/api/agents/health',
    RESUME_GENERATE: '/api/agents/resume/generate',
    RESUME_ATS_SCORE: '/api/agents/resume/ats-score',
    RESEARCH_JOB_MARKET: '/api/agents/research/job-market',
  },
  
  // Notifications
  NOTIFICATIONS: {
    LIST: '/api/notifications',
    MARK_READ: (id: string) => `/api/notifications/${id}/read`,
    MARK_ALL_READ: '/api/notifications/read-all',
    SETTINGS: '/api/notifications/settings',
  },
};

// HTTP Methods
export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  PATCH = 'PATCH',
  DELETE = 'DELETE',
}

// Request Configuration
export interface RequestConfig {
  method: HttpMethod;
  url: string;
  data?: any;
  params?: Record<string, any>;
  headers?: Record<string, string>;
  timeout?: number;
  retries?: number;
}

// Response Types
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  success: boolean;
  status?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Error Types
export class ApiError extends Error {
  status: number;
  code?: string;
  details?: any;

  constructor(message: string, status: number, code?: string, details?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.code = code;
    this.details = details;
  }
}

// Token Management
export const TokenManager = {
  async getToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem('auth_token');
    } catch (error) {
      console.error('Error getting token:', error);
      return null;
    }
  },

  async setToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem('auth_token', token);
    } catch (error) {
      console.error('Error setting token:', error);
    }
  },

  async removeToken(): Promise<void> {
    try {
      await AsyncStorage.removeItem('auth_token');
    } catch (error) {
      console.error('Error removing token:', error);
    }
  },

  async getRefreshToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem('refresh_token');
    } catch (error) {
      console.error('Error getting refresh token:', error);
      return null;
    }
  },

  async setRefreshToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem('refresh_token', token);
    } catch (error) {
      console.error('Error setting refresh token:', error);
    }
  },

  async removeRefreshToken(): Promise<void> {
    try {
      await AsyncStorage.removeItem('refresh_token');
    } catch (error) {
      console.error('Error removing refresh token:', error);
    }
  },
};

// Request Interceptors
export const getAuthHeaders = async (): Promise<Record<string, string>> => {
  const token = await TokenManager.getToken();
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  return headers;
};

// Utility Functions
export const buildUrl = (endpoint: string, params?: Record<string, any>): string => {
  const url = new URL(endpoint, API_CONFIG.BASE_URL);
  
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.append(key, String(value));
      }
    });
  }
  
  return url.toString();
};

export const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Network Status
export const NetworkStatus = {
  isOnline: true,
  
  setOnline(status: boolean) {
    this.isOnline = status;
  },
  
  getStatus(): boolean {
    return this.isOnline;
  },
};

// Cache Configuration
export const CACHE_CONFIG = {
  TTL: {
    SHORT: 5 * 60 * 1000, // 5 minutes
    MEDIUM: 30 * 60 * 1000, // 30 minutes
    LONG: 24 * 60 * 60 * 1000, // 24 hours
  },
  KEYS: {
    USER_PROFILE: 'cache_user_profile',
    RESUMES: 'cache_resumes',
    JOBS: 'cache_jobs',
    APPLICATIONS: 'cache_applications',
    LINKEDIN_PROFILE: 'cache_linkedin_profile',
  },
};

// Offline Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
  SETTINGS: 'app_settings',
  OFFLINE_QUEUE: 'offline_queue',
  LAST_SYNC: 'last_sync',
};
