import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { ResumeStackParamList } from '../../types';

type ResumePreviewScreenNavigationProp = StackNavigationProp<ResumeStackParamList, 'ResumePreview'>;
type ResumePreviewScreenRouteProp = RouteProp<ResumeStackParamList, 'ResumePreview'>;

interface Props {
  navigation: ResumePreviewScreenNavigationProp;
  route: ResumePreviewScreenRouteProp;
}

export default function ResumePreviewScreen({ navigation, route }: Props) {
  const { resumeId } = route.params;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Resume Preview</Text>
      <Text style={styles.subtitle}>Resume ID: {resumeId}</Text>
      <Text style={styles.placeholder}>
        Resume preview interface will be implemented here with:
        {'\n'}• Full resume preview
        {'\n'}• PDF generation
        {'\n'}• Share functionality
        {'\n'}• Download options
        {'\n'}• Print preview
        {'\n'}• ATS score display
        {'\n'}• Optimization suggestions
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 24,
  },
  placeholder: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
});
