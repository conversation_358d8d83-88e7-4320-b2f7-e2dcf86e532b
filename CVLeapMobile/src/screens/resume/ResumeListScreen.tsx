import React, { useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  Alert,
} from 'react-native';
import { useAppDispatch, useAppSelector } from '../../store';
import { fetchResumes, deleteResume } from '../../store/slices/resumeSlice';
import { StackNavigationProp } from '@react-navigation/stack';
import { ResumeStackParamList } from '../../types';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LoadingScreen from '../../components/common/LoadingScreen';

type ResumeListScreenNavigationProp = StackNavigationProp<ResumeStackParamList, 'ResumeList'>;

interface Props {
  navigation: ResumeListScreenNavigationProp;
}

export default function ResumeListScreen({ navigation }: Props) {
  const dispatch = useAppDispatch();
  const { resumes, isLoading, error } = useAppSelector((state) => state.resume);
  const [refreshing, setRefreshing] = React.useState(false);

  useEffect(() => {
    loadResumes();
  }, []);

  const loadResumes = async () => {
    try {
      await dispatch(fetchResumes()).unwrap();
    } catch (error) {
      console.error('Error loading resumes:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadResumes();
    setRefreshing(false);
  };

  const handleCreateResume = () => {
    navigation.navigate('ResumeEditor', { resumeId: undefined });
  };

  const handleEditResume = (resumeId: string) => {
    navigation.navigate('ResumeEditor', { resumeId });
  };

  const handlePreviewResume = (resumeId: string) => {
    navigation.navigate('ResumePreview', { resumeId });
  };

  const handleDeleteResume = (resumeId: string, title: string) => {
    Alert.alert(
      'Delete Resume',
      `Are you sure you want to delete "${title}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await dispatch(deleteResume(resumeId)).unwrap();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete resume');
            }
          },
        },
      ]
    );
  };

  const renderResumeItem = ({ item }: { item: any }) => (
    <View style={styles.resumeCard}>
      <TouchableOpacity
        style={styles.resumeContent}
        onPress={() => handleEditResume(item.id)}
      >
        <View style={styles.resumeHeader}>
          <Text style={styles.resumeTitle}>{item.title}</Text>
          <View style={styles.resumeActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handlePreviewResume(item.id)}
            >
              <Icon name="visibility" size={20} color="#6B7280" />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleDeleteResume(item.id, item.title)}
            >
              <Icon name="delete" size={20} color="#EF4444" />
            </TouchableOpacity>
          </View>
        </View>
        <Text style={styles.resumeSubtitle}>
          Updated {new Date(item.updatedAt).toLocaleDateString()}
        </Text>
        {item.atsScore && (
          <View style={styles.atsScore}>
            <Text style={styles.atsScoreLabel}>ATS Score:</Text>
            <Text style={[styles.atsScoreValue, { color: getScoreColor(item.atsScore) }]}>
              {item.atsScore}%
            </Text>
          </View>
        )}
      </TouchableOpacity>
    </View>
  );

  const getScoreColor = (score: number) => {
    if (score >= 80) return '#10B981';
    if (score >= 60) return '#F59E0B';
    return '#EF4444';
  };

  if (isLoading && resumes.length === 0) {
    return <LoadingScreen message="Loading resumes..." />;
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={resumes}
        renderItem={renderResumeItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Icon name="description" size={64} color="#D1D5DB" />
            <Text style={styles.emptyTitle}>No resumes yet</Text>
            <Text style={styles.emptySubtitle}>
              Create your first resume to get started
            </Text>
            <TouchableOpacity style={styles.createButton} onPress={handleCreateResume}>
              <Text style={styles.createButtonText}>Create Resume</Text>
            </TouchableOpacity>
          </View>
        }
      />
      
      {resumes.length > 0 && (
        <TouchableOpacity style={styles.fab} onPress={handleCreateResume}>
          <Icon name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  listContainer: {
    padding: 16,
    flexGrow: 1,
  },
  resumeCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  resumeContent: {
    padding: 16,
  },
  resumeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  resumeTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1F2937',
    flex: 1,
  },
  resumeActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 4,
  },
  resumeSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
  },
  atsScore: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  atsScoreLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginRight: 8,
  },
  atsScoreValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24,
  },
  createButton: {
    backgroundColor: '#3B82F6',
    borderRadius: 8,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  createButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3B82F6',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
});
