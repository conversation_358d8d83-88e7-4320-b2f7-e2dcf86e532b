import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { ResumeStackParamList } from '../../types';

type ResumeEditorScreenNavigationProp = StackNavigationProp<ResumeStackParamList, 'ResumeEditor'>;
type ResumeEditorScreenRouteProp = RouteProp<ResumeStackParamList, 'ResumeEditor'>;

interface Props {
  navigation: ResumeEditorScreenNavigationProp;
  route: ResumeEditorScreenRouteProp;
}

export default function ResumeEditorScreen({ navigation, route }: Props) {
  const { resumeId } = route.params;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Resume Editor</Text>
      <Text style={styles.subtitle}>
        {resumeId ? `Editing resume: ${resumeId}` : 'Creating new resume'}
      </Text>
      <Text style={styles.placeholder}>
        Resume editor interface will be implemented here with:
        {'\n'}• Personal information form
        {'\n'}• Work experience editor
        {'\n'}• Education section
        {'\n'}• Skills management
        {'\n'}• Template selection
        {'\n'}• Real-time preview
        {'\n'}• ATS optimization suggestions
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 24,
  },
  placeholder: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
});
