import React, { useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { useAppDispatch, useAppSelector } from '../../store';
import { fetchResumes } from '../../store/slices/resumeSlice';
import { fetchApplications } from '../../store/slices/applicationSlice';
import { fetchNotifications } from '../../store/slices/notificationSlice';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Components
import DashboardCard from '../../components/dashboard/DashboardCard';
import QuickActions from '../../components/dashboard/QuickActions';
import RecentActivity from '../../components/dashboard/RecentActivity';
import ApplicationStats from '../../components/dashboard/ApplicationStats';

export default function DashboardScreen() {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const { resumes } = useAppSelector((state) => state.resume);
  const { applications } = useAppSelector((state) => state.application);
  const { unreadCount } = useAppSelector((state) => state.notification);
  
  const [refreshing, setRefreshing] = React.useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      await Promise.all([
        dispatch(fetchResumes()),
        dispatch(fetchApplications()),
        dispatch(fetchNotifications()),
      ]);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  const dashboardStats = [
    {
      title: 'Resumes',
      value: resumes.length.toString(),
      icon: 'description',
      color: '#3B82F6',
      onPress: () => {
        // Navigate to resumes
      },
    },
    {
      title: 'Applications',
      value: applications.length.toString(),
      icon: 'assignment',
      color: '#10B981',
      onPress: () => {
        // Navigate to applications
      },
    },
    {
      title: 'Notifications',
      value: unreadCount.toString(),
      icon: 'notifications',
      color: '#F59E0B',
      onPress: () => {
        // Navigate to notifications
      },
    },
    {
      title: 'Profile Score',
      value: '85%',
      icon: 'trending-up',
      color: '#8B5CF6',
      onPress: () => {
        // Navigate to LinkedIn profile
      },
    },
  ];

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.greeting}>
            {getGreeting()}, {user?.name?.split(' ')[0] || 'there'}!
          </Text>
          <Text style={styles.subtitle}>
            Ready to accelerate your career?
          </Text>
        </View>
        <TouchableOpacity style={styles.notificationButton}>
          <Icon name="notifications" size={24} color="#6B7280" />
          {unreadCount > 0 && (
            <View style={styles.notificationBadge}>
              <Text style={styles.notificationBadgeText}>
                {unreadCount > 99 ? '99+' : unreadCount.toString()}
              </Text>
            </View>
          )}
        </TouchableOpacity>
      </View>

      {/* Stats Cards */}
      <View style={styles.statsContainer}>
        {dashboardStats.map((stat, index) => (
          <DashboardCard
            key={index}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            color={stat.color}
            onPress={stat.onPress}
          />
        ))}
      </View>

      {/* Quick Actions */}
      <QuickActions />

      {/* Application Stats */}
      <ApplicationStats applications={applications} />

      {/* Recent Activity */}
      <RecentActivity />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#FFFFFF',
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    marginTop: 4,
  },
  notificationButton: {
    position: 'relative',
    padding: 8,
  },
  notificationBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: '#EF4444',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    paddingVertical: 20,
    gap: 16,
  },
});
