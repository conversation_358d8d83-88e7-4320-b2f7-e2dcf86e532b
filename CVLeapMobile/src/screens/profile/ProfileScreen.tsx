import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export default function ProfileScreen() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Profile Settings</Text>
      <Text style={styles.placeholder}>
        Profile settings interface will be implemented here with:
        {'\n'}• User account information
        {'\n'}• Profile photo upload
        {'\n'}• Personal details editor
        {'\n'}• Notification preferences
        {'\n'}• Privacy settings
        {'\n'}• Subscription management
        {'\n'}• Data export options
        {'\n'}• Account deletion
        {'\n'}• Support and help
        {'\n'}• App version and updates
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 24,
  },
  placeholder: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
});
