import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { ApplicationStackParamList } from '../../types';

type ApplicationDetailsScreenNavigationProp = StackNavigationProp<ApplicationStackParamList, 'ApplicationDetails'>;
type ApplicationDetailsScreenRouteProp = RouteProp<ApplicationStackParamList, 'ApplicationDetails'>;

interface Props {
  navigation: ApplicationDetailsScreenNavigationProp;
  route: ApplicationDetailsScreenRouteProp;
}

export default function ApplicationDetailsScreen({ navigation, route }: Props) {
  const { applicationId } = route.params;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Application Details</Text>
      <Text style={styles.subtitle}>Application ID: {applicationId}</Text>
      <Text style={styles.placeholder}>
        Application details interface will be implemented here with:
        {'\n'}• Complete application information
        {'\n'}• Job details and requirements
        {'\n'}• Application timeline
        {'\n'}• Interview scheduling
        {'\n'}• Notes and follow-up reminders
        {'\n'}• Status updates
        {'\n'}• Contact information
        {'\n'}• Document attachments
        {'\n'}• Communication history
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 24,
  },
  placeholder: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
});
