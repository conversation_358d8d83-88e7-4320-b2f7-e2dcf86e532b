import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { JobStackParamList } from '../../types';

type JobDetailsScreenNavigationProp = StackNavigationProp<JobStackParamList, 'JobDetails'>;
type JobDetailsScreenRouteProp = RouteProp<JobStackParamList, 'JobDetails'>;

interface Props {
  navigation: JobDetailsScreenNavigationProp;
  route: JobDetailsScreenRouteProp;
}

export default function JobDetailsScreen({ navigation, route }: Props) {
  const { jobId } = route.params;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Job Details</Text>
      <Text style={styles.subtitle}>Job ID: {jobId}</Text>
      <Text style={styles.placeholder}>
        Job details interface will be implemented here with:
        {'\n'}• Complete job description
        {'\n'}• Company information
        {'\n'}• Requirements and qualifications
        {'\n'}• Salary and benefits
        {'\n'}• Application deadline
        {'\n'}• Quick apply button
        {'\n'}• Save job functionality
        {'\n'}• Share job posting
        {'\n'}• Similar jobs suggestions
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 24,
  },
  placeholder: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
});
