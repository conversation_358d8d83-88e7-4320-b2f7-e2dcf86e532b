import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export default function JobSearchScreen() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Job Search</Text>
      <Text style={styles.placeholder}>
        Job search interface will be implemented here with:
        {'\n'}• Search filters and keywords
        {'\n'}• Location-based search
        {'\n'}• Swipe-based job browsing
        {'\n'}• Quick apply functionality
        {'\n'}• Save jobs for later
        {'\n'}• AI-powered job matching
        {'\n'}• Salary range filters
        {'\n'}• Company size and type filters
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 24,
  },
  placeholder: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
});
