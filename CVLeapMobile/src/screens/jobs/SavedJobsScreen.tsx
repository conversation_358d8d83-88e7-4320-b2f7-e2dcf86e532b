import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export default function SavedJobsScreen() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Saved Jobs</Text>
      <Text style={styles.placeholder}>
        Saved jobs interface will be implemented here with:
        {'\n'}• List of bookmarked jobs
        {'\n'}• Quick apply from saved jobs
        {'\n'}• Remove from saved list
        {'\n'}• Sort by date saved or relevance
        {'\n'}• Filter by job type or location
        {'\n'}• Bulk actions (apply to multiple)
        {'\n'}• Export saved jobs list
        {'\n'}• Set job alerts for similar positions
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 24,
  },
  placeholder: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
});
