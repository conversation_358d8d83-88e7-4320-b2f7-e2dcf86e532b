import {
  API_CONFIG,
  HttpMethod,
  RequestConfig,
  ApiResponse,
  ApiError,
  TokenManager,
  getAuthHeaders,
  buildUrl,
  delay,
  NetworkStatus,
} from '../config/api';

class ApiService {
  private baseURL: string;
  private timeout: number;
  private retryAttempts: number;
  private retryDelay: number;

  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
    this.timeout = API_CONFIG.TIMEOUT;
    this.retryAttempts = API_CONFIG.RETRY_ATTEMPTS;
    this.retryDelay = API_CONFIG.RETRY_DELAY;
  }

  async request<T = any>(config: RequestConfig): Promise<ApiResponse<T>> {
    const { method, url, data, params, headers = {}, timeout = this.timeout, retries = this.retryAttempts } = config;

    // Check network status
    if (!NetworkStatus.getStatus()) {
      throw new ApiError('No internet connection', 0, 'NETWORK_ERROR');
    }

    // Build full URL
    const fullUrl = buildUrl(url, params);

    // Get auth headers
    const authHeaders = await getAuthHeaders();
    const requestHeaders = { ...authHeaders, ...headers };

    // Prepare fetch options
    const fetchOptions: RequestInit = {
      method,
      headers: requestHeaders,
      signal: AbortSignal.timeout(timeout),
    };

    if (data && (method === HttpMethod.POST || method === HttpMethod.PUT || method === HttpMethod.PATCH)) {
      fetchOptions.body = JSON.stringify(data);
    }

    let lastError: Error;

    // Retry logic
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const response = await fetch(fullUrl, fetchOptions);
        
        // Handle different response types
        let responseData: any;
        const contentType = response.headers.get('content-type');
        
        if (contentType && contentType.includes('application/json')) {
          responseData = await response.json();
        } else {
          responseData = await response.text();
        }

        // Handle HTTP errors
        if (!response.ok) {
          const errorMessage = responseData?.error || responseData?.message || `HTTP ${response.status}`;
          
          // Handle authentication errors
          if (response.status === 401) {
            await this.handleAuthError();
          }
          
          throw new ApiError(
            errorMessage,
            response.status,
            responseData?.code,
            responseData
          );
        }

        // Return successful response
        return {
          data: responseData,
          success: true,
          status: response.status,
        };

      } catch (error) {
        lastError = error as Error;
        
        // Don't retry on certain errors
        if (error instanceof ApiError) {
          if (error.status === 400 || error.status === 401 || error.status === 403 || error.status === 404) {
            throw error;
          }
        }

        // Don't retry on the last attempt
        if (attempt === retries) {
          break;
        }

        // Wait before retrying
        await delay(this.retryDelay * (attempt + 1));
      }
    }

    // Throw the last error if all retries failed
    throw lastError!;
  }

  private async handleAuthError(): Promise<void> {
    try {
      const refreshToken = await TokenManager.getRefreshToken();
      
      if (refreshToken) {
        // Try to refresh the token
        const response = await this.refreshToken(refreshToken);
        
        if (response.success && response.data?.token) {
          await TokenManager.setToken(response.data.token);
          return;
        }
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
    }

    // Clear tokens and redirect to login
    await TokenManager.removeToken();
    await TokenManager.removeRefreshToken();
    
    // Emit auth error event for app to handle
    // This would typically trigger a navigation to login screen
  }

  private async refreshToken(refreshToken: string): Promise<ApiResponse> {
    return this.request({
      method: HttpMethod.POST,
      url: '/api/auth/refresh',
      data: { refreshToken },
    });
  }

  // Convenience methods
  async get<T = any>(url: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: HttpMethod.GET,
      url,
      params,
    });
  }

  async post<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: HttpMethod.POST,
      url,
      data,
    });
  }

  async put<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: HttpMethod.PUT,
      url,
      data,
    });
  }

  async patch<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: HttpMethod.PATCH,
      url,
      data,
    });
  }

  async delete<T = any>(url: string): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: HttpMethod.DELETE,
      url,
    });
  }

  // File upload method
  async uploadFile<T = any>(url: string, file: any, additionalData?: Record<string, any>): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, String(value));
      });
    }

    const authHeaders = await getAuthHeaders();
    delete authHeaders['Content-Type']; // Let browser set content-type for FormData

    return this.request<T>({
      method: HttpMethod.POST,
      url,
      data: formData,
      headers: authHeaders,
    });
  }

  // Download method
  async download(url: string, params?: Record<string, any>): Promise<Blob> {
    const fullUrl = buildUrl(url, params);
    const authHeaders = await getAuthHeaders();

    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: authHeaders,
    });

    if (!response.ok) {
      throw new ApiError(`Download failed: ${response.statusText}`, response.status);
    }

    return response.blob();
  }
}

// Create singleton instance
export const apiService = new ApiService();

// Export for use in other services
export default apiService;
