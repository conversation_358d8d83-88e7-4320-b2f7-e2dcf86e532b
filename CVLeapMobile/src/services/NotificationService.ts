import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { store } from '../store';
import { addNotification } from '../store/slices/notificationSlice';
import { NotificationData, NotificationType } from '../types';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

class NotificationService {
  private static instance: NotificationService;
  private expoPushToken: string | null = null;

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  static initialize(): void {
    const service = NotificationService.getInstance();
    service.setupNotifications();
  }

  async setupNotifications(): Promise<void> {
    try {
      // Register for push notifications
      await this.registerForPushNotifications();
      
      // Set up notification listeners
      this.setupNotificationListeners();
      
      // Request permissions
      await this.requestPermissions();
    } catch (error) {
      console.error('Error setting up notifications:', error);
    }
  }

  private async registerForPushNotifications(): Promise<void> {
    if (!Device.isDevice) {
      console.log('Must use physical device for Push Notifications');
      return;
    }

    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return;
      }

      const token = await Notifications.getExpoPushTokenAsync({
        projectId: 'your-expo-project-id', // Replace with your actual project ID
      });

      this.expoPushToken = token.data;
      
      // Store token locally
      await AsyncStorage.setItem('expo_push_token', token.data);
      
      // Send token to your backend
      await this.sendTokenToBackend(token.data);

      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });
      }
    } catch (error) {
      console.error('Error registering for push notifications:', error);
    }
  }

  private async sendTokenToBackend(token: string): Promise<void> {
    try {
      // Send the token to your backend API
      // This would typically be an API call to register the device
      console.log('Push token:', token);
      
      // Example API call (uncomment and modify as needed):
      // await apiService.post('/api/notifications/register-device', {
      //   token,
      //   platform: Platform.OS,
      // });
    } catch (error) {
      console.error('Error sending token to backend:', error);
    }
  }

  private setupNotificationListeners(): void {
    // Handle notification received while app is in foreground
    Notifications.addNotificationReceivedListener((notification) => {
      console.log('Notification received:', notification);
      this.handleNotificationReceived(notification);
    });

    // Handle notification tapped
    Notifications.addNotificationResponseReceivedListener((response) => {
      console.log('Notification tapped:', response);
      this.handleNotificationTapped(response);
    });
  }

  private handleNotificationReceived(notification: Notifications.Notification): void {
    const { title, body, data } = notification.request.content;
    
    // Add notification to Redux store
    const notificationData: NotificationData = {
      id: notification.request.identifier,
      title: title || 'Notification',
      body: body || '',
      data: data || {},
      type: (data?.type as NotificationType) || NotificationType.SYSTEM,
      createdAt: new Date(),
      read: false,
    };

    store.dispatch(addNotification(notificationData));
  }

  private handleNotificationTapped(response: Notifications.NotificationResponse): void {
    const { data } = response.notification.request.content;
    
    // Handle navigation based on notification data
    if (data?.screen) {
      // Navigate to specific screen
      // This would typically use your navigation service
      console.log('Navigate to:', data.screen);
    }
  }

  async requestPermissions(): Promise<boolean> {
    try {
      const { status } = await Notifications.requestPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  }

  async scheduleLocalNotification(
    title: string,
    body: string,
    data?: any,
    trigger?: Notifications.NotificationTriggerInput
  ): Promise<string | null> {
    try {
      const identifier = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: data || {},
          sound: true,
        },
        trigger: trigger || null,
      });

      return identifier;
    } catch (error) {
      console.error('Error scheduling local notification:', error);
      return null;
    }
  }

  async cancelNotification(identifier: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(identifier);
    } catch (error) {
      console.error('Error canceling notification:', error);
    }
  }

  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error canceling all notifications:', error);
    }
  }

  async setBadgeCount(count: number): Promise<void> {
    try {
      await Notifications.setBadgeCountAsync(count);
    } catch (error) {
      console.error('Error setting badge count:', error);
    }
  }

  async clearBadge(): Promise<void> {
    try {
      await Notifications.setBadgeCountAsync(0);
    } catch (error) {
      console.error('Error clearing badge:', error);
    }
  }

  // Predefined notification types
  async scheduleJobAlert(jobTitle: string, company: string, delay: number = 0): Promise<void> {
    await this.scheduleLocalNotification(
      'New Job Match!',
      `${jobTitle} at ${company} matches your profile`,
      {
        type: NotificationType.JOB_ALERT,
        screen: 'Jobs',
      },
      delay > 0 ? { seconds: delay } : null
    );
  }

  async scheduleApplicationReminder(company: string, delay: number): Promise<void> {
    await this.scheduleLocalNotification(
      'Application Follow-up',
      `Don't forget to follow up on your application at ${company}`,
      {
        type: NotificationType.APPLICATION_UPDATE,
        screen: 'Applications',
      },
      { seconds: delay }
    );
  }

  async scheduleInterviewReminder(company: string, interviewDate: Date): Promise<void> {
    const now = new Date();
    const reminderTime = new Date(interviewDate.getTime() - 60 * 60 * 1000); // 1 hour before
    
    if (reminderTime > now) {
      await this.scheduleLocalNotification(
        'Interview Reminder',
        `You have an interview with ${company} in 1 hour`,
        {
          type: NotificationType.INTERVIEW_REMINDER,
          screen: 'Applications',
        },
        { date: reminderTime }
      );
    }
  }

  async scheduleProfileTip(tip: string): Promise<void> {
    await this.scheduleLocalNotification(
      'Profile Optimization Tip',
      tip,
      {
        type: NotificationType.PROFILE_TIP,
        screen: 'LinkedIn',
      },
      { seconds: 24 * 60 * 60 } // 24 hours from now
    );
  }

  getExpoPushToken(): string | null {
    return this.expoPushToken;
  }
}

export default NotificationService;
