import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { LinkedInProfile, LinkedInState } from '../../types';
import { apiService } from '../../services/api';
import { API_ENDPOINTS } from '../../config/api';

const initialState: LinkedInState = {
  profile: null,
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchLinkedInProfile = createAsyncThunk(
  'linkedin/fetchProfile',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.get(API_ENDPOINTS.LINKEDIN.PROFILE);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to fetch LinkedIn profile');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch LinkedIn profile');
    }
  }
);

export const updateLinkedInProfile = createAsyncThunk(
  'linkedin/updateProfile',
  async (profileData: Partial<LinkedInProfile>, { rejectWithValue }) => {
    try {
      const response = await apiService.post(API_ENDPOINTS.LINKEDIN.PROFILE, profileData);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to update LinkedIn profile');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update LinkedIn profile');
    }
  }
);

export const analyzeLinkedInProfile = createAsyncThunk(
  'linkedin/analyzeProfile',
  async (
    { targetRole, targetIndustry }: { targetRole?: string; targetIndustry?: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await apiService.post(API_ENDPOINTS.LINKEDIN.ANALYZE, {
        targetRole,
        targetIndustry,
      });
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to analyze LinkedIn profile');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to analyze LinkedIn profile');
    }
  }
);

export const optimizeLinkedInSection = createAsyncThunk(
  'linkedin/optimizeSection',
  async (
    {
      section,
      targetRole,
      targetIndustry,
      currentContent,
      tone,
      keywords,
    }: {
      section: 'headline' | 'summary' | 'about' | 'skills';
      targetRole?: string;
      targetIndustry?: string;
      currentContent?: string;
      tone?: 'professional' | 'creative' | 'technical' | 'executive';
      keywords?: string[];
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await apiService.post(API_ENDPOINTS.LINKEDIN.OPTIMIZE, {
        section,
        targetRole,
        targetIndustry,
        currentContent,
        tone: tone || 'professional',
        keywords,
      });
      
      if (response.success && response.data) {
        return { section, ...response.data };
      } else {
        return rejectWithValue(response.error || 'Failed to optimize LinkedIn section');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to optimize LinkedIn section');
    }
  }
);

const linkedinSlice = createSlice({
  name: 'linkedin',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateProfileField: (state, action: PayloadAction<{ field: keyof LinkedInProfile; value: any }>) => {
      if (state.profile) {
        (state.profile as any)[action.payload.field] = action.payload.value;
      }
    },
    setProfile: (state, action: PayloadAction<LinkedInProfile>) => {
      state.profile = action.payload;
    },
    clearProfile: (state) => {
      state.profile = null;
    },
    updateOptimizationSuggestions: (
      state,
      action: PayloadAction<{
        headlineSuggestions?: string[];
        summarySuggestions?: string[];
        keywordSuggestions?: string[];
        optimizationTips?: string[];
      }>
    ) => {
      if (state.profile) {
        const { headlineSuggestions, summarySuggestions, keywordSuggestions, optimizationTips } = action.payload;
        
        if (headlineSuggestions) {
          state.profile.headlineSuggestions = headlineSuggestions;
        }
        if (summarySuggestions) {
          state.profile.summarySuggestions = summarySuggestions;
        }
        if (keywordSuggestions) {
          state.profile.keywordSuggestions = keywordSuggestions;
        }
        if (optimizationTips) {
          state.profile.optimizationTips = optimizationTips;
        }
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch LinkedIn profile
    builder
      .addCase(fetchLinkedInProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchLinkedInProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.profile = action.payload;
        state.error = null;
      })
      .addCase(fetchLinkedInProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update LinkedIn profile
    builder
      .addCase(updateLinkedInProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateLinkedInProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.profile = action.payload;
        state.error = null;
      })
      .addCase(updateLinkedInProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Analyze LinkedIn profile
    builder
      .addCase(analyzeLinkedInProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(analyzeLinkedInProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        if (action.payload.profile) {
          state.profile = action.payload.profile;
        }
        state.error = null;
      })
      .addCase(analyzeLinkedInProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Optimize LinkedIn section
    builder
      .addCase(optimizeLinkedInSection.pending, (state) => {
        state.error = null;
      })
      .addCase(optimizeLinkedInSection.fulfilled, (state, action) => {
        const { section, optimizedContent } = action.payload;
        
        if (state.profile && optimizedContent) {
          // Update suggestions based on section
          switch (section) {
            case 'headline':
              if (optimizedContent.suggestions) {
                state.profile.headlineSuggestions = optimizedContent.suggestions;
              }
              break;
            case 'summary':
              if (optimizedContent.suggestions) {
                state.profile.summarySuggestions = optimizedContent.suggestions;
              }
              break;
            case 'about':
              // About section suggestions could be stored separately or in summarySuggestions
              break;
            case 'skills':
              if (optimizedContent.keywords) {
                state.profile.keywordSuggestions = optimizedContent.keywords;
              }
              break;
          }
          
          // Update general keyword suggestions if provided
          if (optimizedContent.keywords) {
            state.profile.keywordSuggestions = [
              ...new Set([...state.profile.keywordSuggestions, ...optimizedContent.keywords])
            ];
          }
        }
        
        state.error = null;
      })
      .addCase(optimizeLinkedInSection.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  updateProfileField,
  setProfile,
  clearProfile,
  updateOptimizationSuggestions,
} = linkedinSlice.actions;

export default linkedinSlice.reducer;
