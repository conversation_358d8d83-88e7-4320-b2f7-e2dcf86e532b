import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { JobApplication, ApplicationState, ApplicationStatus } from '../../types';
import { apiService } from '../../services/api';
import { API_ENDPOINTS } from '../../config/api';

const initialState: ApplicationState = {
  applications: [],
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchApplications = createAsyncThunk(
  'application/fetchApplications',
  async (filters?: { status?: ApplicationStatus }, { rejectWithValue }) => {
    try {
      const response = await apiService.get(API_ENDPOINTS.APPLICATIONS.LIST, filters);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to fetch applications');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch applications');
    }
  }
);

export const fetchApplication = createAsyncThunk(
  'application/fetchApplication',
  async (applicationId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.get(API_ENDPOINTS.APPLICATIONS.GET(applicationId));
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to fetch application');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch application');
    }
  }
);

export const createApplication = createAsyncThunk(
  'application/createApplication',
  async (
    applicationData: {
      jobId: string;
      resumeId: string;
      coverLetterId?: string;
      notes?: string;
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await apiService.post(API_ENDPOINTS.APPLICATIONS.CREATE, applicationData);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to create application');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create application');
    }
  }
);

export const updateApplication = createAsyncThunk(
  'application/updateApplication',
  async (
    {
      applicationId,
      updates,
    }: {
      applicationId: string;
      updates: Partial<JobApplication>;
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await apiService.put(API_ENDPOINTS.APPLICATIONS.UPDATE(applicationId), updates);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to update application');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update application');
    }
  }
);

export const updateApplicationStatus = createAsyncThunk(
  'application/updateApplicationStatus',
  async (
    {
      applicationId,
      status,
      notes,
    }: {
      applicationId: string;
      status: ApplicationStatus;
      notes?: string;
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await apiService.patch(API_ENDPOINTS.APPLICATIONS.UPDATE(applicationId), {
        status,
        notes,
      });
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to update application status');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update application status');
    }
  }
);

export const deleteApplication = createAsyncThunk(
  'application/deleteApplication',
  async (applicationId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.delete(API_ENDPOINTS.APPLICATIONS.DELETE(applicationId));
      
      if (response.success) {
        return applicationId;
      } else {
        return rejectWithValue(response.error || 'Failed to delete application');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to delete application');
    }
  }
);

export const fetchApplicationStats = createAsyncThunk(
  'application/fetchApplicationStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.get(API_ENDPOINTS.APPLICATIONS.STATS);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to fetch application stats');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch application stats');
    }
  }
);

const applicationSlice = createSlice({
  name: 'application',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    addApplication: (state, action: PayloadAction<JobApplication>) => {
      const existingIndex = state.applications.findIndex(app => app.id === action.payload.id);
      if (existingIndex === -1) {
        state.applications.unshift(action.payload);
      }
    },
    removeApplication: (state, action: PayloadAction<string>) => {
      state.applications = state.applications.filter(app => app.id !== action.payload);
    },
    updateApplicationInList: (state, action: PayloadAction<JobApplication>) => {
      const index = state.applications.findIndex(app => app.id === action.payload.id);
      if (index !== -1) {
        state.applications[index] = action.payload;
      }
    },
    sortApplications: (state, action: PayloadAction<'date' | 'status' | 'company'>) => {
      const sortBy = action.payload;
      state.applications.sort((a, b) => {
        switch (sortBy) {
          case 'date':
            return new Date(b.appliedAt).getTime() - new Date(a.appliedAt).getTime();
          case 'status':
            return a.status.localeCompare(b.status);
          case 'company':
            return a.job.company.localeCompare(b.job.company);
          default:
            return 0;
        }
      });
    },
  },
  extraReducers: (builder) => {
    // Fetch applications
    builder
      .addCase(fetchApplications.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchApplications.fulfilled, (state, action) => {
        state.isLoading = false;
        state.applications = action.payload;
        state.error = null;
      })
      .addCase(fetchApplications.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch single application
    builder
      .addCase(fetchApplication.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchApplication.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.applications.findIndex(app => app.id === action.payload.id);
        if (index !== -1) {
          state.applications[index] = action.payload;
        } else {
          state.applications.push(action.payload);
        }
        state.error = null;
      })
      .addCase(fetchApplication.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Create application
    builder
      .addCase(createApplication.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createApplication.fulfilled, (state, action) => {
        state.isLoading = false;
        state.applications.unshift(action.payload);
        state.error = null;
      })
      .addCase(createApplication.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update application
    builder
      .addCase(updateApplication.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateApplication.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.applications.findIndex(app => app.id === action.payload.id);
        if (index !== -1) {
          state.applications[index] = action.payload;
        }
        state.error = null;
      })
      .addCase(updateApplication.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update application status
    builder
      .addCase(updateApplicationStatus.pending, (state) => {
        state.error = null;
      })
      .addCase(updateApplicationStatus.fulfilled, (state, action) => {
        const index = state.applications.findIndex(app => app.id === action.payload.id);
        if (index !== -1) {
          state.applications[index] = action.payload;
        }
        state.error = null;
      })
      .addCase(updateApplicationStatus.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Delete application
    builder
      .addCase(deleteApplication.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteApplication.fulfilled, (state, action) => {
        state.isLoading = false;
        state.applications = state.applications.filter(app => app.id !== action.payload);
        state.error = null;
      })
      .addCase(deleteApplication.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch application stats
    builder
      .addCase(fetchApplicationStats.pending, (state) => {
        state.error = null;
      })
      .addCase(fetchApplicationStats.fulfilled, (state, action) => {
        // Stats are typically handled separately or in a different slice
        state.error = null;
      })
      .addCase(fetchApplicationStats.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  addApplication,
  removeApplication,
  updateApplicationInList,
  sortApplications,
} = applicationSlice.actions;

export default applicationSlice.reducer;
