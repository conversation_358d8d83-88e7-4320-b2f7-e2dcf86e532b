import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Job, JobState, JobFilters } from '../../types';
import { apiService } from '../../services/api';
import { API_ENDPOINTS } from '../../config/api';

const initialState: JobState = {
  jobs: [],
  savedJobs: [],
  filters: {},
  isLoading: false,
  error: null,
};

// Async thunks
export const searchJobs = createAsyncThunk(
  'job/searchJobs',
  async (filters: JobFilters, { rejectWithValue }) => {
    try {
      const response = await apiService.get(API_ENDPOINTS.JOBS.SEARCH, filters);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to search jobs');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to search jobs');
    }
  }
);

export const fetchJob = createAsyncThunk(
  'job/fetchJob',
  async (jobId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.get(API_ENDPOINTS.JOBS.GET(jobId));
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to fetch job');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch job');
    }
  }
);

export const saveJob = createAsyncThunk(
  'job/saveJob',
  async (jobId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.post(API_ENDPOINTS.JOBS.SAVE(jobId));
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to save job');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to save job');
    }
  }
);

export const unsaveJob = createAsyncThunk(
  'job/unsaveJob',
  async (jobId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.delete(API_ENDPOINTS.JOBS.UNSAVE(jobId));
      
      if (response.success) {
        return jobId;
      } else {
        return rejectWithValue(response.error || 'Failed to unsave job');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to unsave job');
    }
  }
);

export const fetchSavedJobs = createAsyncThunk(
  'job/fetchSavedJobs',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.get(API_ENDPOINTS.JOBS.SAVED);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to fetch saved jobs');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch saved jobs');
    }
  }
);

export const applyToJob = createAsyncThunk(
  'job/applyToJob',
  async (
    { jobId, resumeId, coverLetterId }: { jobId: string; resumeId: string; coverLetterId?: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await apiService.post(API_ENDPOINTS.JOBS.APPLY(jobId), {
        resumeId,
        coverLetterId,
      });
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to apply to job');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to apply to job');
    }
  }
);

const jobSlice = createSlice({
  name: 'job',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setFilters: (state, action: PayloadAction<JobFilters>) => {
      state.filters = action.payload;
    },
    updateFilters: (state, action: PayloadAction<Partial<JobFilters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {};
    },
    clearJobs: (state) => {
      state.jobs = [];
    },
    addJob: (state, action: PayloadAction<Job>) => {
      const existingIndex = state.jobs.findIndex(job => job.id === action.payload.id);
      if (existingIndex === -1) {
        state.jobs.push(action.payload);
      }
    },
    removeJob: (state, action: PayloadAction<string>) => {
      state.jobs = state.jobs.filter(job => job.id !== action.payload);
    },
  },
  extraReducers: (builder) => {
    // Search jobs
    builder
      .addCase(searchJobs.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(searchJobs.fulfilled, (state, action) => {
        state.isLoading = false;
        state.jobs = action.payload.data || action.payload;
        state.error = null;
      })
      .addCase(searchJobs.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch single job
    builder
      .addCase(fetchJob.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchJob.fulfilled, (state, action) => {
        state.isLoading = false;
        const existingIndex = state.jobs.findIndex(job => job.id === action.payload.id);
        if (existingIndex !== -1) {
          state.jobs[existingIndex] = action.payload;
        } else {
          state.jobs.push(action.payload);
        }
        state.error = null;
      })
      .addCase(fetchJob.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Save job
    builder
      .addCase(saveJob.pending, (state) => {
        state.error = null;
      })
      .addCase(saveJob.fulfilled, (state, action) => {
        const job = action.payload;
        const existingIndex = state.savedJobs.findIndex(savedJob => savedJob.id === job.id);
        if (existingIndex === -1) {
          state.savedJobs.push(job);
        }
        state.error = null;
      })
      .addCase(saveJob.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Unsave job
    builder
      .addCase(unsaveJob.pending, (state) => {
        state.error = null;
      })
      .addCase(unsaveJob.fulfilled, (state, action) => {
        state.savedJobs = state.savedJobs.filter(job => job.id !== action.payload);
        state.error = null;
      })
      .addCase(unsaveJob.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Fetch saved jobs
    builder
      .addCase(fetchSavedJobs.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSavedJobs.fulfilled, (state, action) => {
        state.isLoading = false;
        state.savedJobs = action.payload;
        state.error = null;
      })
      .addCase(fetchSavedJobs.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Apply to job
    builder
      .addCase(applyToJob.pending, (state) => {
        state.error = null;
      })
      .addCase(applyToJob.fulfilled, (state, action) => {
        // Application created successfully
        state.error = null;
      })
      .addCase(applyToJob.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setFilters,
  updateFilters,
  clearFilters,
  clearJobs,
  addJob,
  removeJob,
} = jobSlice.actions;

export default jobSlice.reducer;
