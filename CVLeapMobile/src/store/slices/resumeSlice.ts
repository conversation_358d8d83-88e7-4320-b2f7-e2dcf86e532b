import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Resume, ResumeState } from '../../types';
import { apiService } from '../../services/api';
import { API_ENDPOINTS } from '../../config/api';

const initialState: ResumeState = {
  resumes: [],
  currentResume: null,
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchResumes = createAsyncThunk(
  'resume/fetchResumes',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.get(API_ENDPOINTS.RESUMES.LIST);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to fetch resumes');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch resumes');
    }
  }
);

export const fetchResume = createAsyncThunk(
  'resume/fetchResume',
  async (resumeId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.get(API_ENDPOINTS.RESUMES.GET(resumeId));
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to fetch resume');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch resume');
    }
  }
);

export const createResume = createAsyncThunk(
  'resume/createResume',
  async (resumeData: Partial<Resume>, { rejectWithValue }) => {
    try {
      const response = await apiService.post(API_ENDPOINTS.RESUMES.CREATE, resumeData);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to create resume');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create resume');
    }
  }
);

export const updateResume = createAsyncThunk(
  'resume/updateResume',
  async ({ resumeId, updates }: { resumeId: string; updates: Partial<Resume> }, { rejectWithValue }) => {
    try {
      const response = await apiService.put(API_ENDPOINTS.RESUMES.UPDATE(resumeId), updates);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to update resume');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update resume');
    }
  }
);

export const deleteResume = createAsyncThunk(
  'resume/deleteResume',
  async (resumeId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.delete(API_ENDPOINTS.RESUMES.DELETE(resumeId));
      
      if (response.success) {
        return resumeId;
      } else {
        return rejectWithValue(response.error || 'Failed to delete resume');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to delete resume');
    }
  }
);

export const duplicateResume = createAsyncThunk(
  'resume/duplicateResume',
  async (resumeId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.post(API_ENDPOINTS.RESUMES.DUPLICATE(resumeId));
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to duplicate resume');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to duplicate resume');
    }
  }
);

export const generateATSScore = createAsyncThunk(
  'resume/generateATSScore',
  async (resumeId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.post(API_ENDPOINTS.RESUMES.ATS_SCORE(resumeId));
      
      if (response.success && response.data) {
        return { resumeId, atsScore: response.data.score };
      } else {
        return rejectWithValue(response.error || 'Failed to generate ATS score');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to generate ATS score');
    }
  }
);

const resumeSlice = createSlice({
  name: 'resume',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentResume: (state, action: PayloadAction<Resume | null>) => {
      state.currentResume = action.payload;
    },
    updateCurrentResume: (state, action: PayloadAction<Partial<Resume>>) => {
      if (state.currentResume) {
        state.currentResume = { ...state.currentResume, ...action.payload };
      }
    },
    clearCurrentResume: (state) => {
      state.currentResume = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch resumes
    builder
      .addCase(fetchResumes.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchResumes.fulfilled, (state, action) => {
        state.isLoading = false;
        state.resumes = action.payload;
        state.error = null;
      })
      .addCase(fetchResumes.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch single resume
    builder
      .addCase(fetchResume.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchResume.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentResume = action.payload;
        state.error = null;
      })
      .addCase(fetchResume.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Create resume
    builder
      .addCase(createResume.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createResume.fulfilled, (state, action) => {
        state.isLoading = false;
        state.resumes.unshift(action.payload);
        state.currentResume = action.payload;
        state.error = null;
      })
      .addCase(createResume.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update resume
    builder
      .addCase(updateResume.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateResume.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.resumes.findIndex(r => r.id === action.payload.id);
        if (index !== -1) {
          state.resumes[index] = action.payload;
        }
        if (state.currentResume?.id === action.payload.id) {
          state.currentResume = action.payload;
        }
        state.error = null;
      })
      .addCase(updateResume.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Delete resume
    builder
      .addCase(deleteResume.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteResume.fulfilled, (state, action) => {
        state.isLoading = false;
        state.resumes = state.resumes.filter(r => r.id !== action.payload);
        if (state.currentResume?.id === action.payload) {
          state.currentResume = null;
        }
        state.error = null;
      })
      .addCase(deleteResume.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Duplicate resume
    builder
      .addCase(duplicateResume.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(duplicateResume.fulfilled, (state, action) => {
        state.isLoading = false;
        state.resumes.unshift(action.payload);
        state.error = null;
      })
      .addCase(duplicateResume.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Generate ATS score
    builder
      .addCase(generateATSScore.pending, (state) => {
        state.error = null;
      })
      .addCase(generateATSScore.fulfilled, (state, action) => {
        const { resumeId, atsScore } = action.payload;
        const index = state.resumes.findIndex(r => r.id === resumeId);
        if (index !== -1) {
          state.resumes[index].atsScore = atsScore;
        }
        if (state.currentResume?.id === resumeId) {
          state.currentResume.atsScore = atsScore;
        }
        state.error = null;
      })
      .addCase(generateATSScore.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setCurrentResume, updateCurrentResume, clearCurrentResume } = resumeSlice.actions;
export default resumeSlice.reducer;
