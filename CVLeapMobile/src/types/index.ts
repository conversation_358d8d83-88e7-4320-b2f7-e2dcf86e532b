// User Types
export interface User {
  id: string;
  email: string;
  name?: string;
  image?: string;
  emailVerified?: Date;
}

export interface UserProfile {
  id: string;
  userId: string;
  phone?: string;
  location?: string;
  website?: string;
  linkedinUrl?: string;
  githubUrl?: string;
  summary?: string;
  skills: Skill[];
  experiences: Experience[];
  educations: Education[];
  certifications: Certification[];
}

// Resume Types
export interface Resume {
  id: string;
  userId: string;
  title: string;
  content: any;
  template: string;
  isPublic: boolean;
  atsScore?: number;
  createdAt: Date;
  updatedAt: Date;
}

// Job Types
export interface Job {
  id: string;
  title: string;
  company: string;
  location: string;
  type: JobType;
  remote: boolean;
  salaryMin?: number;
  salaryMax?: number;
  description: string;
  requirements: string[];
  skills: string[];
  postedDate: Date;
  applicationDeadline?: Date;
  source: string;
  url: string;
  atsKeywords: string[];
}

export enum JobType {
  FULL_TIME = 'FULL_TIME',
  PART_TIME = 'PART_TIME',
  CONTRACT = 'CONTRACT',
  FREELANCE = 'FREELANCE',
  INTERNSHIP = 'INTERNSHIP',
}

// Application Types
export interface JobApplication {
  id: string;
  userId: string;
  jobId: string;
  resumeId: string;
  coverLetterId?: string;
  status: ApplicationStatus;
  appliedAt: Date;
  notes?: string;
  followUpDate?: Date;
  interviewDate?: Date;
  feedback?: string;
  job: Job;
  resume: Resume;
}

export enum ApplicationStatus {
  SAVED = 'SAVED',
  APPLIED = 'APPLIED',
  UNDER_REVIEW = 'UNDER_REVIEW',
  INTERVIEW_SCHEDULED = 'INTERVIEW_SCHEDULED',
  INTERVIEWED = 'INTERVIEWED',
  OFFER_RECEIVED = 'OFFER_RECEIVED',
  REJECTED = 'REJECTED',
  WITHDRAWN = 'WITHDRAWN',
}

// LinkedIn Types
export interface LinkedInProfile {
  id: string;
  userId: string;
  headline?: string;
  industry?: string;
  location?: string;
  summary?: string;
  currentPosition?: string;
  currentCompany?: string;
  connectionsCount?: number;
  profileViews?: number;
  searchAppearances?: number;
  profileStrength?: string;
  profileUrl?: string;
  profileImageUrl?: string;
  isOpenToWork: boolean;
  profileScore?: number;
  completenessScore?: number;
  visibilityScore?: number;
  engagementScore?: number;
  missingFields: string[];
  optimizationTips: string[];
  keywordSuggestions: string[];
  headlineSuggestions: string[];
  summarySuggestions: string[];
}

// Common Types
export interface Skill {
  id: string;
  name: string;
  level: SkillLevel;
  category?: string;
}

export enum SkillLevel {
  BEGINNER = 'BEGINNER',
  INTERMEDIATE = 'INTERMEDIATE',
  ADVANCED = 'ADVANCED',
  EXPERT = 'EXPERT',
}

export interface Experience {
  id: string;
  title: string;
  company: string;
  location?: string;
  startDate: Date;
  endDate?: Date;
  current: boolean;
  description?: string;
  achievements: string[];
}

export interface Education {
  id: string;
  institution: string;
  degree: string;
  field: string;
  startDate: Date;
  endDate?: Date;
  gpa?: number;
  description?: string;
}

export interface Certification {
  id: string;
  name: string;
  issuer: string;
  issueDate: Date;
  expiryDate?: Date;
  credentialId?: string;
  url?: string;
}

// Navigation Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Resumes: undefined;
  Jobs: undefined;
  Applications: undefined;
  LinkedIn: undefined;
  Profile: undefined;
};

export type ResumeStackParamList = {
  ResumeList: undefined;
  ResumeCreate: undefined;
  ResumeEdit: { resumeId: string };
  ResumePreview: { resumeId: string };
};

export type JobStackParamList = {
  JobSearch: undefined;
  JobDetails: { jobId: string };
  JobFilters: undefined;
};

export type ApplicationStackParamList = {
  ApplicationList: undefined;
  ApplicationDetails: { applicationId: string };
  ApplicationCreate: { jobId: string };
};

export type LinkedInStackParamList = {
  LinkedInProfile: undefined;
  LinkedInOptimize: undefined;
  LinkedInAnalysis: undefined;
};

// API Response Types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// State Types
export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface ResumeState {
  resumes: Resume[];
  currentResume: Resume | null;
  isLoading: boolean;
  error: string | null;
}

export interface JobState {
  jobs: Job[];
  savedJobs: Job[];
  filters: JobFilters;
  isLoading: boolean;
  error: string | null;
}

export interface JobFilters {
  query?: string;
  location?: string;
  type?: JobType[];
  remote?: boolean;
  salaryMin?: number;
  salaryMax?: number;
  skills?: string[];
  source?: string[];
}

export interface ApplicationState {
  applications: JobApplication[];
  isLoading: boolean;
  error: string | null;
}

export interface LinkedInState {
  profile: LinkedInProfile | null;
  isLoading: boolean;
  error: string | null;
}

// Notification Types
export interface NotificationData {
  id: string;
  title: string;
  body: string;
  data?: any;
  type: NotificationType;
  createdAt: Date;
  read: boolean;
}

export enum NotificationType {
  JOB_ALERT = 'JOB_ALERT',
  APPLICATION_UPDATE = 'APPLICATION_UPDATE',
  INTERVIEW_REMINDER = 'INTERVIEW_REMINDER',
  PROFILE_TIP = 'PROFILE_TIP',
  SYSTEM = 'SYSTEM',
}
