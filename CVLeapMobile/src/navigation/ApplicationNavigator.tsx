import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { ApplicationStackParamList } from '../types';

// Placeholder screens
import ApplicationListScreen from '../screens/applications/ApplicationListScreen';
import ApplicationDetailsScreen from '../screens/applications/ApplicationDetailsScreen';

const Stack = createStackNavigator<ApplicationStackParamList>();

export default function ApplicationNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#FFFFFF',
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.1,
          shadowRadius: 3.84,
          elevation: 5,
        },
        headerTintColor: '#1F2937',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen
        name="ApplicationList"
        component={ApplicationListScreen}
        options={{
          title: 'My Applications',
        }}
      />
      <Stack.Screen
        name="ApplicationDetails"
        component={ApplicationDetailsScreen}
        options={{
          title: 'Application Details',
        }}
      />
    </Stack.Navigator>
  );
}
