import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { LinkedInStackParamList } from '../types';

// Placeholder screens
import LinkedInProfileScreen from '../screens/linkedin/LinkedInProfileScreen';
import LinkedInOptimizationScreen from '../screens/linkedin/LinkedInOptimizationScreen';

const Stack = createStackNavigator<LinkedInStackParamList>();

export default function LinkedInNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#FFFFFF',
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.1,
          shadowRadius: 3.84,
          elevation: 5,
        },
        headerTintColor: '#1F2937',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen
        name="LinkedInProfile"
        component={LinkedInProfileScreen}
        options={{
          title: 'LinkedIn Profile',
        }}
      />
      <Stack.Screen
        name="LinkedInOptimization"
        component={LinkedInOptimizationScreen}
        options={{
          title: 'Profile Optimization',
        }}
      />
    </Stack.Navigator>
  );
}
