import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { ResumeStackParamList } from '../types';

// Screens
import ResumeListScreen from '../screens/resume/ResumeListScreen';
import ResumeEditorScreen from '../screens/resume/ResumeEditorScreen';
import ResumePreviewScreen from '../screens/resume/ResumePreviewScreen';

const Stack = createStackNavigator<ResumeStackParamList>();

export default function ResumeNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#FFFFFF',
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.1,
          shadowRadius: 3.84,
          elevation: 5,
        },
        headerTintColor: '#1F2937',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen
        name="ResumeList"
        component={ResumeListScreen}
        options={{
          title: 'My Resumes',
        }}
      />
      <Stack.Screen
        name="ResumeEditor"
        component={ResumeEditorScreen}
        options={{
          title: 'Edit Resume',
        }}
      />
      <Stack.Screen
        name="ResumePreview"
        component={ResumePreviewScreen}
        options={{
          title: 'Preview Resume',
        }}
      />
    </Stack.Navigator>
  );
}
