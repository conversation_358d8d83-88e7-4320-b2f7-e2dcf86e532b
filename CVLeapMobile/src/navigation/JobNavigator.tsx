import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { JobStackParamList } from '../types';

// Placeholder screens
import JobSearchScreen from '../screens/jobs/JobSearchScreen';
import JobDetailsScreen from '../screens/jobs/JobDetailsScreen';
import SavedJobsScreen from '../screens/jobs/SavedJobsScreen';

const Stack = createStackNavigator<JobStackParamList>();

export default function JobNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#FFFFFF',
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.1,
          shadowRadius: 3.84,
          elevation: 5,
        },
        headerTintColor: '#1F2937',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen
        name="JobSearch"
        component={JobSearchScreen}
        options={{
          title: 'Job Search',
        }}
      />
      <Stack.Screen
        name="JobDetails"
        component={JobDetailsScreen}
        options={{
          title: 'Job Details',
        }}
      />
      <Stack.Screen
        name="SavedJobs"
        component={SavedJobsScreen}
        options={{
          title: 'Saved Jobs',
        }}
      />
    </Stack.Navigator>
  );
}
