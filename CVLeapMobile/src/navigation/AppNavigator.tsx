import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { useAppDispatch, useAppSelector } from '../store';
import { loadStoredAuth } from '../store/slices/authSlice';
import { RootStackParamList, MainTabParamList } from '../types';

// Auth Screens
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';
import ForgotPasswordScreen from '../screens/auth/ForgotPasswordScreen';

// Main Screens
import DashboardScreen from '../screens/dashboard/DashboardScreen';
import ResumeNavigator from './ResumeNavigator';
import JobNavigator from './JobNavigator';
import ApplicationNavigator from './ApplicationNavigator';
import LinkedInNavigator from './LinkedInNavigator';
import ProfileScreen from '../screens/profile/ProfileScreen';

// Components
import LoadingScreen from '../components/common/LoadingScreen';
import TabBarIcon from '../components/navigation/TabBarIcon';
import DrawerContent from '../components/navigation/DrawerContent';

// Icons (you'll need to install react-native-vector-icons or use Expo icons)
import Icon from 'react-native-vector-icons/MaterialIcons';

const RootStack = createStackNavigator<RootStackParamList>();
const MainTab = createBottomTabNavigator<MainTabParamList>();
const Drawer = createDrawerNavigator();

// Main Tab Navigator
function MainTabNavigator() {
  return (
    <MainTab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Dashboard':
              iconName = 'dashboard';
              break;
            case 'Resumes':
              iconName = 'description';
              break;
            case 'Jobs':
              iconName = 'work';
              break;
            case 'Applications':
              iconName = 'assignment';
              break;
            case 'LinkedIn':
              iconName = 'business';
              break;
            case 'Profile':
              iconName = 'person';
              break;
            default:
              iconName = 'help';
          }

          return <TabBarIcon name={iconName} size={size} color={color} focused={focused} />;
        },
        tabBarActiveTintColor: '#3B82F6',
        tabBarInactiveTintColor: '#6B7280',
        tabBarStyle: {
          backgroundColor: '#FFFFFF',
          borderTopWidth: 1,
          borderTopColor: '#E5E7EB',
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
        headerShown: false,
      })}
    >
      <MainTab.Screen 
        name="Dashboard" 
        component={DashboardScreen}
        options={{ tabBarLabel: 'Dashboard' }}
      />
      <MainTab.Screen 
        name="Resumes" 
        component={ResumeNavigator}
        options={{ tabBarLabel: 'Resumes' }}
      />
      <MainTab.Screen 
        name="Jobs" 
        component={JobNavigator}
        options={{ tabBarLabel: 'Jobs' }}
      />
      <MainTab.Screen 
        name="Applications" 
        component={ApplicationNavigator}
        options={{ tabBarLabel: 'Applications' }}
      />
      <MainTab.Screen 
        name="LinkedIn" 
        component={LinkedInNavigator}
        options={{ tabBarLabel: 'LinkedIn' }}
      />
      <MainTab.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{ tabBarLabel: 'Profile' }}
      />
    </MainTab.Navigator>
  );
}

// Main Drawer Navigator (for larger screens or additional navigation)
function MainDrawerNavigator() {
  return (
    <Drawer.Navigator
      drawerContent={(props) => <DrawerContent {...props} />}
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          backgroundColor: '#FFFFFF',
          width: 280,
        },
        drawerActiveTintColor: '#3B82F6',
        drawerInactiveTintColor: '#6B7280',
      }}
    >
      <Drawer.Screen 
        name="MainTabs" 
        component={MainTabNavigator}
        options={{
          drawerLabel: 'Home',
          drawerIcon: ({ color, size }) => (
            <Icon name="home" size={size} color={color} />
          ),
        }}
      />
    </Drawer.Navigator>
  );
}

// Auth Stack Navigator
function AuthNavigator() {
  return (
    <RootStack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: '#FFFFFF' },
      }}
    >
      <RootStack.Screen name="Login" component={LoginScreen} />
      <RootStack.Screen name="Register" component={RegisterScreen} />
      <RootStack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
    </RootStack.Navigator>
  );
}

// Main App Navigator
export default function AppNavigator() {
  const dispatch = useAppDispatch();
  const { isAuthenticated, isLoading } = useAppSelector((state) => state.auth);

  useEffect(() => {
    // Load stored authentication on app start
    dispatch(loadStoredAuth());
  }, [dispatch]);

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <NavigationContainer>
      <RootStack.Navigator screenOptions={{ headerShown: false }}>
        {isAuthenticated ? (
          <RootStack.Screen name="Main" component={MainDrawerNavigator} />
        ) : (
          <RootStack.Screen name="Auth" component={AuthNavigator} />
        )}
      </RootStack.Navigator>
    </NavigationContainer>
  );
}
