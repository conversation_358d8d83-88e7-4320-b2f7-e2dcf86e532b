{"name": "cvleapmobile", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.7", "@react-navigation/drawer": "^7.5.8", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.8", "@reduxjs/toolkit": "^2.9.0", "expo": "~54.0.7", "expo-camera": "^17.0.7", "expo-document-picker": "^14.0.7", "expo-local-authentication": "^17.0.7", "expo-notifications": "^0.32.11", "expo-secure-store": "^15.0.7", "expo-sharing": "^14.0.7", "expo-status-bar": "~3.0.8", "react": "19.1.0", "react-native": "0.81.4", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "^2.28.0", "react-native-keychain": "^10.0.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "^4.1.0", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "^4.16.0", "react-native-svg": "^15.13.0", "react-native-vector-icons": "^10.3.0", "react-redux": "^9.2.0"}, "devDependencies": {"@types/react": "~19.1.0", "typescript": "~5.9.2"}, "private": true}