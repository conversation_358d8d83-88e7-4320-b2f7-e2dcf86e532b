# CVLeap Mobile - Native iOS and Android App

CVLeap Mobile is a React Native application built with Expo that provides the full CVLeap experience optimized for mobile devices. The app offers native-quality performance with seamless integration to the CVLeap web platform.

## 🚀 Features

### Core Features
- **Authentication & Security**
  - Secure login/register with JWT tokens
  - Biometric authentication support (Face ID, Touch ID, Fingerprint)
  - Automatic token refresh and session management
  - Password reset functionality

- **Resume Builder**
  - Touch-optimized resume creation and editing
  - Real-time preview and ATS scoring
  - Multiple resume templates
  - Export to PDF and share functionality

- **Job Search & Discovery**
  - Swipe-based job browsing
  - Advanced search filters and location-based search
  - Quick apply functionality
  - Save jobs for later review

- **Application Tracking**
  - Kanban-style application board
  - Status tracking and timeline
  - Interview scheduling and reminders
  - Application analytics and insights

- **LinkedIn Profile Optimization**
  - AI-powered profile analysis
  - Optimization suggestions and keyword recommendations
  - Profile completeness scoring
  - Industry benchmarking

### Mobile-Specific Features
- **Push Notifications**
  - Job alerts and application updates
  - Interview reminders
  - Profile optimization tips
  - Real-time status notifications

- **Offline Support**
  - Local data caching
  - Offline resume editing
  - Sync when connection restored

- **Native Capabilities**
  - Camera integration for profile photos
  - Document picker for file uploads
  - Haptic feedback for interactions
  - Deep linking support

## 🛠 Tech Stack

- **Framework**: React Native with Expo
- **Language**: TypeScript
- **State Management**: Redux Toolkit
- **Navigation**: React Navigation 6
- **UI Components**: React Native Paper + Custom Components
- **Icons**: React Native Vector Icons
- **Storage**: AsyncStorage + Expo SecureStore
- **Notifications**: Expo Notifications
- **Authentication**: JWT with biometric support

## 📱 Installation & Setup

### Prerequisites
- Node.js 18+ and npm
- Expo CLI (`npm install -g @expo/cli`)
- iOS Simulator (for iOS development)
- Android Studio/Emulator (for Android development)

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd CVLeapMobile

# Install dependencies
npm install

# Start the development server
npm start
```

### Running on Devices
```bash
# iOS Simulator
npm run ios

# Android Emulator
npm run android

# Web Browser
npm run web

# Physical Device (Expo Go)
# Scan QR code with Expo Go app
```

## 📁 Project Structure

```
CVLeapMobile/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── common/         # Common components (LoadingScreen, etc.)
│   │   ├── dashboard/      # Dashboard-specific components
│   │   └── navigation/     # Navigation components
│   ├── navigation/         # Navigation configuration
│   │   ├── AppNavigator.tsx
│   │   ├── ResumeNavigator.tsx
│   │   ├── JobNavigator.tsx
│   │   └── ...
│   ├── screens/           # Screen components
│   │   ├── auth/          # Authentication screens
│   │   ├── dashboard/     # Dashboard screens
│   │   ├── resume/        # Resume-related screens
│   │   ├── jobs/          # Job search screens
│   │   ├── applications/  # Application tracking screens
│   │   ├── linkedin/      # LinkedIn optimization screens
│   │   └── profile/       # Profile settings screens
│   ├── store/             # Redux store configuration
│   │   ├── slices/        # Redux slices
│   │   └── index.ts       # Store configuration
│   ├── services/          # API and external services
│   │   ├── api.ts         # HTTP client
│   │   └── NotificationService.ts
│   ├── config/            # Configuration files
│   │   └── api.ts         # API endpoints and config
│   └── types/             # TypeScript type definitions
│       └── index.ts
├── App.tsx                # Main app component
├── package.json
└── README.md
```

## 🔧 Configuration

### API Configuration
Update `src/config/api.ts` with your backend API URL:
```typescript
export const API_CONFIG = {
  BASE_URL: 'https://your-api-domain.com',
  TIMEOUT: 10000,
  // ...
};
```

### Push Notifications
Update `src/services/NotificationService.ts` with your Expo project ID:
```typescript
const token = await Notifications.getExpoPushTokenAsync({
  projectId: 'your-expo-project-id',
});
```

## 🚀 Development

### State Management
The app uses Redux Toolkit for state management with the following slices:
- `authSlice` - Authentication and user management
- `resumeSlice` - Resume data and operations
- `jobSlice` - Job search and saved jobs
- `applicationSlice` - Application tracking
- `linkedinSlice` - LinkedIn profile optimization
- `notificationSlice` - Push notifications and settings

### API Integration
All API calls are handled through the centralized `apiService` with:
- Automatic token management
- Request/response interceptors
- Error handling and retry logic
- Network status monitoring

### Navigation
The app uses React Navigation with:
- Stack navigators for hierarchical navigation
- Tab navigator for main sections
- Drawer navigator for additional options
- Deep linking support

## 📱 Platform-Specific Features

### iOS
- Face ID/Touch ID authentication
- iOS-specific UI components
- Haptic feedback
- iOS Shortcuts integration (planned)

### Android
- Fingerprint authentication
- Material Design components
- Android widgets (planned)
- Background sync

## 🔒 Security

- JWT token storage in Expo SecureStore
- Biometric authentication for app access
- Automatic token refresh
- Secure API communication with HTTPS
- Input validation and sanitization

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run E2E tests
npm run test:e2e
```

## 📦 Building for Production

### iOS
```bash
# Build for iOS
eas build --platform ios

# Submit to App Store
eas submit --platform ios
```

### Android
```bash
# Build for Android
eas build --platform android

# Submit to Google Play
eas submit --platform android
```

## 🔄 Backend Integration

The mobile app integrates with the CVLeap web platform backend:
- Shared user accounts and authentication
- Real-time data synchronization
- Cross-platform resume and application data
- Unified notification system

## 📈 Performance Optimization

- Lazy loading of screens and components
- Image optimization and caching
- Efficient list rendering with FlatList
- Memory management for large datasets
- Network request optimization

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

## 🗺 Roadmap

### Phase 1 (Current)
- ✅ Core app architecture and navigation
- ✅ Authentication and user management
- ✅ Basic UI components and screens
- ✅ Redux state management
- ✅ API integration

### Phase 2 (Next)
- 🔄 Complete resume builder implementation
- 🔄 Advanced job search with filters
- 🔄 Application tracking with Kanban board
- 🔄 LinkedIn profile optimization tools
- 🔄 Push notification implementation

### Phase 3 (Future)
- 📱 Offline mode and data sync
- 🎨 Advanced UI animations
- 📊 Analytics and insights dashboard
- 🔗 Deep linking and sharing
- 🌐 Multi-language support

---

**CVLeap Mobile** - Accelerate your career on the go! 🚀
