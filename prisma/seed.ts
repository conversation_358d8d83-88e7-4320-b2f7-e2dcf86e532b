import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Create Resume Templates
  const templates = [
    {
      id: 'modern-professional',
      name: 'Modern Professional',
      category: 'Professional',
      description: 'Clean, modern design perfect for corporate environments and professional roles.',
      preview: '/templates/modern-professional.png',
      isPremium: false,
      atsOptimized: true,
      styles: {
        colors: {
          primary: '#3b82f6',
          secondary: '#f1f5f9',
          text: '#0f172a',
          background: '#ffffff',
        },
        fonts: {
          heading: 'Inter',
          body: 'Inter',
        },
        layout: 'single-column',
      },
    },
    {
      id: 'creative-designer',
      name: 'Creative Designer',
      category: 'Creative',
      description: 'Bold, creative design ideal for designers, artists, and creative professionals.',
      preview: '/templates/creative-designer.png',
      isPremium: true,
      atsOptimized: true,
      styles: {
        colors: {
          primary: '#8b5cf6',
          secondary: '#f3e8ff',
          text: '#1f2937',
          background: '#ffffff',
        },
        fonts: {
          heading: 'Poppins',
          body: 'Inter',
        },
        layout: 'two-column',
      },
    },
    {
      id: 'tech-minimalist',
      name: 'Tech Minimalist',
      category: 'Technology',
      description: 'Minimalist design optimized for tech roles and software engineering positions.',
      preview: '/templates/tech-minimalist.png',
      isPremium: false,
      atsOptimized: true,
      styles: {
        colors: {
          primary: '#10b981',
          secondary: '#ecfdf5',
          text: '#111827',
          background: '#ffffff',
        },
        fonts: {
          heading: 'JetBrains Mono',
          body: 'Inter',
        },
        layout: 'modern',
      },
    },
    {
      id: 'executive-classic',
      name: 'Executive Classic',
      category: 'Executive',
      description: 'Traditional, elegant design for senior executives and C-level positions.',
      preview: '/templates/executive-classic.png',
      isPremium: true,
      atsOptimized: true,
      styles: {
        colors: {
          primary: '#1f2937',
          secondary: '#f9fafb',
          text: '#111827',
          background: '#ffffff',
        },
        fonts: {
          heading: 'Playfair Display',
          body: 'Inter',
        },
        layout: 'classic',
      },
    },
    {
      id: 'startup-dynamic',
      name: 'Startup Dynamic',
      category: 'Startup',
      description: 'Dynamic, energetic design perfect for startup environments and entrepreneurial roles.',
      preview: '/templates/startup-dynamic.png',
      isPremium: false,
      atsOptimized: true,
      styles: {
        colors: {
          primary: '#f59e0b',
          secondary: '#fef3c7',
          text: '#1f2937',
          background: '#ffffff',
        },
        fonts: {
          heading: 'Montserrat',
          body: 'Inter',
        },
        layout: 'modern',
      },
    },
    {
      id: 'academic-scholar',
      name: 'Academic Scholar',
      category: 'Academic',
      description: 'Scholarly design ideal for academic positions, research roles, and educational institutions.',
      preview: '/templates/academic-scholar.png',
      isPremium: true,
      atsOptimized: true,
      styles: {
        colors: {
          primary: '#1e40af',
          secondary: '#dbeafe',
          text: '#1f2937',
          background: '#ffffff',
        },
        fonts: {
          heading: 'Crimson Text',
          body: 'Inter',
        },
        layout: 'classic',
      },
    },
  ];

  console.log('📄 Creating resume templates...');
  for (const template of templates) {
    await prisma.resumeTemplate.upsert({
      where: { id: template.id },
      update: template,
      create: template,
    });
  }

  // Create System Configuration
  const systemConfigs = [
    {
      key: 'openai_model',
      value: 'gpt-4-turbo-preview',
    },
    {
      key: 'max_resumes_free',
      value: '3',
    },
    {
      key: 'max_resumes_pro',
      value: '50',
    },
    {
      key: 'max_resumes_enterprise',
      value: 'unlimited',
    },
    {
      key: 'ats_score_threshold',
      value: '75',
    },
    {
      key: 'job_search_rate_limit',
      value: '100',
    },
    {
      key: 'ai_agent_rate_limit',
      value: '50',
    },
  ];

  console.log('⚙️ Creating system configuration...');
  for (const config of systemConfigs) {
    await prisma.systemConfig.upsert({
      where: { key: config.key },
      update: { value: config.value },
      create: config,
    });
  }

  // Create sample jobs for testing
  const sampleJobs = [
    {
      id: 'job-1',
      title: 'Senior Software Engineer',
      company: 'TechCorp Inc.',
      location: 'San Francisco, CA',
      type: 'FULL_TIME' as const,
      remote: true,
      salaryMin: 150000,
      salaryMax: 200000,
      salaryCurrency: 'USD',
      salaryPeriod: 'YEARLY' as const,
      description: 'We are looking for a Senior Software Engineer to join our growing team...',
      requirements: [
        '5+ years of software development experience',
        'Proficiency in React, Node.js, and TypeScript',
        'Experience with cloud platforms (AWS, GCP, or Azure)',
        'Strong problem-solving skills',
      ],
      skills: ['React', 'Node.js', 'TypeScript', 'AWS', 'PostgreSQL'],
      postedDate: new Date(),
      source: 'Company Website',
      url: 'https://techcorp.com/careers/senior-software-engineer',
      atsKeywords: ['software', 'engineer', 'react', 'nodejs', 'typescript', 'aws'],
    },
    {
      id: 'job-2',
      title: 'Product Manager',
      company: 'InnovateLabs',
      location: 'New York, NY',
      type: 'FULL_TIME' as const,
      remote: false,
      salaryMin: 120000,
      salaryMax: 160000,
      salaryCurrency: 'USD',
      salaryPeriod: 'YEARLY' as const,
      description: 'Join our product team to drive innovation and growth...',
      requirements: [
        '3+ years of product management experience',
        'Experience with agile methodologies',
        'Strong analytical and communication skills',
        'Technical background preferred',
      ],
      skills: ['Product Management', 'Agile', 'Analytics', 'Strategy'],
      postedDate: new Date(),
      source: 'LinkedIn',
      url: 'https://linkedin.com/jobs/product-manager-innovatelabs',
      atsKeywords: ['product', 'manager', 'agile', 'analytics', 'strategy'],
    },
  ];

  console.log('💼 Creating sample jobs...');
  for (const job of sampleJobs) {
    await prisma.job.upsert({
      where: { id: job.id },
      update: job,
      create: job,
    });
  }

  console.log('✅ Database seed completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during database seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
