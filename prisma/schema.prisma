// CVLeap Database Schema
// Comprehensive schema for AI-powered resume builder and job search platform

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  emailVerified DateTime?
  name          String?
  image         String?
  password      String?   // For credentials provider
  resetToken    String?   // For password reset
  resetTokenExpires DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Authentication
  accounts Account[]
  sessions Session[]

  // User Profile
  profile UserProfile?

  // User Content
  resumes         Resume[]
  jobApplications JobApplication[]
  savedJobs       SavedJob[]
  coverLetters    CoverLetter[]

  // Analytics
  analytics UserAnalytics?
  analyticsEvents AnalyticsEvent[]
  experimentAssignments UserExperimentAssignment[]
  experimentConversions ExperimentConversion[]
  onboardingProgress UserOnboardingProgress[]
  goals UserGoal[]

  // LinkedIn Profile
  linkedinProfile LinkedInProfile?

  // Subscription
  subscription Subscription?

  // AI Agent Interactions
  agentInteractions AgentInteraction[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// User Profile and Personal Information
model UserProfile {
  id          String   @id @default(cuid())
  userId      String   @unique
  phone       String?
  location    String?
  website     String?
  linkedinUrl String?
  githubUrl   String?
  summary     String?  @db.Text
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Personal Details
  skills          Skill[]
  experiences     Experience[]
  educations      Education[]
  certifications  Certification[]
  languages       Language[]
  projects        Project[]
  achievements    Achievement[]

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_profiles")
}

// LinkedIn Profile Optimization
model LinkedInProfile {
  id                    String   @id @default(cuid())
  userId                String   @unique
  headline              String?
  industry              String?
  location              String?
  summary               String?  @db.Text
  currentPosition       String?
  currentCompany        String?
  connectionsCount      Int?
  profileViews          Int?
  searchAppearances     Int?
  profileStrength       String?  // e.g., "Beginner", "Intermediate", "Advanced", "All-Star"
  profileUrl            String?
  profileImageUrl       String?
  backgroundImageUrl    String?
  isOpenToWork          Boolean  @default(false)
  openToWorkDetails     String?  @db.Text
  featuredSection       String?  @db.Text
  aboutSection          String?  @db.Text
  contactInfo           Json?    // Phone, email, website, etc.

  // Optimization Metrics
  profileScore          Int?     // 0-100 score
  keywordDensity        Float?   // Keyword optimization score
  completenessScore     Int?     // Profile completeness percentage
  visibilityScore       Int?     // Recruiter visibility score
  engagementScore       Int?     // Profile engagement metrics

  // Analysis Results
  missingFields         String[] // Fields that need to be completed
  optimizationTips      String[] // AI-generated improvement suggestions
  keywordSuggestions    String[] // Recommended keywords for industry
  headlineSuggestions   String[] // AI-generated headline options
  summarySuggestions    String[] // AI-generated summary options

  // Tracking
  lastAnalyzed          DateTime?
  lastOptimized         DateTime?
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("linkedin_profiles")
}

// Skills Management
model Skill {
  id            String      @id @default(cuid())
  name          String
  category      String?
  proficiency   Int?        @default(1) // 1-5 scale
  userProfileId String
  createdAt     DateTime    @default(now())

  userProfile UserProfile @relation(fields: [userProfileId], references: [id], onDelete: Cascade)

  @@unique([name, userProfileId])
  @@map("skills")
}

// Work Experience
model Experience {
  id            String      @id @default(cuid())
  company       String
  position      String
  location      String
  startDate     DateTime
  endDate       DateTime?
  current       Boolean     @default(false)
  description   String[]
  skills        String[]
  userProfileId String
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  userProfile UserProfile @relation(fields: [userProfileId], references: [id], onDelete: Cascade)

  @@map("experiences")
}

// Education
model Education {
  id            String      @id @default(cuid())
  institution   String
  degree        String
  field         String
  location      String
  startDate     DateTime
  endDate       DateTime?
  gpa           Float?
  achievements  String[]
  userProfileId String
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  userProfile UserProfile @relation(fields: [userProfileId], references: [id], onDelete: Cascade)

  @@map("educations")
}

// Certifications
model Certification {
  id            String      @id @default(cuid())
  name          String
  issuer        String
  issueDate     DateTime
  expiryDate    DateTime?
  credentialId  String?
  url           String?
  userProfileId String
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  userProfile UserProfile @relation(fields: [userProfileId], references: [id], onDelete: Cascade)

  @@map("certifications")
}

// Languages
model Language {
  id            String            @id @default(cuid())
  name          String
  proficiency   LanguageProficiency
  userProfileId String
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt

  userProfile UserProfile @relation(fields: [userProfileId], references: [id], onDelete: Cascade)

  @@unique([name, userProfileId])
  @@map("languages")
}

// Projects
model Project {
  id            String      @id @default(cuid())
  name          String
  description   String      @db.Text
  technologies  String[]
  url           String?
  githubUrl     String?
  startDate     DateTime
  endDate       DateTime?
  userProfileId String
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  userProfile UserProfile @relation(fields: [userProfileId], references: [id], onDelete: Cascade)

  @@map("projects")
}

// Achievements
model Achievement {
  id            String      @id @default(cuid())
  title         String
  description   String      @db.Text
  date          DateTime
  category      String
  userProfileId String
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  userProfile UserProfile @relation(fields: [userProfileId], references: [id], onDelete: Cascade)

  @@map("achievements")
}

// Resume Management
model Resume {
  id          String      @id @default(cuid())
  userId      String
  title       String
  templateId  String
  content     Json        // Stores the complete resume content
  atsScore    Int?
  isPublic    Boolean     @default(false)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  user         User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  template     ResumeTemplate   @relation(fields: [templateId], references: [id])
  applications JobApplication[]

  @@map("resumes")
}

// Resume Templates
model ResumeTemplate {
  id          String   @id @default(cuid())
  name        String
  category    String
  description String   @db.Text
  preview     String   // URL to preview image
  isPremium   Boolean  @default(false)
  atsOptimized Boolean @default(true)
  styles      Json     // Template styling configuration
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  resumes Resume[]

  @@map("resume_templates")
}

// Cover Letters
model CoverLetter {
  id          String   @id @default(cuid())
  userId      String
  title       String
  content     String   @db.Text
  jobId       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user         User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  job          Job?             @relation(fields: [jobId], references: [id])
  applications JobApplication[]

  @@map("cover_letters")
}

// Job Management
model Job {
  id                  String      @id @default(cuid())
  title               String
  company             String
  location            String
  type                JobType
  remote              Boolean     @default(false)
  salaryMin           Int?
  salaryMax           Int?
  salaryCurrency      String?     @default("USD")
  salaryPeriod        SalaryPeriod?
  description         String      @db.Text
  requirements        String[]
  skills              String[]
  postedDate          DateTime
  applicationDeadline DateTime?
  source              String      // e.g., "LinkedIn", "Indeed", "Company Website"
  url                 String
  atsKeywords         String[]
  createdAt           DateTime    @default(now())
  updatedAt           DateTime    @updatedAt

  applications JobApplication[]
  savedJobs    SavedJob[]
  coverLetters CoverLetter[]

  @@map("jobs")
}

// Job Applications Tracking
model JobApplication {
  id             String            @id @default(cuid())
  userId         String
  jobId          String
  resumeId       String
  coverLetterId  String?
  status         ApplicationStatus @default(SAVED)
  appliedDate    DateTime          @default(now())
  lastUpdated    DateTime          @updatedAt
  notes          String?           @db.Text
  followUpDate   DateTime?
  interviewDates DateTime[]
  feedback       String?           @db.Text

  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  job         Job          @relation(fields: [jobId], references: [id], onDelete: Cascade)
  resume      Resume       @relation(fields: [resumeId], references: [id])
  coverLetter CoverLetter? @relation(fields: [coverLetterId], references: [id])

  @@unique([userId, jobId])
  @@map("job_applications")
}

// Saved Jobs
model SavedJob {
  id        String   @id @default(cuid())
  userId    String
  jobId     String
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  job  Job  @relation(fields: [jobId], references: [id], onDelete: Cascade)

  @@unique([userId, jobId])
  @@map("saved_jobs")
}

// Analytics and Insights
model UserAnalytics {
  id                    String   @id @default(cuid())
  userId                String   @unique
  totalApplications     Int      @default(0)
  responseRate          Float    @default(0)
  interviewRate         Float    @default(0)
  offerRate             Float    @default(0)
  averageResponseTime   Float?   // in days
  topSkills             String[]
  industryBreakdown     Json     // { "Tech": 45, "Finance": 30, ... }
  monthlyStats          Json     // Array of monthly statistics
  lastCalculated        DateTime @default(now())
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_analytics")
}

// Subscription Management
model Subscription {
  id                   String           @id @default(cuid())
  userId               String           @unique
  plan                 SubscriptionPlan @default(FREE)
  status               SubscriptionStatus @default(ACTIVE)
  currentPeriodStart   DateTime
  currentPeriodEnd     DateTime
  cancelAtPeriodEnd    Boolean          @default(false)
  stripeCustomerId     String?
  stripeSubscriptionId String?
  createdAt            DateTime         @default(now())
  updatedAt            DateTime         @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

// AI Agent System
model AgentInteraction {
  id         String    @id @default(cuid())
  userId     String
  agentType  AgentType
  prompt     String    @db.Text
  response   String    @db.Text
  context    Json?     // Additional context data
  success    Boolean   @default(true)
  tokens     Int?      // Token usage for billing
  createdAt  DateTime  @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("agent_interactions")
}

// System Configuration
model SystemConfig {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String   @db.Text
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_config")
}

// Enums
enum LanguageProficiency {
  BASIC
  CONVERSATIONAL
  FLUENT
  NATIVE
}

enum JobType {
  FULL_TIME
  PART_TIME
  CONTRACT
  FREELANCE
  INTERNSHIP
}

enum SalaryPeriod {
  HOURLY
  MONTHLY
  YEARLY
}

enum ApplicationStatus {
  SAVED
  APPLIED
  UNDER_REVIEW
  INTERVIEW_SCHEDULED
  INTERVIEWED
  OFFER_RECEIVED
  REJECTED
  WITHDRAWN
}

enum SubscriptionPlan {
  FREE
  PRO
  ENTERPRISE
}

enum SubscriptionStatus {
  ACTIVE
  CANCELED
  PAST_DUE
  INCOMPLETE
  INCOMPLETE_EXPIRED
  TRIALING
  UNPAID
}

enum AgentType {
  RESEARCH
  RESUME
  PROFILE
  APPLICATION
  ANALYTICS
  TEMPLATE_RENDERER
  LINKEDIN_OPTIMIZER
}

// Analytics and Performance Tracking Models

model AnalyticsEvent {
  id         String   @id @default(cuid())
  userId     String?
  sessionId  String?
  eventType  String
  properties Json
  timestamp  DateTime @default(now())
  userAgent  String?
  ipAddress  String?
  referrer   String?
  page       String?

  user User? @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([eventType])
  @@index([timestamp])
  @@map("analytics_events")
}

model Experiment {
  id          String   @id @default(cuid())
  name        String
  description String
  type        String
  status      String
  config      Json
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  assignments UserExperimentAssignment[]
  conversions ExperimentConversion[]

  @@map("experiments")
}

model UserExperimentAssignment {
  id           String   @id @default(cuid())
  userId       String
  experimentId String
  variantId    String
  assignedAt   DateTime @default(now())

  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  experiment Experiment @relation(fields: [experimentId], references: [id], onDelete: Cascade)

  @@unique([userId, experimentId])
  @@map("user_experiment_assignments")
}

model ExperimentConversion {
  id           String   @id @default(cuid())
  userId       String
  experimentId String
  variantId    String
  metric       String
  value        Float
  timestamp    DateTime @default(now())

  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  experiment Experiment @relation(fields: [experimentId], references: [id], onDelete: Cascade)

  @@index([experimentId, variantId])
  @@map("experiment_conversions")
}

model OnboardingFlow {
  id          String   @id @default(cuid())
  type        String
  name        String
  description String
  config      Json
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  userProgress UserOnboardingProgress[]

  @@map("onboarding_flows")
}

model UserOnboardingProgress {
  id             String   @id @default(cuid())
  userId         String
  flowId         String
  currentStepId  String?
  completedSteps String[]
  skippedSteps   String[]
  startedAt      DateTime @default(now())
  completedAt    DateTime?
  lastActiveAt   DateTime @default(now())
  metadata       Json?

  user User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  flow OnboardingFlow @relation(fields: [flowId], references: [id], onDelete: Cascade)

  @@unique([userId, flowId])
  @@map("user_onboarding_progress")
}

model UserGoal {
  id          String    @id @default(cuid())
  userId      String
  type        String
  title       String
  description String
  target      Float
  current     Float     @default(0)
  deadline    DateTime?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("user_goals")
}
