// Database seeder for resume templates
// This script populates the database with the extracted Figma templates

import { PrismaClient } from '@prisma/client';
import { TEMPLATE_CATALOG } from '../src/lib/templates/template-catalog';

const prisma = new PrismaClient();

async function seedTemplates() {
  console.log('🌱 Seeding resume templates...');

  try {
    // Clear existing templates (optional - remove in production)
    await prisma.resumeTemplate.deleteMany({});
    console.log('🗑️  Cleared existing templates');

    // Seed templates from catalog
    for (const template of TEMPLATE_CATALOG) {
      const templateData = {
        id: template.id,
        name: template.name,
        category: template.category,
        description: template.description,
        preview: template.previewUrl || `/api/templates/${template.id}/preview`,
        isPremium: template.isPremium,
        atsOptimized: template.atsOptimized,
        styles: {
          figmaNodeId: template.figmaNodeId,
          dimensions: template.dimensions,
          sections: template.sections,
          colorScheme: template.colorScheme,
          layout: template.layout,
          hasPhoto: template.hasPhoto,
          tags: template.tags,
          metadata: {
            created: new Date().toISOString(),
            version: '1.0.0',
            compatibility: ['web', 'mobile', 'pdf'],
            atsScore: template.atsOptimized ? 95 : 80,
          }
        }
      };

      const createdTemplate = await prisma.resumeTemplate.create({
        data: templateData
      });

      console.log(`✅ Created template: ${createdTemplate.name} (${createdTemplate.id})`);
    }

    // Create additional template categories for future expansion
    const additionalTemplates = [
      {
        id: 'executive-classic',
        name: 'Executive Classic',
        category: 'executive',
        description: 'Traditional executive resume template with formal layout and conservative design',
        preview: '/api/templates/executive-classic/preview',
        isPremium: true,
        atsOptimized: true,
        styles: {
          figmaNodeId: 'placeholder',
          dimensions: { width: 595, height: 842 },
          sections: ['contact', 'executive-summary', 'experience', 'education', 'board-positions'],
          colorScheme: {
            primary: '#1a1a1a',
            secondary: '#666666',
            background: '#ffffff',
            text: '#333333'
          },
          layout: 'single-column',
          hasPhoto: false,
          tags: ['executive', 'formal', 'traditional', 'leadership'],
          metadata: {
            created: new Date().toISOString(),
            version: '1.0.0',
            compatibility: ['web', 'mobile', 'pdf'],
            atsScore: 98,
          }
        }
      },
      {
        id: 'academic-research',
        name: 'Academic Research CV',
        category: 'academic',
        description: 'Comprehensive academic CV template for researchers and professors',
        preview: '/api/templates/academic-research/preview',
        isPremium: true,
        atsOptimized: false, // Academic CVs have different requirements
        styles: {
          figmaNodeId: 'placeholder',
          dimensions: { width: 595, height: 1200 }, // Longer format for academic CVs
          sections: ['contact', 'education', 'research', 'publications', 'grants', 'teaching', 'conferences'],
          colorScheme: {
            primary: '#2c3e50',
            secondary: '#7f8c8d',
            background: '#ffffff',
            text: '#2c3e50'
          },
          layout: 'single-column',
          hasPhoto: false,
          tags: ['academic', 'research', 'publications', 'cv'],
          metadata: {
            created: new Date().toISOString(),
            version: '1.0.0',
            compatibility: ['web', 'mobile', 'pdf'],
            atsScore: 70, // Academic CVs prioritize content over ATS optimization
          }
        }
      },
      {
        id: 'creative-portfolio',
        name: 'Creative Portfolio',
        category: 'creative',
        description: 'Vibrant template for creative professionals with portfolio showcase',
        preview: '/api/templates/creative-portfolio/preview',
        isPremium: false,
        atsOptimized: false, // Creative roles often bypass ATS
        styles: {
          figmaNodeId: 'placeholder',
          dimensions: { width: 595, height: 842 },
          sections: ['contact', 'portfolio', 'experience', 'skills', 'education', 'awards'],
          colorScheme: {
            primary: '#e74c3c',
            secondary: '#f39c12',
            accent: '#9b59b6',
            background: '#ffffff',
            text: '#2c3e50'
          },
          layout: 'two-column',
          hasPhoto: true,
          tags: ['creative', 'portfolio', 'design', 'colorful'],
          metadata: {
            created: new Date().toISOString(),
            version: '1.0.0',
            compatibility: ['web', 'mobile', 'pdf'],
            atsScore: 60, // Creative templates prioritize visual appeal
          }
        }
      }
    ];

    for (const template of additionalTemplates) {
      const createdTemplate = await prisma.resumeTemplate.create({
        data: template
      });

      console.log(`✅ Created additional template: ${createdTemplate.name} (${createdTemplate.id})`);
    }

    console.log('🎉 Template seeding completed successfully!');
    
    // Print summary
    const totalTemplates = await prisma.resumeTemplate.count();
    const premiumTemplates = await prisma.resumeTemplate.count({ where: { isPremium: true } });
    const atsOptimizedTemplates = await prisma.resumeTemplate.count({ where: { atsOptimized: true } });
    
    console.log('\n📊 Template Summary:');
    console.log(`   Total templates: ${totalTemplates}`);
    console.log(`   Premium templates: ${premiumTemplates}`);
    console.log(`   ATS optimized: ${atsOptimizedTemplates}`);
    
    // Print templates by category
    const categories = await prisma.resumeTemplate.groupBy({
      by: ['category'],
      _count: { category: true }
    });
    
    console.log('\n📂 Templates by category:');
    categories.forEach(cat => {
      console.log(`   ${cat.category}: ${cat._count.category} templates`);
    });

  } catch (error) {
    console.error('❌ Error seeding templates:', error);
    throw error;
  }
}

async function main() {
  try {
    await seedTemplates();
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeder
if (require.main === module) {
  main();
}

export { seedTemplates };
