#!/usr/bin/env tsx

import { initializeDatabase, runHealthCheck } from '../src/lib/db/migrations';

async function main() {
  console.log('🚀 CVLeap Database Setup');
  console.log('========================');

  try {
    // Run health check first
    console.log('🔍 Checking database health...');
    const health = await runHealthCheck();
    
    console.log('Database Status:');
    console.log(`  Connection: ${health.database ? '✅' : '❌'}`);
    console.log(`  Tables: ${health.tables ? '✅' : '❌'}`);
    console.log(`  Seed Data: ${health.seed ? '✅' : '❌'}`);

    if (!health.database) {
      console.log('\n❌ Database connection failed!');
      console.log('Please ensure:');
      console.log('1. PostgreSQL is running');
      console.log('2. DATABASE_URL is correctly set in .env.local');
      console.log('3. Database "cvleap" exists');
      console.log('\nTo start PostgreSQL with Docker:');
      console.log('  docker-compose up -d postgres');
      process.exit(1);
    }

    if (!health.tables) {
      console.log('\n📋 Database tables not found.');
      console.log('Please run the following commands:');
      console.log('  npm run db:push    # Create tables');
      console.log('  npm run db:seed    # Add seed data');
      process.exit(1);
    }

    if (!health.seed) {
      console.log('\n🌱 Initializing database with seed data...');
      await initializeDatabase();
    }

    console.log('\n✅ Database setup completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Start the development server: npm run dev');
    console.log('2. Visit http://localhost:3000');
    console.log('3. Check database health: http://localhost:3000/api/health');

  } catch (error) {
    console.error('\n❌ Database setup failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
