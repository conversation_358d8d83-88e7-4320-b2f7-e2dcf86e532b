import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    // Check if test user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
    });

    if (existingUser) {
      console.log('✅ Test user already exists:', existingUser.id);
      return existingUser;
    }

    // Create test user
    const testUser = await prisma.user.create({
      data: {
        id: 'test-user',
        email: '<EMAIL>',
        name: 'Test User',
        emailVerified: new Date(),
        profile: {
          create: {
            phone: '******-0123',
            location: 'San Francisco, CA',
            summary: 'Test user for CVLeap development and testing',
          },
        },
      },
      include: {
        profile: true,
      },
    });

    console.log('✅ Test user created successfully:', testUser.id);
    return testUser;
  } catch (error) {
    console.error('❌ Failed to create test user:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  createTestUser()
    .then(() => {
      console.log('🎉 Test user setup complete!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test user setup failed:', error);
      process.exit(1);
    });
}

export { createTestUser };
