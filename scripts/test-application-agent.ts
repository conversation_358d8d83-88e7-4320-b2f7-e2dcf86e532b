import { PrismaClient } from '@prisma/client';
import { ApplicationAgent } from '../src/agents/application/application-agent';
import { agentRegistry } from '../src/lib/ai/agent-registry';

const prisma = new PrismaClient();

async function testApplicationAgent() {
  console.log('🧪 Testing Application Agent...\n');

  try {
    // Register the ApplicationAgent
    if (!agentRegistry.isRegistered('APPLICATION')) {
      agentRegistry.register(new ApplicationAgent());
      console.log('✅ ApplicationAgent registered successfully');
    } else {
      console.log('ℹ️ ApplicationAgent already registered');
    }

    // Create test user if not exists
    let testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
    });

    if (!testUser) {
      testUser = await prisma.user.create({
        data: {
          id: 'test-user-app',
          email: '<EMAIL>',
          name: 'Test User',
          emailVerified: new Date(),
          profile: {
            create: {
              phone: '******-0123',
              location: 'San Francisco, CA',
              summary: 'Test user for CVLeap development and testing',
            },
          },
        },
        include: {
          profile: true,
        },
      });
      console.log('✅ Test user created:', testUser.id);
    } else {
      console.log('ℹ️ Using existing test user:', testUser.id);
    }

    // Create test job if not exists
    let testJob = await prisma.job.findFirst({
      where: { title: 'Test Software Engineer' },
    });

    if (!testJob) {
      testJob = await prisma.job.create({
        data: {
          title: 'Test Software Engineer',
          company: 'TechCorp Inc.',
          location: 'San Francisco, CA',
          type: 'FULL_TIME',
          remote: true,
          salaryMin: 120000,
          salaryMax: 180000,
          salaryCurrency: 'USD',
          salaryPeriod: 'YEARLY',
          description: 'We are looking for a skilled software engineer to join our team. You will be responsible for developing scalable web applications using React, Node.js, and modern technologies.',
          requirements: [
            '5+ years of experience in software development',
            'Proficiency in React and Node.js',
            'Experience with cloud platforms (AWS, GCP)',
            'Strong problem-solving skills',
          ],
          skills: ['React', 'Node.js', 'TypeScript', 'AWS', 'Docker'],
          postedDate: new Date(),
          applicationDeadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          source: 'Company Website',
          url: 'https://techcorp.com/careers/software-engineer',
          atsKeywords: ['React', 'Node.js', 'JavaScript', 'TypeScript', 'AWS', 'Docker', 'Agile'],
        },
      });
      console.log('✅ Test job created:', testJob.id);
    } else {
      console.log('ℹ️ Using existing test job:', testJob.id);
    }

    // Create test resume if not exists
    let testResume = await prisma.resume.findFirst({
      where: { userId: testUser.id },
    });

    if (!testResume) {
      // First create a template if it doesn't exist
      let template = await prisma.resumeTemplate.findFirst({
        where: { name: 'Default' },
      });

      if (!template) {
        template = await prisma.resumeTemplate.create({
          data: {
            name: 'Default',
            category: 'Professional',
            description: 'Default resume template for testing',
            preview: 'https://example.com/preview.png',
            isPremium: false,
            atsOptimized: true,
            styles: {
              sections: ['header', 'summary', 'experience', 'education', 'skills'],
              layout: 'standard',
              colors: {
                primary: '#000000',
                secondary: '#666666',
              },
              fonts: {
                primary: 'Arial',
                secondary: 'Arial',
              },
            },
          },
        });
      }

      testResume = await prisma.resume.create({
        data: {
          userId: testUser.id,
          title: 'Test Resume - Software Engineer',
          content: `
JOHN DOE
Software Engineer
Email: <EMAIL> | Phone: (*************
LinkedIn: linkedin.com/in/johndoe | GitHub: github.com/johndoe

PROFESSIONAL SUMMARY
Experienced software engineer with 5+ years of expertise in full-stack development using React, Node.js, and cloud technologies. Proven track record of building scalable applications and leading development teams.

TECHNICAL SKILLS
• Frontend: React, TypeScript, JavaScript, HTML5, CSS3
• Backend: Node.js, Express, Python, RESTful APIs
• Cloud: AWS (EC2, S3, Lambda), Docker, Kubernetes
• Databases: PostgreSQL, MongoDB, Redis
• Tools: Git, Jenkins, JIRA, Agile methodologies

PROFESSIONAL EXPERIENCE

Senior Software Engineer | TechCorp Inc. | 2021 - Present
• Developed and maintained React-based web applications serving 100K+ users
• Improved application performance by 40% through code optimization and caching strategies
• Led a team of 5 developers in delivering a $2M revenue-generating e-commerce platform
• Implemented CI/CD pipelines reducing deployment time from 2 hours to 15 minutes

Software Engineer | StartupXYZ | 2019 - 2021
• Built scalable Node.js APIs handling 10M+ requests per day
• Collaborated with cross-functional teams to deliver features on tight deadlines
• Mentored junior developers and conducted code reviews

EDUCATION
Bachelor of Science in Computer Science | University of California | 2019
          `,
          templateId: template.id,
          isPublic: false,
        },
      });
      console.log('✅ Test resume created:', testResume.id);
    } else {
      console.log('ℹ️ Using existing test resume:', testResume.id);
    }

    console.log('\n🧪 Running Application Agent Tests...\n');

    // Test 1: Cover Letter Generation
    console.log('📝 Test 1: Cover Letter Generation');
    const coverLetterRequest = {
      jobTitle: testJob.title,
      companyName: testJob.company,
      jobDescription: testJob.description,
      resumeContent: testResume.content || '',
      tone: 'professional' as const,
      length: 'medium' as const,
    };

    const coverLetterResponse = await agentRegistry.execute('APPLICATION', {
      input: coverLetterRequest,
      context: {
        userId: testUser.id,
        sessionId: 'test-session-1',
        metadata: {
          test: true,
          operation: 'cover-letter-generation',
        },
      },
      options: {
        temperature: 0.7,
        maxTokens: 2000,
      },
    });

    if (coverLetterResponse.success) {
      console.log('✅ Cover letter generated successfully');
      console.log('📄 Content preview:', coverLetterResponse.data?.content?.substring(0, 200) + '...');
      console.log('📧 Title:', coverLetterResponse.data?.title);
      console.log('🎯 Key points:', coverLetterResponse.data?.keyPoints?.slice(0, 2));
    } else {
      console.log('❌ Cover letter generation failed:', coverLetterResponse.error);
    }

    // Test 2: Application Optimization
    console.log('\n🎯 Test 2: Application Optimization');
    const optimizationRequest = {
      jobDescription: testJob.description,
      resumeContent: testResume.content || '',
      targetRole: 'Senior Software Engineer',
      applicationHistory: [
        {
          jobTitle: 'Software Engineer',
          company: 'Previous Company',
          status: 'APPLIED',
          appliedDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        },
      ],
    };

    const optimizationResponse = await agentRegistry.execute('APPLICATION', {
      input: optimizationRequest,
      context: {
        userId: testUser.id,
        sessionId: 'test-session-2',
        metadata: {
          test: true,
          operation: 'application-optimization',
        },
      },
      options: {
        temperature: 0.6,
        maxTokens: 2500,
      },
    });

    if (optimizationResponse.success) {
      console.log('✅ Application optimization completed successfully');
      console.log('📊 Overall score:', optimizationResponse.data?.overallScore);
      console.log('🔍 Keyword matches:', optimizationResponse.data?.keywordMatches?.score);
      console.log('💡 Top recommendations:');
      optimizationResponse.data?.recommendations?.slice(0, 3).forEach((rec: any, index: number) => {
        console.log(`   ${index + 1}. [${rec.priority.toUpperCase()}] ${rec.suggestion}`);
      });
    } else {
      console.log('❌ Application optimization failed:', optimizationResponse.error);
    }

    // Test 3: Job Application Submission
    console.log('\n📤 Test 3: Job Application Submission');
    const applicationRequest = {
      jobId: testJob.id,
      resumeId: testResume.id,
      generateCoverLetter: true,
      customMessage: 'I am very excited about this opportunity and believe my skills align perfectly with your requirements.',
      applicationNotes: 'Test application submission',
    };

    const applicationResponse = await agentRegistry.execute('APPLICATION', {
      input: applicationRequest,
      context: {
        userId: testUser.id,
        sessionId: 'test-session-3',
        metadata: {
          test: true,
          operation: 'job-application',
        },
      },
      options: {
        temperature: 0.7,
        maxTokens: 2000,
      },
    });

    if (applicationResponse.success) {
      console.log('✅ Job application submitted successfully');
      console.log('🆔 Application ID:', applicationResponse.data?.applicationId);
      console.log('📋 Status:', applicationResponse.data?.status);
      console.log('📝 Cover letter generated:', applicationResponse.data?.coverLetterId ? 'Yes' : 'No');
      console.log('🎯 Recommendations count:', applicationResponse.data?.recommendations?.length || 0);
    } else {
      console.log('❌ Job application submission failed:', applicationResponse.error);
    }

    console.log('\n🎉 Application Agent testing completed!');

  } catch (error) {
    console.error('💥 Test failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
if (require.main === module) {
  testApplicationAgent()
    .then(() => {
      console.log('\n✅ All tests completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Tests failed:', error);
      process.exit(1);
    });
}

export { testApplicationAgent };
