import { PrismaClient } from '@prisma/client';
import { ApplicationAgent } from '../src/agents/application/application-agent';

const prisma = new PrismaClient();

async function verifySchemaFix() {
  console.log('🔍 Verifying CoverLetter Schema Fix...\n');

  try {
    // Test 1: Verify CoverLetter model schema
    console.log('📋 Test 1: CoverLetter Model Schema Verification');
    
    // Create a test cover letter directly with Prisma to verify schema
    const testCoverLetter = await prisma.coverLetter.create({
      data: {
        userId: 'test-user',
        title: 'Test Cover Letter - Schema Verification',
        content: 'This is a test cover letter to verify the schema fix.',
        jobId: null, // Optional field
      },
    });
    
    console.log('✅ CoverLetter created successfully with title field');
    console.log('🆔 Cover Letter ID:', testCoverLetter.id);
    console.log('📝 Title:', testCoverLetter.title);
    
    // Test 2: Verify ApplicationAgent CoverLetterResponse interface
    console.log('\n🤖 Test 2: ApplicationAgent Interface Verification');
    
    const agent = new ApplicationAgent();
    const testRequest = {
      jobTitle: 'Software Engineer',
      companyName: 'TestCorp',
      jobDescription: 'Test job description',
      resumeContent: 'Test resume content',
      tone: 'professional' as const,
      length: 'medium' as const,
    };
    
    // Test the cover letter generation (this will use mock responses in dev mode)
    const result = await agent.execute({
      input: testRequest,
      context: {
        userId: 'test-user',
        sessionId: 'test-session',
        metadata: { test: true },
      },
      options: {
        temperature: 0.7,
        maxTokens: 1000,
      },
    });
    
    if (result.success && result.data) {
      console.log('✅ ApplicationAgent cover letter generation successful');
      console.log('📝 Generated title:', result.data.title);
      console.log('📄 Content preview:', result.data.content?.substring(0, 100) + '...');
      console.log('🎯 Key points:', result.data.keyPoints?.slice(0, 2));
      
      // Verify the response has 'title' field instead of 'subject'
      if ('title' in result.data && !('subject' in result.data)) {
        console.log('✅ Interface correctly uses "title" field (not "subject")');
      } else {
        console.log('❌ Interface still has "subject" field or missing "title"');
      }
    } else {
      console.log('❌ ApplicationAgent cover letter generation failed:', result.error);
    }
    
    // Test 3: Clean up test data
    console.log('\n🧹 Test 3: Cleanup');
    await prisma.coverLetter.delete({
      where: { id: testCoverLetter.id },
    });
    console.log('✅ Test cover letter cleaned up');
    
    console.log('\n🎉 Schema Fix Verification Complete!');
    console.log('✅ All tests passed - CoverLetter schema mismatch resolved');
    
  } catch (error) {
    console.error('❌ Schema verification failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the verification
if (require.main === module) {
  verifySchemaFix()
    .then(() => {
      console.log('\n✅ Schema fix verification successful!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Schema fix verification failed:', error);
      process.exit(1);
    });
}

export { verifySchemaFix };
