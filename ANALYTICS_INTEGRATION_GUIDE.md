# CVLeap Analytics Integration Guide

## 🎯 Overview

This guide demonstrates how to integrate and use CVLeap's comprehensive Analytics, UX & Performance system across your application.

## 📊 Core Analytics Integration

### 1. Basic Event Tracking

```typescript
import { useAnalytics } from '@/lib/hooks/useAnalytics';
import { AnalyticsEventType } from '@/lib/analytics/analytics-service';

function ResumeBuilder() {
  const { trackEvent, trackInteraction } = useAnalytics();

  const handleResumeCreated = async (resumeData: any) => {
    // Track resume creation
    await trackEvent(AnalyticsEventType.RESUME_CREATED, {
      templateId: resumeData.templateId,
      resumeName: resumeData.title,
      sections: resumeData.sections.length,
      hasPhoto: !!resumeData.personalInfo.photo,
    });
  };

  const handleTemplateSelected = (templateId: string) => {
    // Track user interactions
    trackInteraction('template_selected', {
      templateId,
      category: 'professional',
      source: 'template_gallery',
    });
  };

  return (
    <div>
      {/* Your component JSX */}
    </div>
  );
}
```

### 2. Performance Monitoring

```typescript
import { performanceMonitor, PerformanceMetricType } from '@/lib/analytics/performance-monitor';

function APIService() {
  const generateResume = async (data: any) => {
    // Measure API performance
    const endMeasurement = performanceMonitor.measureApiCall('/api/resumes/generate', userId);
    
    try {
      const response = await fetch('/api/resumes/generate', {
        method: 'POST',
        body: JSON.stringify(data),
      });
      
      const result = await response.json();
      endMeasurement(); // Records the performance metric
      
      return result;
    } catch (error) {
      endMeasurement(); // Still record even on error
      throw error;
    }
  };
}
```

### 3. A/B Testing Integration

```typescript
import { abTestingEngine } from '@/lib/analytics/ab-testing';

function TemplateGallery() {
  const [variant, setVariant] = useState(null);
  const { session } = useSession();

  useEffect(() => {
    const loadVariant = async () => {
      if (session?.user?.id) {
        const userVariant = await abTestingEngine.getUserVariant(
          session.user.id,
          'template-gallery-layout'
        );
        setVariant(userVariant);
      }
    };
    loadVariant();
  }, [session]);

  const handleTemplateClick = async (templateId: string) => {
    // Track conversion for A/B test
    if (variant && session?.user?.id) {
      await abTestingEngine.trackConversion(
        session.user.id,
        'template-gallery-layout',
        'template_selected',
        1
      );
    }
  };

  // Render different layouts based on variant
  if (variant?.id === 'grid-layout') {
    return <GridTemplateGallery onTemplateClick={handleTemplateClick} />;
  } else {
    return <ListTemplateGallery onTemplateClick={handleTemplateClick} />;
  }
}
```

## 🚀 Onboarding System Integration

### 1. Initialize Onboarding

```typescript
import { OnboardingFlow } from '@/components/onboarding/OnboardingFlow';
import { OnboardingFlowType } from '@/lib/onboarding/onboarding-system';

function Dashboard() {
  const { session } = useSession();
  const [showOnboarding, setShowOnboarding] = useState(false);

  useEffect(() => {
    // Check if user needs onboarding
    const checkOnboarding = async () => {
      if (session?.user?.id) {
        const response = await fetch('/api/onboarding', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'check-progress',
            flowType: OnboardingFlowType.FIRST_TIME_USER,
          }),
        });
        
        const { progress } = await response.json();
        setShowOnboarding(!progress?.completedAt);
      }
    };
    
    checkOnboarding();
  }, [session]);

  return (
    <div>
      {showOnboarding && (
        <OnboardingFlow
          flowType={OnboardingFlowType.FIRST_TIME_USER}
          userId={session?.user?.id}
          onComplete={() => setShowOnboarding(false)}
        />
      )}
      {/* Your dashboard content */}
    </div>
  );
}
```

### 2. Custom Onboarding Steps

```typescript
import { onboardingSystem } from '@/lib/onboarding/onboarding-system';

// Create custom onboarding flow
const customFlow = {
  id: 'resume-builder-flow',
  type: 'feature_introduction',
  name: 'Resume Builder Introduction',
  description: 'Learn how to use the resume builder',
  steps: [
    {
      id: 'welcome',
      title: 'Welcome to Resume Builder',
      content: 'Let\'s create your first professional resume!',
      type: 'modal',
      target: null,
      actions: [{ type: 'continue', label: 'Get Started' }],
    },
    {
      id: 'template-selection',
      title: 'Choose a Template',
      content: 'Select a template that matches your industry',
      type: 'tooltip',
      target: '#template-gallery',
      actions: [
        { type: 'continue', label: 'Next' },
        { type: 'skip', label: 'Skip Tour' },
      ],
    },
    // More steps...
  ],
};

// Initialize custom flow
await onboardingSystem.createFlow(customFlow);
```

## 📈 Dashboard Analytics Integration

### 1. Display Analytics Dashboard

```typescript
import { AnalyticsDashboard } from '@/components/analytics/AnalyticsDashboard';

function UserDashboard() {
  const { session } = useSession();

  return (
    <div className="space-y-6">
      <h1>Your Resume Performance</h1>
      
      {session?.user?.id && (
        <AnalyticsDashboard userId={session.user.id} />
      )}
      
      {/* Other dashboard content */}
    </div>
  );
}
```

### 2. Custom Analytics Queries

```typescript
import { dashboardAnalytics } from '@/lib/analytics/dashboard-analytics';

function CustomAnalytics() {
  const [insights, setInsights] = useState(null);
  const { session } = useSession();

  useEffect(() => {
    const loadInsights = async () => {
      if (session?.user?.id) {
        const userInsights = await dashboardAnalytics.getDashboardInsights(session.user.id);
        setInsights(userInsights);
      }
    };
    loadInsights();
  }, [session]);

  const handleGoalCreation = async (goalData: any) => {
    await dashboardAnalytics.createGoal(session.user.id, {
      type: 'application_target',
      title: 'Monthly Applications',
      description: 'Apply to 20 jobs this month',
      target: 20,
      deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
    });
  };

  return (
    <div>
      {insights && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded-lg shadow">
            <h3>Success Rate</h3>
            <p className="text-2xl font-bold">{insights.overallSuccessRate}%</p>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow">
            <h3>Total Applications</h3>
            <p className="text-2xl font-bold">{insights.totalApplications}</p>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow">
            <h3>Interview Rate</h3>
            <p className="text-2xl font-bold">
              {((insights.totalInterviews / insights.totalApplications) * 100).toFixed(1)}%
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
```

## 🔧 API Integration Examples

### 1. Track Custom Events

```typescript
// Track AI content generation
const trackAIGeneration = async (section: string, success: boolean) => {
  await fetch('/api/analytics/track', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      eventType: 'AI_CONTENT_GENERATED',
      properties: {
        section,
        success,
        timestamp: new Date().toISOString(),
      },
    }),
  });
};

// Track feature usage
const trackFeatureUsage = async (feature: string, context: any) => {
  await fetch('/api/analytics/track', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      eventType: 'FEATURE_USED',
      properties: {
        action: feature,
        ...context,
      },
    }),
  });
};
```

### 2. Create A/B Test Experiments

```typescript
const createExperiment = async () => {
  const response = await fetch('/api/analytics/ab-testing', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      action: 'create',
      experiment: {
        name: 'Resume Template Recommendation',
        description: 'Test AI vs manual template recommendations',
        type: 'feature_comparison',
        variants: [
          {
            id: 'ai-recommendations',
            name: 'AI Recommendations',
            weight: 50,
            config: { useAI: true },
          },
          {
            id: 'manual-selection',
            name: 'Manual Selection',
            weight: 50,
            config: { useAI: false },
          },
        ],
        successMetrics: {
          primary: 'template_selection_rate',
          secondary: ['time_to_selection', 'user_satisfaction'],
        },
      },
    }),
  });
  
  return response.json();
};
```

## 🎯 Best Practices

### 1. Event Naming Convention

```typescript
// Use consistent event naming
const EventTypes = {
  // User actions
  USER_SIGNUP: 'user_signup',
  USER_LOGIN: 'user_login',
  
  // Resume actions
  RESUME_CREATED: 'resume_created',
  RESUME_UPDATED: 'resume_updated',
  RESUME_EXPORTED: 'resume_exported',
  
  // Feature usage
  TEMPLATE_SELECTED: 'template_selected',
  AI_CONTENT_GENERATED: 'ai_content_generated',
  
  // Application tracking
  JOB_APPLICATION_SUBMITTED: 'job_application_submitted',
  INTERVIEW_SCHEDULED: 'interview_scheduled',
} as const;
```

### 2. Performance Monitoring

```typescript
// Monitor critical user journeys
const monitorUserJourney = () => {
  // Track page load performance
  const endPageLoad = performanceMonitor.measurePageLoad('/resumes/builder', userId);
  
  // Track AI processing time
  const endAIProcessing = performanceMonitor.measureAIProcessing('resume_generation', userId);
  
  // Track database query performance
  const endDBQuery = performanceMonitor.measureDatabaseQuery('user_resumes', userId);
};
```

### 3. Privacy and Compliance

```typescript
// Ensure GDPR compliance
const trackWithConsent = async (eventType: string, properties: any) => {
  const hasConsent = await checkUserConsent();
  
  if (hasConsent) {
    await analyticsService.trackEvent({
      eventType,
      properties: {
        ...properties,
        // Remove PII
        email: undefined,
        fullName: undefined,
      },
    });
  }
};
```

## 🚀 Production Deployment

### 1. Environment Configuration

```bash
# Analytics configuration
ANALYTICS_ENABLED=true
ANALYTICS_SAMPLE_RATE=1.0
PERFORMANCE_MONITORING_ENABLED=true
AB_TESTING_ENABLED=true

# External integrations
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
MIXPANEL_TOKEN=your_mixpanel_token
AMPLITUDE_API_KEY=your_amplitude_key
```

### 2. Database Migration

```bash
# Run Prisma migration for analytics tables
npx prisma migrate deploy

# Seed initial onboarding flows
npx prisma db seed
```

### 3. Monitoring Setup

```typescript
// Set up error tracking for analytics
import { captureException } from '@sentry/nextjs';

const safeTrackEvent = async (eventType: string, properties: any) => {
  try {
    await analyticsService.trackEvent({ eventType, properties });
  } catch (error) {
    captureException(error);
    console.error('Analytics tracking failed:', error);
  }
};
```

This comprehensive analytics system provides CVLeap with enterprise-grade data collection, user behavior insights, performance monitoring, and optimization capabilities that will drive continuous improvement and business growth.
